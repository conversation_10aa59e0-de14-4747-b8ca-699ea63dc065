{"": "", " has entered manual underwriting, with the underwriting case number": "", " has not successfully passed the automatic {{rule}} check and has been declined.": "", " has not successfully passed the automatic underwriting check and has been declined.": "", ".pdf, .xls, .xlsx, .png, .jpg, .jpeg, .doc, .docx": "", "(Group Policy No. {{groupPolicyNo}})": "", "(wording waiting to be provided)": "(wording waiting to be provided)", "{{ objectType }} Info": "", "{{ stage }} Stage": "{{ stage }} Stage", "{{action}} UW Authority Configuration": "{{action}} UW Authority Configuration", "{{currentConditionEnumKey}} is not existed in factorEnums.": "{{currentConditionEnumKey}} is not existed in factorEnums.", "{{docTypes}} is a multi-version file": "", "{{elapsedDay}} Days Elapsed": "{{elapsedDay}} Days Elapsed", "{{fileName}} Log": "", "{{first}}_{{second}}": "{{-first}}_{{-second}}", "{{frequency}} {{type}}": "{{frequency}} {{type}}", "{{index}} Order No. {{orderNumber}}": "{{index}} Order No. {{orderNumber}}", "{{index}}. Location": "", "{{index}}. Location: {{title}}": "", "{{index}}. New Object": "", "{{index}}. Object: {{title}}": "", "{{levelname}} already exists": "{{levelname}} already exists", "{{mode}} Claim Stack Definition": "", "{{mode}} Details": "", "{{mode}} Invoice Details": "", "{{name}} company": "", "{{name}} Company": "{{name}} Company", "{{name}}_v{{version}}": "", "{{objectKeyName}}: {{objectNumber}}": "", "{{objectName}} {{index}}": "", "{{objectTitle}} Info": "", "{{premiumType}} (including tax and discount)": "{{premiumType}} (including tax and discount)", "{{premiumType}} (without tax and discount)": "{{premiumType}} (without tax and discount)", "{{prev}}-{{next}}": "{{prev}} - {{next}}", "{{rate}}{{separator}}{{date}}": "", "{{role}} Info": "{{role}} Info", "{{ruleTitle}} Generation Rule": "", "{{startDate}} ~ {{endDate}}": "{{startDate}} ~ {{endDate}}", "{{text}} %": "", "{{type}}: {{No}}": "{{type}}: {{No}}", "{Team Name} is not assigned any strategy, Confirm to submit?": "{{ team<PERSON><PERSON><PERSON> }} is not assigned any strategy, Confirm to submit?", "%": "", "+ Add": "+ Add", "+ Add loading/Discount": "", "+ Add New": "+ Add New", "+ Add New Master Agreement": "+ Add New Master Agreement", "+ Add New Rule": "", "+ Add New Team": "", "+ Add Rule Condition": "", "+ Upload": "", "< Back to Search": "", "< Back To task pool": "", "1st Screening Result": "", "2nd Screening Result": "", "A general rule without a specified Goods already exists. Please select a Goods.": "", "Abbreviation Name": "Abbreviation Name", "Accept": "", "Acceptance_in_Progess": "Acceptance in Progess", "Acceptedby": "Accepted by", "Accident Degree": "Accident Degree", "Accident Number in Last 3 Year": "Accident Number in Last 3 Year", "Accident Number Last Year": "Accident Number Last Year", "Accident Summary": "", "Account": "Account", "Account Balance": "Account <PERSON><PERSON>", "Account Holder Name": "", "Account Info": "Account Info", "Account Name": "", "Account No.": "Account No.", "Account Number": "Account Number", "Account Transaction Details": "Account Transaction Details", "Account Transaction Type": "Account Transaction Type", "Account Type": "Account Type", "Accumulated days from all pending proposal status": "Accumulated days from all pending proposal status: ", "Accumulated RB Allocation Amount": "Accumulated RB Allocation Amount", "Accumulated Value": "Accumulated Value", "Action(s)": "", "Activate": "", "Activate Case": "", "Activate Case On": "", "Activate\\Inactivate Flag": "", "Activate\\Inactivate History": "", "Actual Arrival Time": "Actual Arrival Time", "Actual Delivery Time": "Actual Delivery Time", "Actual Departure Time": "Actual Departure Time", "Actual Payable Amount": "Actual Payable Amount", "Actual Premium": "Actual Premium", "Actual SA": "", "Actual Total No. of Vehicles {{number}}": "Actual Total No. of Vehicles {{number}}", "Actual Total Premium {{totalPremium}}": "Actual Total Premium {{totalPremium}}", "Ad hoc Single top up": "Ad hoc Single top up", "Ad-hoc Notification": "Ad-hoc Notification", "Ad-hoc Single Top Up": "", "Add": "", "Add a Condition": "Add a Condition", "Add a Reminder": "Add a Reminder", "Add Account Info": "", "Add Attachment Type": "Add Attachment Type", "Add Comments": "", "Add Employee Category": "", "Add Extra Loading": "", "Add Factors": "Add Factors", "Add Insured Object": "", "Add Location Based Object": "", "Add Location-based Object": "", "Add New": "Add New", "Add New Comments": "", "Add New Level": "Add New Level", "Add New Master Agreement": "", "Add New Members": "", "Add New Organization": "", "Add New Proposal": "", "Add New Quotation": "Add New Quotation", "Add New SME Application": "Add New SME Application", "Add New Strategy": "Add New Strategy", "Add New Team": "Add New Team", "Add New Transaction": "", "Add Product": "Add Product", "Add/Update Time": "", "Additional Equipment": "Additional Equipment", "Additional Limit & Deductible Info": "", "ADDITIONAL_LIMIT_DEDUCTIBLE": "", "Address": "Address", "Address Info": "Address Info", "Address Type": "Address Type", "Adj Annual Prem": "", "Adj Annual Prem (Rate)": "", "Adj Annual Prem（Rate）": "", "Adj Net Prem": "", "Adj Net Premium": "", "Adjusted Net Premium": "Adjusted Net Premium", "Adjustment Comments": "", "Adjustment Date": "", "Adjustment Information": "", "Adult Number": "Adult Number", "Advanced Payment Amount": "Advanced Payment Amount: {{amount}} {{currency}}", "After": "After", "After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.": "After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.", "After modifying the information, it needs to be recalculated. Please confirm.": "", "Age": "", "Age: {{age}}": "", "Agency": "Agency", "Agency Name": "Agency Name", "Agency: {{agencyName}}": "", "Agent": "", "Agent Code": "", "Agent Name": "", "Aggregate Amount": "", "Aggregated Amount (Applying)": "Aggregated Amount (Applying)", "Aggregated Amount (Inforce)": "Aggregated Amount (Inforce)", "Aggregated Amount (Total)": "Aggregated Amount (Total)", "Agreement Settlement Rule": "Agreement Settlement Rule", "Airline Company": "Airline Company", "Alert": "<PERSON><PERSON>", "All": "", "All designated beneficiary information will be deleted. Please Confirm.": "", "All goods": "", "All goods categories": "", "All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?": "All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?", "All questions have been answered.": "All questions have been answered.", "All upload data you currently submit will be recorded. If the file verification failed, it will not be submitted.": "", "All upload data you currently submit will be recorded. If the file verified failed, it will not be saved.": "", "All upload data you currently submit will be recorded.If the file verification failed,it will not be submitted.": "", "Allocation": "Allocation", "Allocation Amount": "Allocation Amount", "Allocation Date": "Allocation Date", "Allocation Frequency": "", "Almost!": "", "Amount": "Amount", "Amount Name": "", "Ancestor Policy No.": "Ancestor Certificate No.", "Annual Prem": "", "Annual Prem. Methods": "", "Annual Premium": "", "Annual Standard Planned Premium": "", "Annual Standard Premium": "Annual Standard Premium", "Annualized Premium / Single Premium": "Annualized Premium / Single Premium", "Annualized Regular Premium": "", "Annuity": "Annuity", "Annuity Allocation History": "", "Annuity Amount": "Annuity Amount", "Annuity Defer Period Type": "Annuity Defer Period Type", "Annuity guarantee Period": "Annuity guarantee Period", "Annuity Info": "Annuity Info", "Annuity liability": "", "Annuity Payment Account": "Annuity Payment Account", "Annuity Payment Frequency": "Annuity Payment Frequency", "Annuity Payment Option": "Annuity Payment Option", "Annuity Payment Period Type": "Annuity Payment Period Type", "ANNUITY_INFO": "", "Applicable": "", "Application": "", "Application Date": "Application Date", "Application Item": "", "Application No": "Application No.", "Application No {{ appNo }}": "Application No: {{ appNo }}", "Application No.": "Application No.", "Application Type": "Application Type", "Applied to waive premium liability by POS": "Applied to waive premium liability by POS", "Applied Withdrawal Amount": "Applied Withdrawal Amount", "Appliedby": "Applied by", "Apply Master Plan": "", "Apply to all products": "", "Apply to partial paid proposal?": "Apply to partial paid proposal?", "Appointment Rate": "Appointment Rate", "Approval": "", "Approval Comment": "", "Approval Date": "Approval Date", "Approval History": "", "Approval Result": "", "Approval Time": "", "Approval_in_Progess": "Approval in Progess", "Approve": "", "Approvedby": "Approved by", "Are you sure to activate this case?": "Are you sure to activate this case?", "Are you sure to cancel?": "Are you sure to cancel?", "Are you sure to change {{type}}?": "", "Are you sure to clear all the upload records?": "", "Are you sure to delete current level?  If delete, the all level order will change.": "", "Are you sure to delete selected type? It will remove all files under this type.": "", "Are you sure to delete the level?": "Are you sure to delete the level?", "Are you sure to delete the level?  Current authority level is in force.": "", "Are you sure to delete the level? Current authority level is in force.": "", "Are you sure to delete this comment?": "", "Are you sure to delete this document?": "Are you sure to delete this document?", "Are you sure to delete this product?": "", "Are you sure to delete this task?": "Are you sure to delete this task?", "Are you sure to delete?": "Are you sure to delete?", "Are you sure to remove it?": "", "Are you sure to submit current authority configuration? After submitting, the status of level will become effective.": "", "Are you sure you want to approve the insurance application?": "", "Are you sure you want to decline the insurance application?": "", "Are you sure you want to release this UW task?": "", "Are you sure you want to return the application for the current product?": "", "Are you sure you want to return the entire submission?": "", "Are you sure you want to save the current sort?": "Are you sure you want to save the current sort?", "Are you sure you want to submit this insurance application?": "", "Arrival Airport": "Arrival Airport", "Arrival Delay Time": "Arrival Delay Time", "Assign": "", "Assignee": "Assignee", "ASSIGNEE": "", "Assignee Type": "Assignee Type", "Associated goods": "Associated goods", "Associated Policy": "", "Associated Proposal": "", "Associated UW Task": "", "Associated UW Task Application No.": "Associated UW Task Application No.", "At least one item must be created under the address, please check.": "", "At least one should be selected": "", "at_least_one_input": "Please enter at least one search criteria", "Attached To": "Attached To: {{ productName }}", "Attached To Product": "", "Attachment": "Attachment", "ATTACHMENT": "Attachment", "Attachment Configuration": "Attachment Configuration", "Attachment Quotation Stage": "Attachment Quotation Stage", "Attachment Type": "Attachment Type", "ATTACHMENT_BUNDLE": "", "Attachment/Mandatory": "Attachment/Mandatory", "Attachments": "Attachments", "Authority Configuration": "Authority Configuration", "Authorization Agreement": "", "Authorization Agreement No.": "", "Authorization Agreement Signing Date": "", "Auto": "Auto", "Auto compliance rule result": "Auto compliance rule result", "Auto Compliance Rule Result": "", "Auto Rebalancing": "Auto Rebalancing", "Auto Rebalancing Close": "Auto Rebalancing Close", "Auto Rebalancing Opened": "Auto Rebalancing Opened", "Auto Renewal": "Auto Renewal", "Auto UW Rule Result": "Auto UW Rule Result", "Auto Verification Rule Result": "Auto Verification Rule Result", "AUTO_UW_RULE_RESULT_V2": "", "Automated Fill Condition": "", "Automatic Compliance Result": "", "Automatic Premium Loan": "Automatic Contribution Loan", "Automatic Premium Loan Detail": "Automatic Contribution Loan Detail", "Automatic Underwriting Result": "", "Autopay Flag": "Autopay Flag", "Average Driving Distance": "Average Driving Distance", "Back": "Back", "Back to Edit": "", "Back to main page": "Back to main page", "Back to Modify": "", "Back to Search": "Back to Search", "Back To task pool": "", "Back To Worksheet": "", "Bank Account No.": "", "Bank Address": "", "Bank Branch Address": "", "Bank Branch Code": "", "Bank Branch Name": "", "Bank Code": "", "Bank Code/Bank Name": "", "Bank Name": "", "Bank Transfer / Account Type": "Bank Transfer / Account Type", "Based on Calendar Days": "", "basic info": "basic info", "Basic Info": "Basic Info", "Basic Info Info": "Basic Info Info", "BASIC_INFO": "Basic Info", "BASIC_INFO_VIEW": "Basic Info", "BASIC_INFO_WITH_STAND_ALONE": "", "BASIC_INFO_WITHOUT_GOODS": "Basic Info", "Batch list Goods": "Goods", "Batch List Upload": "Batch List Upload", "Batch List Upload History": "Batch List Upload History", "Batch Number": "Batch Number", "Batch Reassign": "", "Batch Uploading": "Batch Uploading", "BCP": "", "Be Attached By": "Be Attached By: {{productNameList}}", "Before (Rate/Amount)": "Before (Rate/Amount)", "Before Tax & Service Fee": "", "Beneficial Owner": "", "BENEFICIAL_OWNER": "", "Beneficiary": "", "BENEFICIARY": "", "Beneficiary Ratio": "Beneficiary <PERSON><PERSON>", "Beneficiary ratio is": "Beneficiary ratio is", "Beneficiary Type": "", "BENEFICIARY_CN": "BENEFICIARY", "BeneficiaryRatio": "Beneficiary <PERSON><PERSON>(%)", "beneficiaryRatioTooltip": "The total beneficiary ratio for all beneficiaries must equal 100%. Please confirm.", "Benefit": "Benefit", "Benefit Account Balance": "Benefit Account <PERSON>", "Benefit Allocation History": "Benefit Allocation History", "Benefit Amount": "Benefit Amount", "Benefit info": "Benefit info", "Benefit Info": "Benefit Info", "Benefit Next Due Date": "Benefit Next Due Date", "Benefit Option": "", "Benefit Payment Account Info": "Benefit Payment Account Info", "Benefit Schedule": "Benefit Schedule", "BENEFITS_INFO": "", "Bill Amount": "<PERSON>", "Bill Information": "Bill Information", "bill_amount_details": "<PERSON>", "Bill_No": "Bill No.", "Bind Task Assignment Rule": "", "Birthday": "", "blacklist_search_result_unit": "Record", "Body Type": "Body Type", "BONUS": "", "Bonus Allocation History": "", "Bonus Amount": "Bonus Amount", "Bonus Code": "", "Bonus Info": "Bonus Info", "Bonus Name": "", "Bonus Next Due Data": "", "Bonus Next Due Date": "Bonus Next Due Date", "Bonus Payment Account Info": "Bonus Payment Account Info", "Bonus Total Balance": "Bonus Total Balance", "Bonus Type": "", "Bonus/malus": "Bonus/malus", "Booking Number": "Booking Number", "Booking Time": "Booking Time", "Both": "Both", "BOTH_GOODS_CANNOT_BE_ISSUED_SIMULTANEOUSLY {{primaryGoods}}{{secondaryGoods}}": "", "Branch Name: {{name}}": "Branch Name: {{name}}", "Building Info": "Building Info", "Building Size": "Building Size", "Building Type": "Building Type", "Building up the File, please wait patiently.": "Building up the File, please wait patiently.", "Built Year": "Built Year", "Burglar Alarm": "Burglar Alarm", "Business Activity/Sector": "Business Activity/Sector", "Business License No.": "Business License No.", "Business Month": "Business Month", "Business No": "Business No", "Business No. Generation Rule": "Business No. Generation Rule", "Business No. n Generation Rule": "", "Business Scenario": "", "Business Transaction No": "", "Business Transaction No.": "Business Transaction No.", "Business Type": "Business Type", "Buy/Sell": "Buy/Sell", "By Commission Category": "By Commission Category", "By Product": "By Product", "BY_EVENT_INFO": "", "by:": "", "By: userName": "By: {{userName}}", "By:{{creator}}": "", "ByEvent_Policy_Data": "ByEvent Certificate Data", "Calculate": "Calculate", "Calculate successfully": "", "Calculate Tax when Clear Account": "Calculate Tax when Clear Account", "Calculation Basis": "", "Calculation Date": "", "Calculation Direction": "Calculation Direction", "Calculation Frequency": "Calculation Frequency", "Calculation Level": "Calculation Level", "Calculation Order": "Calculation Order", "Calculation/Capitalization Period": "Calculation/Capitalization Period", "Campaign": "", "Campaign Discount": "Campaign Discount", "Campaign Discount Type": "Campaign Discount Type", "Campaign info": "", "Campaign Info": "", "Campaign Type": "Campaign Type", "Campaign_code": "Campaign Code", "Campaign_name": "Campaign Name", "Cancel": "Cancel", "Cancel pin": "", "Canceled": "", "Cancelled": "Cancelled", "Car Owner": "Car Owner", "Car Owner & Driver & Renter": "Car Owner & Driver & Renter", "Car Owner Birthday": "Car Owner Birthday", "Car Owner Gender": "Car Owner Gender", "Car Owner ID Number": "Car Owner ID Number", "Car Owner ID Type": "Car Owner ID Type", "Car Owner Name": "Car Owner Name", "Car Owner Name2": "", "Car Owner Name3": "", "Carring Goods Type": "Carring Goods Type", "Case Operation": "", "Case Owner": "", "Cash Before Cover": "", "Cash Bonus": "Cash Bonus", "Cash Bonus Account Transaction Details": "Cash Bonus Account Transaction Details", "Cash Bonus Allocation Details": "Cash Bonus Allocation Details", "Cash Value Saving Account": "Cash Value Saving Account", "Certificate No": "Certificate No", "Change Payment Info": "", "Change Principal": "", "Change the current process flow from the process flow template": "", "Change Underwriter": "", "Changes made after submission will not be revoked.": "", "Channel": "Channel", "Channel Name": "Channel Name", "Channel User No.": "Channel User No. {{channelUserNo}}", "channelCode": "Channel Code.", "channelCode{{channelCode}}": "Channel Code. {{channelCode}}", "channelUserNo": "Channel User No.", "channelUserNo{{channelUserNo}}": "Channel User No. {{channelUserNo}}", "Charge Amount": "", "Charge Code": "", "Charge Due Date": "", "Charge Period": "", "Charge Type": "", "Chassis No.": "Chassis No.", "Check relatives or not": "Check relatives or not", "Check Segment": "", "Check staff is on duty or not": "Check staff is on duty or not", "Children Number": "Children Number", "City": "", "Claim": "", "CLAIM": "", "Claim Amount": "<PERSON><PERSON><PERSON>", "Claim Compensation": "", "Claim Experience": "", "Claim History": "Claim History", "Claim Name": "", "Claim No": "", "Claim No.": "Claim No.", "Claim Number in Last 3 Year": "Claim Number in Last 3 Year", "Claim Number Last Year": "Claim Number Last Year", "Claim Ratio": "<PERSON><PERSON><PERSON>", "Claim Stack": "<PERSON><PERSON><PERSON>", "Claim Status": "Claim Status", "Claim Summary": "", "Claim Type": "Claim Type", "Claim Waive": "<PERSON><PERSON><PERSON>", "Claim_Archives_Room": "Claim Archives Room", "CLAIM_DETAILS": "", "CLAIM_HISTORY": "", "CLAIM_INFO": "", "Claimable Period": "Claimable Period", "Clause Information": "Clause Information", "Clear all": "", "Clear All": "", "Clear successed!": "", "Clear Successfully": "", "Click Add another type": "", "Click on the policy version number below to switch policy version information.": "", "Click or drag file here area to upload": "Click or drag file here area to upload.", "Click or drag file here to upload": "Click or drag file here to upload", "Click or drag the file here to upload": "Click or drag the file here to upload", "close": "", "Close": "Close", "Closed": "", "Code": "Code", "Code No. {{code}}": "Code No. {{code}}", "Collapse": "Collapse", "Collapse All": "Collapse All", "Collected Amount(Original Currency)": "Collected Amount(Original Currency)", "Collected Amount(Policy Currency)": "Collected Amount(Certificate Currency)", "Collection & Refund": "Collection & Refund", "Collection Amount Detail": "Collection Amount Detail", "Collection Method": "", "COLLECTION_AND_REFUND": "", "collection_and_refund_amount": "Collection&Refund Amount", "collection_or_refund_amount": "Collection/Refund Amount", "collection_refund_amount_details": "Collection&Refund Amount Details", "collection_refund_item": "Collection/Refund Item", "Color of Plate No": "Color of Plate No", "Combination Sequence": "", "Comment": "", "COMMENTS": "Comments", "Comments: {{remark}}": "", "Commision": "", "Commission": "Commission", "Commission (On Top of Premium)": "", "Commission & Service Fee": "Commission & Service Fee", "Commission Details": "Commission Details", "Commission Generated Date": "Commission Generated Date", "COMMISSION_AGENT": "", "COMMISSION_AND_SERVICE_FEE": "", "Commission（On Top of Premium)": "Commission（On Top of Contribution)", "CommissionAmount": "Commission Amount", "CommissionType": "Commission Type", "Commodity Information": "", "Common Info": "", "Compaign Discount": "", "Company Name": "Company Name", "Compiance History": "", "Complete": "Complete", "Complete Date": "Complete Date", "Complete Time": "", "Completed Date": "Completed Date", "compliance": "compliance", "Compliance": "", "Compliance Check": "Compliance Check", "Compliance Decision": "Compliance Decision", "Compliance Decision Details": "Compliance Decision Details", "Compliance History": "", "Compliance Info": "Compliance Info", "Compliance Result": "", "Compliance Status": "Compliance Status", "Compliance Task No.": "Compliance Task No.", "Compliance Type": "", "COMPLIANCE_INFO": "", "COMPLIANCE_RESULT": "", "Concurrent Case": "", "CONCURRENT_CASE": "", "Conditional Accepted": "Conditional Accepted", "Configure the Task Assignment Rule first": "", "Confim to delete this plan?": "", "Confirm": "Confirm", "Confirm Application": "Confirm Application", "Confirm Date": "Confirm Date", "Confirm to clear all data?": "", "Confirm to delete": "", "Confirm to delete current document?": "Confirm to delete current document?", "Confirm to delete?": "Confirm to delete?", "Confirmation Date": "Confirmation Date", "Confirmation Required": "", "Confirmed Fund Price": "Confirmed Fund Price", "Consentee": "Consentee", "CONSENTEE": "", "Contact Address": "", "Contact Person": "Contact Person", "Contact Person Info": "Contact Person Info", "Contact Phone Info": "Contact Phone Info", "ContactPerson": "", "content": "", "Content": "Content", "Continue to Submit": "", "Contract Date": "Contract Date", "Contract Number": "Contract Number", "Copy": "Copy", "copy failed": "", "Copy from": "", "Copy from {{tabName}} Product": "", "Copy from Motor Product": "", "Copy Master Agreement": "Copy Master Agreement", "Copy master agreement  to Relational Policy": "", "Copy successful!": "", "Copy Successfully": "", "Copy To": "", "Copy to New Product": "Copy to New Product", "Copy to New Version": "Copy to New Version", "Country": "Country", "Country Code": "", "Country of Residence": "", "Coverage": "Coverage", "Coverage / Sub Coverage": "", "Coverage Detail": "Coverage Detail", "Coverage Details": "", "Coverage Info": "", "Coverage Level": "", "Coverage Period": "Coverage Period", "Coverage Period Type": "Coverage Period Type", "Coverage Period Value": "Coverage Period Value", "Coverage Plan": "", "Coverage plan cannot be matched due to incompleted information, please complete the object information first.": "", "Coverage Plan Details": "", "Coverage plan will be refreshed, please confirm.": "", "Coverage Total Prem": "", "COVERAGE_GOODS_DETAIL_BUNDLE": "", "COVERAGE_INFO": "Coverage Info", "COVERAGE_INFO_DRAWER_CLAIM_STACK": "<PERSON><PERSON><PERSON>", "COVERAGE_INFO_DRAWER_COVERAGE_DETAILS": "Coverage Details", "COVERAGE_INFO_DRAWER_DCA_ARRANGEMENT": "DCA Arrangement", "COVERAGE_INFO_DRAWER_FUND_APPOINTMENT": "Fund Appointment", "COVERAGE_INFO_DRAWER_LIABILITY": "Liability", "COVERAGE_INFO_DRAWER_PORTFOLIO_REBALANCING": "Portfolio Rebalancing", "COVERAGE_INFO_DRAWER_PREMIUM_INFO": "Premium Info", "COVERAGE_INFO_DRAWER_RETIREMENT_OPTION": "Retirement Option", "COVERAGE_INFO_DRAWER_TOP_UP": "Top Up", "COVERAGE_PLAN": "Coverage Plan", "COVERAGE_PLANS": "", "COVERAGE_PREMIUM": "", "COVERAGE_PREMIUM_BUNDLE": "", "Create Date": "Create Date", "Create Policy under Master Agreement": "Create Policy under Master Agreement", "Create task assignment rule to match {{module}}.": "", "Create team and bind task assignment rule. The {{module}} hit binded rule will be pushed into this team.": "", "create time": "create time", "Creation Date": "", "Creator": "Creator", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_amount_Ap": "{{currency}} - {{amount}}", "currency_combine_amount": "{{currency}} {{amount}}", "Current Handler": "Current Handler", "Current Policy Change Overview": "Current Policy Change Overview", "Current Policy Overview": "", "Current Premium Amount": "Current Premium Amount", "Current process flow": "", "Current system date": "Current system date", "Current Underwriting Level": "Current Underwriting Level", "Current Version": "", "Currently policyholder & insured are different person. But after the change, we find their info is the same and system will treat them as one person. Confirm to merge them?": "Currently policyholder & insured are different person. But after the change, we find their info is the same and system will treat them as one person. Confirm to merge them?", "currentOperator": "Current Operator", "Customer": "", "Customer Grade": "", "Customer ID Number": "", "Customer List": "Customer List", "Customer Screening Result": "", "Customer Type": "", "Customer type not selected yet. Please confirm.": "", "CUSTOMER_INFO": "", "CUSTOMER_PROFILE": "", "Daily": "Daily", "Data Entry in Progress, Pending Proposal Check, Waiting for Issuance": "Data Entry in Progress, Pending Proposal Check, Waiting for Issuance", "Data is modified but not saved. Do you want to save the modified content?": "Data is modified but not saved. Do you want to save the modified content?", "Date of birth": "", "Date Of Birth": "Date Of Birth", "Date Of Witness": "Date Of Witness", "Date Quote Needed": "Date Quote Needed", "DAY": "", "Day(s)": "Day(s)", "Days": "Day(s)", "DayS": "Days", "Days Types": "", "DCA": "", "DCA Amount (for each period)": "DCA Amount (for each period)", "DCA Arrangement": "DCA Arrangement", "DCA Frequency": "DCA Frequency", "Death Date: {{dateOfDeath}}": "", "Debit Note Amount": "Debit Note Amount", "Debit Note Information": "Debit Note Information", "Debit Note Information ": "", "Debit Note No.": "", "Debit Note No. No. {{debitNoteNo}}": "", "Deceased": "", "Decision": "Decision", "Decision Details": "", "Decision Fail Decline": "", "Decision Failed": "", "Decision Reason": "Decision Reason", "Decision Successfully": "", "Decision Time": "", "Declaration Date": "Declaration Date", "Declaration Stage": "Declaration Stage", "Declaration_Information": "Health Declaration Information", "Decline": "", "Deducted From Investment": "Deducted From Investment", "Deductible": "", "Deductible Amount": "Deductible Amount", "Deductible Info": "", "Deduction Source": "Deduction Source", "Deduction Type": "Deduction Type", "Defer Period": "Defer Period", "Delete": "", "Delete success": "", "Delete Successfully": "", "Deleting a goods will also delete all corresponding data, please confirm.": "", "Delivery Status": "Delivery Status", "Department Code": "", "Departure": "Departure", "Departure Airport": "Departure Airport", "Departure City": "Departure City", "Departure Country": "Departure Country", "Departure Delay Time": "Departure Delay Time", "Departure Point": "Departure Point", "Departure Time Zone": "Departure Time Zone", "Deposit Account Balance": "Depo<PERSON>t Account <PERSON>", "Description": "", "Designated Beneficiary": "", "Designated Beneficiary :": "", "Designated beneficiary ratio must equal to 100%": "", "Destination": "Destination", "Destination City": "Destination City", "Destination Country": "Destination Country", "Destination Region": "Destination Region", "Destination Time Zone": "Destination Time Zone", "detail": "Detail", "Details": "Details", "Device Abs": "<PERSON><PERSON>", "Device AEB": "Device AEB", "Device Airbag": "<PERSON><PERSON>", "Device Alarm": "<PERSON><PERSON>", "Device Brand": "<PERSON>ce <PERSON>", "Device Buyer ID": "Device Buyer ID", "Device Buyer Review Score": "Device Buyer Review Score", "Device Category": "Device Category", "Device Description": "Device Description", "Device Gear or steering lock": "Device Gear or steering lock", "Device GPS": "Device GPS", "Device ID": "Device ID", "Device Immobiliser": "Device Immobiliser", "Device Info": "Device Info", "Device Installed": "Device Installed", "Device Manufacturer": "Device Manufacturer", "Device Market Value": "Device Market Value", "Device Model": "Device Model", "Device Name": "Device Name", "Device Number": "Device Number", "Device Perchuase Time": "<PERSON><PERSON>", "Device Price": "<PERSON><PERSON>", "Device Seller ID": "<PERSON><PERSON>", "Device Seller Review Score": "<PERSON><PERSON> Review Score", "Device Status": "Device Status", "Device Tracking": "<PERSON>ce Tracking", "Device User": "Device User", "Digit Length": "", "Digit Position": "", "Direct sales": "", "Disbursement Method Information": "Disbursement Method Information", "Discount": "", "Discount Amount": "Discount Amount", "Discount Period (Premium Due No)": "Discount Period (Contribution Due No)", "discount_type": "Discount Type", "Distance Confirmation Date": "Distance Confirmation Date", "Distribution Method": "", "Ditrh of birth": "", "Dividend Payment Method": "", "DOB": "", "Document": "", "Document Generation": "Document Generation", "Document Generation Management": "Document Generation Management", "Document Name": "Document Name", "Document Name {{currentImgTitle}}": "Document Name: {{currentImgTitle}}", "Document Type": "Document Type", "Document({{total}})": "", "Documents": "Documents", "Dollar Cost Averaging": "Dollar Cost Averaging", "Don't Need Reminder": "Don't Need <PERSON>minder", "Don't Use Sub-item": "Don't Use Sub-item", "Down Sell SA": "", "Download ({{size}})": "Download ({{size}})", "Download {{size}}": "", "Download & Send Password": "", "Download All": "", "Download All Event Policies": "Download All Event Policies", "Download E-Policy": "", "Download failed": "Download failed", "Download successfully": "Download successfully", "Download Template": "Download Template", "DRAFT": "", "Drive ID Type": "Drive ID Type", "Driver": "Driver", "Driver Birthday": "Driver Birthday", "Driver Experience": "Driver Experience", "Driver Gender": "Driver Gender", "Driver ID Number": "Driver ID Number", "Driver IdNumber": "<PERSON> Id<PERSON><PERSON><PERSON>", "Driver IdType": "Driver IdType", "Driver Industry": "Driver Industry", "Driver Information": "", "Driver License Number": "Driver License Number", "Driver License Registration Date": "Driver License Registration Date", "Driver Marital Status": "Driver Marital Status", "Driver MaritalStatus": "Driver <PERSON><PERSON><PERSON><PERSON>", "Driver Name": "Driver Name", "Driver Name2": "", "Driver Name3": "", "Driver Occupation": "Driver Occupation", "Driver Tier": "Driver Tier", "Driver Type": "Driver Type", "Driving Distance": "Driving Distance", "Driving Experience": "", "Driving License No.": "Driving License No.", "Driving License Registration Date": "", "Driving Licensen No.": "", "Due Date": "", "due to the change of insured information": "due to the change of insured information", "Due Unpaid Premium": "", "Duplicate configuration, please check.": "", "Duplicate Master Agreement No.": "Duplicate Master Agreement No.", "Duplicated Tag": "", "Duration": "", "E-mail": "", "E-mail Info": "E-mail Info", "E-policy": "E-policy", "Each product chooses at least one liability": "", "EB_COVERAGE_GOODS_DETAIL": "", "EB_COVERAGE_PREMIUM": "", "EB_INSURED": "", "Edit": "Edit", "Edit by": "", "Edit By": "", "Edit Document": "", "Edit Extra Loading": "", "Edit Name": "", "Edit Now": "Edit Now", "Edit Process Flow": "", "Edit Strategy": "Edit Strategy", "Edit Team": "", "Editing": "Editing", "effective": "effective", "Effective": "", "Effective Date": "Effective Date", "Effective Date Time Zone": "Effective Date Time Zone", "Effective Period": "Effective Period", "Effective Sub Policy": "", "Effective Time": "Effective Time", "EffectiveDate": "Effective Date", "ELECTRONIC_EQUIPMENT": "", "Email": "Email", "Employee Category": "", "Employee Category {{No}}": "", "EMPLOYEE_GROUP": "", "End": "", "End Day": "", "End Time": "End Time", "Engine No.": "Engine No.", "Enrollment Transaction": "", "Entry Time": "Entry Time", "Error": "", "Error File": "Error File", "Error happened during upload.": "Error happened during upload.", "Error Notification": "", "Escalate": "Escalate", "Escalate or Reassign": "Escalate or Reassign", "Escalate Stage": "Escalate Stage", "Escalate Successfully!": "Escalate Successfully!", "Escalation Task": "Escalation Task", "esclated/esclate": "esclated/esclate", "Estimate Lapse Date": "Estimate Lapse Date", "Estimated Total  Premium": "", "Estimated Total No. of Vehicles": "Estimated Total No. of Vehicles", "Evaluatedby": "Evaluated by", "Evaluation Decision": "Evaluation Decision", "Evaluation_in_Progess": "Evaluation in Progess", "Event Policy Issue Switch": "Event Certificate Issue Switch", "Event Policy No.": "Event Certificate No.", "Event Policy Upload": "Event Policy Upload", "Event Policy Upload History": "Event Policy Upload History", "Every {{frequency}}": "Every {{value}} {{unit}}", "Examination Date": "", "Examination Description": "", "Examination Result": "", "Exchange Rate": "Exchange Rate", "Exchange Rate (PremCur/BaseCur)": "", "Exchange Rate (SaCur/BaseCur)": "", "Exchange Rate (SaCur/PremCur)": "", "Exchange Rate Date (PremCur/BaseCur)": "", "Exchange Rate Date (SaCur/BaseCur)": "", "Exchange Rate Date (SaCur/PremCur)": "", "Excluding Promotion Discount": "Excluding Promotion Discount", "Exclusion": "Exclusion", "EXCLUSION": "", "Exclusion Category": "", "Exclusion Clause": "", "Exclusion Code": "", "Exclusion Content": "", "Exclusion List": "", "Exclusion Lists": "", "Exclusion Reason": "", "Expand": "Expand", "Expand All": "", "Expired": "", "Expired Date": "", "Expiry": "", "Expiry Date": "Expiry Date", "Expiry Date should be later than effective date": "Expiry Date should be later than effective date", "Expiry Date Time Zone": "Expiry Date Time Zone", "ExpiryDate": "Expiry Date", "Export": "", "Export All": "", "Export Log": "", "Export Time": "", "External Name": "", "External_Doc_No": "External Doc No.", "Extra Loading": "Extra Loading", "Extra Loading amount": "", "Extra Loading Type": "Extra Loading Type", "Extra Premium": "Extra Contribution", "Extra Premium Due Day": "Extra Premium Due Day", "Extra Setting": "Extra Setting", "Extract Period": "Extract Period", "Extract period was entered error, please check!": "Extract period was entered error, please check!", "Extract Policy Condition": "Extract Policy Condition", "Fail Reason": "Fail Reason", "Failed Reason": "", "Failed Record List": "Failed Record List", "Failed Records": "", "Falculative": "Falculative", "fee_status": "Ujrah Status", "Feedback": "", "FeeType": "Ujrah Type", "FF Weight": "FF Weight", "Fidelity Guarantee": "Fidelity Guarantee", "Field can only be digital or letter": "Field can only be digital or letter", "Field Value": "", "Fields Name": "Fields Name", "File Creator": "Creator", "File Name": "File Name", "File Name: {{fileName}}": "", "File size cannot exceed": "", "File Upload Time": "Upload Time", "Fill In": "Fill In", "Fill in from Vehicle List": "", "Fill in the Exisiting Customer": "", "Fill in the Exisiting Customer >>": "", "Fill in the existing account": "", "Fill in the Existing Account": "Fill in the Existing Account", "Filter": "", "Final Decision": "Final Decision", "Final Underwriting Level": "Final Underwriting Level", "FINAL_DECISION": "", "Finance": "", "FINISH_PAYMENT": "", "Fire Alarm": "Fire Alarm", "First Name": "", "first.": "", "Fold Menu": "", "Follow the Investment Strategy": "Follow the Investment Strategy", "Follow the same appointment rate as planned premium and top ups.": "Follow the same appointment rate as planned premium and top ups.", "For Liability": "For Liability", "For List Data": "For List Data", "For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.": "For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.", "For Product": "", "For Regular Data": "For Regular Data", "For single cash value type, only one formula could be defined. No duplicate allowed.": "For single cash value type, only one formula could be defined. No duplicate allowed.", "For Team": "For Team", "For the same rule or rule set, only one record is allowed.": "For the same rule or rule set, only one record is allowed.", "Formula Category": "", "Formula Code": "", "FR Weight": "FR Weight", "Free amount of Liability SA": "", "Free Investment Amount": "Free Investment Amount", "Free_amount_of_liability_sa": "Free Amount of Liability SA", "Free_policy_info": "Free Certificate Information", "Free_policy_no": "Free Certificate No.", "Frequency": "", "Frequency of Payment": "", "Fronting": "Fronting", "Full Name": "Full Name", "Full Records": "", "Fund": "Fund", "Fund Allocation": "Fund Allocation", "Fund Application Date": "Fund Application Date", "Fund Appointment": "Fund Appointment", "Fund Appointment After Rebalancing": "Fund Appointment After Rebalancing", "Fund Appointment Before Rebalancing": "Fund Appointment Before Rebalancing", "Fund Appointment For Rebalancing": "Fund Appointment For Rebalancing", "Fund Balance": "Fund Balance", "Fund Code": "Fund Code", "Fund Currency": "Fund Currency", "Fund Name": "Fund Name", "Fund Price": "Fund Price", "Fund Transaction Date": "Fund Transaction Date", "Fund transaction details": "Fund transaction details", "Fund Transaction Details": "Fund Transaction Details", "Fund Value": "Fund Value", "Fund Value After Rebalancing": "Fund Value After Rebalancing", "Fund Value Before Rebalancing": "Fund Value Before Rebalancing", "G Weight": "G Weight", "Gender": "Gender", "Generate": "", "Generate Offer": "", "Generate Policy Schedule": "", "Generate Premium": "", "Generate Quotation Form": "Generate Quotation Form", "Generate/Regenerate Reason": "Generate/Regenerate Reason", "Generated Date": "Generated Date", "Generation Time": "Generation Time", "Get Result": "", "Gift Code": "Gift Code", "Gift Delivery Dimension": "Gift Delivery Dimension", "Gift Delivery Method": "Gift Delivery Method", "Gift Delivery Time": "Gift Delivery Time", "Go to New Quote": "", "Go to Quote Bound": "", "Go to Quote Sent": "", "Goods": "Goods", "GOODS": "", "Goods cannot be empty": "", "Goods Category": "Goods Category", "Goods Code/GoodsName": "", "Goods in Transit": "Goods in Transit", "Goods is not on sale, please check.": "", "Goods Name": "Goods Name", "Goods Name and Model": "Goods Name and Model", "Goods Summary": "", "Goods Version": "Goods Version", "GoodsName": "Goods Name: {{goodsName}}", "GoodsVersion": "GoodsVersion", "Got it": "", "Green Card Fee": "", "Green Card No": "Green Card No", "Group Level": "", "Group No": "", "Group Personal Accident": "Group Personal Accident", "Group Policy": "Group Policy", "Group Policy No.": "", "Group Policy No. {{groupPolicyNo}}": "", "Group Policy Query": "", "Guarantee Period": "Guarantee Period", "HALFYEAR": "", "has not successfully passed the automatic underwriting check and has been declined.": "", "has Original Pol": "has Original Pol", "has successfully passed the automatic check.": "", "Haulage Permit No": "Haulage Permit No", "HEADER_MORE_INFO": "", "High Level": "High Level", "Hight of Vehicle": "Hight of Vehicle", "Historical Permium Holiday": "", "Historical Premium Holiday": "", "History": "", "History Type": "History Type", "HOLDER": "", "Home Protection Schema (HPS) Exemption: {{hpsExemption}}": "Home Protection Schema (HPS) Exemption: {{hpsExemption}}", "Hospital": "", "Hospital Name": "", "Hour(s)": "", "How to Deal with Balance Amount": "How to Deal with Balance Amount", "How to Use": "", "IBAN": "", "ID Card": "", "ID No.": "", "ID NO.": "ID NO.", "ID No. ": "", "ID Type": "ID Type", "Identifier Info": "Identifier Info", "If": "If", "If satisfy all of the following conditions": "If satisfy all of the following conditions", "If satisfy any of the following conditions": "If satisfy any of the following conditions", "If the file you want to upload does not belong to the above file type, click Add another type": "If the file you want to upload does not belong to the above file type, click Add another type", "If the option is selected, the system will generate reminder based on days only. Otherwise, specific times will be considered.": "", "If the option is selected, the system will withdraw the proposal based on days only. Otherwise, specific times will be considered.": "", "Ignore the rule and proceed": "", "ILP Bonus": "", "Image": "Image", "Immediate Effect": "", "Import Type": "Import Type", "Inactivate Case": "", "Inactivate Case On": "", "Incident Date": "Incident Date", "Incident Reason": "Incident Reason", "Including Promotion Discount": "Including Promotion Discount", "Individual": "Individual", "Individual Policy Upload": "", "Individual Policy Upload Under Master Agreement": "", "Industrial Classification": "Industrial Classification", "Info not Completed": "", "Initial Number": "", "Initial Premium Amount": "Initial Premium Amount", "Initial Premium Collected": "", "Initial Premium Due": "", "Initial Premium Payment": "Initial Premium Payment", "Initial Principal": "Initial Principal", "Initial_Premium_Payment_Method": "Initial Contribution Payment Method", "Initiallment_Premium_Payment_Method": "Installment Contribution Payment Method", "Initiate Manual Underwriting": "", "Input": "Input", "Inspection Expiry Date": "Inspection Expiry Date", "Installment": "", "Installment / Renewal Premium Payment": "Installment / Renewal Premium Payment", "Installment No.": "", "Installment Number": "", "Installment Premium": "Installment Contribution", "Installment Premium (Before Campaign)": "Installment Contribution (Before Campaign)", "Installment Premium (Before Tax & Service Fee)": "Installment Contribution (Before Tax & Service Ujrah)", "Installment Premium (Total)": "Installment Contribution (Total)", "Installment Premium period": "Installment Premium period", "Installment_Premium_Bill": "Installment Contribution Bill", "Installment_Premium_including_tax_discount": "Installment Contribution (including tax and discount)", "Installment_Premium_without_tax_discount": "Installment Contribution (without tax and discount)", "Instruction": "", "Insurable Interest": "Insurable Interest", "Insurance": "<PERSON><PERSON><PERSON>", "Insured": "Insured", "INSURED": "Insured", "Insured (Main Product)": "Insured (Main Product)", "Insured Certificate Type": "Insured Certificate Type", "Insured Email": "Insured Email", "Insured ID No": "Insured ID No.", "Insured Id No.": "", "Insured Info": "", "Insured Info will be cleared and replaced by policyholder Info. Are you sure to proceed?": "Insured Info will be cleared and replaced by policyholder <PERSON><PERSON>. Are you sure to proceed?", "Insured Location": "", "Insured Name": "Insured Name", "Insured Name: {{insuredName}}": "", "Insured Name2": "Insured Name2", "Insured Object": "", "Insured Object Category": "", "Insured Object Information": "", "Insured Object Name": "", "Insured Object Name: {{name}}": "", "Insured Object of Building": "Insured Object of Building", "Insured Object of Device": "Insured Object of Device", "Insured Object of Loan Guarantee": "", "Insured Object of Order": "Insured Object of Order", "Insured Object of Pet": "Insured Object of Pet", "Insured Object of Product": "", "Insured Object of Vehicle": "Insured Object of Vehicle", "Insured Object Type": "", "Insured Query": "", "Insured Type": "Insured Type", "INSURED_CN": "Insured", "Insured_Object_Info": "Insured Object Info", "INSURED_OBJECT_INFO": "", "INSURED_OBJECT_PROFILE": "", "insured_period": "Benefit Period", "InsuredIDNo": "Insured ID No", "Insuresd ID No.": "Insuresd ID No.", "Insuresd Name": "Insuresd Name", "Interest": "", "Interest Balance": "Interest Balance", "Interest Balance Change": "Interest Balance Change", "Interest Rate": "Interest Rate", "Internal Offset Amount": "", "Invalid": "", "Investment & Loan Info": "Investment & Loan Info", "Investment Horizon": "Investment Horizon", "Investment Info": "Investment Info", "Investment Strategy": "Investment Strategy", "INVESTMENT_INFO": "", "Invoice No.": "", "Invoice Number": "", "Invoice Received Date": "", "Invoice Status": "", "Invoice Task Pool": "", "Invoice/Credit Note Number": "", "is Loan Vehicle": "is Loan Vehicle", "is New Vehicle": "is New Vehicle", "is Not Registed": "is Not Registed", "Is Renewal Policy": "Is Renewal Policy", "Is renewal quote process required?": "Is renewal quote process required?", "is Special Shape Vehicle": "is Special Shape Vehicle", "Issue": "", "Issue Date": "Issue Date", "Issue Policy": "", "Issue policy under master agreement": "", "Issue successfully": "", "Issue Tips": "", "Issue without payment": "Issue without payment", "Issue Without Payment": "", "Issue without payment for individual policy": "", "ISSUE_AGENT": "", "Issued": "", "Issued successfully": "", "Issued successfully!": "", "Issured Name": "", "It is not supported to save exactly the same type information": "", "item": "<PERSON><PERSON>", "Item Code": "", "Item Name": "Item Name", "Key Node": "", "Kindly search the Policy No. first.": "", "Label": "", "Label: Value": "", "Laibility Detail": "Laibility Detail", "Landline": "", "Language": "", "Lapsed": "Lapsed", "Lapsed Reason": "Lapsed Reason", "LapsedDate": "Lapsed Date", "LapsedReason": "Lapsed Reason", "Last Decision": "Last Decision", "Last Name": "", "Last Operation Time": "", "Last Price Date": "Last Price Date", "Last Update User": "", "Last Year Driving Distance": "Last Year Driving Distance", "Latest Interest Amount": "Latest Interest Amount", "Latest Interest Calculation Date": "Latest Interest Calculation Date", "Latest Interest Capitalization Date": "Latest Interest Capitalization Date", "Latest Status": "", "latitude": "latitude", "Legal {{type}}": "Legal {{type}}", "Legal Beneficiary": "Legal Beneficiary", "Legal Representative Info": "Legal Representative <PERSON><PERSON>", "Legal Trustee": "Legal Trustee", "Length of Vehicle": "Length of Vehicle", "Length: {{length}}": "", "Level": "", "Level Name": "Level Name", "Level Order": "Level Order", "Levy": "<PERSON>", "Liabilities": "Liabilities", "Liability": "", "Liability Category": "", "Liability Coverage Period": "", "Liability ID": "", "Liability Name": "Liability Name", "Liability Premium": "", "Liability SA": "", "Lien": "", "Lien Exclusion Clause": "", "Limit": "", "Limit Info": "", "Link Product": "", "Linked Investment Product": "", "List Data": "List Data", "Loading": "", "Loading & Down Sell": "", "Loading List": "", "Loading Method": "", "Loading Period": "", "Loading Period Type": "", "Loading Type": "", "Loading Value": "", "Loan Balance": "<PERSON><PERSON>", "Loan Balance Details": "Loan Balance Details", "Loan Company": "Loan Company", "Loan Contract No.": "Loan Contract No.", "Loan Effective Date": "Loan Effective Date", "Loan Info": "Loan <PERSON>", "Loan Provider": "<PERSON>an Provider", "Loan Years": "Loan Years", "Location {{index}}": "", "Location Based Object": "", "Location Details": "", "Location-based Object": "", "longitude": "longitude", "Machinery Breaakdown": "<PERSON><PERSON>", "MACHINERY_EQUIPMENT": "", "Main": "", "Main / Rider": "", "Main Benefit": "Main Benefit", "Main Condition Type": "Main Condition Type", "Main Driving Area": "Main Driving Area", "Main Insured": "Main Insured", "Main Insured ID No.": "", "Main Insured ID Type": "", "Main Insured Name": "", "Main Insured Sub Policy No.": "", "Main Product": "Main Product", "Main_Rider": "Main/Rider", "Manager": "Manager", "Manager has been changed. Please check.": "Manager has been changed. Please check.", "Mandatory": "", "Manual Compliance Decision": "", "Manual Input": "Manual Input", "Manual UW Query": "", "Manual UW Task Pool": "Manual UW Task Pool", "Marital Status": "", "Market Price": "Market Price", "MARKET_SEGMENTATION": "", "Marketing Goods Selection": "Marketing Goods Selection", "Marketing Goods Settings": "Marketing Goods Settings", "marriageStatus": "Marriage Status", "Master Agreement Change": "", "Master Agreement Effective Date": "Master Agreement Effective Date", "Master Agreement Effective Time": "Master Agreement Effective Time", "Master Agreement Expiry Date": "Master Agreement Expiry Date", "Master Agreement No {{busiNo}}": "Master Agreement No {{busiNo}}", "Master Agreement No.": "Master Agreement No.", "Master Agreement No. {{masterPolicylNo}}": "", "Master Agreement No. {{masterPolicyNo}}": "", "Master Agreement No.: {{masterPolicyNo}}": "", "Master Agreement Status": "Master Agreement Status", "Master Agreement Sub-category": "Master Agreement Sub-category", "Master Agreement Task Pool": "", "Master Insured Name": "Master Insured Name", "Master Policy": "Master Policy", "Master Policy No.": "master policy no.", "Master Policyholder": "Master Policyholder", "Master Policyholder Name": "Master Policyholder Name", "MASTER_AGREEMENT_BASIC_INFO": "", "Master_policy_Status": "Master Certificate Status", "masterPolicyChangeProcessed": "", "masterPolicyChangeProcessing": "", "Matched Tag": "Matched Tag", "Mater Policy No": "", "Mater Policy No.": "", "Maturity Agreement": "Maturity Agreement", "Maturity Benefit": "Maturity Benefit", "Maturity Benefit Account Transaction Detail": "Maturity Benefit Account Transaction Detail", "Maturity Date": "Maturity Date", "Maturity Reminder Date Compare to Policy Expiry Date": "Maturity Reminder Date Compare to Certificate Expiry Date", "MaximumPaymenttime": "Maximum Payment time", "Medical Examination": "Medical Examination", "Medical examination is not allowed to edit after issue, confirm to issue": "Medical examination is not allowed to edit after issue, confirm to issue", "Medical examination is not allowed to edit after issue, confirm to issue?": "", "Medical Examination Item": "Medical Examination Item", "Medical Examination Item ": "", "Medical Examination Plan": "", "Medical Expense Invoice": "", "Medical Plan": "Medical Plan", "Medical Plan Code": "", "Medical Plan Name": "Medical Plan Name", "Medical Plan Value": "Medical Plan Value", "Medical Requirement Status": "", "Method of adding account": "", "Min Premium": "Min Contribution", "Min-Premium Type / Min-Premium": "Min-Premium Type / Min-Premium", "Min-Premium Type / Min-Premium should be completed or empty": "Min-Premium Type / Min-Premium should be completed or empty", "Minimum Investment Period": "Minimum Investment Period", "Minimum Investment Period Type": "Minimum Investment Period Type", "Minimum Investment Period Value": "Minimum Investment Period Value", "Minimum Length": "", "Minimum Protection Value": "", "MIP End Date: {{endDate}}": "MIP End Date: {{endDate}}", "MIP Start Date: {{stateDate}}": "MIP Start Date: {{stateDate}}", "Mobile": "Mobile", "Mobile Phone": "Mobile Phone", "Model Portfolio": "Model Portfolio", "Model Portfolio Code": "", "Model Portfolio Details": "", "Model Portfolio Name": "Model Portfolio Name", "Modifying the selection of sub coverage will clear the configured limit and deductible information. Please confirm.": "", "MONTH": "", "Month(s)": "", "Monthly": "Monthly", "More Action": "", "More Info": "", "Motor NCD": "Motor NCD", "MOTOR_FLEET_POLICY_PREMIUM": "", "MOTOR_FLEET_VEHICLE_UPLOAD": "", "MPV": "", "Msg_back_to_policy_info": "Back to Certificate Info", "Msg_claim_claim_applied_by": "Claim Applied by", "Msg_claim_claim_evaluated_by": "Claim Evaluated by", "Msg_claim_claim_evaluation_date": "Claim Evaluation Date", "Msg_claim_claimant_id_no": "Claimant ID No.", "Msg_claim_claimant_id_type": "Claimant ID Type", "Msg_claim_claimant_name": "Claimant Name", "Msg_claim_insured_email": "Insured Email", "Msg_claim_insured_id_no": "Insured ID No.", "Msg_claim_Insured_ID_Type": "Insured ID Type", "Msg_claim_insured_mobile_number": "Insured Mobile Number", "Msg_claim_insured_name": "Insured Name", "Msg_claim_last_document_received_date": "Last Document Received Date", "Msg_claim_last_update_date": "Last Update Date", "Msg_claim_newly_received": "<PERSON>ly Received", "Msg_claim_over_30day": "Over 30D", "Msg_claim_Payment_Method": "Payment Method", "Msg_claim_pend_reason": "Pend Reason", "Msg_claim_pending_case_status": "Pending Case Status", "Msg_claim_product_name": "Product Name", "Msg_claim_query_Claim_Query": "<PERSON><PERSON><PERSON>", "Msg_claim_registered_by": "Claim Registered by", "Msg_claim_registration_date": "Claim Registration Date", "Msg_claim_report_date": "Report Date", "Msg_common_query_POS_Capture_Date": "POS Capture Date", "Msg_common_query_POS_Captured_By": "POS Captured By", "Msg_common_query_POS_Item": "POS Item", "Msg_common_query_POS_No": "POS No.", "Msg_common_query_POS_Registered_By": "POS Registered By", "Msg_common_query_POS_Registration_Date": "POS Registration Date", "Msg_common_query_POS_Status": "POS Status", "Msg_common_query_Sort_by_POS_No": "Sort by POS No.", "Msg_common_relationship_name": "Relationship Name", "Msg_customer_service_item": "POS Item", "Msg_day": "Day", "Msg_Days": "Days", "Msg_detail_Contract_Information": "Contract Information", "Msg_error_passwordToE": "Password sending failed", "Msg_Market_Master_Policy_No": "Master Certificate No.", "Msg_Month": "Month", "Msg_Months": "Months", "Msg_moreinfo": "More Info", "Msg_paymentperiod_single": "Single", "Msg_paymentperiod_wholelife": "Wholelife", "Msg_paymentperiod_years_old": "Years Old", "Msg_please_input_right_format": "Please input right format", "Msg_Pos_basic_info": "POS Basic Info", "Msg_Pos_change_reason": "Change reason", "Msg_Pos_Other_Change_Reason": "Other Change reason", "Msg_pos_query_posQuery": "POS Query", "Msg_query_Acount_Info": "Acount Info", "Msg_query_Actual_Amount": "Actual Amount", "Msg_query_Additional_Excess": "Additional Excess", "Msg_query_answer": "Client's answer", "Msg_query_Arrival_Place": "Arrival Place", "Msg_query_Arrival_Place_ID": "Arrival Place ID", "Msg_query_Arrival_Place_Name": "Arrival Place Name", "Msg_query_Ascending": "Ascending", "Msg_query_Ascending_Order": "Ascending Order", "Msg_query_Auction_Item": "Auction Item", "Msg_query_Body_Type": "Body Type", "Msg_query_brand_premium_partner": "Brand Contribution Partner (Service Company Partner)", "Msg_query_Claim_Documentations": "Claim Documentations", "Msg_query_Claim_Number": "Claim Number", "Msg_query_Claim_Workflow": "Claim Workflow", "Msg_query_collection": "Collection", "Msg_query_Contract_Effective_Date": "Contract Effective Date", "Msg_query_Contract_No": "Contract No.", "Msg_query_Contract_Termination_Date": "Contract Termination Date", "Msg_query_Contract_Type": "Contract Type", "Msg_query_Contract_Value": "Contract Value", "Msg_query_Create_Time": "Create Time", "Msg_query_data_source": "Data Source", "Msg_query_Delay": "Delay", "Msg_query_Delivery_Information": "Delivery Information", "Msg_query_Departure_Place": "Departure Place", "Msg_query_Departure_Place_ID": "Departure Place ID", "Msg_query_Departure_Place_Name": "Departure Place Name", "Msg_query_Descending": "Descending", "Msg_query_Descending_Order": "Descending Order", "Msg_query_Documentation_Name": "Documentation Name", "Msg_query_Download_Send_Pas": "Download & Send Password", "Msg_query_Download_successful": "Download successful", "Msg_query_downLoadError": "Download failure", "Msg_query_Engine_Capacity": "Engine Capacity", "Msg_query_FIN": "FIN", "Msg_query_Generated_Date": "Generated Date", "Msg_query_generatedDate": "Generated Date", "Msg_query_Gts": "GST", "Msg_query_Home_Appliance_Information": "Home Appliance Information", "Msg_query_Image_info": "Image info", "Msg_query_Insured_Object_Basic_Information_Change": "Insured Object Basic Information Change", "Msg_query_Insured_Object_Information": "Insured Object Information", "Msg_query_Insured_Type": "Insured Type", "Msg_query_insuredStatusText": "The Insured is in blacklist", "Msg_query_Is_Main": "Is Main", "Msg_query_Liability_Category": "Liability Category", "Msg_query_Liability_Name": "Liability Name", "Msg_query_Make": "Make", "Msg_query_MCC_code": "MCC Code", "Msg_query_MCC_name": "MCC Name", "Msg_query_Meal_Type": "Meal Type", "Msg_query_merchant_name": "Merchant Name", "Msg_query_Model": "Model", "Msg_query_more10000": "Query results are more than 10000. Please optimize search criteria", "Msg_query_no": "No.", "Msg_query_Number_of_Item_type": "Number of Item type", "Msg_query_Number_of_Item_Type": "Number of Item Type", "Msg_query_Object_Category": "Object Category", "Msg_query_occupation_class": "Occupation Class", "Msg_query_Order_ID": "Order ID", "Msg_query_Order_Price": "Order Price", "Msg_query_Order_Status": "Order Status", "Msg_query_Order_Time": "Order Time", "Msg_query_Original_Start_Date": "Original Start Date", "Msg_query_Payable": "Payable", "Msg_query_Payment_Info": "Payment Info", "Msg_query_Policy_Documentations": "Certificate Documentations", "Msg_query_Policy_Issue_Date": "Certificate Issue Date", "Msg_query_POS_Documentations": "POS Documentations", "Msg_query_POS_Workflow": "POS Workflow", "Msg_query_productName_version": "Goods Name _Version", "Msg_query_Rate_Type": "Rate Type", "Msg_query_Receipts": "Receipts", "Msg_query_Receivable": "Receivable", "Msg_query_Reconciliation_Status": "Reconciliation Status", "Msg_query_records": "Records", "Msg_query_refund": "Refund", "Msg_query_Refund": "Refund", "Msg_query_Registration_Date": "Registration Date", "Msg_query_Scheduled_Arrival_Time": "Scheduled Arrival Time", "Msg_query_Scheduled_Departure_Time": "Scheduled Departure Time", "Msg_query_Seating_Capity": "Seating Capacity", "Msg_query_sendEmail": "Password has been sent to the mailbox", "Msg_query_service_type": "Service Type", "Msg_query_snack_modifier": "Snack+ Modifier (Membership Modifier)", "Msg_query_Sort_by_Claim_No": "Sort by <PERSON><PERSON>m No.", "Msg_query_Sort_by_Relation_Policy_No": "Sort by Relational Certificate No.", "Msg_query_Sort_by_Relation_Pos_No": "Sort by <PERSON><PERSON><PERSON>anl Pos No.", "Msg_query_Sort_by_report_date": "Sort by Report Date", "Msg_query_Source": "Source", "Msg_query_Status": "Status", "Msg_query_Sum_Assured": "Sum Assured", "Msg_query_Tax_Detail": "Tax Detail", "Msg_query_Tax_Rate_Value": "Tax Rate/Value", "Msg_query_Tax_Type": "Tax Type", "Msg_query_The_policy_holder_is_in_blacklist": "The certificate holder is in blacklist", "Msg_query_titleDes": "* This is an encrypted file. The password will be sent to your email.", "Msg_query_total": "Total", "Msg_query_transactionNo": "Transaction No", "Msg_query_transactionType": "Transaction Type", "Msg_query_Transport_Information": "Transport Information", "Msg_query_Transportation_Number": "Transportation Number", "Msg_query_trigger_category": "Trigger Category", "Msg_query_type": "Type", "Msg_query_unique_ID": "Unique ID", "Msg_query_Use_of_Vehicle": "Use of Vehicle", "Msg_query_Vehicl_No": "Vehicle No.", "Msg_query_Vehicle": "Vehicle", "Msg_query_Vehicle_Age": "Vehicle Age", "Msg_query_Vehicle_Identification_No": "Vehicle Identification No.", "Msg_query_View_all": "View all", "Msg_query_Year_of_Make": "Year of Make", "Msg_reconciliation_channel": "Sales Channel", "Msg_Relation_Pos_Number": "Relational POS No.", "Msg_Total": "Total", "Msg_transaction_type": "Transaction Type", "Msg_version": "Version", "Msg_Virtural_Insured": "Virtural Insured", "Msg_week": "Week", "Msg_weeks": "Weeks", "Msg_Years": "Years", "MULTI_BENEFICIARY": "", "MULTI_INSURED": "Insured", "MULTI_PAYER": "", "MULTIPLE_OBJECT_INFO": "", "My Task": "", "N Year Risk Amount": "", "name": "Name", "Name": "Name", "nameCombine": "{{code}}_{{name}}", "Named Insured": "Named Insured", "Nationality": "Nationality", "NCD": "", "NCD %": "NCD %", "NCD Amount": "NCD Amount", "Need Advanced Payment": "Need Advanced Payment", "Need DCA arrangement?": "Need DCA arrangement?", "Need Vehicle Examination": "", "Net Prem": "", "Net Premium": "Net Premium", "New": "New", "New Business": "New Business", "New Business & Renewal Configuration": "New Business & Renewal Configuration", "New Business Configuration": "New Business Configuration", "New Business Info": "", "New Document": "New Document", "New Master Agreement": "New Master Agreement", "New SA": "", "New Vehicle": "", "NEW_BUSINESS_INFO": "", "Next": "Next", "Next Due Date": "Next Due Date", "Next Due Date: {{date}}": "", "Next Rebalancing Due Date": "Next Rebalancing Due Date", "NextPremiumIncludingTax": "Next Contribution (including tax)", "NextPremiumWithoutTax": "Next Contribution (without tax)", "NLG Benefit:": "NLG Benefit:", "no": "No", "NO": "", "No attachment has been uploaded under the selected product.": "", "No case number found": "", "No Claim Discount": "No Claim Discount", "No discount configured for current period": "No discount configured for current period", "No record": "--", "No Results": "", "No valid application elements: INSURED": "", "No valid application elements: PAYER": "", "No valid application elements: POLICY_HOLDER": "", "No Valid Data": "", "No valid master policy is available for this normal policy renewal, please check.": "", "no_data": "No Data", "No_Data": "", "No.": "No.", "No. {{appNo}}": "", "No. {{policyNo}}": "", "No. of Accident Free Years": "No. of Accident Free Years", "noData": "No Data", "Nominee": "<PERSON><PERSON><PERSON>", "NOMINEE": "", "Non Standard Tariff": "", "Non-Location": "", "Non-location Based Object": "", "None": "None", "Normal Policy List": "", "NORMAL_INSURED": "", "NORMAL_PAYMENT": "", "NORMAL_POLICY_HOLDER": "", "normalPolicyList": "", "Not Within Premium Holiday": "Not Within Premium Holiday", "Note": "", "Notice Reminder": "", "Notification History": "Notification History", "Notification Type": "Notification Type", "Now You Can Create the Team": "", "Number of Accident": "", "Number of Active Policies": "Number of Active Policies", "Number of Employees": "", "Number of Installment": "Number of Installment", "Number of Luggage": "Number of Luggage", "Number of Objects": "Number of Objects", "Number of Order": "Number of Order", "Number of Pending Policies": "Number of Pending Policies", "Number of Records": "Number of Records", "Number of Renewal Time": "Number of Renewal Time", "Number of Seat": "Number of Seat", "Number of task assign when user ask": "Number of task assign when user ask", "Number of Vehicle Owned": "Number of Vehicle Owned", "Object": "", "Object ID": "Object ID", "OBJECT LIST": "", "OBJECT_INFO": "", "Occupation": "Occupation", "Off-Peak Car": "Off-Peak Car", "OIB": "", "Only Failed": "Only Failed", "Only insured": "Only insured", "Only policyholder": "Only policyholder", "Only view image files?": "", "Open Menu": "", "Open the link in a new window": "", "Operation": "", "Operation History": "Operation History", "operation time": "operation time", "Operation_Approve": "approve by", "Operation_Capture": "Captured by", "Operation_Current": "Current Operator by", "Operation_Decide": "Decision Made by", "Operation_Operate": "Operator by", "Operation_Register": "Register by", "OPERATIONS_COMMENTS": "", "Operator": "", "OPT_IN Check Text": "Opt-in Stage has no manual workbench. Please make sure the rule decisions don't include \"Manual\" decision. Only \"Accept\" and \"Reject\" decisions are allowed. ", "Opt-In Check": "Opt-In Check", "Opt-In Rules": "Opt-In Rules", "Opt-In-Rules": "", "Optional Covers": "Optional Covers", "Optional Text": "", "Order Currency": "Order Currency", "Order Date": "Order Date", "Order ID": "Order ID", "Order Info": "Order Info", "Order No.": "Order No.{{no}}", "Order Number": "Order Number", "Order Price": "Order Price", "Order the Teams while the assignment rule are same": "Order the Teams while the assignment rule are same", "Order Type": "Order Type", "Order Value": "Order Value", "Ordinary condition": "Ordinary condition", "Organization": "Organization", "Organization ID No.": "", "Organization ID Type": "", "Organization ID Type/No.": "", "Organization Name": "", "Origin SA": "", "Original Master Agreement No.": "", "Original Pol No": "Original Pol No", "Original SA": "", "Original Start Date": "", "Original Sum Assured": "Original Sum Assured", "Original_policy_no": "Original Certificate No.", "Other": "", "Other Information": "", "Other Policy Info": "Other Policy Info", "Other Product": "", "Other Properties": "", "OTHER_PARTY_ROLES": "Other Party Roles", "OTHER_PARTY_ROLES_CN": "Other Party Roles", "Others": "Others", "Over All": "Over All", "Overriding Commission": "", "Package": "Package", "Package Code": "", "Package Level": "", "Package Name": "Package Name", "PackageName": "Package Name: {{packageName}}", "Packages": "", "Packing Information": "", "Pad with zeros": "", "Parcel": "<PERSON><PERSON><PERSON>", "Parcel Number": "Pa<PERSON>el <PERSON>", "Parcel Tier": "<PERSON><PERSON><PERSON>", "Parcel Value": "Parcel Value", "Parcel Volume": "Parcel Volume", "Parcel Weight": "<PERSON><PERSON><PERSON>", "Part Questionnaire Purpose": "Part Questionnaire Purpose", "Partner": "Partner", "partnerCode": "", "partnerType": "", "Pass": "Pass", "Passed": "Passed", "Pay Account": "", "pay_frequency": "Contribution Payment Frequency", "PAYEE": "", "Payee Info": "", "Payee Name": "", "Payee Type": "", "Payer": "", "payer of Liability": "", "Payer Role": "", "Payer Type": "Payer Type", "Payer/Payee Type": "", "Payment Amount": "", "Payment Amount By Assignee": "Payment Amount By Assignee", "Payment By Assignee": "Payment By Assignee", "Payment Currency By Assignee": "Payment Currency By Assignee", "Payment Date": "", "Payment Frequency": "Payment Frequency", "Payment History": "", "Payment information could not be copied due to differences in some configurations.": "", "Payment information has been successfully copied automatically.": "", "Payment Mehtod": "", "Payment Method": "Payment Method", "Payment Method / Account Type": "", "Payment Method/Account Type": "", "Payment Method/Account Type Details": "", "Payment Option": "Payment Option", "Payment Period": "Payment Period", "Payment Periods": "", "Payment Plan": "Payment Plan", "Payment Status": "", "PAYMENT_ACCOUNT_INFO": "Payment Account Info", "PAYMENT_INFO": "", "PAYMENT_INFO_VIEW": "", "PAYMENT_PLAN": "", "PAYMENT_PLAN_PAYER": "Payer", "PAYMENT_PLAN_PREMIUM_PAYMENT": "", "PayMethod": "PayMethod", "PayMethod / Account Type": "{{-payMethod}}/{{-accountType}}", "PAYOR": "", "payText": "Payment", "Pending Proposal Check": "", "Pending Transaction Amount（Fund Currency）": "Pending Transaction Amount（Fund Currency）", "Pending Transaction Unit": "Pending Transaction Unit", "Period": "", "Period Type": "Period Type", "Period Value": "Period Value", "PeriodAge": "", "PeriodYears": "{{value}} year(s)", "Permanent Address": "", "Person List": "", "Personal Record": "Personal Record", "Pet": "", "Pet ID": "Pet ID", "Pet Info": "Pet Info", "Pet Type": "Pet Type", "Pet Varieties": "<PERSON> Varieties", "Phone": "Phone", "Phone No": "", "Phone Number": "Phone Number", "Phone Type": "Phone Type", "Pin": "", "Place of Incorporation": "Place of Incorporation", "Place of Interest": "Place of Interest", "Plan": "Plan", "Plan Code": "", "Plan Goods Version": "Plan Goods Version", "Plan Level": "", "Plan Name": "Plan Name", "Plan Premium Model": "", "planName: {{goodsPlanName}}": "planName: {{goodsPlanName}}", "Planned Premium": "Planned Contribution", "Planned Premium Amount": "", "Planned Premium Collected": "Planned Contribution Collected", "Planned Premium Layer Details": "Planned Premium Layer Details", "Planned Premium: {{amount}}": "", "Plate No.": "Plate No.", "Plate Type": "", "Please": "", "Please add at least one piece of data": "Please add at least one piece of data", "Please add at least one product": "", "Please check schema correction. Some schemas have category without name.": "Please check schema correction. Some schemas have category without name.", "Please click 'Apply Master Plan' button to match coverage plan information.": "", "Please confirm the following information is correct . Once confirmed, it cannot be modified.": "Please confirm the following information is correct . Once confirmed, it cannot be modified.", "Please confirm the following information is correct.Once confirmed,it cannot be modified": "", "Please confirm whether the entered Master Agreement information has been saved before uploading the file.": "Please confirm whether the entered Master Agreement information has been saved before uploading the file.", "Please Download Template first.": "Please <2>Download Template</2> first.", "Please enter {{fieldName}} before submitting.": "", "Please enter {{productName}} product decision before submitting the task.": "", "Please enter a number greater than 0": "Please enter a number greater than 0", "Please enter a number greater than 0 but less than 100": "Please enter a number greater than 0 but less than 100", "Please enter a number greater than 0 but less than 200": "Please enter a number greater than 0 but less than 200", "Please enter the decision!": "", "Please fill in the Setting Table": "Please fill in the Setting Table", "Please generate the offer first.": "Please generate the offer first.", "Please generate the premium first.": "", "please input": "", "Please input": "Please input", "Please input a number": "Please input a number", "Please input number": "", "Please input positive integer": "Please input positive integer", "Please Input Range": "Please Input {{minValue}}-{{maxValue}}", "Please input valid party ID!": "", "Please input your Product Type!": "", "Please input your Team Name!": "", "Please note when choosing DCA arrangement,  the amount for each period will invest in the fund based on fund appointment rate of planned premium.": "", "Please note when choosing the investment strategy, the fund appointment for premium & portfolio rebalancing will follow the defination on the strategy.": "Please note when choosing the investment strategy, the fund appointment for premium & portfolio rebalancing will follow the defination on the strategy.", "Please notice that the entered coverage along with its related limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage and limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage information will be cleared. Do you want to continue?": "", "Please return to the task pool, search for the proposal number {{proposalNo}} and try again later.": "Please return to the task pool, search for the proposal number {{proposalNo}} and try again later.", "Please save or delete current attachmentType": "Please save or delete current attachmentType", "Please save the policy information before adding comments": "", "Please search or input": "Please search or input", "Please search transaction first": "", "please select": "", "Please select a higher level underwriter to escalate the case.": "Please select a higher level underwriter to escalate the case.", "Please select a higher level underwriter to referral the case.": "", "Please select an underwriter to escalate the case.": "", "Please select an underwriter to reassign the case.": "Please select an underwriter to reassign the case.", "Please select an underwriter to referral the case.": "", "Please Select At Least One Condition!": "Please Select At Least One Condition!", "Please Select At Least One Liability": "", "Please select at least one record": "", "Please select decision": "", "Please select effective date": "Please select effective date", "Please select Exclusion": "", "Please select factor first": "", "Please select first": "Please select {{text}} first", "Please select language": "Please select language", "Please select Liability": "", "please select one": "please select one", "Please select one Goods before submitting!": "", "Please select one Sub-category before submitting!": "", "Please select policy effective date": "Please select policy effective date", "Please select policy expiry date": "Please select policy expiry date", "Please select policy type submitting!": "", "Please select Product": "", "Please select the Goods Name first.": "", "Please select the insurance applications that need manual underwriting": "", "Please select whole days": "Please select whole days", "Please select your Bind Task Assignment Rule!": "", "Please select your Team Members!": "", "Please select your Team Type!": "", "Please set policyholder": "", "Please Upload": "", "Please Upload Document": "", "Please Upload File": "Please Upload File", "Please Upload invoice": "", "Please upload one file": "", "Please_enter_at_least3characters": "Please enter at least 3 characters", "please_select": "", "POLICY": "", "Policy Assignment": "", "Policy Basic Infomation": "", "Policy Change": "", "Policy Charge": "", "Policy Configuration": "Policy Configuration", "Policy Currency": "Policy Currency", "Policy Delivery Method": "Certificate Delivery Method: {{method}}", "Policy E-Document Type": "Certificate E-Document Type", "Policy Effective": "", "Policy Effective Check": "Policy Effective Check", "Policy Effective Date": "Policy Effective Date", "Policy Effective Date ": "", "Policy Effective Without Collection (NB)": "", "Policy Expiry Date": "Policy Expiry Date", "Policy History": "Policy History", "Policy Holder": "", "Policy Info": "Policy Info", "Policy Information": "", "Policy Issuance Compliance Check （After Premium Payment)": "Policy Issuance Compliance Check （After Premium Payment)", "Policy Issuance Rules": "Policy Issuance Rules", "Policy Issuance UW Check (After Premium Payment)": "", "Policy Issue Date": "Policy Issue Date", "Policy List": "", "Policy Loan": "Policy Loan", "Policy Loan Detail": "Certificate Loan Detail", "Policy Maturity Termination Method": "Policy Maturity Termination Method", "Policy No": "Policy No.", "Policy No.": "Policy No.", "Policy No. {{policyNo}}": "Policy No. {{policyNo}}", "Policy Number Generation Rule": "", "Policy Period": "", "Policy Regeneration": "Policy Regeneration", "Policy Serial Number": "Policy Serial Number", "Policy Sign Off Date": "Certificate Sign Off Date", "Policy Sign Off Rule": "Policy Sign Off Rule", "Policy Status": "Policy Status", "Policy Tag": "", "Policy Tag History": "", "Policy Tag List": "", "Policy Tagging": "", "Policy Toolip": "{{key}}: {{value}}", "Policy Type": "Policy Type", "Policy UW Decision": "", "Policy UW Decision Details": "", "Policy Year": "", "Policy years": "Policy years", "POLICY_CHANGE": "", "POLICY_CHANGE_DETAILS": "", "POLICY_CHANGE_OVERVIEW": "", "POLICY_DETAIL_INFO": "", "POLICY_DETAILS": "", "POLICY_HISTORY": "Policy History", "POLICY_HOLDER": "Policyholder Info", "POLICY_HOLDER_CN": "Policyholder Info", "POLICY_OVERVIEW": "", "Policy_Query": "Certificate Query", "PolicyEffectiveDate": "Certificate Effective Date", "policyEffectiveRuleTooltip": "System will use the \"General Decision\" of rule configured here and trigger the proposal flow based on this decision.If declined,system will block policy issue process. \n  Policy effective stage has no manual workbench.Please make sure the rule decisions don 't include \"Manual\" decision. Only \"Accept\" and \"Reject\" decisions are allowed. ", "Policyhoder Info": "", "PolicyHolder": "", "Policyholder and insured is the same. The change is applied to": "Policyholder and insured is the same. The change is applied to", "Policyholder Certificate Type": "Policyholder Certificate Type", "Policyholder Details": "", "Policyholder Email": "Policyholder Email", "Policyholder ID No": "Policyholder ID No.", "Policyholder Id No.": "", "Policyholder ID No.": "Policyholder ID No.", "Policyholder ID Type": "", "Policyholder Info": "Policyholder Info", "Policyholder Mobile Number": "Participant Mobile Number", "Policyholder Name": "Policyholder Name", "PolicyHolder Name": "", "Policyholder Name2": "Policyholder Name2", "Policyholder Type": "Participant Type", "Policyholder_Email": "Participant Email", "Policyholder_ID_No": "Participant ID No.", "Policyholder_ID_Type": "Participant ID Type", "Policyholder_Mobile_Number": "Participant Mobile Number", "Policyholder_Name": "Participant Name", "PolicyholderIDNo": "Policyholder ID No", "PolicyStatus": "Certificate Status", "Portfolio Rebalancing": "Portfolio Rebalancing", "Portfolio Rebalancing Detail": "Portfolio Rebalancing Detail", "POS Application Date": "", "POS Effective Date": "", "POS Item": "", "POS No.": "POS No.", "POS_Archives_Room": "POS Archives Room", "POS_DETAILS": "", "POS_Effective_Date": "POS Effective Date", "posDecisionEnum.APPROVE": "Approve", "posDecisionEnum.BACK_DATA_ENTRY": "Back to Data Entry", "posDecisionEnum.REJECT": "Reject", "posStatusEnum.APPROVAL_IN_PROGRESS": "Approval in Progress", "posStatusEnum.CANCELLED": "Cancelled", "posStatusEnum.DATA_ENTRY_IN_PROGRESS": "Data Entry in Progress", "posStatusEnum.EFFECTIVE": "Effective", "posStatusEnum.INVALID": "Invalid", "posStatusEnum.REJECTED": "Rejected", "posStatusEnum.WAITING_FOR_APPROVAL": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "posStatusEnum.WAITING_FOR_COLLECTION": "Waiting for Collection", "posStatusEnum.WAITING_FOR_DATA_ENTRY": "Waiting for Data Entry", "posStatusEnum.WAITING_FOR_EFFECTIVE": "Waiting for Effective", "posStatusEnum.WITHDRAW": "<PERSON><PERSON><PERSON>", "posStatusStepEnum.APPROVAL_PROCESSING": "Approval", "posStatusStepEnum.APPROVAL_WAITING": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "posStatusStepEnum.CANCELLED": "Cancelled", "posStatusStepEnum.COLLECTION_PAYMENT": "Collection", "posStatusStepEnum.DATA_ENTRY_CALCULATION": "Calculation", "posStatusStepEnum.DATA_ENTRY_CONFIRMATION": "Confirmation", "posStatusStepEnum.DATA_ENTRY_CS_ITEM": "Data Entry", "posStatusStepEnum.DATA_ENTRY_PAYMENT_COLLECTION": "Payment & Collection", "posStatusStepEnum.EFFECTIVE": "Effective", "posStatusStepEnum.INVALID": "Invalid", "posStatusStepEnum.REGISTER": "Registration", "posStatusStepEnum.REJECTED": "Rejected", "posStatusStepEnum.SELECT_CS_ITEM": "POS Item Selection", "posStatusStepEnum.WITHDRAW": "<PERSON><PERSON><PERSON>", "Post Code": "", "Postal Name": "Postal Name", "Power Type": "Power Type", "PRECIOUS_ITEM": "", "Premium": "Premium", "PREMIUM": "Premium", "Premium (Before Campaign)": "Contribution (Before Campaign)", "Premium (Before Tax & Service Fee)": "Contribution (Before Tax & Service Ujrah)", "Premium (Total)": "Contribution (Total)", "Premium & SA Calculation Method": "", "Premium Aggregation Detail": "", "Premium allocation": "Contribution allocation", "Premium Calculation Method": "", "Premium Calulation Method": "", "Premium Collected & Allocation": "Contribution Collected & Allocation", "Premium Collection": "Premium Collection", "Premium Collection Time": "Premium Collection Time", "Premium Detail Download": "", "Premium Details": "", "Premium Discount": "Contribution Discount", "Premium Discount on Net Premium": "Contribution Discount on Net Contribution", "Premium Discount On Tax": "Contribution Discount On Tax", "Premium Due Date": "", "Premium DueDate": "", "Premium Frequency": "", "Premium Funder": "Premium Funder", "Premium Handling Method": "", "Premium Info": "", "Premium Info Detail": "", "Premium Notice Date": "", "Premium Notice Date Compare with Due Date": "", "Premium or SA Info": "Premium or SA Info", "Premium Payer": "Premium Payer", "Premium Per Unit": "Contribution Per Unit", "Premium Period": "Premium Period", "Premium Period Type": "", "Premium Period Value": "Contribution Period Value", "Premium Status": "Premium Status", "Premium Type": "Contribution Type", "PREMIUM_AGGREGATION": "", "PREMIUM_AMOUNT": "", "Premium_Discount": "", "Premium_discount_type": "Contribution Discount Type", "Premium_Due_Date": "Contribution Due Date", "PREMIUM_FUNDER": "", "Premium_including_tax_discount": "Contribution (including tax and discount)", "premium_pay_account": "Contribution Payment Account", "PREMIUM_PAYER": "Premium Payer", "PREMIUM_PAYER_CN": "Premium Payer", "Premium_without_tax_discount": "Contribution (without tax and discount)", "PremiumDuration": "Contribution Duration", "PremiumEndDate": "Contribution End Date", "Press enter to record enums, duplicated keys will be ignored.": "", "Preview Offer": "", "Preview Strategy Details": "Preview Strategy Details", "Previous Policy No.": "Previous Policy No.", "Price Date": "Price Date", "Price Date For Adjustment": "", "Principal Balance": "Principal Balance", "Principal Balance Change": "Principal Balance Change", "Principal Steam Details": "Principal Steam Details", "Print History": "", "Print Name": "", "Print Reason": "", "Print Time": "", "Prior condition": "Prior condition", "Priority": "", "Private Task - Active": "", "Private Task - Inactive": "", "process": "process", "Process Configuration": "Process Configuration", "Process Failed": "", "Process Successfully": "Process Successfully", "Process Underwriter Level": "", "Process UWer Level": "Process UWer Level", "Processing": "", "Product": "Product", "Product Category": "Product Category", "Product Code": "Product Code", "Product Code&Name": "Product Code&Name", "Product Decision": "", "Product Details": "", "Product Discount": "Product Discount", "Product in Goods": "Product in Goods", "Product Info": "Product Info", "Product Level": "", "Product List": "", "Product Name": "Product Name", "Product Name {{name}}": "", "Product name: {{productName}}": "", "Product Premium: {{sa}}: ": "", "Product SA: {{sa}}: ": "", "Product Status": "", "Product Summary": "", "Product Tax": "Product Tax", "Product Type": "", "Product_Amount": "Product Amount", "PRODUCT_BUNDLE_BASIC_INFO": "", "Product_Libility_Info": "Product & Liability Info", "Product_nostyle": "", "Product: {{productCode}}_{{productName}}": "", "productCode_productName": "{{productCode}}_{{productName}}", "Promo Code": "", "Promotion Code": "Promotion Code", "Promotion Discount": "Promotion Discount", "Promotion Discount On Levy": "Promotion Discount On Levy", "Promotion Type": "Promotion Type", "PROPERTY": "", "Property Coverage": "", "Property Product": "", "Proposal": "", "Proposal Compliance Check （Before Premium Payment)": "Proposal Compliance Check （Before Premium Payment)", "Proposal Configuration": "Proposal Configuration", "Proposal Confirmation Date": "Proposal Confirmation Date", "Proposal Date": "Proposal Date", "Proposal Effective Date": "", "Proposal Flow Setting": "Proposal Flow Setting", "Proposal Info": "Proposal Info", "Proposal No": "Proposal No", "Proposal No: ": "", "Proposal No: {{applicationNo}}": "Proposal No: {{applicationNo}}", "Proposal No: {{proposalNo}}": "", "Proposal No.": "", "Proposal No. {{proposalNo}}": "Proposal No. {{proposalNo}}", "Proposal reminder days can not duplicate. Please check.": "Proposal reminder days can not duplicate. Please check.", "Proposal Reminder Rule": "Proposal Reminder Rule", "Proposal Request Date": "", "Proposal Rule": "Proposal Rule", "Proposal Rules": "Proposal Rules", "Proposal Status": "Proposal Status", "Proposal Task Pool": "", "Proposal Task Pool Re-assign": "", "Proposal Withdraw Rule": "Proposal Withdraw Rule", "PROPOSAL_DETAILS": "", "PROPOSAL_INFO": "", "Proposal/Policy": "", "Proposal/Policy No.": "", "Proposal/Policy Status": "", "Proposal/POS No": "Proposal/POS No", "ProposalDate": "ProposalDate", "proposalWithdrawTooltip": "This is to config proposal status and period to withdraw proposal automatically", "Provide Vehicle Photo Later X Days": "", "Public Liability": "Public Liability", "Public Task": "", "Public Tender": "Public Tender", "Public Tender No.": "Public Tender No.", "Purchase Date": "Purchase Date", "Purchase Price": "Purchase Price", "Purpose": "Purpose", "QUARTER": "", "Quarterly": "Quarterly", "Query Error": "Query Error", "Query Escalate Users Failed": "Query Escalate Users Failed", "Query Reassign Users Failed": "Query Reassign Users Failed", "Query Referral Users Failed": "", "QUERY_ATTACHMENTS": "", "QUERY_POLICY_HISTORY": "", "QUERY_RENEWAL_HISTORY": "", "query-Agent": "Agent", "query-Agent Name": "Agent Name", "query-Allocation Amount": "Allocation Amount", "query-Business No": "Business No.", "query-Consentee": "Consentee", "query-Deductible Amount": "Deductible Amount", "query-Effective Date": "Effective Date", "query-Expiry Date": "", "query-Extra Loading": "", "query-Fund Appointment": "Fund Appointment", "query-Fund Currency": "Fund Currency", "query-Fund Name": "Fund Name", "query-Gender": "Gender", "query-Goods Name": "Goods Name", "query-Goods Version": "Goods Version", "query-GoodsName": "Goods Name", "query-GoodsVersion": "Goods Version", "query-History Type": "History Type", "query-ID Type": "ID Type", "query-Identifier Info": "Identifier Info", "query-Individual": "Individual", "query-Insured Email": "Insured Email", "query-Insured Name2": "Insured Name2", "query-Investment Strategy": "Investment Strategy", "query-Is Renewal Policy": "Is Renewal Certificate", "query-Main Benefit": "Main Benefit", "query-Master Policy No.": "Master Certificate No.", "query-Mobile Phone": "", "query-No.": "No.", "query-Nominee": "<PERSON><PERSON><PERSON>", "query-Organization": "Organization", "query-Other Policy Info": "Other Certificate Info", "query-Others": "Others", "query-Payer": "Payer", "query-Payment Frequency": "Payment Frequency", "query-Payment Method": "Payment Method", "query-Payment Period": "Payment Period", "query-PeriodAge": "{{value}} age", "query-Policy Currency": "Certificate Currency", "query-Policy Effective Date": "Policy Effective Date", "query-Policy Issue Date": "Policy Issue Date", "query-Policy Loan": "Certificate Loan", "query-Policy Regeneration": "Policy Regeneration", "query-Policyholder": "", "query-PolicyHolder": "PolicyHolder", "query-Policyholder Email": "Participant Email", "query-Policyholder ID No.": "Participant ID No.", "query-Policyholder Info": "Participant Info", "query-Policyholder Name": "Participant Name", "query-PolicyNo": "Certificate No.", "query-Premium Due Date": "Contribution Due Date", "query-Product": "Product", "query-Proposal No.": "Proposal No.", "query-Registration Date": "Registration Date", "query-Relationship With Insured": "", "query-Relationship With Policyholder": "Relationship With Policyholder", "query-Renewal": "", "query-Sales Channel": "Sales Channel", "query-Select All": "", "query-Service Fee": "Service Ujrah", "query-Settlement Date": "Settlement Date", "query-Social Account": "Social Account", "query-Status": "Status", "query-Transaction Type": "Transaction Type", "query-Trustee": "Trustee", "Questionnaire": "Questionnaire", "QUESTIONNAIRE": "Questionnaire & Declaration", "Questionnaire Info": "Questionnaire Info", "Questionnaire Name": "Questionnaire Name", "QUESTIONNAIRE_INFO": "", "Quick Menu": "Quick Menu", "Quotation": "", "Quotation Configuration": "Quotation Configuration", "Quotation Info": "", "Quotation Information Pre-check": "", "Quotation No": "", "Quotation No.": "Quotation No.", "Quotation No. {{proposalNo}}": "", "Quotation Period": "Quotation Period", "Quotation Query": "", "Quotation Stage": "Quotation Stage", "Quotation Status": "Quotation Status", "Quotation Task Pool": "Quotation Task Pool", "Quote Bound": "", "Random Check": "Random Check", "Random Check Configuration": "Random Check Configuration", "Random Ratio": "Random Ratio", "Rate": "Rate", "Rate Type": "Rate Type", "Rate-Classes BI": "Rate-Classes BI", "Rate-Classes OD": "Rate-Classes OD", "Rate-Classes PD": "Rate-Classes PD", "Rate-Classes PIC": "Rate-Classes PIC", "Rate/Amount": "", "Re-accumulate and affect target": "", "Re-assign": "", "Re-underwriting Reason": "Re-underwriting Reason", "Re-underwriting Type": "Re-underwriting Type", "Re-Upload": "Re-Upload", "Re-Upload Successfully": "", "Read More": "", "Reason": "Reason", "Reason Comments": "", "Reassign": "Reassign", "Reassign | {{selectLength}} Option(s)": "Reassign | {{selectLength}} Option(s)", "reassign control {{proposalNos}}": "", "reassign control batch": "", "Reassign or Referral": "", "Reassign Successfully!": "Reassign Successfully!", "Rebalance Frequency": "", "Rebalancing Date": "Rebalancing Date", "Rebalancing Frequency": "Rebalancing Frequency", "Rebalancing History": "Rebalancing History", "Receivepromotionalemailsornot": "Receive promotional emails", "Recipient": "Recipient", "Reconciliation Status": "Reconciliation Status", "Records / Number of total records": "Records / Number of total records", "Recount": "Recount", "Recover": "", "Recurring Single top up": "Recurring Single top up", "Recurring Single Top Up": "", "Recurring Single Top Up Frequency": "Recurring Single Top Up Frequency", "Recurring Single Top Up Period": "Recurring Single Top Up Period", "Reduce Coverage": "", "Referral": "", "Referral or Reassign": "", "Referral Reason": "", "Referral Response": "", "Referral Successfully!": "", "Referral Task": "", "Refresh Confirm": "", "Regenerate": "Regenerate", "Regenerate Error": "Regenerate Error", "Regenerate Reason": "Regenerate Reason", "Regenerate Successfully": "Regenerate Successfully", "Register Date": "", "registration area": "registration area", "registration category": "registration category", "Registration Date": "Registration Date", "registration hiragana": "registration hiragana", "Registration No.": "Registration No.", "registration serial no": "registration serial no", "Regular Bonus Plan": "", "Regular Premium": "Regular Contribution", "Regular top up": "", "Regular Top Up": "Regular Top Up", "Regular Top Up Collected": "Regular Top Up Collected", "Regular Top Up: {{amount}}": "", "Regular Top-up": "", "Regular Withdrawal": "", "Reinstate": "", "Reinsurance Decision": "", "REINSURANCE_INFO": "", "reject": "reject", "Reject": "Reject", "Rejected": "Rejected", "Related Policy": "Related Certificate", "Related Policy Overview": "", "Related to insured": "", "Related to insured object": "", "Related to policy": "", "Related to policyholder": "", "RELATED_POLICY": "", "RelatedPartiesInformation": "Related Parties Info", "Relation with primary insured": "Relation with primary insured", "Relation_Policy_No": "Relational No.", "Relationship No.": "Relationship No.", "Relationship Type / Relatinship No.": "Relationship Type / Relationship No.", "Relationship Type / Relatinship No. should be completed or empty": "Relationship Type / Relationship No. should be completed or empty.", "Relationship With Insured": "Relationship With Insured", "Relationship with Policyholder": "", "Relationship With Policyholder": "Relationship With Policyholder", "Relationship With Policyholder: {{holderRelationRemark}}": "", "Relative": "", "Release": "", "Release Failed": "", "Release Success": "", "Release Task": "", "Release Time": "", "Remaining Amount": "Remaining Amount ", "RemainingPaymentTime": "Remaining Payment Time", "Remark": "Remark", "remarks": "", "Remarks": "", "REMARKS": "", "Reminder": "", "Reminder Frequency(days)": "Reminder <PERSON>(days)", "Remove": "", "Remove a Reminder": "Remove a Reminder", "Remove Date": "", "Render Error": "", "Renew": "", "Renewal": "Renewal", "Renewal Extraction Date": "Renewal Extraction Date", "Renewal Failure Reason": "Renewal Failure Reason", "Renewal History": "", "Renewal Policy No.": "Renewal Certificate No.", "Renewal Policy No. Generation Rule": "", "Renewal Quotation Expiry Date": "Renewal Quotation Expiry Date", "Renewal Quotation Info": "Renewal Quotation Info", "Renewal Reminder Date": "<PERSON><PERSON> Reminder Date", "Renewal Reminder Date Compare to Policy Expiry Date": "", "Renewal Reminder Rule": "", "Renewal Status": "Renewal Status", "Renewal UW Info": "Renewal UW Info", "RENEWAL_INFO": "", "RENTER": "", "Reopen Comment": "Reopen Comment", "Repayment Amount": "Repayment Amount", "represent_no": "Agreement No.", "Reprint Successfully": "", "Requirement Category": "", "Requirement Code": "", "Requirement Descriptions": "", "Requote": "", "Reset": "Reset", "Residential City": "Residential City", "Residential Status": "", "Retirement Age (Insured)": "", "Retirement Option": "", "Retirement Option Start": "", "Retirement Option Start Date": "", "Retirement option start date must be future date.": "", "retured": "retured", "Return": "", "Return Current Product to Data Entry": "", "Return Entire Submission to Data Entry": "", "Return Reason": "", "Return to Data Entry": "", "Reupload": "", "Reversed": "", "Reversionary Bonus": "Reversionary Bonus", "Reversionary Bonus Allocation Details": "Reversionary Bonus Allocation Details", "RF Weight": "RF Weight", "Riders": "Riders", "Risk Aggregation": "", "Risk Aggregation Detail": "", "Risk Category": "Risk Category", "Risk Classification": "", "Risk Sub-category": "Risk Sub-category", "Risk Underwriting Decision": "", "RISK_AGGREGATION": "", "RiskStartDate": "Risk Start Date", "Role": "Role", "Role Name": "", "Roles": "", "RR Weight": "RR Weight", "Rule Code": "", "Rule Code/Rule Set": "", "Rule Condition": "", "Rule Configuration": "Rule Configuration", "Rule Details": "", "Rule Name": "", "Rule Result": "", "Rule Type": "", "Rule/Rule Set Category": "", "Rule/Rule Set Code": "", "Rule\\Rule Set": "Rule\\Rule Set", "Running": "", "SA After Down Sell": "SA After Down Sell", "SA Multiplier": "", "Sales Agreement Code": "Sales Agreement Code", "Sales Channel": "Sales Channel", "Sales Channel Code": "Sales Channel Code", "Sales Channel Name": "", "Sales Channel Type": "", "Sales Time": "Sales Time", "Sales_Channel": "Sale Channel", "SALES_CHANNEL": "", "SalesChannel": "Sales Channel", "Same as": "", "Same As": "", "Same as Initial Premium Payment": "Same as Initial Premium Payment", "Same As Payer of Liability": "", "Save": "", "Save Failed": "Save Failed", "Save Successfully": "Save Successfully", "Saving Successfully!": "Saving Successfully!", "Schedule Period Type": "Schedule Period Type", "Schedule Period Value": "Schedule Period Value", "Scheduled Arrival Time": "Scheduled Arrival Time", "Scheduled Departure Time": "Scheduled Departure Time", "Scheduled Rate": "", "Scope of Application": "Scope of Application", "Search by Group Policy No.": "", "Search by Master Agreement No.": "Search by Master Agreement No.", "Search by Name": "", "Search by Operator": "", "Search Insured by Customer Type？": "Search Insured by Customer Type？", "Search Name": "", "Search Policyholder by Customer Type？": "Search Participant by Customer Type？", "searchResult": "Search Result", "Secondary Life Insured": "", "SECONDARY_LIFE_INSURED": "", "SecondaryLifeInsured": "", "Segmentation Factor": "Segmentation Factor", "SEGMENTATION_FACTOR": "", "Select": "", "Select a manager for the team": "Select a manager for the team", "Select a Template": "", "Select all": "", "Select All": "Select all", "Select Coverage / Sub Coverage": "", "Select Insured Object": "", "Select Plan Group": "", "Select Questionnaire Language": "Select Questionnaire Language", "Select the process flow you want to use": "", "Select Type": "", "selectAtLeastOne": "selectAtLeastOne", "Send": "Send", "Send Back to Origin UWer": "Send Back to Origin UWer", "Send Back to Task Pool": "", "Send Back to Task Pool failed": "", "Send Back To Task Pool success": "Send Back To Task Pool success", "Send back to Task Pool?": "Send back to Task Pool?", "Send Back to UW Pool": "", "send back to UW task": "send back to UW task", "Send Time": "", "Sender": "Sender", "Sending Status": "", "Senior Number": "Senior Number", "Seperate by Proposal Status": "Seperate by Proposal Status", "Sequence Length": "", "Sequence Value": "", "Sequence_No": "Sequence No.", "Service Company": "Service Company", "Service Company Code": "Service Company Code", "Service Fee": "Service Fee", "Service Fee Amount": "Service Fee Amount", "Service Fee Generated Date": "Service Fee Generated Date", "Service Fee Type": "", "SERVICE_AGENT": "", "Set Deductible": "", "Set Limits": "", "Set Priority": "", "Set task push strategy for each team, such as Round Robin and task push by workload.": "", "Settled": "Processed", "SettleFlag": "Settle Flag", "Settlement Date": "Settlement Date", "Settlement Frequency": "Settlement Frequency", "Settlement Start Date": "Settlement Start Date", "Sex": "", "Short Rate Method": "", "Should higher than previous policy year": "", "Show_with_Card": "Show with Card", "Showing {{current}} to {{pageSize}} of {{total}} results": "Showing {{current}} to {{pageSize}} of {{total}} results", "SINGLE": "", "Single Premium": "", "Single Top Up": "Single Top Up", "Single Top Up Amount": "", "Single Top Up Collected": "Single Top Up Collected", "Single Top Up Type": "", "Single Top Up: {{amount}}": "", "Single Top-up": "", "SMS": "", "Social Account": "Social Account", "Sorry, failed to upload documents because Master Policy NO. hasn't been filled. Please check.": "Sorry, failed to upload documents because Master Policy NO. hasn't been filled. Please check.", "Sort": "Sort", "Sort by Application Date. (Ascending)": "", "Sort by Application Date. (Descending)": "", "Sort by Create Time": "", "Sort By Creation Date (from oldest to newest)": "", "Sort by Group Policy No. (Ascending)": "", "Sort by Group Policy No. (Descending)": "", "Sort by Operation Time": "", "Sort by Proposal No. (Ascending)": "Sort by Proposal No. (Ascending))", "Sort by Proposal No. (Descending)": "Sort by Proposal No. (Descending)", "Sort by Quotation No. (Ascending)": "", "Sort by Quotation No. (Descending)": "", "Sort by Quote Need Date. (Ascending)": "Sort by Quote Need Date. (Ascending)", "Sort by Quote Need Date. (Descending)": "Sort by Quote Need Date. (Descending)", "Sort by time": "Sort by time", "Sort Times Ascending": "Sort Times Ascending", "Sort Times Descending": "Sort Times Descending", "Sort_by_Last_Upload_Time": "Sort by: Last Update Time", "Sort_by_Policy_No": "Sort by Certificate No.", "Special Agreement": "Special Agreement", "Special Agreement Code": "", "Special Agreement Description": "", "Special Agreement Type": "", "Special Code": "Special Code", "Special Description": "Special Description", "SPECIAL_AGREEMENT": "", "SPECIAL_AGREEMENT_WITH_PLAN": "", "Specific Info": "", "SPECIFIC_INFO": "", "Specified Applicable Goods": "", "Stack Code": "", "Stack Description": "", "Stack Liability Name": "Liability Name: {{liabilityName}}", "Stack Name": "Stack Name", "Stack Type": "Stack Type", "Stack Unit": "Stack Unit", "Stack Value": "Stack Value", "Stack Value Type": "", "Stamp Duty": "Stamp Duty", "Standard": "", "Standard Premium": "Standard Premium", "Standard Tariff": "Standard Tariff", "Start": "", "Start | End": "", "Start by Creating a Rule": "", "Start Date": "Start Date", "Start Day": "", "Start Time": "Start Time", "Status": "Status", "STOP_PAYMENT": "", "Strategy": "Strategy {{ name }}", "Strategy Asset Allocation": "", "Strategy Code": "Strategy Code", "Strategy Detail": "Strategy Detail", "Strategy Name": "Strategy Name", "Strategy Name (auto generate)": "Strategy Name (auto generate)", "Strategy Relatives": "Strategy Relatives", "Street Name": "Street Name", "Street No.": "", "Structure": "Structure", "Sub Campaign Category": "Sub Campaign Category", "Sub Coverage": "", "Sub Policy (Main)": "", "Sub Policy (Relative)": "", "Sub Policy Info": "", "Sub Policy No.": "", "Sub Policy No. {{policyNo}}": "Sub Policy No. {{policyNo}}", "Sub Policy Status": "", "Sub Standard": "", "Sub Total": "Sub Total", "SUB_STANDARD_CODE": "", "SUB_STANDARD_RECORD": "", "Sub-items": "Sub-items", "Sub-standard Code": "", "Sub-standard Code List": "", "Subject": "Subject", "Submission No": "", "Submission No.": "", "Submit": "Submit", "Submit Failed": "Submit Failed", "Submit Failed: {{message}}": "", "Submit successfully": "", "Submit Successfully": "Submit Successfully", "Submit Tips": "Submit Tips", "Substandard Code": "", "success": "", "Successful": "Successful", "Successful Records": "", "successfully": "", "Sum Assured": "Sum Assured", "Sum_Assured_with_free_amount": "Sum Assured (with free amount)", "Sum_Assured_without_free_amount": "Sum Assured (without free amount)", "Summary": "", "Sure": "Sure", "Survival Benefit": "Survival Benefit", "Survival Benefit Account Transaction Detail": "Survival Benefit Account Transaction Detail", "Survival Benefit Payment Account": "Survival Benefit Payment Account", "Survival Benefit Payment Frequency": "Survival Benefit Payment Frequency", "Survival Benefit Payment Option": "Survival Benefit Payment Option", "Suspend Reason": "", "Suspension Certificate": "Suspension Certificate", "Sustav će koristiti Ovdje konfiguriranu Opću odluku pravila i pokrenuti tijek prijedloga na temelju ove odluke. Ako se odbije\"": "", "SWIFT Code": "", "Switch Confirm": "", "Switching customer types will clear existing customer data, please confirm.": "", "Switching the plan group will clear the selected products. Please confirm.": "", "Symbol not matched, please check GeneralSymbols.": "Symbol not matched, please check GeneralSymbols.", "System error": "", "System Error": "System Error", "System generates": "", "System logon user is different from the case handler. please check!": "System logon user is different from the case handler. please check!", "System Source": "", "System will trigger automatically confirm the policy sign off X days after policy issue date.": "System will trigger automatically confirm the policy sign off X days after policy issue date.", "System will trigger reminder notification when the proposal stays in below status after X days.": "System will trigger reminder notification when the proposal stays in below status after X days.", "System will trigger the proposal flow based on the 'Underwriting Decision' of the rule configuration here. If declined, system will reject the proposal. And if manual, system will trigger manual underwriting check for this proposal.": "", "System will trigger the proposal flow based on the \"Compliance Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.": "", "System will trigger the proposal flow based on the \"Verification Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will send the proposal to manual verification.": "", "Tag": "Tag", "Tag Name": "", "Target Return(%)": "", "Target Rule No": "", "Target Rule No.": "", "Target Volatility(%)": "", "Task Assignment Rule": "", "Task Assignment Strategy": "", "Task Create Date": "Task Create Date", "Task No.": "Task No.", "Task Pick Up": "", "Task Push Strategy": "Task Push Strategy", "Task Push Strategy has been configured": "Task Push Strategy has been configured", "Task Push Supplementary Strategy": "Task Push Supplementary Strategy", "Task Status": "Task Status", "Task successfully assigned to the user.": "", "Task Successfully Withdrawn": "Task Successfully Withdrawn", "Task Type": "Task Type", "Tax": "Tax", "Tax Amount": "Tax Amount", "Tax Info": "Tax Info", "Tax Rate/Value": "Tax Rate/Value", "Tax Setting": "Tax Setting", "Tax Type": "Tax Type", "TB Type": "TB Type", "Team Maintenance Type": "", "Team Management": "", "Team Members": "", "Team Name": "", "Team Name: {{name}}": "", "Team(s) for the stragegy": "Team(s) for the stragegy", "Terminal Bonus": "Terminal Bonus", "Terminated": "Terminated", "Terminated Reason": "", "Termination": "", "Termination Date": "Termination Date", "Termination Date(Lapsed Date)": "Termination Date(Lapsed Date)", "Termination Reason": "Termination Reason", "TerminationDate": "Termination Date", "TerminationReason": "Termination Reason", "text": "", "text_select": "", "The Amount input should less than original sum assured of the liability.": "", "The Amount input should less than original sum assured of the product.": "", "The amount is consists of 3 parts： Planed Premium,Single Top-Up and Regular Top-Up.": "The amount is consists of 3 parts： Planed Contribution,Single Top-Up and Regular Top-Up.", "The customer should pay the premium before issuing the policy .": "", "The date & benefit amount list below is calculated based on current policy info. If there is any further policy change occurred, the real benefit date & amount may change.": "The date & benefit amount list below is calculated based on current policy info. If there is any further policy change occurred, the real benefit date & amount may change.", "The export may take a long time, you can download the Excel Fails when the status is completed.": "", "The file is still being generated. Please wait a moment.": "", "The Installment premium is changed from ": "The Installment premium is changed from ", "The issuance of proposal {{relatedPolices} depends on proposal {{issuanceNo}}. If the decline of this underwriting task results in the withdrawal of proposal {{issuanceNo}}, proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "", "The issuance of the proposal {{relatedPolices}} depends on this proposal. If this proposal is withdrawn, the proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "", "The location cannot be the same, please check.": "", "The modification of the policy information did not pass the compliance verification. The proposal is canceled, and the underwriting task is closed. Please confirm.": "", "The modification of the policy information has triggered a manual compliance task. Please wait for the submission of the compliance task before continuing with the underwriting task.": "", "The net premium has changed": "", "The object names cannot be the same, please check.": "", "The other process is explained in Withdraw": "The other process is explained in Withdraw", "The payment information for this product has not been entered. Please confirm.": "", "The policy data loaded from master policy will be deleted. Do you want to continue?": "", "The policy data loaded from master policy will be refreshed according to new master policy number. Do you want to continue?": "", "The policy does not exist.": "", "The policy has been issued successfully.": "", "The policy is not within renewal extraction period, please confirm whether to raise renewal.": "", "The policyholder you selected has not been created yet. Please confirm.": "", "The POS application is already withdrawn. You don't need to underwrite it anymore.": "The POS application is already withdrawn. You don't need to underwrite it anymore.", "The Premium Has Changed": "The Premium Has Changed", "The proposal has been sent to New Quote.": "", "The proposal has been sent to Quote Bound.": "", "The proposal has been submitted to manual underwriting.": "", "The proposal has been successfully withdrawn.": "", "The proposal has failed automated underwriting and was declined by the system.": "", "The proposal has failed automated underwriting due to the changes.": "", "The proposal has failed automated underwriting.": "", "The proposal has Lapsed.": "", "The proposal has passed automated underwriting.": "", "The proposal is already declined or postponed by underwriter. You don't need to perform verification anymore.": "The proposal is already declined or postponed by underwriter. You don't need to perform verification anymore.", "The proposal is already reject by manual compliance user. You don't need to perform verification anymore.": "The proposal is already reject by manual compliance user. You don't need to perform verification anymore.", "The proposal is already withdrawn (by client, channel or auto withdrawn by the company). You don't need to perform verification anymore.": "The proposal is already withdrawn (by client, channel or auto withdrawn by the company). You don't need to perform verification anymore.", "The proposal is already withdrawn. You don't need to underwrite it anymore.": "The proposal is already withdrawn. You don't need to underwrite it anymore.", "The proposal will lapse, are you sure to continue？": "", "The renewal proposal is generated successfully. Proposal No.{{No}}": "The renewal proposal is generated successfully. Proposal No. {{No}}", "The renewal quotation is generated successfully. Quotation No.{{No}}.": "The renewal quotation is generated successfully. Quotation No. {{No}}.", "The renewal validation failed, please check whether the current policy meets the renewal conditions.": "The renewal validation failed, please check whether the current policy meets the renewal conditions.", "The required Master Agreement information is insufficient to upload the file.": "The required Master Agreement information is insufficient to upload the file.", "The same person as Policyholder": "", "The same Plan already exists": "", "The same Plan name already exists under the current policy, please modify and submit! ": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding deductible. Please confirm.": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding limit. Please confirm.": "", "The team selected above will be brought in here": "team selected above will be brought in here", "The updated information will not be saved, are you sure to back?": "", "The user who appointed as team manager is deleted, please reset team manager if needs.": "The user who appointed as team manager is deleted, please reset team manager if needs.", "The verification task has been submitted.": "The verification task has been submitted.", "There is duplicate exclusion record exist. Please check": "", "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?": "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?", "Third Party Collection": "Third Party Collection", "Third Party Transaction No": "Third Party Transaction No", "This case is escalated from {{user}}.": "This case is escalated from {{user}}.", "This document contains vehicle information, premium details and riders information.": "", "This factor is an enumeration type, but enumKey is missing.": "This factor is an enumeration type, but enumKey is missing.", "This liability is mutually-exclusive with {{tipsText}}": "This liability is mutually-exclusive with {{tipsText}}", "This rule has been binded by team {{teamNames}}, please unbind from team first.": "", "This team has been binded by strategy {{StrategyNames}}, please unbind from strategy first.": "", "This underwriting case is currently under {{user}}. Reassigning it may affect the progress of tasks being processed. Please confirm.": "", "Threshold for Rebalancing": "", "Ticket": "Ticket", "Ticket Number": "Ticket Number", "Ticket Price": "Ticket Price", "Ticket Type": "Ticket Type", "Times": "Times", "Times Types": "", "Tips": "Tips", "Title": "Title", "to": "to", "To be expired": "", "Tonnage": "Tonnage", "Top Up Due Date": "", "Top Up Period:": "", "Total": "", "Total {{count}} {{objectName}}s": "", "Total Allocated Bonus": "", "Total Amount": "", "Total Beneficiary ratio should be equal to 100%": "Total Beneficiary ratio should be equal to 100%", "Total Campaign Discount": "Total Campaign Discount", "Total CB Allocation": "Total CB Allocation", "Total Claim Amount": "Total Claim Amount", "Total Commission Amount": "Total Commission Amount", "Total Extra Loading": "Total Extra Loading", "Total Fund Value": "Total Fund Value: {{value}}", "Total Installments": "", "Total Insured No": "", "Total Insured No.": "", "Total Investment Amount": "Total Investment Amount", "Total items": "", "Total Loan Balance": "Total Loan Balance", "Total Outstanding Premium": "", "Total Paid Premium": "Total Paid Contribution", "Total Premium": "", "Total Premium Amount": "Total Premium Amount", "Total Premium Amount Detail": "Total Premium Amount Detail", "Total Premium Amount Details": "Total Premium Amount Details", "Total Premium Collected": "Total Contribution Collected", "Total Premium Name": "", "Total Price": "", "Total Primary Insured No.": "", "Total Principal Amount": "Total Principal Amount", "Total Product Discount": "Total Product Discount", "Total Refund Premium": "Total Refund Contribution", "Total Risk Amount": "", "Total Risk Amount Details": "", "Total Sub Policy": "", "Total Sum Assured": "", "Total Tax": "Total Tax", "Total TIV": "Total TIV", "Total Unpaid Premium (due & undue)": "", "total_amount": "total_amount", "TOTAL_PREMIUM": "", "Total: {{amount}} insured": "", "Total: {{total}}": "", "Total: {{total}} Strategies": "Total: {{total}} Strategies", "Total: Records": "Total: {{total}} Records", "Transaction": "Transaction", "Transaction Amount": "Transaction Amount", "Transaction Date": "Transaction Date", "Transaction Effective Date": "Transaction Effective Date", "Transaction Efffective Date": "", "Transaction Name": "", "Transaction No.": "transaction no.", "Transaction Status": "Transaction Status", "Transaction Time": "Transaction Time", "Transaction Type": "", "Transaction Unit": "Transaction Unit", "TransactionDate": "Transaction Date", "Transation Effective Date": "", "Transit ID": "Transit ID", "Transport Information": "Transport Information", "Transportation Number": "Transportation Number", "Transportation Type": "Transportation Type", "Transportion_No": "Transaction Date", "Travel Agency": "Travel Wakalah", "Travel End Date": "Travel End Date", "Travel Expense": "Travel Expense", "Travel Info": "Travel Info", "Travel Order Number": "Travel Order Number", "Travel Order Type": "Travel Order Type", "Travel Start Date": "Travel Start Date", "Travel Type": "Travel Type", "TRAVEL_OBJECT_INFO": "", "Trip Info": "Trip Info", "Trip Type": "Trip Type", "Trustee": "Trustee", "TRUSTEE": "", "Turn back to Manual UW Task Pool": "Turn back to Manual UW Task Pool", "Type": "", "Type a Comment...": "", "Type of Business": "", "UI Template": "UI Template", "Unanswered question exists, please confirm.": "Unanswered question exists, please confirm.", "Underwriter": "Underwriter", "Underwriter Name": "Underwriter Name", "underwriting": "underwriting", "Underwriting": "", "Underwriting Authority": "Underwriting Authority", "Underwriting case is under review.": "", "Underwriting Check": "Underwriting Check", "Underwriting Check Text": "System will give underwriting decision or trigger related manual UW work based on the \"UW Decision\"  of rule configured here. And the underwriting decision is attached on each product level.", "Underwriting Configuration": "Underwriting Configuration", "Underwriting Criteria": "Underwriting Criteria", "Underwriting History": "", "Underwriting Level": "Underwriting Level", "Underwriting Strategy": "Underwriting Strategy", "Underwriting Task": "", "UNDERWRITING_DECISION": "", "UNDERWRITING_TAG": "", "Unit": "Unit", "Unit Adjustment": "", "Unit No.": "Unit No.", "Unit No. and Building Name": "Unit No. and Building Name", "Unit Premium": "", "Units To Be Adjusted": "", "Universal Saving Account": "Universal Saving Account", "Unnamed Insured": "", "Unpaid Amount": "", "Update Date": "", "Update OCR Result": "Update OCR Result", "Updated at": "", "updateTime": "", "updateUser": "", "Upload": "Upload", "Upload Application Form": "Upload Application Form", "Upload Attachment": "", "Upload Date": "", "Upload Document": "Upload Document", "Upload Failed": "Upload Failed", "Upload Invoice": "", "Upload New Document": "Upload New Document", "Upload Result": "", "Upload Successfully": "Upload Successfully", "Upload Time": "Upload Time", "Uploading": "", "Usage": "", "Usage Based Premium Detail": "", "Usage Code": "Usage Code", "Usage Upload": "Usage Upload", "Usage Upload History": "Usage Upload History", "Use Sub-item": "Use Sub-item", "User": "", "User List": "", "User Name": "", "UW Case Operation": "", "UW Case Required Level": "", "UW Comments": "UW Comments", "UW Criteria": "", "UW Critieria Standard": "UW Critieria Standard", "UW Decision": "UW Decision", "UW Decision Detail": "UW Decision Detail", "UW Decision Details": "UW Decision Details", "UW Decision History": "", "UW Entry Date": "", "Uw History": "Uw History", "UW History": "UW History", "UW in Process": "UW in Process", "UW Message": "UW Message", "UW Owner": "", "UW Query": "UW Query", "UW Stage": "UW Stage", "UW Task": "", "UW Task No": "UW Task No.", "UW Task will be submitted.": "UW Task will be submitted.", "UW_Info": "UW Info", "UW_INFO": "", "UW_OPERATION": "", "V": "V", "Valid": "", "Valid input: {{minValue}} to {{maxValue}}": "", "Value": "Value", "Value Type": "", "Vehicle": "", "Vehicle Additional Equipment": "", "Vehicle Age": "Vehicle Age", "Vehicle Capacity": "Vehicle Capacity", "Vehicle Color": "Vehicle Color", "Vehicle Damaged": "", "Vehicle Examination Area": "", "Vehicle Examination Way": "", "Vehicle Info": "Vehicle Info", "Vehicle Inspection Information": "", "Vehicle Loan": "", "Vehicle Make": "Vehicle Make", "Vehicle Model": "Vehicle Model", "Vehicle Plate No.": "", "Vehicle Premium Detail Download": "", "Vehicle Structure": "Vehicle Structure", "Vehicle Type": "Vehicle Type", "Vehicle Usage": "Vehicle Usage", "VEHICLE_INFO": "", "VEHICLE_LIST_UPLOAD": "Vehicle List Upload", "Verification Check": "Verification Check", "Verification Comment": "", "Verification Decision": "", "Verification Detail": "", "Verification Fail": "Verification Fail", "Verification failed,please upload again.Upload vehicle statistics:": "Verification failed,please upload again.Upload vehicle statistics:", "Verification failed.": "", "Verification History": "", "Verification Pass": "Verification Pass", "Verification Reason": "Verification Reason", "Verification Task Pool": "Verification Task Pool", "Verification Task Pool Re-assign": "Verification Task Pool Re-assign", "Verification task will be submitted.": "", "Verification/Compliance/UW Process Flow Configuration": "Verification/Compliance/UW Process Flow Configuration", "Verify success.Upload vehicle statistics:": "Verify success.Upload vehicle statistics:", "Version": "Version", "Vesting age is invalid if it is earlier or equal to insured entry age.": "", "Vesting: {{vestingAge}}": "Vesting: {{vestingAge}}", "view": "", "View": "View", "View All": "", "View Attachment": "View Attachment", "View Deductible": "", "View Detail": "View Detail", "View History": "", "View Liability": "View Liability", "View Limits": "", "View More": "", "view my task": "view my task", "View Policy Detail": "view policy detail", "view public task": "view public task", "View Tax Details": "", "View the photocopy in a new browser page": "View the photocopy in a new browser page", "View Withdrawal Schedule": "", "ViewAll": "View all", "VIN No": "", "Vin No.": "Vin No.", "Waiting Days to Withdraw": "Waiting Days to Withdraw", "Waiting Effective": "", "Waiting For Compliance": "Waiting For Compliance", "Waiting for process": "Waiting for process", "Waiting For Underwriting": "Waiting For Underwriting", "Waiting For Verification": "Waiting For Verification", "Waiting_for_Acceptance": "Waiting for Acceptance", "Waiting_for_Approval": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "Waiting_for_Evaluation": "Waiting for Evaluation", "Waived": "", "waiver": "", "WAIVER_PAYMENT": "", "Weekly": "Weekly", "Weight": "Weight", "When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated": "When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated", "When you change it, the current data content will be cleared. Are you sure to change it?": "", "Whether need to double check for rejected case": "Whether need to double check for rejected case", "WHOLELIFE": "", "Width of Vehicle": "Width of Vehicle", "With Open Issue": "With Open Issue", "With Open Pending Case": "With Open Pending Case", "With Open Pending Issue": "With Open Pending Issue", "With Pending Case": "With Pending Case", "withDraw": "with<PERSON>raw", "Withdraw Proposal": "", "Withdraw Reason": "", "Withdraw Reason Content": "", "Withdraw Task": "Withdraw Task", "Withdraw the Task": "Withdraw the Task", "Withdrawal Amount": "", "Withdrawal By Amount": "", "Withdrawal Due Date": "", "Withdrawal Period": "", "Withdrawal Reason": "<PERSON><PERSON><PERSON> Reason", "Withdrawal Reason Content": "Withdrawal Reason Content", "Withdrawal Schedule": "", "Withdrawal Successful": "", "Withdrawed successfully": "", "withdrawn": "withdrawn", "Within {{Product}}, some responsibilities have mutually exclusive relationships, please check.": "", "Within Premium Holiday": "Within Premium Holiday", "Witness": "Witness", "Witness Info": "Witness Info", "Work Injury Compensation": "Work Injury Compensation", "Workflow": "Workflow", "Write Off Amount": "Write Off Amount", "Year": "", "YEAR": "", "Year of Manufacturing": "Year of Manufacturing", "year(s)": "", "Year(s)": "", "Yearly": "Yearly", "yes": "Yes", "You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.": "You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.", "You can download the file and check detail reason.": "You can download the file and check detail reason.", "You can only upload PDF/DOC/XLS/PNG/JPG": "You can only upload PDF/DOC/XLS/PNG/JPG", "You can only upload PDF/EXCL/DOC/XLS/PNG/JPG": "You can only upload PDF/EXCL/DOC/XLS/PNG/JPG", "You can only upload PDF/PNG/JPG/TIFF/ZIP": "", "You can only upload XLS": "You can only upload XLS", "You can only upload xlsx": "You can only upload xlsx", "You can only upload XLSX": "You can only upload XLSX", "You can query out the task in UW query after being withdrawn . But no further action allowed. Confirm to withdraw it?": "", "You can query out the task in UW query after being withdrawn . But no furthur action allowed. Confirm to withdrawn it?": "", "You have been assigned {X} tasks": "You have been assigned {{number}} tasks", "You have changed master policy information and will impact uploaded vehicle data. Do you want to re-upload the vehicle list?": "You have changed master policy information and will impact uploaded vehicle data. Do you want to re-upload the vehicle list?", "You have some proposals under the same relation number. You can select the corresponding proposals and issue them together.": "", "Zip Code": "Zip Code"}