{"": "", " has entered manual underwriting, with the underwriting case number": "", " has not successfully passed the automatic {{rule}} check and has been declined.": "", " has not successfully passed the automatic underwriting check and has been declined.": "", ".pdf, .xls, .xlsx, .png, .jpg, .jpeg, .doc, .docx": "", "(Group Policy No. {{groupPolicyNo}})": "", "(wording waiting to be provided)": "", "{{ objectType }} Info": "", "{{ stage }} Stage": "", "{{action}} UW Authority Configuration": "", "{{currentConditionEnumKey}} is not existed in factorEnums.": "", "{{docTypes}} is a multi-version file": "", "{{elapsedDay}} Days Elapsed": "{{elapsedDay}}天已过", "{{fileName}} Log": "", "{{first}}_{{second}}": "{{-first}}_{{-second}}", "{{frequency}} {{type}}": "", "{{index}} Order No. {{orderNumber}}": "", "{{index}}. Location": "", "{{index}}. Location: {{title}}": "", "{{index}}. New Object": "", "{{index}}. Object: {{title}}": "", "{{levelname}} already exists": "", "{{mode}} Claim Stack Definition": "", "{{mode}} Details": "{{mode}}明细", "{{mode}} Invoice Details": "", "{{name}} company": "", "{{name}} Company": "", "{{name}}_v{{version}}": "", "{{objectKeyName}}: {{objectNumber}}": "", "{{objectName}} {{index}}": "", "{{objectTitle}} Info": "", "{{premiumType}} (including tax and discount)": "", "{{premiumType}} (without tax and discount)": "", "{{prev}}-{{next}}": "{{prev}} - {{next}}", "{{rate}}{{separator}}{{date}}": "", "{{role}} Info": "", "{{ruleTitle}} Generation Rule": "", "{{startDate}} ~ {{endDate}}": "", "{{text}} %": "", "{{type}}: {{No}}": "", "{Team Name} is not assigned any strategy, Confirm to submit?": "", "%": "", "+ Add": "", "+ Add loading/Discount": "", "+ Add New": "", "+ Add New Master Agreement": "", "+ Add New Rule": "", "+ Add New Team": "", "+ Add Rule Condition": "", "+ Upload": "", "< Back to Search": "", "< Back To task pool": "", "1st Screening Result": "", "2nd Screening Result": "", "A general rule without a specified Goods already exists. Please select a Goods.": "", "Abbreviation Name": "", "Accept": "", "Acceptance_in_Progess": "", "Acceptedby": "", "Accident Degree": "", "Accident Number in Last 3 Year": "", "Accident Number Last Year": "", "Accident Summary": "", "Account": "", "Account Balance": "", "Account Holder Name": "", "Account Info": "", "Account Name": "", "Account No.": "", "Account Number": "", "Account Transaction Details": "", "Account Transaction Type": "", "Account Type": "", "Accumulated days from all pending proposal status": "", "Accumulated RB Allocation Amount": "累计保额红利", "Accumulated Value": "", "Action(s)": "", "Activate": "", "Activate Case": "", "Activate Case On": "", "Activate\\Inactivate Flag": "", "Activate\\Inactivate History": "", "Actual Arrival Time": "", "Actual Delivery Time": "实际赠送时间", "Actual Departure Time": "", "Actual Payable Amount": "", "Actual Premium": "", "Actual SA": "", "Actual Total No. of Vehicles {{number}}": "", "Actual Total Premium {{totalPremium}}": "", "Ad hoc Single top up": "", "Ad-hoc Notification": "临时通知", "Ad-hoc Single Top Up": "", "Add": "", "Add a Condition": "", "Add a Reminder": "增加提醒天数", "Add Account Info": "", "Add Attachment Type": "", "Add Comments": "", "Add Employee Category": "", "Add Extra Loading": "", "Add Factors": "", "Add Insured Object": "", "Add Location Based Object": "", "Add Location-based Object": "", "Add New": "", "Add New Comments": "", "Add New Level": "", "Add New Master Agreement": "", "Add New Members": "", "Add New Organization": "", "Add New Proposal": "", "Add New Quotation": "", "Add New SME Application": "", "Add New Strategy": "", "Add New Team": "", "Add New Transaction": "", "Add Product": "", "Add/Update Time": "", "Additional Equipment": "", "Additional Limit & Deductible Info": "", "ADDITIONAL_LIMIT_DEDUCTIBLE": "", "Address": "", "Address Info": "", "Address Type": "", "Adj Annual Prem": "", "Adj Annual Prem (Rate)": "", "Adj Annual Prem（Rate）": "", "Adj Net Prem": "", "Adj Net Premium": "", "Adjusted Net Premium": "", "Adjustment Comments": "", "Adjustment Date": "", "Adjustment Information": "", "Adult Number": "", "Advanced Payment Amount": "", "After": "", "After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.": "", "After modifying the information, it needs to be recalculated. Please confirm.": "", "Age": "年龄", "Age: {{age}}": "年龄: {{age}}", "Agency": "代理店", "Agency Name": "", "Agency: {{agencyName}}": "", "Agent": "", "Agent Code": "代理代码", "Agent Name": "", "Aggregate Amount": "", "Aggregated Amount (Applying)": "累计金额（申请中）", "Aggregated Amount (Inforce)": "累计金额（有效中）", "Aggregated Amount (Total)": "累计金额（总和）", "Agreement Settlement Rule": "Agreement Settlement Rule", "Airline Company": "", "Alert": "警告提醒", "All": "", "All designated beneficiary information will be deleted. Please Confirm.": "", "All goods": "所有商品", "All goods categories": "所有商品类别", "All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?": "切换客户类型会导致现有的客户数据清空，请确认。", "All questions have been answered.": "", "All upload data you currently submit will be recorded. If the file verification failed, it will not be submitted.": "", "All upload data you currently submit will be recorded. If the file verified failed, it will not be saved.": "", "All upload data you currently submit will be recorded.If the file verification failed,it will not be submitted.": "", "Allocation": "分配比率", "Allocation Amount": "分配比率金额", "Allocation Date": "分配比率日期", "Allocation Frequency": "", "Almost!": "", "Amount": "", "Amount Name": "", "Ancestor Policy No.": "", "Annual Prem": "", "Annual Prem. Methods": "", "Annual Premium": "", "Annual Standard Planned Premium": "", "Annual Standard Premium": "年标准保费", "Annualized Premium / Single Premium": "年度保费/趸缴保费", "Annualized Regular Premium": "", "Annuity": "", "Annuity Allocation History": "", "Annuity Amount": "年金额", "Annuity Defer Period Type": "年金等待期间类型", "Annuity guarantee Period": "年金保证领取期间", "Annuity Info": "年金信息", "Annuity liability": "", "Annuity Payment Account": "年金支付账户", "Annuity Payment Frequency": "年金支付频率", "Annuity Payment Option": "年金领取方式", "Annuity Payment Period Type": "年金支付期类型", "ANNUITY_INFO": "", "Applicable": "", "Application": "", "Application Date": "报案日期", "Application Item": "", "Application No": "报案编号", "Application No {{ appNo }}": "", "Application No.": "", "Application Type": "", "Applied to waive premium liability by POS": "", "Applied Withdrawal Amount": "", "Appliedby": "", "Apply Master Plan": "", "Apply to all products": "适用于所有产品", "Apply to partial paid proposal?": "", "Appointment Rate": "基金分配比例", "Approval": "", "Approval Comment": "", "Approval Date": "通过日期", "Approval History": "", "Approval Result": "", "Approval Time": "", "Approval_in_Progess": "", "Approve": "", "Approvedby": "", "Are you sure to activate this case?": "你确定激活这个案件吗？", "Are you sure to cancel?": "", "Are you sure to change {{type}}?": "", "Are you sure to clear all the upload records?": "", "Are you sure to delete current level?  If delete, the all level order will change.": "", "Are you sure to delete selected type? It will remove all files under this type.": "确定要删除选择的类型吗？它将会删除该类型下所有的文件", "Are you sure to delete the level?": "", "Are you sure to delete the level?  Current authority level is in force.": "", "Are you sure to delete the level? Current authority level is in force.": "", "Are you sure to delete this comment?": "", "Are you sure to delete this document?": "确定删除该文件？", "Are you sure to delete this product?": "", "Are you sure to delete this task?": "", "Are you sure to delete?": "", "Are you sure to remove it?": "确定要解除吗？", "Are you sure to submit current authority configuration? After submitting, the status of level will become effective.": "", "Are you sure you want to approve the insurance application?": "", "Are you sure you want to decline the insurance application?": "", "Are you sure you want to release this UW task?": "", "Are you sure you want to return the application for the current product?": "", "Are you sure you want to return the entire submission?": "", "Are you sure you want to save the current sort?": "", "Are you sure you want to submit this insurance application?": "", "Arrival Airport": "", "Arrival Delay Time": "", "Assign": "任务分派", "Assignee": "", "ASSIGNEE": "", "Assignee Type": "", "Associated goods": "", "Associated Policy": "关联保单", "Associated Proposal": "关联投保单", "Associated UW Task": "", "Associated UW Task Application No.": "", "At least one item must be created under the address, please check.": "地址下至少要创建一个标的，请检查。", "At least one should be selected": "", "at_least_one_input": "请至少输入一项查询条件", "Attached To": "附加到: {{ productName }}", "Attached To Product": "", "Attachment": "", "ATTACHMENT": "", "Attachment Configuration": "", "Attachment Quotation Stage": "", "Attachment Type": "", "ATTACHMENT_BUNDLE": "", "Attachment/Mandatory": "", "Attachments": "", "Authority Configuration": "", "Authorization Agreement": "", "Authorization Agreement No.": "", "Authorization Agreement Signing Date": "", "Auto": "", "Auto compliance rule result": "自动合规规则校验结果", "Auto Compliance Rule Result": "", "Auto Rebalancing": "自动再平衡", "Auto Rebalancing Close": "", "Auto Rebalancing Opened": "", "Auto Renewal": "", "Auto UW Rule Result": "自核规则提示", "Auto Verification Rule Result": "", "AUTO_UW_RULE_RESULT_V2": "", "Automated Fill Condition": "", "Automatic Compliance Result": "", "Automatic Premium Loan": "保单自动贷款历史记录", "Automatic Premium Loan Detail": "保单自动贷款历史记录明细", "Automatic Underwriting Result": "", "Autopay Flag": "", "Average Driving Distance": "", "Back": "", "Back to Edit": "", "Back to main page": "回到主页面", "Back to Modify": "", "Back to Search": "返回搜索", "Back To task pool": "回到任务池", "Back To Worksheet": "", "Bank Account No.": "", "Bank Address": "", "Bank Branch Address": "", "Bank Branch Code": "", "Bank Branch Name": "", "Bank Code": "", "Bank Code/Bank Name": "", "Bank Name": "", "Bank Transfer / Account Type": "银行转移/账户类型", "Based on Calendar Days": "", "basic info": "基础信息", "Basic Info": "基础信息", "Basic Info Info": "", "BASIC_INFO": "基础信息", "BASIC_INFO_VIEW": "基础信息", "BASIC_INFO_WITH_STAND_ALONE": "", "BASIC_INFO_WITHOUT_GOODS": "基础信息", "Batch list Goods": "保险商品", "Batch List Upload": "批量列表上传", "Batch List Upload History": "批次列表上传历史", "Batch Number": "批次编号", "Batch Reassign": "", "Batch Uploading": "", "BCP": "", "Be Attached By": "被附加: {{productNameList}}", "Before (Rate/Amount)": "", "Before Tax & Service Fee": "", "Beneficial Owner": "", "BENEFICIAL_OWNER": "", "Beneficiary": "", "BENEFICIARY": "", "Beneficiary Ratio": "", "Beneficiary ratio is": "", "Beneficiary Type": "", "BENEFICIARY_CN": "", "BeneficiaryRatio": "", "beneficiaryRatioTooltip": "所有受益人的受益比例之和应为100%，请确认。", "Benefit": "", "Benefit Account Balance": "收益账户余额", "Benefit Allocation History": "收益分配历史", "Benefit Amount": "保单利益金额", "Benefit info": "保单利益信息", "Benefit Info": "", "Benefit Next Due Date": "下期收益日", "Benefit Option": "", "Benefit Payment Account Info": "保单支付账户信息", "Benefit Schedule": "保单收益计划", "BENEFITS_INFO": "", "Bill Amount": "", "Bill Information": "", "bill_amount_details": "", "Bill_No": "", "Bind Task Assignment Rule": "", "Birthday": "", "blacklist_search_result_unit": "条", "Body Type": "", "BONUS": "", "Bonus Allocation History": "", "Bonus Amount": "分红金额", "Bonus Code": "", "Bonus Info": "分红信息", "Bonus Name": "", "Bonus Next Due Data": "", "Bonus Next Due Date": "分红日期", "Bonus Payment Account Info": "分红支付账户信息", "Bonus Total Balance": "分红余额", "Bonus Type": "", "Bonus/malus": "", "Booking Number": "", "Booking Time": "", "Both": "所有人", "BOTH_GOODS_CANNOT_BE_ISSUED_SIMULTANEOUSLY {{primaryGoods}}{{secondaryGoods}}": "{{secondaryGoods}}依赖于{{primaryGoods}}。如果您想删除{{primaryGoods}}，请先删除{{secondaryGoods}}。", "Branch Name: {{name}}": "销售分支名称: {{name}}", "Building Info": "", "Building Size": "", "Building Type": "", "Building up the File, please wait patiently.": "文件处理中，请耐心等待", "Built Year": "", "Burglar Alarm": "", "Business Activity/Sector": "", "Business License No.": "", "Business Month": "", "Business No": "Business No", "Business No. Generation Rule": "", "Business No. n Generation Rule": "", "Business Scenario": "", "Business Transaction No": "", "Business Transaction No.": "", "Business Type": "Business Type", "Buy/Sell": "买卖方式", "By Commission Category": "佣金类别维度", "By Product": "产品维度", "BY_EVENT_INFO": "", "by:": "", "By: userName": "By: {{userName}}", "By:{{creator}}": "", "ByEvent_Policy_Data": "", "Calculate": "", "Calculate successfully": "", "Calculate Tax when Clear Account": "", "Calculation Basis": "计算依据", "Calculation Date": "", "Calculation Direction": "", "Calculation Frequency": "", "Calculation Level": "", "Calculation Order": "", "Calculation/Capitalization Period": "", "Campaign": "", "Campaign Discount": "营销活动折扣", "Campaign Discount Type": "", "Campaign info": "", "Campaign Info": "", "Campaign Type": "", "Campaign_code": "", "Campaign_name": "", "Cancel": "取消", "Cancel pin": "", "Canceled": "", "Cancelled": "", "Car Owner": "", "Car Owner & Driver & Renter": "", "Car Owner Birthday": "", "Car Owner Gender": "", "Car Owner ID Number": "", "Car Owner ID Type": "", "Car Owner Name": "", "Car Owner Name2": "", "Car Owner Name3": "", "Carring Goods Type": "", "Case Operation": "", "Case Owner": "", "Cash Before Cover": "", "Cash Bonus": "现金分红", "Cash Bonus Account Transaction Details": "现金分红账户交易明细", "Cash Bonus Allocation Details": "现金分红分配明细", "Cash Value Saving Account": "", "Certificate No": "证书号", "Change Payment Info": "", "Change Principal": "", "Change the current process flow from the process flow template": "", "Change Underwriter": "", "Changes made after submission will not be revoked.": "", "Channel": "渠道", "Channel Name": "", "Channel User No.": "", "channelCode": "", "channelCode{{channelCode}}": "", "channelUserNo": "", "channelUserNo{{channelUserNo}}": "", "Charge Amount": "", "Charge Code": "", "Charge Due Date": "", "Charge Period": "", "Charge Type": "", "Chassis No.": "", "Check relatives or not": "", "Check Segment": "", "Check staff is on duty or not": "", "Children Number": "", "City": "", "Claim": "", "CLAIM": "", "Claim Amount": "", "Claim Compensation": "", "Claim Experience": "", "Claim History": "", "Claim Name": "", "Claim No": "", "Claim No.": "", "Claim Number in Last 3 Year": "", "Claim Number Last Year": "", "Claim Ratio": "", "Claim Stack": "", "Claim Status": "", "Claim Summary": "", "Claim Type": "", "Claim Waive": "理赔豁免", "Claim_Archives_Room": "", "CLAIM_DETAILS": "", "CLAIM_HISTORY": "", "CLAIM_INFO": "", "Claimable Period": "", "Clause Information": "", "Clear all": "", "Clear All": "", "Clear successed!": "", "Clear Successfully": "", "Click Add another type": "", "Click on the policy version number below to switch policy version information.": "", "Click or drag file here area to upload": "", "Click or drag file here to upload": "点击或拖拽文件至此处上传", "Click or drag the file here to upload": "", "close": "", "Close": "关闭", "Closed": "", "Code": "", "Code No. {{code}}": "", "Collapse": "", "Collapse All": "", "Collected Amount(Original Currency)": "", "Collected Amount(Policy Currency)": "", "Collection & Refund": "", "Collection Amount Detail": "", "Collection Method": "", "COLLECTION_AND_REFUND": "", "collection_and_refund_amount": "Collection&Refund Amount", "collection_or_refund_amount": "Collection/Refund Amount", "collection_refund_amount_details": "", "collection_refund_item": "收款/退款项目", "Color of Plate No": "", "Combination Sequence": "", "Comment": "", "COMMENTS": "", "Comments: {{remark}}": "", "Commision": "", "Commission": "佣金", "Commission (On Top of Premium)": "", "Commission & Service Fee": "佣金及服务费", "Commission Details": "佣金明细", "Commission Generated Date": "佣金生成日期", "COMMISSION_AGENT": "", "COMMISSION_AND_SERVICE_FEE": "", "Commission（On Top of Premium)": "佣金（价外支付）", "CommissionAmount": "佣金金额", "CommissionType": "", "Commodity Information": "", "Common Info": "基本信息", "Compaign Discount": "", "Company Name": "", "Compiance History": "", "Complete": "", "Complete Date": "完成日期", "Complete Time": "", "Completed Date": "Completed Date", "compliance": "compliance", "Compliance": "", "Compliance Check": "", "Compliance Decision": "合规决定", "Compliance Decision Details": "Compliance Decision Details", "Compliance History": "", "Compliance Info": "Compliance Info", "Compliance Result": "", "Compliance Status": "合规状态", "Compliance Task No.": "Compliance Task No.", "Compliance Type": "", "COMPLIANCE_INFO": "", "COMPLIANCE_RESULT": "", "Concurrent Case": "", "CONCURRENT_CASE": "", "Conditional Accepted": "条件承保", "Configure the Task Assignment Rule first": "", "Confim to delete this plan?": "", "Confirm": "确认", "Confirm Application": "", "Confirm Date": "", "Confirm to clear all data?": "", "Confirm to delete": "", "Confirm to delete current document?": "确定删除当前文件？", "Confirm to delete?": "", "Confirmation Date": "确认日", "Confirmation Required": "", "Confirmed Fund Price": "", "Consentee": "", "CONSENTEE": "", "Contact Address": "", "Contact Person": "", "Contact Person Info": "", "Contact Phone Info": "", "ContactPerson": "", "content": "", "Content": "", "Continue to Submit": "", "Contract Date": "", "Contract Number": "", "Copy": "", "copy failed": "", "Copy from": "", "Copy from {{tabName}} Product": "", "Copy from Motor Product": "", "Copy Master Agreement": "", "Copy master agreement  to Relational Policy": "", "Copy successful!": "", "Copy Successfully": "", "Copy To": "", "Copy to New Product": "", "Copy to New Version": "", "Country": "", "Country Code": "", "Country of Residence": "", "Coverage": "", "Coverage / Sub Coverage": "", "Coverage Detail": "", "Coverage Details": "", "Coverage Info": "", "Coverage Level": "", "Coverage Period": "保障期间", "Coverage Period Type": "保障期间类型", "Coverage Period Value": "保障期间的值", "Coverage Plan": "覆盖计划", "Coverage plan cannot be matched due to incompleted information, please complete the object information first.": "", "Coverage Plan Details": "", "Coverage plan will be refreshed, please confirm.": "", "Coverage Total Prem": "", "COVERAGE_GOODS_DETAIL_BUNDLE": "", "COVERAGE_INFO": "", "COVERAGE_INFO_DRAWER_CLAIM_STACK": "", "COVERAGE_INFO_DRAWER_COVERAGE_DETAILS": "", "COVERAGE_INFO_DRAWER_DCA_ARRANGEMENT": "DCA计划", "COVERAGE_INFO_DRAWER_FUND_APPOINTMENT": "", "COVERAGE_INFO_DRAWER_LIABILITY": "", "COVERAGE_INFO_DRAWER_PORTFOLIO_REBALANCING": "投资组合再平衡", "COVERAGE_INFO_DRAWER_PREMIUM_INFO": "", "COVERAGE_INFO_DRAWER_RETIREMENT_OPTION": "退休计划", "COVERAGE_INFO_DRAWER_TOP_UP": "", "COVERAGE_PLAN": "", "COVERAGE_PLANS": "", "COVERAGE_PREMIUM": "", "COVERAGE_PREMIUM_BUNDLE": "", "Create Date": "创建日期", "Create Policy under Master Agreement": "", "Create task assignment rule to match {{module}}.": "", "Create team and bind task assignment rule. The {{module}} hit binded rule will be pushed into this team.": "", "create time": "创建时间", "Creation Date": "创建日期", "Creator": "", "Currency": "", "currency_amount_Ap": "", "currency_combine_amount": "", "Current Handler": "当前处理者", "Current Policy Change Overview": "", "Current Policy Overview": "", "Current Premium Amount": "", "Current process flow": "", "Current system date": "", "Current Underwriting Level": "当前核保等级", "Current Version": "", "Currently policyholder & insured are different person. But after the change, we find their info is the same and system will treat them as one person. Confirm to merge them?": "当前投被保险人不为同一人。信息变更后，投被保险人信息相同，系统将对其合并为同一人。确认合并？", "currentOperator": "", "Customer": "", "Customer Grade": "", "Customer ID Number": "", "Customer List": "", "Customer Screening Result": "", "Customer Type": "", "Customer type not selected yet. Please confirm.": "尚未选择客户类型，请确认。", "CUSTOMER_INFO": "", "CUSTOMER_PROFILE": "", "Daily": "Daily", "Data Entry in Progress, Pending Proposal Check, Waiting for Issuance": "", "Data is modified but not saved. Do you want to save the modified content?": "", "Date of birth": "", "Date Of Birth": "出生日期", "Date Of Witness": "", "Date Quote Needed": "", "DAY": "", "Day(s)": "", "Days": "天数", "DayS": "", "Days Types": "", "DCA": "", "DCA Amount (for each period)": "（每期）DCA金额", "DCA Arrangement": "DCA计划", "DCA Frequency": "DCA频率", "Death Date: {{dateOfDeath}}": "", "Debit Note Amount": "", "Debit Note Information": "", "Debit Note Information ": "", "Debit Note No.": "", "Debit Note No. No. {{debitNoteNo}}": "", "Deceased": "", "Decision": "核保决定", "Decision Details": "", "Decision Fail Decline": "根据最新的投保单信息，提交此核保案件将导致拒保，请确认。", "Decision Failed": "", "Decision Reason": "", "Decision Successfully": "", "Decision Time": "", "Declaration Date": "", "Declaration Stage": "", "Declaration_Information": "", "Decline": "", "Deducted From Investment": "从投资账户中扣除", "Deductible": "免赔额", "Deductible Amount": "", "Deductible Info": "", "Deduction Source": "", "Deduction Type": "", "Defer Period": "等待期间", "Delete": "", "Delete success": "", "Delete Successfully": "", "Deleting a goods will also delete all corresponding data, please confirm.": "删除商品会同步删除所有对应数据，请确认。", "Delivery Status": "交付状态", "Department Code": "部门编码", "Departure": "", "Departure Airport": "", "Departure City": "", "Departure Country": "", "Departure Delay Time": "", "Departure Point": "Departure Point", "Departure Time Zone": "", "Deposit Account Balance": "储蓄账户余额", "Description": "", "Designated Beneficiary": "", "Designated Beneficiary :": "", "Designated beneficiary ratio must equal to 100%": "", "Destination": "", "Destination City": "", "Destination Country": "", "Destination Region": "", "Destination Time Zone": "", "detail": "明细", "Details": "详细", "Device Abs": "", "Device AEB": "", "Device Airbag": "", "Device Alarm": "", "Device Brand": "", "Device Buyer ID": "", "Device Buyer Review Score": "", "Device Category": "", "Device Description": "", "Device Gear or steering lock": "", "Device GPS": "", "Device ID": "", "Device Immobiliser": "", "Device Info": "", "Device Installed": "", "Device Manufacturer": "", "Device Market Value": "", "Device Model": "", "Device Name": "", "Device Number": "", "Device Perchuase Time": "", "Device Price": "", "Device Seller ID": "", "Device Seller Review Score": "", "Device Status": "", "Device Tracking": "", "Device User": "", "Digit Length": "", "Digit Position": "", "Direct sales": "直销", "Disbursement Method Information": "", "Discount": "", "Discount Amount": "", "Discount Period (Premium Due No)": "", "discount_type": "", "Distance Confirmation Date": "", "Distribution Method": "分红方式", "Ditrh of birth": "", "Dividend Payment Method": "", "DOB": "", "Document": "", "Document Generation": "文件生成", "Document Generation Management": "文档生成管理", "Document Name": "文件名称", "Document Name {{currentImgTitle}}": "Document Name: {{currentImgTitle}}", "Document Type": "Document Type", "Document({{total}})": "文件({{total}})", "Documents": "", "Dollar Cost Averaging": "美元成本平均法", "Don't Need Reminder": "关闭提醒", "Don't Use Sub-item": "", "Down Sell SA": "", "Download ({{size}})": "", "Download {{size}}": "", "Download & Send Password": "", "Download All": "", "Download All Event Policies": "下载所有Event保单", "Download E-Policy": "", "Download failed": "", "Download successfully": "", "Download Template": "下载模板", "DRAFT": "", "Drive ID Type": "", "Driver": "", "Driver Birthday": "", "Driver Experience": "", "Driver Gender": "", "Driver ID Number": "", "Driver IdNumber": "", "Driver IdType": "", "Driver Industry": "", "Driver Information": "", "Driver License Number": "", "Driver License Registration Date": "", "Driver Marital Status": "", "Driver MaritalStatus": "", "Driver Name": "", "Driver Name2": "", "Driver Name3": "", "Driver Occupation": "", "Driver Tier": "", "Driver Type": "", "Driving Distance": "", "Driving Experience": "", "Driving License No.": "", "Driving License Registration Date": "", "Driving Licensen No.": "", "Due Date": "到期日", "due to the change of insured information": "由于保人信息变化", "Due Unpaid Premium": "", "Duplicate configuration, please check.": "", "Duplicate Master Agreement No.": "", "Duplicated Tag": "", "Duration": "", "E-mail": "", "E-mail Info": "", "E-policy": "电子保单", "Each product chooses at least one liability": "每个产品至少选择一种责任。", "EB_COVERAGE_GOODS_DETAIL": "", "EB_COVERAGE_PREMIUM": "", "EB_INSURED": "", "Edit": "编辑", "Edit by": "", "Edit By": "", "Edit Document": "", "Edit Extra Loading": "", "Edit Name": "", "Edit Now": "Edit Now", "Edit Process Flow": "", "Edit Strategy": "", "Edit Team": "", "Editing": "", "effective": "生效", "Effective": "", "Effective Date": "", "Effective Date Time Zone": "", "Effective Period": "", "Effective Sub Policy": "", "Effective Time": "", "EffectiveDate": "", "ELECTRONIC_EQUIPMENT": "", "Email": "邮箱", "Employee Category": "", "Employee Category {{No}}": "", "EMPLOYEE_GROUP": "", "End": "", "End Day": "", "End Time": "", "Engine No.": "", "Enrollment Transaction": "", "Entry Time": "", "Error": "", "Error File": "", "Error happened during upload.": "", "Error Notification": "", "Escalate": "向上反应", "Escalate or Reassign": "", "Escalate Stage": "", "Escalate Successfully!": "", "Escalation Task": "", "esclated/esclate": "核保上报", "Estimate Lapse Date": "", "Estimated Total  Premium": "", "Estimated Total No. of Vehicles": "", "Evaluatedby": "", "Evaluation Decision": "评估决定", "Evaluation_in_Progess": "", "Event Policy Issue Switch": "", "Event Policy No.": "", "Event Policy Upload": "事件保单上传", "Event Policy Upload History": "事件保单上传历史", "Every {{frequency}}": "", "Examination Date": "", "Examination Description": "", "Examination Result": "", "Exchange Rate": "汇率", "Exchange Rate (PremCur/BaseCur)": "", "Exchange Rate (SaCur/BaseCur)": "", "Exchange Rate (SaCur/PremCur)": "", "Exchange Rate Date (PremCur/BaseCur)": "", "Exchange Rate Date (SaCur/BaseCur)": "", "Exchange Rate Date (SaCur/PremCur)": "", "Excluding Promotion Discount": "", "Exclusion": "Exclusion", "EXCLUSION": "", "Exclusion Category": "", "Exclusion Clause": "", "Exclusion Code": "", "Exclusion Content": "", "Exclusion List": "", "Exclusion Lists": "", "Exclusion Reason": "", "Expand": "", "Expand All": "", "Expired": "", "Expired Date": "", "Expiry": "", "Expiry Date": "", "Expiry Date should be later than effective date": "", "Expiry Date Time Zone": "", "ExpiryDate": "", "Export": "", "Export All": "", "Export Log": "", "Export Time": "", "External Name": "", "External_Doc_No": "", "Extra Loading": "", "Extra Loading amount": "", "Extra Loading Type": "", "Extra Premium": "加费", "Extra Premium Due Day": "额外缴费天数", "Extra Setting": "", "Extract Period": "", "Extract period was entered error, please check!": "", "Extract Policy Condition": "", "Fail Reason": "", "Failed Reason": "", "Failed Record List": "", "Failed Records": "", "Falculative": "临分", "fee_status": "", "Feedback": "", "FeeType": "", "FF Weight": "", "Fidelity Guarantee": "", "Field can only be digital or letter": "", "Field Value": "", "Fields Name": "", "File Creator": "创建者", "File Name": "文件名", "File Name: {{fileName}}": "", "File size cannot exceed": "", "File Upload Time": "上传时间", "Fill In": "", "Fill in from Vehicle List": "", "Fill in the Exisiting Customer": "", "Fill in the Exisiting Customer >>": "", "Fill in the existing account": "", "Fill in the Existing Account": "", "Filter": "", "Final Decision": "最终核保决定", "Final Underwriting Level": "最终核保等级", "FINAL_DECISION": "", "Finance": "", "FINISH_PAYMENT": "", "Fire Alarm": "", "First Name": "", "first.": "", "Fold Menu": "", "Follow the Investment Strategy": "", "Follow the same appointment rate as planned premium and top ups.": "", "For Liability": "", "For List Data": "", "For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.": "", "For Product": "", "For Regular Data": "", "For single cash value type, only one formula could be defined. No duplicate allowed.": "", "For Team": "", "For the same rule or rule set, only one record is allowed.": "", "Formula Category": "", "Formula Code": "", "FR Weight": "", "Free amount of Liability SA": "", "Free Investment Amount": "Free Investment Amount", "Free_amount_of_liability_sa": "", "Free_policy_info": "", "Free_policy_no": "", "Frequency": "领取频率", "Frequency of Payment": "", "Fronting": "前端业务", "Full Name": "", "Full Records": "", "Fund": "基金", "Fund Allocation": "", "Fund Application Date": "", "Fund Appointment": "", "Fund Appointment After Rebalancing": "再平衡后的基金分配比例", "Fund Appointment Before Rebalancing": "再平衡前的基金分配比例", "Fund Appointment For Rebalancing": "投资再平衡的基金分配比例", "Fund Balance": "基金账户余额", "Fund Code": "", "Fund Currency": "", "Fund Name": "", "Fund Price": "基金价格", "Fund Transaction Date": "", "Fund transaction details": "基金交易明细", "Fund Transaction Details": "", "Fund Value": "", "Fund Value After Rebalancing": "", "Fund Value Before Rebalancing": "", "G Weight": "", "Gender": "性别", "Generate": "", "Generate Offer": "", "Generate Policy Schedule": "", "Generate Premium": "", "Generate Quotation Form": "", "Generate/Regenerate Reason": "生成/重新生成的原因", "Generated Date": "Generated Date", "Generation Time": "生成时间", "Get Result": "", "Gift Code": "礼物码", "Gift Delivery Dimension": "礼物交付维度", "Gift Delivery Method": "礼物交付方式", "Gift Delivery Time": "礼物交付时间", "Go to New Quote": "", "Go to Quote Bound": "", "Go to Quote Sent": "", "Goods": "", "GOODS": "", "Goods cannot be empty": "", "Goods Category": "", "Goods Code/GoodsName": "", "Goods in Transit": "", "Goods is not on sale, please check.": "", "Goods Name": "商品名称", "Goods Name and Model": "Goods Name and Model", "Goods Summary": "", "Goods Version": "", "GoodsName": "", "GoodsVersion": "", "Got it": "", "Green Card Fee": "", "Green Card No": "", "Group Level": "", "Group No": "", "Group Personal Accident": "", "Group Policy": "团体保险", "Group Policy No.": "", "Group Policy No. {{groupPolicyNo}}": "", "Group Policy Query": "", "Guarantee Period": "", "HALFYEAR": "", "has not successfully passed the automatic underwriting check and has been declined.": "", "has Original Pol": "", "has successfully passed the automatic check.": "", "Haulage Permit No": "", "HEADER_MORE_INFO": "", "High Level": "", "Hight of Vehicle": "", "Historical Permium Holiday": "", "Historical Premium Holiday": "", "History": "", "History Type": "History Type", "HOLDER": "", "Home Protection Schema (HPS) Exemption: {{hpsExemption}}": "", "Hospital": "", "Hospital Name": "", "Hour(s)": "", "How to Deal with Balance Amount": "如何处理账户余额", "How to Use": "", "IBAN": "", "ID Card": "", "ID No.": "", "ID NO.": "证件号", "ID No. ": "", "ID Type": "证件类型", "Identifier Info": "", "If": "", "If satisfy all of the following conditions": "", "If satisfy any of the following conditions": "", "If the file you want to upload does not belong to the above file type, click Add another type": "", "If the option is selected, the system will generate reminder based on days only. Otherwise, specific times will be considered.": "勾选此选项，系统将按照天数判断是否生成提醒邮件；不勾选则按照天数加时间来判断。", "If the option is selected, the system will withdraw the proposal based on days only. Otherwise, specific times will be considered.": "勾选此选项，系统将按照天数判断是否撤销投保单；不勾选则按照天数加时间来判断。", "Ignore the rule and proceed": "忽略该规则并继续提交", "ILP Bonus": "", "Image": "", "Immediate Effect": "", "Import Type": "", "Inactivate Case": "", "Inactivate Case On": "", "Incident Date": "", "Incident Reason": "", "Including Promotion Discount": "", "Individual": "", "Individual Policy Upload": "个单上传", "Individual Policy Upload Under Master Agreement": "团单协议下的个单上传", "Industrial Classification": "Industrial Classification", "Info not Completed": "", "Initial Number": "", "Initial Premium Amount": "", "Initial Premium Collected": "", "Initial Premium Due": "", "Initial Premium Payment": "", "Initial Principal": "最初元金金額", "Initial_Premium_Payment_Method": "", "Initiallment_Premium_Payment_Method": "", "Initiate Manual Underwriting": "", "Input": "", "Inspection Expiry Date": "", "Installment": "期", "Installment / Renewal Premium Payment": "", "Installment No.": "Installment No", "Installment Number": "", "Installment Premium": "每期保费", "Installment Premium (Before Campaign)": "每期保费（营销折扣前）", "Installment Premium (Before Tax & Service Fee)": "每期保费（税费前）", "Installment Premium (Total)": "每期保费（总计）", "Installment Premium period": "", "Installment_Premium_Bill": "", "Installment_Premium_including_tax_discount": "", "Installment_Premium_without_tax_discount": "", "Instruction": "", "Insurable Interest": "", "Insurance": "Insurance", "Insured": "被保人", "INSURED": "被保人", "Insured (Main Product)": "被保人（主险）", "Insured Certificate Type": "", "Insured Email": "被保者邮箱", "Insured ID No": "被保人身份编号", "Insured Id No.": "", "Insured Info": "", "Insured Info will be cleared and replaced by policyholder Info. Are you sure to proceed?": "", "Insured Location": "", "Insured Name": "被保人姓名", "Insured Name: {{insuredName}}": "", "Insured Name2": "被保者姓名（假名）", "Insured Object": "", "Insured Object Category": "", "Insured Object Information": "", "Insured Object Name": "", "Insured Object Name: {{name}}": "", "Insured Object of Building": "", "Insured Object of Device": "", "Insured Object of Loan Guarantee": "", "Insured Object of Order": "", "Insured Object of Pet": "", "Insured Object of Product": "", "Insured Object of Vehicle": "", "Insured Object Type": "", "Insured Query": "", "Insured Type": "", "INSURED_CN": "被保人", "Insured_Object_Info": "", "INSURED_OBJECT_INFO": "", "INSURED_OBJECT_PROFILE": "", "insured_period": "保障期间", "InsuredIDNo": "", "Insuresd ID No.": "", "Insuresd Name": "", "Interest": "", "Interest Balance": "利息余额变动", "Interest Balance Change": "", "Interest Rate": "", "Internal Offset Amount": "", "Invalid": "", "Investment & Loan Info": "投资与贷款信息", "Investment Horizon": "投资期限", "Investment Info": "投资信息", "Investment Strategy": "投资策略", "INVESTMENT_INFO": "", "Invoice No.": "账单号", "Invoice Number": "", "Invoice Received Date": "", "Invoice Status": "", "Invoice Task Pool": "", "Invoice/Credit Note Number": "", "is Loan Vehicle": "", "is New Vehicle": "", "is Not Registed": "", "Is Renewal Policy": "", "Is renewal quote process required?": "是否需要进行续保报价？", "is Special Shape Vehicle": "", "Issue": "", "Issue Date": "", "Issue Policy": "", "Issue policy under master agreement": "在主保单下出单", "Issue successfully": "", "Issue Tips": "", "Issue without payment": "", "Issue Without Payment": "", "Issue without payment for individual policy": "", "ISSUE_AGENT": "", "Issued": "", "Issued successfully": "", "Issued successfully!": "", "Issured Name": "", "It is not supported to save exactly the same type information": "", "item": "", "Item Code": "", "Item Name": "", "Key Node": "", "Kindly search the Policy No. first.": "请先搜索保单编号。", "Label": "", "Label: Value": "", "Laibility Detail": "", "Landline": "", "Language": "", "Lapsed": "", "Lapsed Reason": "失效原因", "LapsedDate": "", "LapsedReason": "", "Last Decision": "", "Last Name": "", "Last Operation Time": "", "Last Price Date": "", "Last Update User": "", "Last Year Driving Distance": "", "Latest Interest Amount": "最近利息金额", "Latest Interest Calculation Date": "最近利息计算日期", "Latest Interest Capitalization Date": "最近利息资本化日期", "Latest Status": "", "latitude": "", "Legal {{type}}": "", "Legal Beneficiary": "", "Legal Representative Info": "", "Legal Trustee": "", "Length of Vehicle": "", "Length: {{length}}": "", "Level": "", "Level Name": "", "Level Order": "", "Levy": "保单征费", "Liabilities": "", "Liability": "", "Liability Category": "", "Liability Coverage Period": "", "Liability ID": "", "Liability Name": "", "Liability Premium": "", "Liability SA": "", "Lien": "", "Lien Exclusion Clause": "", "Limit": "", "Limit Info": "", "Link Product": "", "Linked Investment Product": "", "List Data": "", "Loading": "", "Loading & Down Sell": "", "Loading List": "", "Loading Method": "", "Loading Period": "", "Loading Period Type": "", "Loading Type": "", "Loading Value": "", "Loan Balance": "贷款余额", "Loan Balance Details": "贷款余额明细", "Loan Company": "", "Loan Contract No.": "", "Loan Effective Date": "贷款生效日期", "Loan Info": "贷款信息", "Loan Provider": "", "Loan Years": "", "Location {{index}}": "", "Location Based Object": "", "Location Details": "", "Location-based Object": "", "longitude": "", "Machinery Breaakdown": "", "MACHINERY_EQUIPMENT": "", "Main": "", "Main / Rider": "主险/附加险", "Main Benefit": "", "Main Condition Type": "主要条件类型", "Main Driving Area": "", "Main Insured": "", "Main Insured ID No.": "", "Main Insured ID Type": "", "Main Insured Name": "", "Main Insured Sub Policy No.": "", "Main Product": "主险", "Main_Rider": "", "Manager": "经理", "Manager has been changed. Please check.": "经理已变更，请检查。", "Mandatory": "", "Manual Compliance Decision": "", "Manual Input": "", "Manual UW Query": "", "Manual UW Task Pool": "人工核保工作池", "Marital Status": "", "Market Price": "", "MARKET_SEGMENTATION": "", "Marketing Goods Selection": "", "Marketing Goods Settings": "", "marriageStatus": "", "Master Agreement Change": "", "Master Agreement Effective Date": "主协议单生效日", "Master Agreement Effective Time": "主协议生效时间", "Master Agreement Expiry Date": "主协议单满期日", "Master Agreement No {{busiNo}}": "", "Master Agreement No.": "协议单单号", "Master Agreement No. {{masterPolicylNo}}": "", "Master Agreement No. {{masterPolicyNo}}": "", "Master Agreement No.: {{masterPolicyNo}}": "", "Master Agreement Status": "协议单状态", "Master Agreement Sub-category": "主协议子类别", "Master Agreement Task Pool": "", "Master Insured Name": "主被保人名", "Master Policy": "主投保人", "Master Policy No.": "主协议单号", "Master Policyholder": "主投保人", "Master Policyholder Name": "主投保人名", "MASTER_AGREEMENT_BASIC_INFO": "", "Master_policy_Status": "", "masterPolicyChangeProcessed": "", "masterPolicyChangeProcessing": "", "Matched Tag": "", "Mater Policy No": "", "Mater Policy No.": "", "Maturity Agreement": "满期约定", "Maturity Benefit": "", "Maturity Benefit Account Transaction Detail": "满期金账户交易详情", "Maturity Date": "满期日", "Maturity Reminder Date Compare to Policy Expiry Date": "满期通知日（与保单满期日比较）", "MaximumPaymenttime": "", "Medical Examination": "体检", "Medical examination is not allowed to edit after issue, confirm to issue": "", "Medical examination is not allowed to edit after issue, confirm to issue?": "", "Medical Examination Item": "", "Medical Examination Item ": "", "Medical Examination Plan": "", "Medical Expense Invoice": "", "Medical Plan": "", "Medical Plan Code": "", "Medical Plan Name": "", "Medical Plan Value": "", "Medical Requirement Status": "医疗需求状态", "Method of adding account": "", "Min Premium": "Min Premium", "Min-Premium Type / Min-Premium": "Min-Premium Type / Min-Premium", "Min-Premium Type / Min-Premium should be completed or empty": "Min-Premium Type / Min-Premium should be completed or empty", "Minimum Investment Period": "", "Minimum Investment Period Type": "MIP期限类型", "Minimum Investment Period Value": "MIP期限值", "Minimum Length": "", "Minimum Protection Value": "", "MIP End Date: {{endDate}}": "", "MIP Start Date: {{stateDate}}": "", "Mobile": "手机号", "Mobile Phone": "", "Model Portfolio": "投资组合", "Model Portfolio Code": "投资组合代码", "Model Portfolio Details": "", "Model Portfolio Name": "投资组合名称", "Modifying the selection of sub coverage will clear the configured limit and deductible information. Please confirm.": "修改子险种的选择会清空已经配置的限额和免赔额信息，请确认。", "MONTH": "", "Month(s)": "", "Monthly": "Monthly", "More Action": "", "More Info": "", "Motor NCD": "", "MOTOR_FLEET_POLICY_PREMIUM": "", "MOTOR_FLEET_VEHICLE_UPLOAD": "", "MPV": "", "Msg_back_to_policy_info": "返回保单信息页面", "Msg_claim_claim_applied_by": "", "Msg_claim_claim_evaluated_by": "", "Msg_claim_claim_evaluation_date": "", "Msg_claim_claimant_id_no": "", "Msg_claim_claimant_id_type": "", "Msg_claim_claimant_name": "", "Msg_claim_insured_email": "", "Msg_claim_insured_id_no": "", "Msg_claim_Insured_ID_Type": "", "Msg_claim_insured_mobile_number": "", "Msg_claim_insured_name": "", "Msg_claim_last_document_received_date": "", "Msg_claim_last_update_date": "", "Msg_claim_newly_received": "", "Msg_claim_over_30day": "", "Msg_claim_Payment_Method": "", "Msg_claim_pend_reason": "", "Msg_claim_pending_case_status": "", "Msg_claim_product_name": "", "Msg_claim_query_Claim_Query": "理赔查询", "Msg_claim_registered_by": "", "Msg_claim_registration_date": "", "Msg_claim_report_date": "", "Msg_common_query_POS_Capture_Date": "保全提交日期", "Msg_common_query_POS_Captured_By": "保全提交者", "Msg_common_query_POS_Item": "保全项", "Msg_common_query_POS_No": "保全号", "Msg_common_query_POS_Registered_By": "保全注册者", "Msg_common_query_POS_Registration_Date": "保全注册日期", "Msg_common_query_POS_Status": "保全状态", "Msg_common_query_Sort_by_POS_No": "按保全号排序", "Msg_common_relationship_name": "", "Msg_customer_service_item": "保全项目", "Msg_day": "天", "Msg_Days": "", "Msg_detail_Contract_Information": "", "Msg_error_passwordToE": "密码发送失败", "Msg_Market_Master_Policy_No": "", "Msg_Month": "", "Msg_Months": "", "Msg_moreinfo": "更多信息", "Msg_paymentperiod_single": "趸缴", "Msg_paymentperiod_wholelife": "终身缴", "Msg_paymentperiod_years_old": "岁", "Msg_please_input_right_format": "请输入正确的格式", "Msg_Pos_basic_info": "", "Msg_Pos_change_reason": "", "Msg_Pos_Other_Change_Reason": "", "Msg_pos_query_posQuery": "保全查询", "Msg_query_Acount_Info": "", "Msg_query_Actual_Amount": "", "Msg_query_Additional_Excess": "", "Msg_query_answer": "", "Msg_query_Arrival_Place": "", "Msg_query_Arrival_Place_ID": "目的地ID", "Msg_query_Arrival_Place_Name": "目的地名称", "Msg_query_Ascending": "", "Msg_query_Ascending_Order": "升序排列", "Msg_query_Auction_Item": "拍卖物", "Msg_query_Body_Type": "", "Msg_query_brand_premium_partner": "", "Msg_query_Claim_Documentations": "理赔相关文档", "Msg_query_Claim_Number": "", "Msg_query_Claim_Workflow": "", "Msg_query_collection": "", "Msg_query_Contract_Effective_Date": "合同开始日期", "Msg_query_Contract_No": "", "Msg_query_Contract_Termination_Date": "合同结束日期", "Msg_query_Contract_Type": "", "Msg_query_Contract_Value": "合同金额", "Msg_query_Create_Time": "创建时间", "Msg_query_data_source": "", "Msg_query_Delay": "延误", "Msg_query_Delivery_Information": "", "Msg_query_Departure_Place": "", "Msg_query_Departure_Place_ID": "出发地ID", "Msg_query_Departure_Place_Name": "出发地名称", "Msg_query_Descending": "", "Msg_query_Descending_Order": "降序排列", "Msg_query_Documentation_Name": "文档名字", "Msg_query_Download_Send_Pas": "", "Msg_query_Download_successful": "下载成功", "Msg_query_downLoadError": "下载失败", "Msg_query_Engine_Capacity": "", "Msg_query_FIN": "FIN", "Msg_query_Generated_Date": "", "Msg_query_generatedDate": "", "Msg_query_Gts": "消费税", "Msg_query_Home_Appliance_Information": "", "Msg_query_Image_info": "", "Msg_query_Insured_Object_Basic_Information_Change": "标的基本信息修改", "Msg_query_Insured_Object_Information": "标的信息", "Msg_query_Insured_Type": "", "Msg_query_insuredStatusText": "被保人为黑名单客户", "Msg_query_Is_Main": "", "Msg_query_Liability_Category": "", "Msg_query_Liability_Name": "", "Msg_query_Make": "", "Msg_query_MCC_code": "", "Msg_query_MCC_name": "", "Msg_query_Meal_Type": "外卖类型", "Msg_query_merchant_name": "", "Msg_query_Model": "", "Msg_query_more10000": "", "Msg_query_no": "", "Msg_query_Number_of_Item_type": "项目类型号码", "Msg_query_Number_of_Item_Type": "", "Msg_query_Object_Category": "标的分类", "Msg_query_occupation_class": "", "Msg_query_Order_ID": "订单ID", "Msg_query_Order_Price": "订单价格", "Msg_query_Order_Status": "订单状态", "Msg_query_Order_Time": "订单时间", "Msg_query_Original_Start_Date": "原开始日期", "Msg_query_Payable": "", "Msg_query_Payment_Info": "", "Msg_query_Policy_Documentations": "保单相关文档", "Msg_query_Policy_Issue_Date": "", "Msg_query_POS_Documentations": "POS相关文档", "Msg_query_POS_Workflow": "", "Msg_query_productName_version": "商品名称 _版本", "Msg_query_Rate_Type": "税率类别", "Msg_query_Receipts": "", "Msg_query_Receivable": "", "Msg_query_Reconciliation_Status": "", "Msg_query_records": "记录", "Msg_query_refund": "", "Msg_query_Refund": "", "Msg_query_Registration_Date": "", "Msg_query_Scheduled_Arrival_Time": "计划到达时间", "Msg_query_Scheduled_Departure_Time": "计划出发时间", "Msg_query_Seating_Capity": "", "Msg_query_sendEmail": "密码已发送到你的邮箱", "Msg_query_service_type": "", "Msg_query_snack_modifier": "", "Msg_query_Sort_by_Claim_No": "按赔案号排序", "Msg_query_Sort_by_Relation_Policy_No": "", "Msg_query_Sort_by_Relation_Pos_No": "", "Msg_query_Sort_by_report_date": "", "Msg_query_Source": "", "Msg_query_Status": "", "Msg_query_Sum_Assured": "", "Msg_query_Tax_Detail": "税详情", "Msg_query_Tax_Rate_Value": "税率/值", "Msg_query_Tax_Type": "税类别", "Msg_query_The_policy_holder_is_in_blacklist": "", "Msg_query_titleDes": "文档已加密。密码将会发送到你的邮箱。", "Msg_query_total": "总计", "Msg_query_transactionNo": "", "Msg_query_transactionType": "", "Msg_query_Transport_Information": "", "Msg_query_Transportation_Number": "交通号", "Msg_query_trigger_category": "", "Msg_query_type": "", "Msg_query_unique_ID": "", "Msg_query_Use_of_Vehicle": "", "Msg_query_Vehicl_No": "", "Msg_query_Vehicle": "", "Msg_query_Vehicle_Age": "", "Msg_query_Vehicle_Identification_No": "", "Msg_query_View_all": "查看所有", "Msg_query_Year_of_Make": "", "Msg_reconciliation_channel": "", "Msg_Relation_Pos_Number": "", "Msg_Total": "", "Msg_transaction_type": "交易类型", "Msg_version": "版本定义", "Msg_Virtural_Insured": "虚拟被保人", "Msg_week": "", "Msg_weeks": "", "Msg_Years": "", "MULTI_BENEFICIARY": "", "MULTI_INSURED": "被保人", "MULTI_PAYER": "", "MULTIPLE_OBJECT_INFO": "", "My Task": "", "N Year Risk Amount": "", "name": "姓名", "Name": "姓名", "nameCombine": "", "Named Insured": "", "Nationality": "国籍", "NCD": "", "NCD %": "", "NCD Amount": "", "Need Advanced Payment": "", "Need DCA arrangement?": "是否需要DCA计划？", "Need Vehicle Examination": "", "Net Prem": "", "Net Premium": "", "New": "新生成", "New Business": "", "New Business & Renewal Configuration": "新单与续保", "New Business Configuration": "", "New Business Info": "", "New Document": "New Document", "New Master Agreement": "", "New SA": "新保额", "New Vehicle": "", "NEW_BUSINESS_INFO": "", "Next": "", "Next Due Date": "", "Next Due Date: {{date}}": "", "Next Rebalancing Due Date": "", "NextPremiumIncludingTax": "", "NextPremiumWithoutTax": "", "NLG Benefit:": "", "no": "否", "NO": "", "No attachment has been uploaded under the selected product.": "选中的产品下并没有上传附件。", "No case number found": "", "No Claim Discount": "", "No discount configured for current period": "本期未配置折扣", "No record": "--", "No Results": "", "No valid application elements: INSURED": "", "No valid application elements: PAYER": "", "No valid application elements: POLICY_HOLDER": "", "No Valid Data": "", "No valid master policy is available for this normal policy renewal, please check.": "", "no_data": "暂无数据", "No_Data": "", "No.": "编号", "No. {{appNo}}": "", "No. {{policyNo}}": "", "No. of Accident Free Years": "", "noData": "暂无数据", "Nominee": "", "NOMINEE": "", "Non Standard Tariff": "", "Non-Location": "", "Non-location Based Object": "", "None": "None", "Normal Policy List": "", "NORMAL_INSURED": "", "NORMAL_PAYMENT": "", "NORMAL_POLICY_HOLDER": "", "normalPolicyList": "", "Not Within Premium Holiday": "", "Note": "", "Notice Reminder": "", "Notification History": "通知历史记录", "Notification Type": "", "Now You Can Create the Team": "", "Number of Accident": "", "Number of Active Policies": "有效保单数量", "Number of Employees": "", "Number of Installment": "", "Number of Luggage": "", "Number of Objects": "团单车辆数", "Number of Order": "", "Number of Pending Policies": "待处理保单数量", "Number of Records": "记录数量", "Number of Renewal Time": "", "Number of Seat": "", "Number of task assign when user ask": "", "Number of Vehicle Owned": "", "Object": "", "Object ID": "", "OBJECT LIST": "", "OBJECT_INFO": "", "Occupation": "职业", "Off-Peak Car": "", "OIB": "", "Only Failed": "", "Only insured": "仅被保险人", "Only policyholder": "仅投保人", "Only view image files?": "", "Open Menu": "", "Open the link in a new window": "", "Operation": "", "Operation History": "操作历史", "operation time": "操作时间", "Operation_Approve": "", "Operation_Capture": "", "Operation_Current": "", "Operation_Decide": "", "Operation_Operate": "", "Operation_Register": "", "OPERATIONS_COMMENTS": "", "Operator": "", "OPT_IN Check Text": "加入计划节点的校验时，没有人工操作流程及页面。请确保在进行规则关联时，规则的结论不得包含“转人工”结论。仅能包含“通过”或“拒绝”结论。", "Opt-In Check": "Opt-In Check", "Opt-In Rules": "Opt-In Rules", "Opt-In-Rules": "", "Optional Covers": "", "Optional Text": "", "Order Currency": "", "Order Date": "", "Order ID": "Order ID", "Order Info": "", "Order No.": "", "Order Number": "", "Order Price": "", "Order the Teams while the assignment rule are same": "", "Order Type": "", "Order Value": "Order Value", "Ordinary condition": "", "Organization": "组织", "Organization ID No.": "", "Organization ID Type": "", "Organization ID Type/No.": "", "Organization Name": "", "Origin SA": "", "Original Master Agreement No.": "", "Original Pol No": "", "Original SA": "", "Original Start Date": "", "Original Sum Assured": "原始保险金额", "Original_policy_no": "", "Other": "", "Other Information": "", "Other Policy Info": "保单其他信息", "Other Product": "", "Other Properties": "", "OTHER_PARTY_ROLES": "", "OTHER_PARTY_ROLES_CN": "", "Others": "Others", "Over All": "", "Overriding Commission": "抽佣", "Package": "", "Package Code": "", "Package Level": "", "Package Name": "", "PackageName": "", "Packages": "", "Packing Information": "", "Pad with zeros": "", "Parcel": "<PERSON><PERSON><PERSON>", "Parcel Number": "Pa<PERSON>el <PERSON>", "Parcel Tier": "<PERSON><PERSON><PERSON>", "Parcel Value": "Parcel Value", "Parcel Volume": "Parcel Volume", "Parcel Weight": "<PERSON><PERSON><PERSON>", "Part Questionnaire Purpose": "", "Partner": "", "partnerCode": "", "partnerType": "", "Pass": "Pass", "Passed": "", "Pay Account": "", "pay_frequency": "保费支付频率", "PAYEE": "", "Payee Info": "", "Payee Name": "", "Payee Type": "", "Payer": "", "payer of Liability": "", "Payer Role": "", "Payer Type": "", "Payer/Payee Type": "", "Payment Amount": "", "Payment Amount By Assignee": "", "Payment By Assignee": "", "Payment Currency By Assignee": "", "Payment Date": "", "Payment Frequency": "", "Payment History": "", "Payment information could not be copied due to differences in some configurations.": "由于部分配置不同，缴费信息未成功复制。", "Payment information has been successfully copied automatically.": "缴费信息已成功自动复制。", "Payment Mehtod": "", "Payment Method": "", "Payment Method / Account Type": "", "Payment Method/Account Type": "", "Payment Method/Account Type Details": "", "Payment Option": "", "Payment Period": "", "Payment Periods": "", "Payment Plan": "", "Payment Status": "", "PAYMENT_ACCOUNT_INFO": "", "PAYMENT_INFO": "", "PAYMENT_INFO_VIEW": "", "PAYMENT_PLAN": "", "PAYMENT_PLAN_PAYER": "", "PAYMENT_PLAN_PREMIUM_PAYMENT": "", "PayMethod": "", "PayMethod / Account Type": "{{-payMethod}}/{{-accountType}}", "PAYOR": "", "payText": "支付", "Pending Proposal Check": "", "Pending Transaction Amount（Fund Currency）": "待成交交易金额（基金币种）", "Pending Transaction Unit": "待成交交易份额", "Period": "", "Period Type": "", "Period Value": "", "PeriodAge": "", "PeriodYears": "{{value}} 年", "Permanent Address": "", "Person List": "", "Personal Record": "", "Pet": "", "Pet ID": "", "Pet Info": "", "Pet Type": "", "Pet Varieties": "", "Phone": "", "Phone No": "", "Phone Number": "", "Phone Type": "", "Pin": "", "Place of Incorporation": "", "Place of Interest": "", "Plan": "", "Plan Code": "", "Plan Goods Version": "", "Plan Level": "", "Plan Name": "", "Plan Premium Model": "", "planName: {{goodsPlanName}}": "", "Planned Premium": "", "Planned Premium Amount": "", "Planned Premium Collected": "", "Planned Premium Layer Details": "", "Planned Premium: {{amount}}": "", "Plate No.": "", "Plate Type": "", "Please": "", "Please add at least one piece of data": "", "Please add at least one product": "", "Please check schema correction. Some schemas have category without name.": "", "Please click 'Apply Master Plan' button to match coverage plan information.": "", "Please confirm the following information is correct . Once confirmed, it cannot be modified.": "", "Please confirm the following information is correct.Once confirmed,it cannot be modified": "", "Please confirm whether the entered Master Agreement information has been saved before uploading the file.": "在文件上传前请确认是否保存已输入的协议单信息", "Please Download Template first.": "请先<2>下载模板</2>", "Please enter {{fieldName}} before submitting.": "", "Please enter {{productName}} product decision before submitting the task.": "", "Please enter a number greater than 0": "", "Please enter a number greater than 0 but less than 100": "", "Please enter a number greater than 0 but less than 200": "", "Please enter the decision!": "", "Please fill in the Setting Table": "", "Please generate the offer first.": "请先生成offer", "Please generate the premium first.": "请先计算保费", "please input": "", "Please input": "", "Please input a number": "Please input a number", "Please input number": "", "Please input positive integer": "请输入正整数", "Please Input Range": "", "Please input valid party ID!": "", "Please input your Product Type!": "", "Please input your Team Name!": "", "Please note when choosing DCA arrangement,  the amount for each period will invest in the fund based on fund appointment rate of planned premium.": "", "Please note when choosing the investment strategy, the fund appointment for premium & portfolio rebalancing will follow the defination on the strategy.": "请注意，若选择了投资策略，保费的基金分配比例和投资再平衡的基金分配比例都会使用投资策略中的比例。", "Please notice that the entered coverage along with its related limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage and limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage information will be cleared. Do you want to continue?": "", "Please return to the task pool, search for the proposal number {{proposalNo}} and try again later.": "", "Please save or delete current attachmentType": "", "Please save the policy information before adding comments": "", "Please search or input": "", "Please search transaction first": "", "please select": "", "Please select a higher level underwriter to escalate the case.": "", "Please select a higher level underwriter to referral the case.": "", "Please select an underwriter to escalate the case.": "", "Please select an underwriter to reassign the case.": "", "Please select an underwriter to referral the case.": "", "Please Select At Least One Condition!": "", "Please Select At Least One Liability": "", "Please select at least one record": "", "Please select decision": "", "Please select effective date": "", "Please select Exclusion": "", "Please select factor first": "", "Please select first": "", "Please select language": "", "Please select Liability": "", "please select one": "", "Please select one Goods before submitting!": "请选择商品后再进行提交！", "Please select one Sub-category before submitting!": "", "Please select policy effective date": "", "Please select policy expiry date": "", "Please select policy type submitting!": "", "Please select Product": "", "Please select the Goods Name first.": "", "Please select the insurance applications that need manual underwriting": "", "Please select whole days": "", "Please select your Bind Task Assignment Rule!": "", "Please select your Team Members!": "", "Please select your Team Type!": "", "Please set policyholder": "请设定投保人", "Please Upload": "", "Please Upload Document": "", "Please Upload File": "", "Please Upload invoice": "", "Please upload one file": "", "Please_enter_at_least3characters": "", "please_select": "", "POLICY": "", "Policy Assignment": "", "Policy Basic Infomation": "", "Policy Change": "", "Policy Charge": "", "Policy Configuration": "保单配置", "Policy Currency": "", "Policy Delivery Method": "保单递送方式: {{method}}", "Policy E-Document Type": "", "Policy Effective": "", "Policy Effective Check": "", "Policy Effective Date": "保单生效日期", "Policy Effective Date ": "", "Policy Effective Without Collection (NB)": "", "Policy Expiry Date": "", "Policy History": "", "Policy Holder": "", "Policy Info": "保单信息", "Policy Information": "", "Policy Issuance Compliance Check （After Premium Payment)": "Policy Issuance Compliance Check （After Premium Payment)", "Policy Issuance Rules": "Policy Issuance Rules", "Policy Issuance UW Check (After Premium Payment)": "", "Policy Issue Date": "", "Policy List": "", "Policy Loan": "", "Policy Loan Detail": "保单贷款明细", "Policy Maturity Termination Method": "保单满期终止方式", "Policy No": "保单号", "Policy No.": "", "Policy No. {{policyNo}}": "Policy No. {{policyNo}}", "Policy Number Generation Rule": "", "Policy Period": "", "Policy Regeneration": "保单再生成", "Policy Serial Number": "保单序列号", "Policy Sign Off Date": "", "Policy Sign Off Rule": "", "Policy Status": "保单状态", "Policy Tag": "保单标签", "Policy Tag History": "保单标签历史", "Policy Tag List": "", "Policy Tagging": "保单标签", "Policy Toolip": "", "Policy Type": "", "Policy UW Decision": "保单核保决定", "Policy UW Decision Details": "", "Policy Year": "", "Policy years": "", "POLICY_CHANGE": "", "POLICY_CHANGE_DETAILS": "", "POLICY_CHANGE_OVERVIEW": "", "POLICY_DETAIL_INFO": "", "POLICY_DETAILS": "", "POLICY_HISTORY": "保单历史", "POLICY_HOLDER": "", "POLICY_HOLDER_CN": "", "POLICY_OVERVIEW": "", "Policy_Query": "", "PolicyEffectiveDate": "", "policyEffectiveRuleTooltip": "系统将根据绑定规则的“通用结论”进行投保单状态流转。如果结论为拒绝，系统将阻止出单。 \n 投保单进入生效节点时，没有人工操作流程及页面。请确保在进行规则关联时，规则的结论不得包含“转人工”结论。仅能包含“通过”或“拒绝”结论。", "Policyhoder Info": "", "PolicyHolder": "", "Policyholder and insured is the same. The change is applied to": "投被保险人为同一人，信息修改将适用于", "Policyholder Certificate Type": "", "Policyholder Details": "", "Policyholder Email": "", "Policyholder ID No": "投保人证件号", "Policyholder Id No.": "", "Policyholder ID No.": "", "Policyholder ID Type": "", "Policyholder Info": "", "Policyholder Mobile Number": "投保人电话号码", "Policyholder Name": "投保人姓名", "PolicyHolder Name": "", "Policyholder Name2": "投保者姓名（假名）", "Policyholder Type": "投保人类型", "Policyholder_Email": "", "Policyholder_ID_No": "", "Policyholder_ID_Type": "", "Policyholder_Mobile_Number": "", "Policyholder_Name": "", "PolicyholderIDNo": "", "PolicyStatus": "", "Portfolio Rebalancing": "投资组合再平衡", "Portfolio Rebalancing Detail": "", "POS Application Date": "", "POS Effective Date": "", "POS Item": "", "POS No.": "", "POS_Archives_Room": "", "POS_DETAILS": "", "POS_Effective_Date": "", "posDecisionEnum.APPROVE": "通过", "posDecisionEnum.BACK_DATA_ENTRY": "返回数据录入", "posDecisionEnum.REJECT": "驳回", "posStatusEnum.APPROVAL_IN_PROGRESS": "审批中", "posStatusEnum.CANCELLED": "取消", "posStatusEnum.DATA_ENTRY_IN_PROGRESS": "资料录入中", "posStatusEnum.EFFECTIVE": "生效", "posStatusEnum.INVALID": "失效", "posStatusEnum.REJECTED": "拒绝", "posStatusEnum.WAITING_FOR_APPROVAL": "等待批准", "posStatusEnum.WAITING_FOR_COLLECTION": "等待收费", "posStatusEnum.WAITING_FOR_DATA_ENTRY": "等待数据输入", "posStatusEnum.WAITING_FOR_EFFECTIVE": "等待生效", "posStatusEnum.WITHDRAW": "退出", "posStatusStepEnum.APPROVAL_PROCESSING": "审批", "posStatusStepEnum.APPROVAL_WAITING": "等待批准", "posStatusStepEnum.CANCELLED": "取消", "posStatusStepEnum.COLLECTION_PAYMENT": "收费", "posStatusStepEnum.DATA_ENTRY_CALCULATION": "计算", "posStatusStepEnum.DATA_ENTRY_CONFIRMATION": "确认", "posStatusStepEnum.DATA_ENTRY_CS_ITEM": "数据输入", "posStatusStepEnum.DATA_ENTRY_PAYMENT_COLLECTION": "支付/收费", "posStatusStepEnum.EFFECTIVE": "生效", "posStatusStepEnum.INVALID": "失效", "posStatusStepEnum.REGISTER": "注册", "posStatusStepEnum.REJECTED": "拒绝", "posStatusStepEnum.SELECT_CS_ITEM": "保全项选择", "posStatusStepEnum.WITHDRAW": "退出", "Post Code": "", "Postal Name": "", "Power Type": "", "PRECIOUS_ITEM": "", "Premium": "", "PREMIUM": "", "Premium (Before Campaign)": "保费（营销折扣前）", "Premium (Before Tax & Service Fee)": "保费（税费前）", "Premium (Total)": "保费（总计）", "Premium & SA Calculation Method": "", "Premium Aggregation Detail": "", "Premium allocation": "保费投资分配比率", "Premium Calculation Method": "", "Premium Calulation Method": "", "Premium Collected & Allocation": "", "Premium Collection": "", "Premium Collection Time": "", "Premium Detail Download": "", "Premium Details": "", "Premium Discount": "保费折扣", "Premium Discount on Net Premium": "", "Premium Discount On Tax": "", "Premium Due Date": "", "Premium DueDate": "", "Premium Frequency": "", "Premium Funder": "保费出资者", "Premium Handling Method": "", "Premium Info": "", "Premium Info Detail": "", "Premium Notice Date": "", "Premium Notice Date Compare with Due Date": "", "Premium or SA Info": "", "Premium Payer": "", "Premium Per Unit": "", "Premium Period": "保费期间", "Premium Period Type": "", "Premium Period Value": "缴费期间", "Premium Status": "", "Premium Type": "保费类型", "PREMIUM_AGGREGATION": "", "PREMIUM_AMOUNT": "", "Premium_Discount": "", "Premium_discount_type": "", "Premium_Due_Date": "", "PREMIUM_FUNDER": "", "Premium_including_tax_discount": "", "premium_pay_account": "保费支付账户", "PREMIUM_PAYER": "", "PREMIUM_PAYER_CN": "", "Premium_without_tax_discount": "", "PremiumDuration": "", "PremiumEndDate": "", "Press enter to record enums, duplicated keys will be ignored.": "", "Preview Offer": "", "Preview Strategy Details": "", "Previous Policy No.": "", "Price Date": "基金价格日期", "Price Date For Adjustment": "", "Principal Balance": "本金余额", "Principal Balance Change": "", "Principal Steam Details": "", "Print History": "", "Print Name": "", "Print Reason": "", "Print Time": "", "Prior condition": "", "Priority": "", "Private Task - Active": "", "Private Task - Inactive": "", "process": "处理", "Process Configuration": "", "Process Failed": "", "Process Successfully": "处理成功", "Process Underwriter Level": "当前核保人等级", "Process UWer Level": "", "Processing": "", "Product": "产品", "Product Category": "产品分类", "Product Code": "产品号", "Product Code&Name": "产品代码&名称", "Product Decision": "", "Product Details": "", "Product Discount": "", "Product in Goods": "", "Product Info": "", "Product Level": "", "Product List": "", "Product Name": "", "Product Name {{name}}": "", "Product name: {{productName}}": "", "Product Premium: {{sa}}: ": "", "Product SA: {{sa}}: ": "", "Product Status": "", "Product Summary": "", "Product Tax": "产品税", "Product Type": "", "Product_Amount": "", "PRODUCT_BUNDLE_BASIC_INFO": "", "Product_Libility_Info": "", "Product_nostyle": "产品", "Product: {{productCode}}_{{productName}}": "", "productCode_productName": "", "Promo Code": "", "Promotion Code": "", "Promotion Discount": "", "Promotion Discount On Levy": "", "Promotion Type": "", "PROPERTY": "", "Property Coverage": "", "Property Product": "", "Proposal": "", "Proposal Compliance Check （Before Premium Payment)": "Proposal Compliance Check （Before Premium Payment)", "Proposal Configuration": "投保单设定", "Proposal Confirmation Date": "投保单确认日", "Proposal Date": "", "Proposal Effective Date": "案件生效日期", "Proposal Flow Setting": "", "Proposal Info": "", "Proposal No": "", "Proposal No: ": "", "Proposal No: {{applicationNo}}": "", "Proposal No: {{proposalNo}}": "", "Proposal No.": "投保单号", "Proposal No. {{proposalNo}}": "Proposal No. {{proposalNo}}", "Proposal reminder days can not duplicate. Please check.": "投保单提醒天数存在重复的配置，请检查", "Proposal Reminder Rule": "投保单提醒规则", "Proposal Request Date": "", "Proposal Rule": "", "Proposal Rules": "", "Proposal Status": "投保单状态", "Proposal Task Pool": "提案任务池", "Proposal Task Pool Re-assign": "", "Proposal Withdraw Rule": "", "PROPOSAL_DETAILS": "", "PROPOSAL_INFO": "", "Proposal/Policy": "", "Proposal/Policy No.": "", "Proposal/Policy Status": "", "Proposal/POS No": "投保单号/保全号", "ProposalDate": "", "proposalWithdrawTooltip": "", "Provide Vehicle Photo Later X Days": "", "Public Liability": "", "Public Task": "", "Public Tender": "公共采购", "Public Tender No.": "公共采购号", "Purchase Date": "", "Purchase Price": "", "Purpose": "", "QUARTER": "", "Quarterly": "Quarterly", "Query Error": "", "Query Escalate Users Failed": "", "Query Reassign Users Failed": "", "Query Referral Users Failed": "", "QUERY_ATTACHMENTS": "", "QUERY_POLICY_HISTORY": "", "QUERY_RENEWAL_HISTORY": "", "query-Agent": "代理人", "query-Agent Name": "", "query-Allocation Amount": "分配金额", "query-Business No": "", "query-Consentee": "监护人", "query-Deductible Amount": "免赔额", "query-Effective Date": "", "query-Expiry Date": "", "query-Extra Loading": "", "query-Fund Appointment": "基金分配比例", "query-Fund Currency": "", "query-Fund Name": "", "query-Gender": "", "query-Goods Name": "商品名称", "query-Goods Version": "商品版本", "query-GoodsName": "", "query-GoodsVersion": "", "query-History Type": "", "query-ID Type": "身份证明类型", "query-Identifier Info": "", "query-Individual": "", "query-Insured Email": "被保者邮箱", "query-Insured Name2": "被保者姓名（假名）", "query-Investment Strategy": "投资策略", "query-Is Renewal Policy": "", "query-Main Benefit": "", "query-Master Policy No.": "主保单号码", "query-Mobile Phone": "", "query-No.": "", "query-Nominee": "继任投保人", "query-Organization": "", "query-Other Policy Info": "保单其他信息", "query-Others": "", "query-Payer": "付款人", "query-Payment Frequency": "", "query-Payment Method": "支付方案", "query-Payment Period": "", "query-PeriodAge": "{{value}} 岁", "query-Policy Currency": "", "query-Policy Effective Date": "", "query-Policy Issue Date": "", "query-Policy Loan": "保单贷款", "query-Policy Regeneration": "", "query-Policyholder": "", "query-PolicyHolder": "投保人", "query-Policyholder Email": "投保人电子邮箱", "query-Policyholder ID No.": "投保人身份证明号", "query-Policyholder Info": "投保人信息", "query-Policyholder Name": "投保人姓名", "query-PolicyNo": "", "query-Premium Due Date": "保单到期日", "query-Product": "产品", "query-Proposal No.": "", "query-Registration Date": "", "query-Relationship With Insured": "", "query-Relationship With Policyholder": "Relationship With Policyholder", "query-Renewal": "续保", "query-Sales Channel": "贩卖渠道", "query-Select All": "", "query-Service Fee": "服务费", "query-Settlement Date": "", "query-Social Account": "社交账户", "query-Status": "", "query-Transaction Type": "交易类型", "query-Trustee": "受托人", "Questionnaire": "", "QUESTIONNAIRE": "", "Questionnaire Info": "", "Questionnaire Name": "", "QUESTIONNAIRE_INFO": "", "Quick Menu": "", "Quotation": "", "Quotation Configuration": "询价单设定", "Quotation Info": "", "Quotation Information Pre-check": "报价信息预校验", "Quotation No": "", "Quotation No.": "账单号", "Quotation No. {{proposalNo}}": "", "Quotation Period": "账单期间", "Quotation Query": "", "Quotation Stage": "", "Quotation Status": "", "Quotation Task Pool": "", "Quote Bound": "", "Random Check": "", "Random Check Configuration": "", "Random Ratio": "", "Rate": "", "Rate Type": "", "Rate-Classes BI": "", "Rate-Classes OD": "", "Rate-Classes PD": "", "Rate-Classes PIC": "", "Rate/Amount": "", "Re-accumulate and affect target": "", "Re-assign": "", "Re-underwriting Reason": "", "Re-underwriting Type": "", "Re-Upload": "重新上传", "Re-Upload Successfully": "", "Read More": "", "Reason": "原因", "Reason Comments": "", "Reassign": "", "Reassign | {{selectLength}} Option(s)": "重新分配 | 已选 {{selectLength}} 项", "reassign control {{proposalNos}}": "当前投保单与其他投保单（{{proposalNos}}）存在关联关系，若改派当前投保单的录单任务，其他关联投保单的录单任务也将一并改派，请确认。", "reassign control batch": "当前批量改派的任务中包含产品组合或联合出单场景下的投保单。若改派这些投保单的录单任务，其他关联投保单的录单任务也将一并改派，请确认。", "Reassign or Referral": "", "Reassign Successfully!": "", "Rebalance Frequency": "", "Rebalancing Date": "", "Rebalancing Frequency": "", "Rebalancing History": "", "Receivepromotionalemailsornot": "", "Recipient": "", "Reconciliation Status": "", "Records / Number of total records": "", "Recount": "", "Recover": "", "Recurring Single top up": "周期性单次追加投资", "Recurring Single Top Up": "", "Recurring Single Top Up Frequency": "周期性单次追加投资缴费频率", "Recurring Single Top Up Period": "", "Reduce Coverage": "降低保障", "Referral": "", "Referral or Reassign": "", "Referral Reason": "", "Referral Response": "", "Referral Successfully!": "", "Referral Task": "", "Refresh Confirm": "", "Regenerate": "重新生成", "Regenerate Error": "重新生成错误", "Regenerate Reason": "重新生成的原因", "Regenerate Successfully": "重新生成成功", "Register Date": "", "registration area": "", "registration category": "", "Registration Date": "", "registration hiragana": "", "Registration No.": "", "registration serial no": "", "Regular Bonus Plan": "", "Regular Premium": "", "Regular top up": "", "Regular Top Up": "定期追加投资（每年）", "Regular Top Up Collected": "", "Regular Top Up: {{amount}}": "", "Regular Top-up": "", "Regular Withdrawal": "定期领取", "Reinstate": "恢复", "Reinsurance Decision": "", "REINSURANCE_INFO": "", "reject": "拒保", "Reject": "Reject", "Rejected": "", "Related Policy": "", "Related Policy Overview": "", "Related to insured": "", "Related to insured object": "", "Related to policy": "", "Related to policyholder": "", "RELATED_POLICY": "", "RelatedPartiesInformation": "", "Relation with primary insured": "", "Relation_Policy_No": "", "Relationship No.": "Relationship No.", "Relationship Type / Relatinship No.": "Relationship Type / Relationship No.", "Relationship Type / Relatinship No. should be completed or empty": "Relationship Type / Relationship No. should be completed or empty", "Relationship With Insured": "Relationship With Insured", "Relationship with Policyholder": "", "Relationship With Policyholder": "", "Relationship With Policyholder: {{holderRelationRemark}}": "", "Relative": "", "Release": "", "Release Failed": "", "Release Success": "", "Release Task": "", "Release Time": "", "Remaining Amount": "", "RemainingPaymentTime": "", "Remark": "", "remarks": "", "Remarks": "", "REMARKS": "", "Reminder": "", "Reminder Frequency(days)": "提醒频率(天数)", "Remove": "", "Remove a Reminder": "删除提醒天数", "Remove Date": "", "Render Error": "", "Renew": "", "Renewal": "续保", "Renewal Extraction Date": "续保抽当日", "Renewal Failure Reason": "续保失败原因", "Renewal History": "续保历史", "Renewal Policy No.": "续保保单号", "Renewal Policy No. Generation Rule": "", "Renewal Quotation Expiry Date": "续保失效日", "Renewal Quotation Info": "续保账单信息", "Renewal Reminder Date": "续保提醒日", "Renewal Reminder Date Compare to Policy Expiry Date": "", "Renewal Reminder Rule": "", "Renewal Status": "续保状态", "Renewal UW Info": "续保核保信息", "RENEWAL_INFO": "", "RENTER": "承租人", "Reopen Comment": "Reopen Comment", "Repayment Amount": "偿还金额", "represent_no": "团单号", "Reprint Successfully": "", "Requirement Category": "", "Requirement Code": "需求编码", "Requirement Descriptions": "需求描述", "Requote": "", "Reset": "Reset", "Residential City": "", "Residential Status": "", "Retirement Age (Insured)": "退休年龄（被保人）", "Retirement Option": "退休计划", "Retirement Option Start": "", "Retirement Option Start Date": "退休计划开始日期", "Retirement option start date must be future date.": "退休计划开始日期必须是未来日期。", "retured": "返回", "Return": "", "Return Current Product to Data Entry": "", "Return Entire Submission to Data Entry": "", "Return Reason": "", "Return to Data Entry": "", "Reupload": "", "Reversed": "已回退", "Reversionary Bonus": "保额红利", "Reversionary Bonus Allocation Details": "保额红利分配详情", "RF Weight": "", "Riders": "附加险", "Risk Aggregation": "", "Risk Aggregation Detail": "", "Risk Category": "风险大类", "Risk Classification": "风险等级", "Risk Sub-category": "风险小类", "Risk Underwriting Decision": "", "RISK_AGGREGATION": "", "RiskStartDate": "", "Role": "", "Role Name": "", "Roles": "", "RR Weight": "", "Rule Code": "", "Rule Code/Rule Set": "", "Rule Condition": "", "Rule Configuration": "", "Rule Details": "", "Rule Name": "", "Rule Result": "", "Rule Type": "", "Rule/Rule Set Category": "", "Rule/Rule Set Code": "", "Rule\\Rule Set": "", "Running": "", "SA After Down Sell": "", "SA Multiplier": "", "Sales Agreement Code": "", "Sales Channel": "贩卖渠道", "Sales Channel Code": "", "Sales Channel Name": "", "Sales Channel Type": "", "Sales Time": "", "Sales_Channel": "", "SALES_CHANNEL": "", "SalesChannel": "", "Same as": "", "Same As": "", "Same as Initial Premium Payment": "", "Same As Payer of Liability": "", "Save": "", "Save Failed": "", "Save Successfully": "", "Saving Successfully!": "", "Schedule Period Type": "计划期间种类", "Schedule Period Value": "计划期间值", "Scheduled Arrival Time": "", "Scheduled Departure Time": "", "Scheduled Rate": "", "Scope of Application": "", "Search by Group Policy No.": "", "Search by Master Agreement No.": "按照协议编号搜索", "Search by Name": "", "Search by Operator": "", "Search Insured by Customer Type？": "", "Search Name": "", "Search Policyholder by Customer Type？": "", "searchResult": "", "Secondary Life Insured": "", "SECONDARY_LIFE_INSURED": "", "SecondaryLifeInsured": "", "Segmentation Factor": "", "SEGMENTATION_FACTOR": "", "Select": "选择", "Select a manager for the team": "", "Select a Template": "", "Select all": "", "Select All": "全选", "Select Coverage / Sub Coverage": "", "Select Insured Object": "", "Select Plan Group": "", "Select Questionnaire Language": "", "Select the process flow you want to use": "", "Select Type": "", "selectAtLeastOne": "", "Send": "", "Send Back to Origin UWer": "", "Send Back to Task Pool": "", "Send Back to Task Pool failed": "", "Send Back To Task Pool success": "", "Send back to Task Pool?": "", "Send Back to UW Pool": "", "send back to UW task": "退回核保任务池", "Send Time": "", "Sender": "", "Sending Status": "", "Senior Number": "", "Seperate by Proposal Status": "", "Sequence Length": "", "Sequence Value": "", "Sequence_No": "", "Service Company": "服务公司", "Service Company Code": "", "Service Fee": "", "Service Fee Amount": "服务费金额", "Service Fee Generated Date": "服务费产生日期", "Service Fee Type": "服务费类型", "SERVICE_AGENT": "", "Set Deductible": "", "Set Limits": "", "Set Priority": "", "Set task push strategy for each team, such as Round Robin and task push by workload.": "", "Settled": "", "SettleFlag": "", "Settlement Date": "Settlement Date", "Settlement Frequency": "Settlement Frequency", "Settlement Start Date": "Settlement Start Date", "Sex": "", "Short Rate Method": "", "Should higher than previous policy year": "", "Show_with_Card": "", "Showing {{current}} to {{pageSize}} of {{total}} results": "", "SINGLE": "", "Single Premium": "", "Single Top Up": "", "Single Top Up Amount": "", "Single Top Up Collected": "", "Single Top Up Type": "", "Single Top Up: {{amount}}": "", "Single Top-up": "", "SMS": "", "Social Account": "", "Sorry, failed to upload documents because Master Policy NO. hasn't been filled. Please check.": "", "Sort": "", "Sort by Application Date. (Ascending)": "", "Sort by Application Date. (Descending)": "", "Sort by Create Time": "", "Sort By Creation Date (from oldest to newest)": "", "Sort by Group Policy No. (Ascending)": "", "Sort by Group Policy No. (Descending)": "", "Sort by Operation Time": "", "Sort by Proposal No. (Ascending)": "", "Sort by Proposal No. (Descending)": "", "Sort by Quotation No. (Ascending)": "", "Sort by Quotation No. (Descending)": "", "Sort by Quote Need Date. (Ascending)": "", "Sort by Quote Need Date. (Descending)": "", "Sort by time": "按时间排序", "Sort Times Ascending": "", "Sort Times Descending": "", "Sort_by_Last_Upload_Time": "", "Sort_by_Policy_No": "", "Special Agreement": "", "Special Agreement Code": "", "Special Agreement Description": "", "Special Agreement Type": "", "Special Code": "", "Special Description": "", "SPECIAL_AGREEMENT": "", "SPECIAL_AGREEMENT_WITH_PLAN": "", "Specific Info": "", "SPECIFIC_INFO": "", "Specified Applicable Goods": "", "Stack Code": "", "Stack Description": "", "Stack Liability Name": "责任名称: {{liabilityName}}", "Stack Name": "累加器名字", "Stack Type": "累加器类型", "Stack Unit": "单位", "Stack Value": "值", "Stack Value Type": "", "Stamp Duty": "", "Standard": "", "Standard Premium": "", "Standard Tariff": "开放保单", "Start": "", "Start | End": "", "Start by Creating a Rule": "", "Start Date": "开始日期", "Start Day": "", "Start Time": "", "Status": "状态", "STOP_PAYMENT": "", "Strategy": "", "Strategy Asset Allocation": "", "Strategy Code": "策略编码", "Strategy Detail": "", "Strategy Name": "策略名称", "Strategy Name (auto generate)": "", "Strategy Relatives": "策略相关性", "Street Name": "", "Street No.": "", "Structure": "", "Sub Campaign Category": "", "Sub Coverage": "", "Sub Policy (Main)": "", "Sub Policy (Relative)": "", "Sub Policy Info": "", "Sub Policy No.": "", "Sub Policy No. {{policyNo}}": "Sub Policy No. {{policyNo}}", "Sub Policy Status": "", "Sub Standard": "", "Sub Total": "小计", "SUB_STANDARD_CODE": "", "SUB_STANDARD_RECORD": "", "Sub-items": "", "Sub-standard Code": "", "Sub-standard Code List": "", "Subject": "", "Submission No": "", "Submission No.": "", "Submit": "", "Submit Failed": "", "Submit Failed: {{message}}": "", "Submit successfully": "", "Submit Successfully": "", "Submit Tips": "", "Substandard Code": "", "success": "", "Successful": "", "Successful Records": "", "successfully": "", "Sum Assured": "保险金额", "Sum_Assured_with_free_amount": "", "Sum_Assured_without_free_amount": "", "Summary": "", "Sure": "确定", "Survival Benefit": "", "Survival Benefit Account Transaction Detail": "生存金账户交易详情", "Survival Benefit Payment Account": "生存金支付账户", "Survival Benefit Payment Frequency": "生存金支付频率", "Survival Benefit Payment Option": "生存金领取方式", "Suspend Reason": "", "Suspension Certificate": "", "Sustav će koristiti Ovdje konfiguriranu Opću odluku pravila i pokrenuti tijek prijedloga na temelju ove odluke. Ako se odbije\"": "", "SWIFT Code": "", "Switch Confirm": "", "Switching customer types will clear existing customer data, please confirm.": "", "Switching the plan group will clear the selected products. Please confirm.": "切换计划组后会清空所选择的产品，请确认。", "Symbol not matched, please check GeneralSymbols.": "", "System error": "", "System Error": "", "System generates": "", "System logon user is different from the case handler. please check!": "当前系统已登录用户与案件处理者不同，请检查！", "System Source": "", "System will trigger automatically confirm the policy sign off X days after policy issue date.": "系统将在保单出单X天后，自动确认保单签收。", "System will trigger reminder notification when the proposal stays in below status after X days.": "当投保单停留在如下状态超过X天后，系统将触发提醒", "System will trigger the proposal flow based on the 'Underwriting Decision' of the rule configuration here. If declined, system will reject the proposal. And if manual, system will trigger manual underwriting check for this proposal.": "", "System will trigger the proposal flow based on the \"Compliance Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.": "", "System will trigger the proposal flow based on the \"Verification Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will send the proposal to manual verification.": "", "Tag": "", "Tag Name": "", "Target Return(%)": "", "Target Rule No": "", "Target Rule No.": "", "Target Volatility(%)": "", "Task Assignment Rule": "", "Task Assignment Strategy": "", "Task Create Date": "任务创建日期", "Task No.": "タスク番号", "Task Pick Up": "", "Task Push Strategy": "", "Task Push Strategy has been configured": "", "Task Push Supplementary Strategy": "", "Task Status": "任务状态", "Task successfully assigned to the user.": "任务已成功分配给用户", "Task Successfully Withdrawn": "任务成功撤销", "Task Type": "任务类型", "Tax": "", "Tax Amount": "", "Tax Info": "", "Tax Rate/Value": "", "Tax Setting": "", "Tax Type": "", "TB Type": "终了红利类型", "Team Maintenance Type": "", "Team Management": "", "Team Members": "", "Team Name": "", "Team Name: {{name}}": "", "Team(s) for the stragegy": "", "Terminal Bonus": "终期红利", "Terminated": "", "Terminated Reason": "", "Termination": "", "Termination Date": "", "Termination Date(Lapsed Date)": "", "Termination Reason": "", "TerminationDate": "", "TerminationReason": "", "text": "", "text_select": "", "The Amount input should less than original sum assured of the liability.": "", "The Amount input should less than original sum assured of the product.": "", "The amount is consists of 3 parts： Planed Premium,Single Top-Up and Regular Top-Up.": "", "The customer should pay the premium before issuing the policy .": "", "The date & benefit amount list below is calculated based on current policy info. If there is any further policy change occurred, the real benefit date & amount may change.": "请注意下方所列的日期及生存金金额是根据目前保单信息预估展示。如后续保单发生保全变更，该信息可能发生变化。", "The export may take a long time, you can download the Excel Fails when the status is completed.": "", "The file is still being generated. Please wait a moment.": "文件还在生成中，请稍等。", "The Installment premium is changed from ": "期缴保费从", "The issuance of proposal {{relatedPolices} depends on proposal {{issuanceNo}}. If the decline of this underwriting task results in the withdrawal of proposal {{issuanceNo}}, proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "投保单{{relatedPolices}}的出单依赖于投保单{{issuanceNo}}，如果拒绝本核保任务而导致投保单{{issuanceNo}}被撤销，投保单{{relatedPolices}}也会被同步撤销，请确认。", "The issuance of the proposal {{relatedPolices}} depends on this proposal. If this proposal is withdrawn, the proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "投保单{{relatedPolices}}的出单依赖于本投保单，如果撤销本投保单，投保单{{relatedPolices}}也会被同步撤销，请确认。", "The location cannot be the same, please check.": "", "The modification of the policy information did not pass the compliance verification. The proposal is canceled, and the underwriting task is closed. Please confirm.": "保单信息的修改未通过合规校验，投保单取消并且核保任务关闭，请确认。", "The modification of the policy information has triggered a manual compliance task. Please wait for the submission of the compliance task before continuing with the underwriting task.": "保单信息的修改触发了人工合规任务，请先等待合规任务的提交再继续处理核保任务。", "The net premium has changed": "", "The object names cannot be the same, please check.": "标的名字不能相同，请检查。", "The other process is explained in Withdraw": "", "The payment information for this product has not been entered. Please confirm.": "该产品的缴费信息未输入，请确认。", "The policy data loaded from master policy will be deleted. Do you want to continue?": "从主保单带出的数据将被清空，确认继续吗？", "The policy data loaded from master policy will be refreshed according to new master policy number. Do you want to continue?": "保单数据将会根据新的主保单带出，确认继续吗？", "The policy does not exist.": "", "The policy has been issued successfully.": "", "The policy is not within renewal extraction period, please confirm whether to raise renewal.": "", "The policyholder you selected has not been created yet. Please confirm.": "你选择的投保人还未完成创建，请确认。", "The POS application is already withdrawn. You don't need to underwrite it anymore.": "保全申请已撤销。您无需再核保。", "The Premium Has Changed": "保费发生变化", "The proposal has been sent to New Quote.": "", "The proposal has been sent to Quote Bound.": "", "The proposal has been submitted to manual underwriting.": "", "The proposal has been successfully withdrawn.": "该投保单已成功取消。", "The proposal has failed automated underwriting and was declined by the system.": "", "The proposal has failed automated underwriting due to the changes.": "", "The proposal has failed automated underwriting.": "", "The proposal has Lapsed.": "", "The proposal has passed automated underwriting.": "", "The proposal is already declined or postponed by underwriter. You don't need to perform verification anymore.": "投保单已被核保拒保或延期。您无需再处理该复核任务。", "The proposal is already reject by manual compliance user. You don't need to perform verification anymore.": "投保单已被合规用户拒绝。您无需再处理该复核任务。", "The proposal is already withdrawn (by client, channel or auto withdrawn by the company). You don't need to perform verification anymore.": "投保单已撤销（由客户，渠道申请撤销或保险公司自动撤销）。您无需再处理该复核任务。", "The proposal is already withdrawn. You don't need to underwrite it anymore.": "投保单已撤销。您无需再核保。", "The proposal will lapse, are you sure to continue？": "", "The renewal proposal is generated successfully. Proposal No.{{No}}": "续保投保单已生成. 投保单号：{{No}}", "The renewal quotation is generated successfully. Quotation No.{{No}}.": "续保报价单已生成. 报价单号：{{No}} ", "The renewal validation failed, please check whether the current policy meets the renewal conditions.": "续保申请失败，请确认当前保单是否满足续保条件。", "The required Master Agreement information is insufficient to upload the file.": "所需要的协议单信息不足，无法上传文件", "The same person as Policyholder": "", "The same Plan already exists": "", "The same Plan name already exists under the current policy, please modify and submit! ": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding deductible. Please confirm.": "选中的保险责任/子保险责任与标的已经配置了对应的免赔额，请确认。", "The selected coverage/sub coverage and insured object have been configured with the corresponding limit. Please confirm.": "选中的保险责任/子保险责任与标的已经配置了对应的限额，请确认。", "The team selected above will be brought in here": "", "The updated information will not be saved, are you sure to back?": "", "The user who appointed as team manager is deleted, please reset team manager if needs.": "指定为团队经理的用户被删除，如有需要请重新指定团队经理。", "The verification task has been submitted.": "", "There is duplicate exclusion record exist. Please check": "", "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?": "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?", "Third Party Collection": "", "Third Party Transaction No": "", "This case is escalated from {{user}}.": "", "This document contains vehicle information, premium details and riders information.": "", "This factor is an enumeration type, but enumKey is missing.": "", "This liability is mutually-exclusive with {{tipsText}}": "", "This rule has been binded by team {{teamNames}}, please unbind from team first.": "", "This team has been binded by strategy {{StrategyNames}}, please unbind from strategy first.": "", "This underwriting case is currently under {{user}}. Reassigning it may affect the progress of tasks being processed. Please confirm.": "该核保案件目前正在{{user}}名下，重新分配可能会影响到正在处理中的任务进程，请确认。", "Threshold for Rebalancing": "", "Ticket": "", "Ticket Number": "", "Ticket Price": "", "Ticket Type": "", "Times": "", "Times Types": "", "Tips": "提示", "Title": "", "to": "更改为", "To be expired": "即将到期", "Tonnage": "", "Top Up Due Date": "", "Top Up Period:": "追加投资期间: ", "Total": "", "Total {{count}} {{objectName}}s": "", "Total Allocated Bonus": "", "Total Amount": "", "Total Beneficiary ratio should be equal to 100%": "", "Total Campaign Discount": "", "Total CB Allocation": "现金分红总额", "Total Claim Amount": "", "Total Commission Amount": "佣金总额", "Total Extra Loading": "加费", "Total Fund Value": "", "Total Installments": "", "Total Insured No": "", "Total Insured No.": "", "Total Investment Amount": "", "Total items": "总计 {{total}}", "Total Loan Balance": "贷款余额总计", "Total Outstanding Premium": "", "Total Paid Premium": "", "Total Premium": "", "Total Premium Amount": "", "Total Premium Amount Detail": "", "Total Premium Amount Details": "", "Total Premium Collected": "", "Total Premium Name": "", "Total Price": "总金额", "Total Primary Insured No.": "", "Total Principal Amount": "", "Total Product Discount": "", "Total Refund Premium": "", "Total Risk Amount": "", "Total Risk Amount Details": "", "Total Sub Policy": "", "Total Sum Assured": "", "Total Tax": "", "Total TIV": "", "Total Unpaid Premium (due & undue)": "", "total_amount": "", "TOTAL_PREMIUM": "", "Total: {{amount}} insured": "", "Total: {{total}}": "", "Total: {{total}} Strategies": "", "Total: Records": "", "Transaction": "交易类型", "Transaction Amount": "交易金额", "Transaction Date": "交易日期", "Transaction Effective Date": "交易生效日", "Transaction Efffective Date": "", "Transaction Name": "", "Transaction No.": "交易编号", "Transaction Status": "交易状态", "Transaction Time": "操作时间", "Transaction Type": "", "Transaction Unit": "交易份额", "TransactionDate": "", "Transation Effective Date": "", "Transit ID": "Transit ID", "Transport Information": "", "Transportation Number": "交通号", "Transportation Type": "交通工具", "Transportion_No": "", "Travel Agency": "", "Travel End Date": "", "Travel Expense": "", "Travel Info": "", "Travel Order Number": "", "Travel Order Type": "", "Travel Start Date": "", "Travel Type": "", "TRAVEL_OBJECT_INFO": "", "Trip Info": "", "Trip Type": "", "Trustee": "", "TRUSTEE": "", "Turn back to Manual UW Task Pool": "", "Type": "", "Type a Comment...": "", "Type of Business": "", "UI Template": "UI 模板", "Unanswered question exists, please confirm.": "", "Underwriter": "核保人", "Underwriter Name": "", "underwriting": "核保", "Underwriting": "", "Underwriting Authority": "", "Underwriting case is under review.": "", "Underwriting Check": "", "Underwriting Check Text": "系统将根据绑定规则的“核保结论”给出核保决定或触发人工核保。并且核保决定将基于每个产品给出。", "Underwriting Configuration": "", "Underwriting Criteria": "", "Underwriting History": "", "Underwriting Level": "核保等级", "Underwriting Strategy": "", "Underwriting Task": "", "UNDERWRITING_DECISION": "", "UNDERWRITING_TAG": "", "Unit": "", "Unit Adjustment": "", "Unit No.": "", "Unit No. and Building Name": "", "Unit Premium": "", "Units To Be Adjusted": "", "Universal Saving Account": "", "Unnamed Insured": "", "Unpaid Amount": "", "Update Date": "", "Update OCR Result": "", "Updated at": "", "updateTime": "", "updateUser": "", "Upload": "上传", "Upload Application Form": "", "Upload Attachment": "", "Upload Date": "", "Upload Document": "Upload Document", "Upload Failed": "", "Upload Invoice": "", "Upload New Document": "Upload New Document", "Upload Result": "", "Upload Successfully": "", "Upload Time": "", "Uploading": "", "Usage": "", "Usage Based Premium Detail": "", "Usage Code": "", "Usage Upload": "", "Usage Upload History": "", "Use Sub-item": "", "User": "使用者", "User List": "", "User Name": "", "UW Case Operation": "", "UW Case Required Level": "核保任务等级", "UW Comments": "核保意见", "UW Criteria": "", "UW Critieria Standard": "", "UW Decision": "核保决定", "UW Decision Detail": "核保决定明细", "UW Decision Details": "", "UW Decision History": "", "UW Entry Date": "", "Uw History": "", "UW History": "", "UW in Process": "核保过程中", "UW Message": "核保信息", "UW Owner": "", "UW Query": "核保查询", "UW Stage": "", "UW Task": "", "UW Task No": "核保任务号", "UW Task will be submitted.": "", "UW_Info": "", "UW_INFO": "", "UW_OPERATION": "", "V": "", "Valid": "", "Valid input: {{minValue}} to {{maxValue}}": "", "Value": "", "Value Type": "", "Vehicle": "", "Vehicle Additional Equipment": "", "Vehicle Age": "", "Vehicle Capacity": "", "Vehicle Color": "", "Vehicle Damaged": "", "Vehicle Examination Area": "", "Vehicle Examination Way": "", "Vehicle Info": "", "Vehicle Inspection Information": "", "Vehicle Loan": "", "Vehicle Make": "", "Vehicle Model": "", "Vehicle Plate No.": "", "Vehicle Premium Detail Download": "", "Vehicle Structure": "", "Vehicle Type": "", "Vehicle Usage": "", "VEHICLE_INFO": "", "VEHICLE_LIST_UPLOAD": "", "Verification Check": "Verification Check", "Verification Comment": "", "Verification Decision": "", "Verification Detail": "", "Verification Fail": "", "Verification failed,please upload again.Upload vehicle statistics:": "", "Verification failed.": "", "Verification History": "", "Verification Pass": "", "Verification Reason": "", "Verification Task Pool": "", "Verification Task Pool Re-assign": "复核任务池再分配", "Verification task will be submitted.": "", "Verification/Compliance/UW Process Flow Configuration": "", "Verify success.Upload vehicle statistics:": "", "Version": "版本", "Vesting age is invalid if it is earlier or equal to insured entry age.": "", "Vesting: {{vestingAge}}": "归属: {{vestingAge}}", "view": "", "View": "查看", "View All": "", "View Attachment": "", "View Deductible": "", "View Detail": "", "View History": "", "View Liability": "", "View Limits": "", "View More": "", "view my task": "查看个人池任务", "View Policy Detail": "查看保单详情", "view public task": "查看公共池任务", "View Tax Details": "", "View the photocopy in a new browser page": "", "View Withdrawal Schedule": "查看领取计划", "ViewAll": "", "VIN No": "", "Vin No.": "", "Waiting Days to Withdraw": "", "Waiting Effective": "", "Waiting For Compliance": "", "Waiting for process": "等待核保", "Waiting For Underwriting": "", "Waiting For Verification": "", "Waiting_for_Acceptance": "", "Waiting_for_Approval": "", "Waiting_for_Evaluation": "", "Waived": "", "waiver": "", "WAIVER_PAYMENT": "", "Weekly": "Weekly", "Weight": "", "When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated": "系统通过批处理将满期保单状态置为终止", "When you change it, the current data content will be cleared. Are you sure to change it?": "", "Whether need to double check for rejected case": "", "WHOLELIFE": "", "Width of Vehicle": "", "With Open Issue": "", "With Open Pending Case": "存在未完结的照会", "With Open Pending Issue": "", "With Pending Case": "存在照会案件", "withDraw": "", "Withdraw Proposal": "", "Withdraw Reason": "", "Withdraw Reason Content": "", "Withdraw Task": "撤销任务", "Withdraw the Task": "撤销任务", "Withdrawal Amount": "提领金额", "Withdrawal By Amount": "", "Withdrawal Due Date": "领取日", "Withdrawal Period": "领取区间", "Withdrawal Reason": "<PERSON><PERSON><PERSON> Reason", "Withdrawal Reason Content": "Withdrawal Reason Content", "Withdrawal Schedule": "定期领取计划", "Withdrawal Successful": "", "Withdrawed successfully": "", "withdrawn": "撤销", "Within {{Product}}, some responsibilities have mutually exclusive relationships, please check.": "在{{Product}}内，部分责任有互斥关系，请检查。", "Within Premium Holiday": "", "Witness": "", "Witness Info": "", "Work Injury Compensation": "", "Workflow": "", "Write Off Amount": "核销金额", "Year": "", "YEAR": "", "Year of Manufacturing": "", "year(s)": "", "Year(s)": "", "Yearly": "Yearly", "yes": "是", "You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.": "", "You can download the file and check detail reason.": "", "You can only upload PDF/DOC/XLS/PNG/JPG": "", "You can only upload PDF/EXCL/DOC/XLS/PNG/JPG": "", "You can only upload PDF/PNG/JPG/TIFF/ZIP": "", "You can only upload XLS": "加入计划节点的校验时，没有人工操作流程及页面。请确保在进行规则关联时，规则的结论不得包含“转人工”结论。仅能包含“通过”或“拒绝”结论。", "You can only upload xlsx": "只允许上传xlsx格式的文件", "You can only upload XLSX": "只允许上传XLSX格式的文件", "You can query out the task in UW query after being withdrawn . But no further action allowed. Confirm to withdraw it?": "", "You can query out the task in UW query after being withdrawn . But no furthur action allowed. Confirm to withdrawn it?": "", "You have been assigned {X} tasks": "你被分配了{{number}}个任务", "You have changed master policy information and will impact uploaded vehicle data. Do you want to re-upload the vehicle list?": "您修改了主协议单的信息将会影响已上传的车辆清单，是否需要重新上传车辆文件？", "You have some proposals under the same relation number. You can select the corresponding proposals and issue them together.": "你有一些投保单在相同的关系单号下。您可以选择对应的投保单一起出单。", "Zip Code": ""}