import { lazy } from 'react';

import '@uw/pages/policy-query-pages/style/index.less';

// 模块的目录名
export const basename = 'uw';

// New Business 部分菜单
const nbRouters = [
  {
    id: 'MasterPolicy',
    path: 'master-policy',
    component: lazy(() => import('./pages/nb-pages/MasterPolicy')),
    oneOfPermissions: ['master.agreement.management.agreement.task.pool.view'],
  },
  {
    id: 'AddMasterPolicy',
    path: 'master-policy/:mode',
    component: lazy(() => import('./pages/nb-pages/EditMasterPolicy')),
    oneOfPermissions: ['master.agreement.management.agreement.task.pool.view'],
  },
  {
    id: 'EditMasterPolicy',
    path: 'master-policy/:mode/:id',
    component: lazy(() => import('./pages/nb-pages/EditMasterPolicy')),
    oneOfPermissions: ['master.agreement.management.agreement.task.pool.view'],
  },
  {
    id: 'EditMasterAgreement',
    path: 'master-agreement/:mode/:masterPolicyNo',
    component: lazy(() => import('./pages/nb-pages/MasterAgreementDetail')),
    oneOfPermissions: ['master.agreement.management.agreement.task.pool.view'],
  },
  {
    id: 'AddMasterAgreement',
    path: 'master-agreement/:mode',
    component: lazy(() => import('./pages/nb-pages/MasterAgreementDetail')),
    oneOfPermissions: ['master.agreement.management.agreement.task.pool.edit'],
  },
  {
    id: 'MasterAgreementDetailQuery',
    path: 'master-agreement/:mode/:masterPolicyNo/:source',
    component: lazy(() => import('./pages/nb-pages/MasterAgreementDetail')),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'UsageUpload',
    path: 'usage-upload',
    component: lazy(() => import('./pages/nb-pages/UsageUpload')),
    oneOfPermissions: [
      'new.business.usage.upload.view',
      'master.agreement.management.usage.upload.view',
    ],
  },
  {
    id: 'EventPolicyUpload',
    path: 'event-policy-upload',
    component: lazy(() => import('./pages/nb-pages/EventPolicyUpload')),
    oneOfPermissions: ['new.business.event-upload.view'],
  },
  {
    id: 'BatchListUpload',
    path: 'batch-list-upload',
    component: lazy(() => import('./pages/nb-pages/BatchListUpload')),
    oneOfPermissions: [
      'new.business.batch.list.upload.view',
      'master.agreement.management.batch.list.upload.view',
    ],
  },
  {
    id: 'PolicyTagging',
    path: 'policy-tagging',
    component: lazy(() => import('./pages/nb-pages/PolicyTagging')),
    oneOfPermissions: ['new.business.policy.tag.view'],
  },
];

// nb configuration routes
export const nbConfigrationRoutes = [
  {
    id: 'NewBusinessConfiguration',
    path: 'nb-configuration/*',
    component: lazy(
      () => import('./pages/configuration-pages/NewBusinessConfiguration')
    ),
    oneOfPermissions: [
      'nb.configuration.proposal.view',
      'policy.configuration.view',
      'nb.configuration.quotation.view',
    ],
    children: [
      {
        id: 'QuotationConfiguration',
        path: 'quotation-configuration',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/QuotationConfiguration/index'
            )
        ),
        oneOfPermissions: ['nb.configuration.quotation.view'],
      },
      {
        id: 'ProposalConfiguration',
        path: 'proposal-configuration',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/ProposalConfiguration'
            )
        ),
        oneOfPermissions: ['nb.configuration.proposal.view'],
      },
      {
        id: 'PolicyConfiguration',
        path: 'policy-configuration',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/PolicyConfiguration'
            )
        ),
        oneOfPermissions: ['policy.configuration.view'],
      },
      {
        id: 'RandomCheckConfiguration',
        path: 'random-check-configuration',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/RandomCheckConfiguration'
            )
        ),
        oneOfPermissions: ['nb.configuration.proposal.view'],
      },
      {
        id: 'AttachmentConfiguration',
        path: 'attachment-configuration',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/AttachmentConfiguration'
            )
        ),
        oneOfPermissions: ['nb.configuration.proposal.view'],
      },
      {
        id: 'PremiumNoticeReminder',
        path: 'premium-notice-reminder',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/PremiumNoticeReminder/index'
            )
        ),
        oneOfPermissions: ['nb.configuration.proposal.view'],
      },
      {
        id: 'UITemplate',
        path: 'ui-template',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/NewBusinessConfiguration/UITemplate/index'
            )
        ),
        oneOfPermissions: ['nb.configuration.proposal.view'],
      },
    ],
  },
];

// Business number generate rule configuration routes
export const numberGenerateRuleConfigrationRoutes = [
  {
    id: 'BusinessNoGenerationRule',
    path: 'business-number-generate-configuration/*',
    component: lazy(
      () => import('./pages/configuration-pages/BusinessNoGenerationRule')
    ),
    oneOfPermissions: ['business.no.generation.rule.view'],
  },
];

// underwriting configuration routes
export const uwConfigrationRoutes = [
  {
    id: 'UwConfiguration',
    path: 'uw-configuration/*',
    component: lazy(
      () => import('./pages/configuration-pages/UwConfiguration')
    ),
    oneOfPermissions: ['uw.configuration.view'],
    children: [
      {
        id: 'MedicalPlan',
        path: 'medical-plan',
        component: lazy(
          () =>
            import('./pages/configuration-pages/UwConfiguration/MedicalPlan')
        ),
        oneOfPermissions: ['uw.configuration.view'],
      },
      {
        id: 'Exclusion',
        path: 'Exclusion',
        component: lazy(
          () => import('./pages/configuration-pages/UwConfiguration/Exclusion')
        ),
        oneOfPermissions: ['uw.configuration.view'],
      },
      {
        id: 'UnderwritingAuthority',
        path: 'Authority',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/UwConfiguration/UnderwritingAuthority'
            )
        ),
        oneOfPermissions: ['uw.configuration.view'],
      },
      {
        id: 'SubStandardCode',
        path: 'substahdard-code',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/UwConfiguration/SubStandardCode'
            )
        ),
      },
      {
        id: 'UnderwritingCriteria',
        path: 'Criteria',
        component: lazy(
          () => import('./pages/configuration-pages/UwConfiguration/Criteria')
        ),
      },
    ],
  },
];

// Task Assignment Strategy Configuration ruotes
export const taskAssignmentStrategyRoutes = [
  {
    id: 'TaskAssignmentStrategyConfiguration',
    path: 'assignment/*',
    component: lazy(
      () => import('./pages/configuration-pages/TaskAssignmentStrategy')
    ),
    oneOfPermissions: [
      'uw.task-assign.configuration.view',
      'claim.task-assign.configuration.view',
      'pos.task-assign.configuration.view',
    ],
    children: [
      {
        id: 'Underwriting',
        path: 'underwriting',
        component: lazy(
          () =>
            import(
              './pages/configuration-pages/TaskAssignmentStrategy/Underwriting'
            )
        ),
        oneOfPermissions: ['uw.task-assign.configuration.view'],
      },
      {
        id: 'Claim',
        path: 'claim',
        component: lazy(
          () =>
            import('./pages/configuration-pages/TaskAssignmentStrategy/Claim')
        ),
        oneOfPermissions: ['claim.task-assign.configuration.view'],
      },
      {
        id: 'Pos',
        path: 'pos',
        component: lazy(
          () => import('./pages/configuration-pages/TaskAssignmentStrategy/Pos')
        ),
        oneOfPermissions: ['pos.task-assign.configuration.view'],
      },
    ],
  },
];

const uwRouters = [
  {
    id: 'TaskPool',
    path: 'task-pool',
    component: lazy(() => import('./pages/uw-pages/taskPool')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    id: 'UwOperationPage',
    path: 'uw-operation/:mode/:taskId',
    component: lazy(() => import('./pages/uw-pages/uwCompatibleOperationPage')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    // UwOperationPage的新版
    id: 'UwWorksheetPage',
    path: 'uw-worksheet/:mode/:taskId',
    component: lazy(() => import('./pages/uw-pages/uwWorksheetPage')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    id: 'UwDecisionPage',
    path: 'uw-decision/:mode/:taskId',
    component: lazy(() => import('./pages/uw-pages/uwDecisionPage')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    // UwDecisionPage的新版
    id: 'UwWorksheetDecisionPage',
    path: 'uw-worksheet-decision/:mode/:taskId',
    component: lazy(() => import('./pages/uw-pages/uwWorksheetDecision')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    id: 'RandomCheck',
    path: 'random-check',
    component: lazy(() => import('./pages/uw-pages/randomCheck')),
    oneOfPermissions: ['new.business.random.check.view'],
  },
  {
    id: 'RandomCheckDetail',
    path: 'random-check-detail/:policyNo/:taskNo/:taskId',
    component: lazy(() => import('./pages/uw-pages/randomCheck/detail')),
    oneOfPermissions: ['new.business.random.check.edit'],
  },
  {
    id: 'RandomCheckRuery',
    path: 'random-check/:source',
    component: lazy(() => import('./pages/uw-pages/randomCheck')),
    oneOfPermissions: [
      'new.business.random.check.view',
      'query.random-check.view',
    ],
  },
  {
    id: 'RandomCheckQueryDetail',
    path: 'random-check-detail/:policyNo/:taskNo/:taskId/:source',
    component: lazy(() => import('./pages/uw-pages/randomCheck/detail')),
    oneOfPermissions: ['new.business.random.check.view'],
  },
  {
    id: 'WatchList',
    path: 'watch-list',
    component: lazy(() => import('./pages/uw-pages/watchList')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
];

const uwQueryRouter = [
  {
    id: 'TaskPoolQuery',
    path: 'task-pool/:source',
    component: lazy(() => import('./pages/uw-pages/taskPool')),
    oneOfPermissions: ['query.uw.view'],
  },
  {
    id: 'UwOperationPageQuery',
    path: 'uw-operation/:mode/:taskId/:source',
    component: lazy(() => import('./pages/uw-pages/uwCompatibleOperationPage')),
    oneOfPermissions: ['query.uw.view'],
  },
  {
    id: 'UwWorksheetPage',
    path: 'uw-worksheet/:mode/:taskId/:source',
    component: lazy(() => import('./pages/uw-pages/uwWorksheetPage')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
  {
    id: 'UwDecisionPageQuery',
    path: 'uw-decision/:mode/:taskId/:source',
    component: lazy(() => import('./pages/uw-pages/uwDecisionPage')),
    oneOfPermissions: ['query.uw.view'],
  },
  {
    id: 'UwWorksheetDecisionPage',
    path: 'uw-worksheet-decision/:mode/:taskId/:source',
    component: lazy(() => import('./pages/uw-pages/uwWorksheetDecision')),
    oneOfPermissions: ['uw.manual.task.view'],
  },
];

const policyQueryRouters = [
  {
    id: 'PolicyQueryManagement',
    pageCode: 'query_policy_search',
    path: 'query-policy/search',
    component: lazy(
      () => import('./pages/policy-query-pages/QueryPolicyManagement')
    ),
    oneOfPermissions: ['query.policy.view'],
  },
  {
    id: 15,
    pageCode: 'query_policy_detail',
    path: 'query-policy/detail',
    component: lazy(
      () => import('./pages/policy-query-pages/DetailCompatible')
    ),
    oneOfPermissions: ['query.policy.view'],
  },
  {
    id: 16,
    pageCode: 'query_policy_relationship',
    path: 'query-policy/relationship/:policyNo',
    component: lazy(
      () =>
        import(
          './pages/policy-query-pages/Detail/Components-Detail/Relationship/NewAllRelationInfo'
        )
    ),
    oneOfPermissions: ['query.policy.view', 'query.proposal.view'],
  },
  {
    id: 17,
    pageCode: 'query_policy_relationship',
    path: 'query-policy/relationship/:policyNo/:isProposal',
    component: lazy(
      () =>
        import(
          './pages/policy-query-pages/Detail/Components-Detail/Relationship/NewAllRelationInfo'
        )
    ),
    oneOfPermissions: ['query.policy.view', 'query.proposal.view'],
  },
];

// Group Policy Query routes
export const groupPolicyQueryRouters = [
  {
    id: 'GroupPolicyQueryTaskPool',
    path: 'group-policy-query/search',
    component: lazy(
      () => import('./pages/group-policy-query-pages/GroupPolicyTaskPool')
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'GroupPolicyDetail',
    path: 'group-policy-query/detail/:groupPolicyNo',
    component: lazy(
      () => import('./pages/group-policy-query-pages/GroupPolicyDetail')
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'InsuredPolicyQueryTaskPool',
    path: 'insured-policy/search/:groupPolicyNo/:goodsId',
    component: lazy(
      () => import('./pages/group-policy-query-pages/InsuredQueryPage')
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'RelatedParty',
    path: 'related-party/:policyNo',
    component: lazy(
      () =>
        import(
          './pages/policy-query-pages/Detail/Components-Detail/Relationship/NewAllRelationInfo'
        )
    ),
    oneOfPermissions: [
      'query.policy.view',
      'master.agreement.management.query.view',
    ],
  },
];

// Proposal Query routes
export const proposalRouters = [
  {
    id: 'ProposalTaskPool',
    path: 'proposal/search',
    component: lazy(() => import('./pages/proposal-pages/ProposalTaskPool')),
    oneOfPermissions: ['query.proposal.view'],
  },
  {
    id: 'ProposalDetail',
    path: 'proposal/detail/:proposalNo',
    component: lazy(
      () => import('./pages/proposal-pages/ProposalDetailCompatible')
    ),
    oneOfPermissions: ['query.proposal.view'],
  },
];

// Verification Query routes
export const verificationRouters = [
  {
    id: 'VerificationTaskPool',
    path: 'verification-task-pool',
    component: lazy(
      () => import('./pages/verification-query-pages/VerificationTaskPool')
    ),
    oneOfPermissions: ['new.business.verification.manual-task.view'],
  },
  {
    id: 'VerificationDetail',
    path: 'v1/verification/detail/:mode/:taskId',
    component: lazy(
      () =>
        import(
          './pages/verification-query-pages/VerificationDetail/OldVerificationDetail'
        )
    ),
    oneOfPermissions: ['new.business.verification.manual-task.view'],
  },
  {
    id: 'VerificationDetail',
    path: 'verification/detail/:mode/:taskId',
    component: lazy(
      () => import('./pages/verification-query-pages/VerificationDetail')
    ),
    oneOfPermissions: ['new.business.verification.manual-task.view'],
  },
];

// Proposal entry related routes
export const proposalEntryRouters = [
  {
    id: 'QuotationTaskPool',
    path: 'proposal/task-pool',
    component: lazy(() => import('./pages/proposal-entry/ProposalTaskPool')),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
  {
    id: 'proposalEntry',
    path: 'proposal/entry/:mode/:subCategory',
    component: lazy(() => import('./pages/proposal-entry/ProposalEntryDetail')),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
  {
    id: 'proposalEntry',
    path: 'proposal/entry/:mode/:subCategory/:temporaryId',
    component: lazy(() => import('./pages/proposal-entry/ProposalEntryDetail')),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
  {
    id: 'combinedJourneyProposalEntryTaskPool',
    path: 'combined-journey-proposal/task-pool',
    component: lazy(
      () => import('./pages/proposal-entry/ProposalTaskPool/TaskPool_New')
    ),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
  {
    id: 'combinedJourneyProposalEntryNew',
    path: 'combined-journey-proposal/entry/:mode/:pageCode',
    component: lazy(
      () => import('./pages/proposal-entry/CombinedJourneyProposalEntryDetail')
    ),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
  {
    id: 'combinedJourneyProposalEntry',
    path: 'combined-journey-proposal/entry/:mode/:pageCode/:temporaryId',
    component: lazy(
      () => import('./pages/proposal-entry/CombinedJourneyProposalEntryDetail')
    ),
    oneOfPermissions: ['new.business.proposal.task-pool.view'],
  },
];

// Policy Regeneration routes
export const policyRegenerationRouters = [
  {
    id: 'QuotationTaskPool',
    path: 'policy-regeneration',
    component: lazy(() => import('./pages/policy-regeneration')),
    oneOfPermissions: ['document.policy.regeneration'],
  },
];

// Master Agreement Query相关路由

export const masterAgreementQueryRouters = [
  {
    id: 'MasterAgreementQuery',
    path: 'master-agreement-query/search',
    component: lazy(
      () =>
        import(
          './pages/master-agreement-query-pages/MasterAgreementqueryTaskPool'
        )
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'ViewMasterAgreementQuery',
    path: 'master-agreement-query/:mode',
    component: lazy(
      () =>
        import(
          './pages/master-agreement-query-pages/MasterAgreementqueryTaskPool'
        )
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
  {
    id: 'BatchUploading',
    path: 'batch-uploading',
    component: lazy(() => import('./pages/nb-pages/BatchUploading')),
    oneOfPermissions: ['master.agreement.batch.uploading.view'],
  },
  {
    id: 'MasterAgreementQueryCommon',
    path: 'master-agreement-query-common/search',
    component: lazy(
      () =>
        import(
          './pages/master-agreement-query-pages/MasterAgreementqueryTaskPool'
        )
    ),
    oneOfPermissions: ['master.agreement.management.query.view'],
  },
];

export const medicalRequirementRouters = [
  {
    id: 'MedicalRequirementTaskPool',
    path: 'medical-requirement-task-pool',
    component: lazy(
      () =>
        import('./pages/medical-requirement-pages/MedicalRequirementTaskPool')
    ),
  },
  {
    id: 'MedicalRequirementTaskPool',
    path: 'medical-requirement-detail/:mode/:taskNo/:caseId',
    component: lazy(
      () => import('./pages/medical-requirement-pages/MedicalRequirementAction')
    ),
  },
  {
    id: 'InvoiceTaskPool',
    path: 'invoice-task-pool',
    component: lazy(
      () => import('./pages/medical-requirement-pages/InvoiceTaskPool')
    ),
  },
  {
    id: 'InvoiceTaskPool',
    path: 'invoice-detail/:mode',
    component: lazy(
      () => import('./pages/medical-requirement-pages/InvoiceDetail')
    ),
    oneOfPermissions: ['uw.medical-invoice.task.edit'],
  },
  {
    id: 'InvoiceTaskPool',
    path: 'invoice-detail/:mode/:id',
    component: lazy(
      () => import('./pages/medical-requirement-pages/InvoiceDetail')
    ),
    oneOfPermissions: [
      'uw.medical-invoice.task.approval',
      'uw.medical-invoice.task.view',
      'uw.medical-invoice.task.edit',
    ],
  },
];
// 嵌入到其他域的路由
export const embedRouters = [
  {
    id: 'ProposalEntryInPos',
    path: 'embed/proposal-entry',
    component: lazy(() => import('./pages/embed/proposal-entry')),
  },
];

export default [
  ...uwRouters,
  ...nbRouters,
  ...nbConfigrationRoutes,
  ...numberGenerateRuleConfigrationRoutes,
  ...uwQueryRouter,
  ...uwConfigrationRoutes,
  ...policyQueryRouters,
  ...groupPolicyQueryRouters,
  ...proposalRouters,
  ...verificationRouters,
  ...taskAssignmentStrategyRoutes,
  ...proposalEntryRouters,
  {
    id: 'ImagePreview',
    path: 'image-preview',
    component: lazy(
      () => import('genesis-web-component/lib/components/ImageView')
    ),
    oneOfPermissions: [
      'query.policy.view',
      'new.business.proposal.task-pool.view',
    ],
  },
  ...policyRegenerationRouters,
  ...masterAgreementQueryRouters,
  ...medicalRequirementRouters,
  ...embedRouters,
];
