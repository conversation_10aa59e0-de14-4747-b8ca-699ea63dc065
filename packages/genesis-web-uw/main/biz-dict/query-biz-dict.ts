import { message } from 'antd';

import { chunk, flatten } from 'lodash-es';

import { BizDict } from 'genesis-web-component/lib/interface';
import { BFFPackageEnumItem, MetadataService } from 'genesis-web-service';

import { MetadataKeys } from './keys';

// 需要统一枚举的label value为itemName enumItemName的dictKeyList
const dictKeyList = [
  'goodsCategory',
  'productCategory',
  'dateFormat',
  'UwTaskTypeEnum',
  'UwTaskStatusEnum',
  'attachmentType',
  'paymentFrequencyBase',
  'agreementBasis',
  'bizNoDynamicRuleTimeStampFormat',
  'biznoRuleModule',
  'biznoRuleType',
  'bizNoDynamicRuleFactor',
  'marketCampaignType',
  'typeOfBusiness',
  'underwritingTaskPriority',
  'underwritingTag',
  'attachmentDocumentType',
];

// 既有itemExtend1 又有 dictValue取dictValue
const dictKeyWithValues = [
  'biznoRuleModule',
  'biznoRuleType',
  'marketCampaignType',
  'bizNoDynamicRuleFactor',
  'attachmentDocumentType',
];

export const handleEnumArray = (
  arrays: (BizDict | BFFPackageEnumItem)[],
  filterKey?: string
) =>
  arrays.reduce(
    (
      cur: Record<string, (BizDict | BFFPackageEnumItem)[]>,
      next: BizDict | BFFPackageEnumItem
    ) => {
      const curTemp = { ...cur };
      const strValue = next.dictValue;
      const num = +strValue;
      const value = strValue !== '' && !Number.isNaN(num) ? num : strValue;
      let curEnum = {
        ...next,
        itemName: next.itemName ?? next.dictValueName,
        label: next.dictValueName,
        value:
          !next.enumItemName ||
          next.enumItemName === '' ||
          next.dictKey === 'agreementBasis'
            ? value
            : next.enumItemName,
      };
      const curKey = filterKey
        ? (curEnum[filterKey] ?? curEnum.dictKey!)
        : curEnum.dictKey!;

      if (dictKeyList.includes(curEnum.dictKey!)) {
        curEnum = {
          ...curEnum,
          enumItemName:
            (curEnum.itemExtend1 as string) || (curEnum.dictValue as string),
          itemName: curEnum.dictValueName as string,
          enumItemValue: curEnum.enumItemName,
        };
        if (dictKeyWithValues.includes(curEnum.dictKey as string)) {
          curEnum = {
            ...curEnum,
            enumItemName: curEnum.dictValue as string,
          };
        }
      }
      if (curEnum.dictKey === 'country') {
        curEnum = {
          ...curEnum,
          itemName: curEnum.dictValueName as string,
        };
      }
      if (curTemp[curKey]) {
        curTemp[curKey] = [
          ...cur[curKey],
          {
            ...curEnum,
            itemName: curEnum.itemName || curEnum.dictValueName || '',
          },
        ];
      } else {
        curTemp[curKey] = [
          {
            ...curEnum,
            itemName: curEnum.itemName || curEnum.dictValueName || '',
          },
        ];
      }
      const { childList } = curEnum as BizDict;
      if (childList?.length) {
        Object.entries(handleEnumArray(childList)).forEach(([key, value]) => {
          curTemp[key] = curTemp[key]?.concat(value) ?? value;
        });
      }
      return curTemp;
    },
    {}
  );

export const queryBizDict = async () => {
  const uniqDictKeys = Array.from(new Set(MetadataKeys)).sort();
  const chunks = chunk(uniqDictKeys, 50);
  try {
    const response = await Promise.all([
      ...(chunks?.map(bizKeysChunk =>
        MetadataService.queryBizDict({ dictKeys: bizKeysChunk })
      ) ?? []),
    ]);

    const dictKeyResponse = flatten(response);
    if (Array.isArray(dictKeyResponse)) {
      return handleEnumArray(dictKeyResponse as BizDict[]);
    }
    return {};
  } catch (error) {
    error?.message && message.error(error?.message);
  }
};
