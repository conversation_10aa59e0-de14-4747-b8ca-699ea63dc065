import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { DefaultOptionType } from 'antd/lib/select';

import { pick } from 'lodash-es';

import { BizDict } from 'genesis-web-component/lib/interface';

import { selectEnums } from '@uw/redux/selector';
import { AppState } from '@uw/redux/state.interface';

// 当前文件统一放metadata相关的hooks

export const useBizDictByKey = (key: string): BizDict[] => {
  const enums = useSelector((state: AppState) => {
    try {
      return selectEnums(state);
    } catch (error) {
      console.warn('Redux store selector error in useBizDictByKey:', error);
      return {};
    }
  });
  return enums?.[key] || [];
};

export const useBizDictByKeys = (keys: string[]): BizDict[][] => {
  const enums = useSelector((state: AppState) => {
    try {
      return selectEnums(state);
    } catch (error) {
      console.warn('Redux store selector error in useBizDictByKeys:', error);
      return {};
    }
  });
  return keys?.map(key => enums?.[key] || []) || [];
};

/**
 * @param keys
 * @return 返回 {[metadataKey]: BizDict[]}对象类型
 */
export const useBizDictMapByKeys = (
  keys: string[]
): Record<string, BizDict[]> => {
  const enums = useSelector((state: AppState) => {
    try {
      return selectEnums(state);
    } catch (error) {
      console.warn('Redux store selector error in useBizDictMapByKeys:', error);
      return {};
    }
  });
  return pick(enums || {}, keys);
};

/**
 * @param key
 * @param keyName
 * @return 返回 [{[keyName]: [itemName]}]数组
 */
export const useDict = (
  key: string | string[],
  keyName: 'dictValue' | 'enumItemName' | 'enumItemValue' = 'enumItemName'
): Record<string, string>[] => {
  // 使用 useSelector，但通过状态检查来处理未初始化的情况
  const enums = useSelector((state: AppState) => {
    try {
      return selectEnums(state);
    } catch (error) {
      console.warn('Redux store selector error in useDict:', error);
      return null;
    }
  });

  const enumsMap = useMemo(() => {
    // 如果 enums 未定义或为空，返回默认空对象
    if (!enums) {
      const keys = typeof key === 'string' ? [key] : key;
      return keys.map(() => ({}));
    }

    if (typeof key === 'string') {
      return [
        enums?.[key]?.reduce(
          (cur, dict) => {
            const curDictMap = { ...cur };
            curDictMap[dict?.[keyName]] = dict.itemName;
            return curDictMap;
          },
          {} as Record<string, string>
        ) || {},
      ];
    }
    return key.map(
      enumKey =>
        enums?.[enumKey]?.reduce(
          (cur, dict) => {
            const curDictMap = { ...cur };
            curDictMap[dict?.[keyName]] = dict.itemName;
            return curDictMap;
          },
          {} as Record<string, string>
        ) || {}
    );
  }, [key, enums, keyName]);

  return [...enumsMap];
};

export const getDictLabel = (
  curEnums: BizDict[] | undefined,
  keyName: string | undefined,
  renderKey = 'dictValueName'
): string | undefined => {
  if (!keyName) return undefined;

  return (
    curEnums?.find(
      enumItem =>
        enumItem?.enumItemName?.toString() === keyName?.toString() ||
        enumItem?.dictValue?.toString() === keyName?.toString()
    )?.[renderKey] ?? keyName
  );
};

export const useBizDictOptions = (
  key: string,
  labelProp = 'itemName',
  valueProp = 'enumItemName'
): DefaultOptionType[] | undefined => {
  const options = useBizDictByKey(key);
  return options?.map(option => ({
    label: option?.[labelProp],
    value: option?.[valueProp],
    ...option,
    id: option.id?.toString(),
  }));
};
