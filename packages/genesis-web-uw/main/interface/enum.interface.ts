import { BizDictItem, PlaceholderEnum } from 'genesis-web-service';

import I18nInstance from '@uw/i18n';
import { i18nFn } from '@uw/util/i18nFn';

// campaign 来的配置项
export enum CampaignUserInputType {
  RATE = 'RATE',
  AMOUNT = 'AMOUNT',
}

// 需要传给 market 的 key
export enum CampaignUserInputMarketType {
  RATE = 'percentage',
  AMOUNT = 'amount',
}

// 已抽出到 web-component
export interface BizDict
  extends Omit<Partial<BizDictItem>, 'childList' | 'enumItemName'> {
  itemName: string;
  itemExtend1?: string;
  enumItemName: string | number;
  code?: string;
  value?: string | number;
  childList?: BizDict[];
  [index: string]: unknown;
  dictValue?: string;
  dictValueName?: string;
  isRequired?: number;
  enumItemValue?: string | number;
  orderNo?: number;
  label: string;
}
export interface SelectProps {
  label: string;
  value: number | string;
  disabled?: boolean;
}

export enum SelectedTableTypeEnum {
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
}

export enum CustomerQueryTypeEnum {
  INDIVIDUAL = 'PERSONAL',
  COMPANY = 'COMPANY',
}

// 已导出至 web-components
export enum Mode {
  View = 'view',
  Edit = 'edit',
  Add = 'add',
  Copy = 'copy',
  Withdraw = 'withdraw',
  Renew = 'renew',
  Delete = 'delete',
  Recover = 'recover',
  Remove = 'remove',
}

export enum YesOrNo {
  YES = 'YES',
  NO = 'NO',
}

export enum SucOrFail {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
}

export enum ZoneInfo {
  ZoneId = 'Asia/Shanghai',
}

export enum QueryPlaceholderEnum {
  QUERY_CHANNEL_TREE = 'QUERY_CHANNEL_TREE',
  QUERY_AGENCY_TREE = 'QUERY_AGENCY_TREE',
  ORGANIZATION = 'ORGANIZATION',
}

export enum MinPremiumDurationTypeEnum {
  MIN_PREMIUM_DURATION_DAILY = 'MIN_PREMIUM_DURATION_DAILY',
  MIN_PREMIUM_DURATION_WEEKLY = 'MIN_PREMIUM_DURATION_WEEKLY',
  MIN_PREMIUM_DURATION_MONTHLY = 'MIN_PREMIUM_DURATION_MONTHLY',
  MIN_PREMIUM_DURATION_QUARTERLY = 'MIN_PREMIUM_DURATION_QUARTERLY',
  MIN_PREMIUM_DURATION_YEARLY = 'MIN_PREMIUM_DURATION_YEARLY',
}

export enum MasterPolicyRelationTypeEnum {
  RENEWAL = 'RENEWAL',
  THIRD_PARTY_RELATION = 'THIRD_PARTY_RELATION',
}

export enum UploadStatus {
  Uploading = 'uploading',
  Done = 'done',
  Error = 'error',
  PartialSuccessful = 'partialSuccessful',
}

const ns = { ns: ['uw', 'common'] };
export const PartnerTypeList = [
  {
    itemName: I18nInstance.t('Agency', ns),
    enumItemName: PlaceholderEnum.AGENCY,
    dictValue: QueryPlaceholderEnum.QUERY_AGENCY_TREE,
  },
  {
    itemName: I18nInstance.t('Channel', ns),
    enumItemName: PlaceholderEnum.SALE_CHANNEL,
    dictValue: QueryPlaceholderEnum.QUERY_CHANNEL_TREE,
  },
  {
    itemName: I18nInstance.t('Organization', ns),
    enumItemName: PlaceholderEnum.ORGANIZATION,
    dictValue: QueryPlaceholderEnum.ORGANIZATION,
  },
];

export const MinPremiumDurationTypeList = [
  {
    itemName: I18nInstance.t('Daily', ns),
    enumItemName: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_DAILY,
    dictValue: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_DAILY,
  },
  {
    itemName: I18nInstance.t('Weekly', ns),
    enumItemName: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_WEEKLY,
    dictValue: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_WEEKLY,
  },
  {
    itemName: I18nInstance.t('Monthly', ns),
    enumItemName: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_MONTHLY,
    dictValue: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_MONTHLY,
  },
  {
    itemName: I18nInstance.t('Quarterly', ns),
    enumItemName: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_QUARTERLY,
    dictValue: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_QUARTERLY,
  },
  {
    itemName: I18nInstance.t('Yearly', ns),
    enumItemName: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_YEARLY,
    dictValue: MinPremiumDurationTypeEnum.MIN_PREMIUM_DURATION_YEARLY,
  },
];

export enum FrequencyEnum {
  SETTLE_FREQUENCY_MONTHLY = 'SETTLE_FREQUENCY_MONTHLY',
}

export const FrequencyList = [
  {
    itemName: I18nInstance.t('Monthly', ns),
    enumItemName: FrequencyEnum.SETTLE_FREQUENCY_MONTHLY,
    dictValue: FrequencyEnum.SETTLE_FREQUENCY_MONTHLY,
  },
];

export const MasterPolicyRelationTypeList = [
  {
    itemName: I18nInstance.t('Others', ns),
    enumItemName: MasterPolicyRelationTypeEnum.THIRD_PARTY_RELATION,
    dictValue: MasterPolicyRelationTypeEnum.THIRD_PARTY_RELATION,
  },
];

export const UwCampaignDiscountSelectList = [
  {
    itemName: I18nInstance.t('Amount', ns),
    enumItemName: CampaignUserInputMarketType.AMOUNT,
    dictValue: CampaignUserInputType.AMOUNT,
  },
  {
    itemName: I18nInstance.t('Rate', ns),
    enumItemName: CampaignUserInputMarketType.RATE,
    dictValue: CampaignUserInputType.RATE,
  },
];

export const PayPeriodTypeEnum: Record<string, string> = {
  YEARFULL: '1',
  SUIFULL: '2',
  WHOLELIFE: '3',
  SINGLE: '4',
};

export enum StatueEnumType {
  UWTASKSTATUS = 'uwTaskStatus',
  INSSUANCESTATUS = 'issuanceStatus',
  POLICYSTATUS = 'policyStatus',
}

// UW TASK RELATED
export enum UwTaskStatusEnum {
  WAITING_FOR_PROCESS = 'WAITING_FOR_PROCESS',
  ESCALATED = 'ESCALATED',
  UW_IN_PROCESS = 'UW_IN_PROCESS',
  CLOSED = 'CLOSED',
  INITIAL = 'INITIAL',
  CANCEL = 'CANCEL',
  RETURNED = 'RETURNED',
}

// POLICY STATUS
export enum POLICYStatusEnum {
  POLICY_EFFECT = 'Effective',
  TERMINATION = 'Terminated',
  LAPSED = 'Lapsed',
  EXPIRY = 'Expiry',
}

// ISSUANCE STATUS
export enum IssuanceStatusEnum {
  EFFECTIVE = 'Effective',
  DATA_ENTRY_IN_PROGRESS = 'Data Entry In Progress',
  PENDING_PROPOSAL_CHECK = 'Pending Proposal Check',
  WAITING_FOR_DATA_ENTRY = 'Waiting For Data Entry',
  WAITING_FOR_ISSUANCE = 'Waiting For Issuance',
  DECLINED = 'Declined',
  WITHDRAWN = 'Withdrawn',
}

export enum VerificationStatusEnum {
  CLOSED = 'CLOSED',
  CANCEL = 'CANCEL',
  ESCALATED = 'ESCALATED',
  INITIAL = 'INITIAL',
  RETURNED = 'RETURNED',
  VERIFICATION_IN_PROCESS = 'VERIFICATION_IN_PROCESS',
  WAITING_FOR_VERIFICATION = 'WAITING_FOR_VERIFICATION',
}

export enum UwHistoryTypeEnum {
  MANUAL = 'MANUAL',
  AUTO = 'AUTO',
}

export enum UwDecisionEnum {
  ACCEPT = 'ACCEPT',
  REJECT = 'REJECT',
  POSTPONE = 'POSTPONE',
  CONDITIONAL_ACCEPT = 'CONDITIONAL_ACCEPT',
  DOWNSELL = 'DOWNSELL',
  EXCLUDE = 'EXCLUDE',
  LOADING = 'LOADING',
  MANUAL = 'MANUAL',
  RETURN = 'RETURN',
  DECLINE = 'DECLINE',
}
export const UwDecisionEnumArray = [
  'ACCEPT',
  'REJECT',
  'POSTPONE',
  'CONDITIONAL_ACCEPT',
];
export enum DecisionAction {
  SAVING = 'SAVING',
  SUBMITTING = 'SUBMITTING',
}
export enum AnchorIdsEnums {
  proposalRule = 'proposalRule',
  policyIssuanceRule = 'policyIssuanceRule',
  optInRule = 'optInRule',
}
export const AnchorIdsMap = {
  [AnchorIdsEnums.proposalRule]: I18nInstance.t('Proposal Rule', ns),
  [AnchorIdsEnums.policyIssuanceRule]: I18nInstance.t(
    'Policy Issuance Rules',
    ns
  ),
  [AnchorIdsEnums.optInRule]: I18nInstance.t('Opt-In-Rules', ns),
};
export enum TypeEnums {
  PROPOSAL_UNDERWRITING = 'PROPOSAL_UNDERWRITING',
  POLICY_EFFECTIVE = 'POLICY_EFFECTIVE',
  PROPOSAL_VERIFICATION = 'PROPOSAL_VERIFICATION',
  PROPOSAL_COMPLIANCE = 'PROPOSAL_COMPLIANCE',
  OPT_IN = 'OPT_IN',
  POLICY_ISSUANCE_COMPLIANCE = 'POLICY_ISSUANCE_COMPLIANCE',
  PENDING_FOR_CUSTOMER = 'PENDING_FOR_CUSTOMER',
  SEPERATE_BY_PROPOSAL_STATUS = 'SEPERATE_BY_PROPOSAL_STATUS',
  ACCUMULATED_VALUE = 'ACCUMULATED_VALUE',
  QUOTATION_UNDERWRITING = 'QUOTATION_UNDERWRITING',
  POLICY_ISSUANCE_UNDERWRITING = 'POLICY_ISSUANCE_UNDERWRITING',
}

export enum BizStatusEnum {
  WAITING_FOR_DATA_ENTRY = 'WAITING_FOR_DATA_ENTRY',
  DATA_ENTRY_IN_PROGRESS = 'DATA_ENTRY_IN_PROGRESS',
  PENDING_PROPOSAL_CHECK = 'PENDING_PROPOSAL_CHECK',
  WAITING_FOR_ISSUANCE = 'WAITING_FOR_ISSUANCE',
  ACCU_WAITING_FOR_DATA_ENTRY = 'ACCU_WAITING_FOR_DATA_ENTRY',
}

export enum PendingProposalCheckBizStatusEnum {
  PROPOSAL_VERIFICATION = 'PROPOSAL_VERIFICATION',
  PROPOSAL_COMPLIANCE = 'PROPOSAL_COMPLIANCE',
  PROPOSAL_UNDERWRITING = 'PROPOSAL_UNDERWRITING',
}

export const ReminderBizStatusEnum = [
  'WAITING_FOR_DATA_ENTRY_REM',
  'DATA_ENTRY_IN_PROGRESS_REM',
  'PENDING_PROPOSAL_CHECK_REM',
  'WAITING_FOR_ISSUANCE_REM',
];

export enum MedicalPlanStatusEnums {
  Draft = 'Draft',
  Issued = 'Issued',
  Resolved = 'Resolved',
}
export enum LoadingMethodEnum {
  FIXED_AMOUNT = 'FIXED_AMOUNT',
  FIX_AMOUNT_PER_1000_SA = 'FIX_AMOUNT_PER_1000_SA',
  EM_VALUE = 'EM_VALUE',
  OTHERS = 'OTHERS',
  PERCENTAGE_ON_STANDARD_PREMIUM = 'PERCENTAGE_ON_STANDARD_PREMIUM',
}
export const loadingUnitMap: Record<string, string> = {
  FIXED_AMOUNT: 'currency',
  FIX_AMOUNT_PER_1000_SA: 'currency',
  EM_VALUE: '',
  OTHERS: '%',
};

export const newLoadingUnitMap: Record<string, string> = {
  FIXED_AMOUNT: 'currency',
  FIX_AMOUNT_PER_1000_SA: 'currency',
  EM_VALUE: '',
  PERCENTAGE_ON_STANDARD_PREMIUM: '%',
};

export enum LoadingPeriodType {
  PERMANENT = 'PERMANENT',
  FIXED_YEAR = 'FIXED_YEAR',
}

export enum ExclusionStatusEnums {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum RenewalSourceEnums {
  NONE_RENEWAL = 'NONE_RENEWAL',
  AUTO_RENEWAL = 'AUTO_RENEWAL',
  MANUAL_RENEWAL = 'MANUAL_RENEWAL',
}

export enum PolicyStatusEnum {
  POLICYEFFECTIVE = 'POLICY_EFFECT',
  TERMINATION = 'TERMINATION',
  LAPSED = 'LAPSED',
  EFFECTIVE = 'EFFECTIVE',
}
export enum ShowMode {
  Card = 'card',
  Table = 'table',
}

// Deprecated use
export enum ProposalStatusEnum {
  EFFECTIVE = 'EFFECTIVE',
  DECLINED = 'DECLINED',
  WITHDRAWN = 'WITHDRAWN',
  DATA_ENTRY_IN_PROGRESS = 'DATA_ENTRY_IN_PROGRESS',
  PENDING_PROPOSAL_CHECK = 'PENDING_PROPOSAL_CHECK',
  WAITING_FOR_ISSUANCE = 'WAITING_FOR_ISSUANCE',
  WAITING_FOR_DATA_ENTRY = 'WAITING_FOR_DATA_ENTRY',
}

export enum QueryTypeEnum {
  PROPOSAL = 'PROPOSAL',
  QUOTATION = 'QUOTATION',
  POLICY = 'POLICY',
  UNDERWRITING = 'UNDERWRITING',
}

export enum QueryModuleEnum {
  PolicyQuery = 'PolicyQuery',
  GroupPolicyQuery = 'GroupPolicyQuery',
  ProposalQuery = 'ProposalQuery',
  QuotationQuery = 'QuotationQuery',
}

export enum ProposalSwitchEnum {
  SEPARATEDCONFIG = 'separatedConfig',
  ACCUMULATEDCONFIG = 'accumulatedConfig',
}

export enum ParamsYesOrNoEnum {
  Yes = 'Y',
  No = 'N',
}

export enum InsuredAndPolicyHolder {
  Insured = 'Insured',
  PolicyHolder = 'PolicyHolder',
  Renter = 'Renter',
  Payer = 'Payer',
  Other = 'Other',
}

export enum PremiumPeriodTypeEnum {
  YEARFULL = 'YEARFULL',
  SUIFULL = 'SUIFULL',
  WHOLELIFE = 'WHOLELIFE',
  SINGLE = 'SINGLE',
}

export enum ChargeType {
  InitialFee = 'INITIAL_FEE',
  PremiumLoad = 'PREMIUM_LOAD',
  PolicyAdminFee = 'POLICY_ADMIN_FEE',
  AccountManagementFee = 'ACCOUNT_MANAGEMENT_FEE',
  EfeBrekerFee = 'ETF_BROKER_FEE',
  EfeManagementFee = 'ETF_MANAGEMENT_FEE',
  RiderPremium = 'RIDER_PREMIUM',
  InsuranceCoverageCharge = 'INSURANCE_COVERAGE_CHARGE',
  FundSwitchCharge = 'FUND_SWITCH_CHARGE',
  SurrenderCharge = 'SURRENDER_CHARGE',
  WithdrawalCharge = 'WITHDRAWAL_CHARGE',
}

export enum SortMapName {
  UW_GMT_MODIFIED = 'UW_GMT_MODIFIED',
  UW_GMT_CREATED = 'UW_GMT_CREATED',
  UW_TASK_PRIORITY_ORDER = 'UW_TASK_PRIORITY_ORDER',
}

export enum PendingCaseModuleEnum {
  Underwriting_NB = 'UNDERWRITING_NEW_BUSINESS',
  Underwriting_POS = 'UNDERWRITING_POS',
}

export enum CategoryType {
  UNDERWRITING = 'UNDERWRITING',
  CLAIM = 'CLAIM',
  COMPLIANCE = 'COMPLIANCE',
  NB_OPERATION = 'NB_OPERATION',
  POS = 'POS',
}
export enum FormEditStatus {
  FormEdit = 'Edit',
  FormCancel = 'Cancel',
  FormSave = 'Save',
}

export enum BenefitType {
  Annuity = 'ANNUITY',
}
export enum ChangeOption {
  BOTH = 'BOTH',
  ONLY_CURRENT = 'ONLY_CURRENT',
}

export enum ChangePosition {
  HOLDER = 'HOLDER',
  INSURANT = 'INSURANT',
}

export enum PartyType {
  COMPANY = 'COMPANY',
  INDIVIDUAL = 'INDIVIDUAL',
}

export const PolicyHolderAndInsuredTypeEnum: Record<string, number> = {
  INDIVIDUAL: 1,
  COMPANY: 2,
};

export enum CustomerSubTypeEnum {
  BASIC_INFO = 1,
  ACCOUNT = 2,
  ADDRESS = 3,
  EMAIL = 4,
  PHONE = 5,
  RELATIONSHIP = 7,
  PAYERRELATIONSHIP = 9,
  SOCIAL_ACCOUNT = 13,
  CONTACT_PERSON = 14,
  TAX_INFO = 15,
}

export const CustomerSubTypeEnumReflect = {
  [CustomerSubTypeEnum.BASIC_INFO]: 'BASIC_INFO',
  [CustomerSubTypeEnum.ACCOUNT]: 'ACCOUNT',
  [CustomerSubTypeEnum.ADDRESS]: 'ADDRESS',
  [CustomerSubTypeEnum.EMAIL]: 'EMAIL',
  [CustomerSubTypeEnum.PHONE]: 'PHONE',
  [CustomerSubTypeEnum.RELATIONSHIP]: 'RELATIONSHIP',
  [CustomerSubTypeEnum.PAYERRELATIONSHIP]: 'PAYERRELATIONSHIP',
  [CustomerSubTypeEnum.SOCIAL_ACCOUNT]: 'SOCIAL_ACCOUNT',
  [CustomerSubTypeEnum.CONTACT_PERSON]: 'CONTACT_PERSON',
  [CustomerSubTypeEnum.TAX_INFO]: 'TAX_INFO',
};

export enum CustomerSubTypeEnums {
  BASIC_INFO = 'BASIC_INFO',
  ACCOUNT = 'ACCOUNT',
  ADDRESS = 'ADDRESS',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SOCIAL_ACCOUNT = 'SOCIAL_ACCOUNT',
  CONTACT_PERSON = 'CONTACT_PERSON',
  TAX_INFO = 'TAX_INFO',
  IDENTIFIER = 'IDENTIFIER',
}

export enum CustomerTypeEnums {
  PERSON = 'PERSON',
}

export enum CustomerTypeEnum {
  HOLDER = 1,
  INSURANT = 2,
}

export enum FactorEnum {
  HOLDER = 1,
  INSURANT = 2,
  PAYER = 3,
  OBJECT = 6,
  POLICY = 11,
  SECONDARY_LIFE_INSURED = 12,
  BENEFICIARY = 13,
  NOMINEE = 101,
  CONSENTEE = 102,
  ASSIGNEE = 103,
  TRUSTEE = 104,
  PREMIUM_FUNDER = 105,
  BENEFICIAL_OWNER = 14,

  // Renter正常topic是放在object下，入单的时候需要单独处理所以单独定义了一个。其他场景如查询页面（还是正常使用OBJECT = 6 来获取renter信息）
  RENTER = 99999,
}

export const FactorEnumMap = {
  [FactorEnum.RENTER]: 'renterItems',
  [FactorEnum.HOLDER]: 'holderItems',
  [FactorEnum.INSURANT]: 'insuredItems',
  [FactorEnum.PAYER]: 'payerItems',
  [FactorEnum.OBJECT]: 'objectItems',
  [FactorEnum.POLICY]: 'policyItems',
  [FactorEnum.SECONDARY_LIFE_INSURED]: 'secondaryLifeInsuredItem',
  [FactorEnum.BENEFICIARY]: 'beneficiaryItems',
  [FactorEnum.NOMINEE]: 'nomineeItems',
  [FactorEnum.CONSENTEE]: 'consenteeItems',
  [FactorEnum.ASSIGNEE]: 'assigneeItems',
  [FactorEnum.TRUSTEE]: 'trusteeItems',
  [FactorEnum.PREMIUM_FUNDER]: 'premiumFunderItems',
  [FactorEnum.BENEFICIAL_OWNER]: 'beneficialOwnerItems',
};

export enum IsRequiredEnum {
  YES = 1,
  NO = 2,
}

export enum ModuleFormEnum {
  Proposal = 'proposal',
  Policy = 'policy',
  Group = 'group',
}
export enum TaskPoolNoticeTypeEnum {
  WITHDRAW_BY_VERIFICATION = 'WITHDRAW_BY_VERIFICATION',
  WITHDRAW_BY_UNDERWRITER = 'WITHDRAW_BY_UNDERWRITER',
  WITHDRAW_BY_COMPLIANCE = 'WITHDRAW_BY_COMPLIANCE',
  PROPOSAL_WITHDRAWN = 'PROPOSAL_WITHDRAWN',
  POS_WITHDRAWN = 'POS_WITHDRAWN',
}

export enum GoodsCategoryEnum {
  AUTO = 22,
  SEM = 24,
}

export enum TaskTypeEnum {
  VERIFICATION = 'VERIFICATION',
  NEW_BUSINESS = 'NEW_BUSINESS',
  POS = 'POS',
  RANDOM_CHECK = 'RANDOM_CHECK',
}

export enum QuotationEditableParentTypeEnum {
  POLICY = 'POLICY',
}

export enum AdjustedAnnualStandardPremiumEnum {
  Premium = 'adjustedAnnualStandardPremium',
  PremiumRate = 'adjustedAnnualStandardPremiumRate',
}

export enum QuotationEditableEnum {
  VEHICLE = 'VEHICLE',
  PRODUCT = 'PRODUCT',
  POLICY = 'POLICY',
  ADDITIONAL_INFO = 'ADDITIONAL_INFO',
  HOLDER = 'HOLDER',
  HOLDER_COMPANY = 'HOLDER_COMPANY',
}

export enum QuotationSaveType {
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  ADD = 'ADD',
}

export enum QuotationOperateType {
  SAVE = 'SAVE',
  CALCULATE = 'CALCULATE',
  CALCULATE_SUMINSURED = 'CALCULATE_SUMINSURED',
}

export enum IsExtension {
  Yes = 1,
  No = 2,
}
export enum FilterMyTaskType {
  RADIO = 'RADIO',
  SWITCH = 'SWITCH',
}

export enum PolicyInsuredObjectType {
  Building = 'BUILDING',
  Device = 'INSURED_DEVICE',
  Driver = 'INSURED_AUTO',
  Order = 'ORDER',
  Pet = 'PET',
  Travel = 'TRAVEL',
  STOCKS = 'STOCKS',
  CONTENTS_FIXTURES_FITTINGS = 'CONTENTS_FIXTURES_FITTINGS',
  LOCATION_INSURED = 'LOCATION_INSURED',
  ADDRESS = 'ADDRESS',
  PLANT_MACHINERY = 'PLANT_MACHINERY',
  OTHER_PROPERTIES = 'OTHER_PROPERTIES',
  AUTO = 'AUTO',
  EQUIPMENT_INSURED = 'EQUIPMENT_INSURED',
  FIDELITY_GUARANTEE = 'FIDELITY_GUARANTEE',
  INLAND_TRANSIT = 'INLAND_TRANSIT',
  PERSONAL_ACCIDENT = 'PERSONAL_ACCIDENT',
  EMPLOYER_LIABILITY = 'EMPLOYER_LIABILITY',
  PUBLIC_LIABILITY = 'PUBLIC_LIABILITY',
  MACHINERY_EQUIPMENTS = 'MACHINERY_EQUIPMENTS',
  PRODUCT_LIABILITY = 'PRODUCT_LIABILITY',
  VEHICLE = 'VEHICLE',
  VEHICLE_ADDITIONAL_EQUIPMENT = 'VEHICLE_ADDITIONAL_EQUIPMENT',
  CAR_OWNER = 'CAR_OWNER',
  VEHICLE_LOAN = 'VEHICLE_LOAN',
}

export enum ObjectCategoryEnum {
  AUTO = 7,
}
export enum ObjectSubCategoryEnum {
  VEHICLE = 7,
}

export enum AttachmentModule {
  Underwriting = 'Underwriting',
  NewBusiness = 'New Business',
}
export enum IsCurrencyAmountTypeEnum {
  Yes = 1,
}

export enum InfoTab {
  Quotation = 'QUOTATION',
  UW = 'UW',
}
export enum QuotationModuleTypeEnum {
  DISCOUNT = 'DISCOUNT',
  EXTRA = 'EXTRA',
  CAMPAIGN_DISCOUNT_PAYER = 'CAMPAIGN_DISCOUNT_PAYER',
}

export enum QuotationPropertyValueAutoNcdEnum {
  BONUSMALUSSCENARIO = 'bonusMalusScenario',
}

export enum QuotationProductInfoModalTypeEnum {
  NAME = 'NAME',
  TAX = 'TAX',
  PRODUCT_DISCOUNT = 'PRODUCT_DISCOUNT',
  EXTRA_LOADING = 'EXTRA_LOADING',
  COMPAIGN_DISCOUNT = 'COMPAIGN_DISCOUNT',
}

export enum PaymentMethodType {
  CASH_PREMIUM = 'CASH_PREMIUM',
  DEDUCTED_FROM_INVESTMENT = 'DEDUCTED_FROM_INVESTMENT',
}

export enum QuotationProductInfoActionTypeEnum {
  REMOVE = 'REMOVE',
  RECOVER = 'RECOVER',
  DELETE = 'DELETE',
  DISABLE_REMOVE = 'DISABLE_REMOVE',
}

export enum CoveragePeriodTypeEnum {
  USERINPUT = 'USERINPUT',
  WHOLELIFE = 'WHOLELIFE',
  SUIFULL = 'SUIFULL',
}

export enum UploadDomainTypeEnum {
  POLICY = 'Policy',
  UNDERWRITING = 'Underwriting',
  POLICY_VERIFICATION = 'Policy-verification',
  POLICY_MANAGEMENT = 'policy-management',
}

export enum UploadSubCategoryTypeEnum {
  NEN_QUOTE = '101001',
  RANDOM_CHECK = '101002',
  UNDERWRITING_MANUAL_TASK = '101003',
  VERIFICATION_MANUAL_TASK = '101004',
  POLICY_UPLOAD_FILE = '101005',
  POLICY_UPLOAD_MILEAGE = '101006',
  EVENT_UPLOAD = '101010',
}

export enum AdditionalInfoMap {
  ISSUANCE_NO = 'issuanceNo',
  TASK_ID = 'taskId',
  TASK_NO = 'taskNo',
  MASTER_POLICY_NO = 'masterPolicyNo',
  GOODS_ID = 'goodsId',
  PLAN_ID = 'planId',
}

export enum LiabilityEnum {
  FIXED = 'FIXED',
  RANGE = 'RANGE',
  ENUMURATION = 'ENUMURATION',
  BY_FORMULA = 'BY_FORMULA',
}

export enum SpecialRateKeyEnum {
  SPECIAL_RISK = '4',
  MHSpecialRiskDiscount = 'MHSpecialRiskDiscount',
}

export enum SpecialPropertyNameEnum {
  EXTRA_RATE = 'extraRate',
  DISCOUNT_RATE = 'discountRate',
}

export enum StepStatusEnum {
  Finish = 'finish',
  Wait = 'wait',
}

export enum SchemaDefTypeEnum {
  POLICY = 5,
}

export enum ExtractPolicyRuleEnum {
  POLICY_EFFECTIVE_DATE = 'POLICY_EFFECTIVE_DATE',
}

export enum RandomCheckUserTypeEnum {
  COMPANY = 'COMPANY',
  PERSON = 'PERSON',
}

export enum PartnerTypeKeyEnum {
  SALE_CHANNEL = '1',
  AGENCY = '2',
  AGENT = '3',
}

export enum SortByUserEnum {
  PUBLIC_TASK = '0',
  PRIVATE_TASK_ACTIVE = '1',
  PRIVATE_TASK_INACTIVE = '2',
}

export enum InstallmentTypeEnum {
  INSTALLMENT = 'INSTALLMENT',
  RENEW = 'RENEW',
}

export enum InsuredTypeEnum {
  DEPUTY_INSURANT = 'DEPUTY_INSURANT',
  JOINT_INSURANT = 'JOINT_INSURANT',
  MAIN_INSURANT = 'MAIN_INSURANT',
}

export enum SchemaObjectSubCategoryCodeEnum {
  EQUIPMENT_INSURED = 26,
  PUBLIC_LIABILITY = 24,
}
export enum CoverageType {
  UW = 'UW',
  POLICY = 'POLICY',
}

export enum PartnerTypeEnum {
  TIED_AGENT = '6',
  AGENCY = '3',
  SALE_CHANNEL = '2',
  INSURANCE = '1',
  SERVICE = '4',
  INSTITUTE = '5',
  BANK = '7',
  LEASE_CHANNEL = '8',
  BROKER_COMPANY = '9',
}

export enum SalesChannelTypeEnum {
  SALES_PLATFORM = '4',
  AGENCY_COMPANY = '3',
  DIRECT_BUSINESS = '1',
  AGENT = '2',
  BANK = '5',
  LEASE_CHANNEL = '6',
  BROKER_COMPANY = '7',
}

export enum BusinessSourceEnum {
  Policy = 'policy',
  Issuance = 'issuance',
  Quotation = 'quotation',
}

export enum RenewModalTypeEnum {
  CONFIRM = 'CONFIRM',
  ALERT = 'ALERT',
  RENEW_QUOTATION = 'RENEW_QUOTATION',
  RENEW_PROPOSAL = 'RENEW_PROPOSAL',
}

export enum ProductInstallmentType {
  RE_CALCULATE_PREMIUM_FOR_EACH_INSTALLMENT = 1,
  PRE_CALCULATE_PREMIUM_FOR_ALL_INSTALLMENTS,
}

export enum SelectAllEnum {
  SELECT_ALL = '0',
}

export enum SortDirectionEnum {
  ASCEND = 'ascend',
  DESCEND = 'descend',
}

export enum SortLowerCase {
  ASC = 'asc',
  DESC = 'desc',
}

export enum SortUpperCase {
  ASC = 'ASC',
  DESC = 'DESC',
  RELATION_ASC = 'RELATION_ASC',
}

export enum ConfigTypeEnum {
  Quotation = 1,
  Proposal,
}

export enum PaymentMethods {
  VOUCHER = 'VOUCHER',
}

export enum QuotationType {
  Quotation = 'QUOTATION_NO',
}

export enum InsuranceType {
  SME = 10040,
}

export enum PremiumType {
  PLANNED_PREMIUM = 'PLANNED_PREMIUM',
}

export enum AntdTableChangeExtraAction {
  Sort = 'sort',
  Paginate = 'paginate',
  Filter = 'filter',
}
export enum GenerationRuleEnum {
  Policy = 'POLICY',
  Underwriting = 'UNDERWRITING',
  Finance = 'FINANCE',
  Compliance = 'COMPLIANCE',
  PosOnline = 'POS_ONLINE',
  Claim = 'CLAIM',
}
export enum ProductTypeEnum {
  MAIN = 'MAIN',
  RIDER = 'RIDER',
}

export const PeriodUnitMap: Record<string, string> = {
  MONTHFULL: i18nFn('Month(s)'),
  MONTH: i18nFn('Month(s)'),
  YEARFULL: i18nFn('Year(s)'),
  Year: i18nFn('Year(s)'),
  SUIFULL: i18nFn('Age'),
  HOUR: i18nFn('Hour(s)'),
};

export enum SaveMode {
  SAVE = 'SAVE',
  SUBMIT = 'Submit',
  CALCULATE = 'CALCULATE',
  MULTIPLE_GOODS_CALCULATE = 'MULTIPLE_GOODS_CALCULATE',
  CONFIRM = 'CONFIRM',
  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',
  WITHDRAW = 'WITHDRAW',
  GENERATE_OFFER = 'GENERATE_OFFER',
  ISSUE = 'ISSUE',
  /** 无业务含义 单纯返回拼接好的params */
  GET_PARAMS = 'GET_PARAMS',
  // https://jira.zaouter.com/browse/GIS-120147?filter=-1
  VEHICLE_LIST_SAVE = 'VEHICLE_LIST_SAVE',
  EB_COPY = 'EB_COPY',
  /** 无业务含义 单纯返回拼接好的params，但不需要经过校验 */
  GET_NO_VALIDATION_PARAMS = 'GET_NO_VALIDATION_PARAMS',
  /** Sync Master policy plan */
  SYNC_MASTER_POLICY_PLAN = 'SYNC_MASTER_POLICY_PLAN',
  ONLY_SAVE = 'ONLY_SAVE',
}

export enum BizDictConfigListEnum {
  CustomerNameGroup = '2',
}

export enum StackValueType {
  FIXED_VALUE = 'FIXED_VALUE',
  ENUMERATION = 'ENUMERATION',
  INPUT = 'INPUT',
  FORMULA = 'FORMULA',
}

export enum FundType {
  PLANNED_PREMIUM = 'PLANNED_PREMIUM',
  SINGLE_TOP_UP = 'SINGLE_TOP_UP',
  REGULAR_TOP_UP = 'REGULAR_TOP_UP',
}

export enum TodoOrDoneStatus {
  TODO = 'TODO',
  DONE = 'DONE',
}

// 已抽出到 web-component
export const bigBizDictKeys = ['industry', 'occupation', 'bank', 'address1'];

export enum CalculationMethodEnum {
  //  保费算保额
  SA_CALCULATED_BY_PREMIUM = 2,
  //  保额算保费
  PREMIUM_CALCULATED_BY_SA = 3,
}

export enum BeneficiaryTypeEnum {
  LEGAL_BENEFICIARY = 'LEGAL_BENEFICIARY',
  DESIGNATE_BENEFICIARY = 'DESIGNATE_BENEFICIARY',
}

export enum LiabilityInsuranceType {
  DEATH = 'DEATH',
  WAIVER = 'WAIVER',
}

export enum GenerationType {
  POLICY = 'POLICY',
}

export enum TabKeyEnum {
  First = '1',
  Secord = '2',
}

// 暂时添加以下类型 后续有需要可继续添加
export enum JSDataTypeEnums {
  String = 'String',
  Number = 'Number',
  Boolean = 'Boolean',
  Array = 'Array',
  Object = 'Object',
  Undefined = 'Undefined',
  Null = 'Null',
}
export enum PremiumItemEnum {
  STANDARD_PREMIUM = 'STANDARD_PREMIUM',
  EXTRA_PREMIUM = 'EXTRA_PREMIUM',
  NO_CLAIM_DISCOUNT = 'NO_CLAIM_DISCOUNT',
  PREMIUM_DISCOUNT = 'PREMIUM_DISCOUNT',
  NET_PREMIUM = 'NET_PREMIUM',
  PRODUCT_TAX = 'PRODUCT_TAX',
  CAMPAIGN_DISCOUNT = 'CAMPAIGN_DISCOUNT',
  SERVICE_FEE = 'SERVICE_FEE',
  COMMISSION = 'COMMISSION',
  STAMP_DUTY = 'STAMP_DUTY',
  LEVY = 'LEVY',
  ADJ_NET_PREMIUM = 'ADJ_NET_PREMIUM',
}

export type TaxDetailMapType = Record<
  string,
  {
    i18n: string;
    responseKey: string[];
  }
>;
export const taxDetailMap: TaxDetailMapType = {
  [PremiumItemEnum.STANDARD_PREMIUM]: {
    i18n: i18nFn('Standard Premium'),
    responseKey: ['excludingTaxAndDiscountPremium'],
  },
  periodPlannedPremium: {
    i18n: i18nFn('Planned Premium'),
    responseKey: ['periodPlannedPremium'],
  },
  periodRegularTopUp: {
    i18n: i18nFn('Regular Top Up'),
    responseKey: ['periodRegularTopUp'],
  },
  periodSingleTopUp: {
    i18n: i18nFn('Single Top Up'),
    responseKey: ['periodSingleTopUp'],
  },
  [PremiumItemEnum.EXTRA_PREMIUM]: {
    i18n: i18nFn('Extra Premium'),
    responseKey: ['periodExtraPremium'],
  },
  [PremiumItemEnum.NO_CLAIM_DISCOUNT]: {
    i18n: i18nFn('No Claim Discount'),
    responseKey: ['periodNoClaimDiscount'],
  },
  [PremiumItemEnum.PREMIUM_DISCOUNT]: {
    i18n: i18nFn('Premium Discount'),
    responseKey: ['premiumDiscount'],
  },
  motorNcdPremium: {
    i18n: i18nFn('Motor NCD'),
    responseKey: ['motorNcdPremium'],
  },
  [PremiumItemEnum.CAMPAIGN_DISCOUNT]: {
    i18n: i18nFn('Campaign Discount'),
    responseKey: ['periodCampaignDiscount'],
  },
  [PremiumItemEnum.PRODUCT_TAX]: {
    i18n: i18nFn('Product Tax'),
    responseKey: ['tax'],
  },
  plannedAmount: {
    i18n: i18nFn('Planned Premium'),
    responseKey: ['plannedAmount'],
  },
  regularAmount: {
    i18n: i18nFn('Regular Top Up'),
    responseKey: ['regularAmount'],
  },
  singleAmount: {
    i18n: i18nFn('Single Top Up'),
    responseKey: ['singleAmount'],
  },
  [PremiumItemEnum.LEVY]: {
    i18n: i18nFn('Levy'),
    responseKey: ['periodLevy', 'levy'],
  },
  [PremiumItemEnum.STAMP_DUTY]: {
    i18n: i18nFn('Stamp Duty'),
    responseKey: ['stampDuty'],
  },
  [PremiumItemEnum.SERVICE_FEE]: {
    i18n: i18nFn('Service Fee'),
    responseKey: ['serviceFee', 'periodServiceFee'],
  },
  [PremiumItemEnum.COMMISSION]: {
    i18n: i18nFn('Commission (On Top of Premium)'),
    responseKey: ['commissionFee', 'periodCommissionFee'],
  },
  [PremiumItemEnum.NET_PREMIUM]: {
    i18n: i18nFn('Net Premium'),
    responseKey: [
      'installmentPremiumBeforeTaxAndServiceFee',
      'coverageTotalDeltaNetPremium',
    ],
  },
  [PremiumItemEnum.ADJ_NET_PREMIUM]: {
    i18n: i18nFn('Adj Net Premium'),
    responseKey: ['installmentPremiumBeforeTaxAndServiceFee'],
  },
};

export enum PolicyholderEditSchemaType {
  IssueWithoutPayment = 'issueWithoutPayment',
}

export enum IsUpdateful {
  YES = 1,
  NO = 2,
}

export enum ComponentButtonType {
  PendingCase = 'pendingcase',
}

export enum HistoryListType {
  PolicyList = 'policyList',
  UwHistory = 'uwHistory',
  ClaimHistory = 'claimHistory',
}

export enum ProductTabType {
  PolicyTaxFeeDetailList = 'policyTaxFeeDetailList',
  PremiumDiscountDetailList = 'premiumDiscountDetailList',
  CampaignDiscountDetailList = 'campaignDiscountDetailList',
  PolicyProductExtraFeeDetailList = 'policyProductExtraFeeDetailList',
  ServiceFeeDetailList = 'serviceFeeDetailList',
}

export enum RelationEnum {
  SELF = 'SELF', // 本人
  SPOUSE = 'SPOUSE', // 配偶
}

export enum CategoryDefault {
  MOTOR_FLEET = 'MOTOR_FLEET',
  MASTER_POLICY = 'MASTER_POLICY',
}

export enum SingleTopUpType {
  AD_HOC_SINGLE_TOP_UP = 'AD_HOC_SINGLE_TOP_UP',
  RECURRING_SINGLE_TOP_UP = 'RECURRING_SINGLE_TOP_UP',
}

export enum BusinessType {
  NEW_BUSINESS = 'NB',
  RENEWAL = 'RENEWAL',
}

export enum ProposalConfigAnchorIdsEnums {
  proposalFlowSetting = 'proposalFlowSetting',
  verificationCheck = 'verificationCheck',
  underwritingCheck = 'underwritingCheck',
  complianceCheck = 'complianceCheck',
  policyIssuanceRules = 'policyIssuanceRules',
  optInRules = 'optInRules',
  proposalWithdrawRule = 'proposalWithdrawRule',
  proposalReminderRule = 'proposalReminderRule',
  policySignOffRule = 'policySignOffRule',
  policyNumberGenerationRule = 'policyNumberGenerationRule',
}

export enum DocumentRelatedKey {
  policyKey = 'policyRelatedAttachment',
}

export enum ZOOM {
  Minus = 'minus',
  Plus = 'plus',
  Center = 'center',
  Fit = 'fit',
}
export enum CustomerIndustryCode {
  IndividualIndustryCode = 'industryCode',
  OrganizationIndustryCode = 'industry',
}

export enum CustomerOccupationCode {
  IndividualOccupationCode = 'OccupationCode',
  OrganizationOccupationCode = 'Occupation',
}

export const IndustryOccupationCode: string[] = [
  CustomerIndustryCode.IndividualIndustryCode,
  CustomerIndustryCode.OrganizationIndustryCode,
  CustomerOccupationCode.IndividualOccupationCode,
  CustomerOccupationCode.OrganizationOccupationCode,
];

export enum TeamTypeEnum {
  ManualAllocation = '1',
  AutomatchedFill = '2',
}

export enum ActionMenuKeyEnum {
  AddComments = 'addComments',
  UploadAttachment = 'uploadAttachment',
  Activate = 'activate',
}

export enum SaPreDefineValueType {
  FIXED = 'FIXED',
  RANGE = 'RANGE',
  ENUMURATION = 'ENUMURATION',
}
export enum RateType {
  BY_SHORT_RATE = 'BY_SHORT_RATE',
  BY_PRO_DATE = 'BY_PRO_DATE',
}

export enum SubCategoryEnum {
  MOTOR_FLEET = 'MOTOR_FLEET',
  LIFE = 'LIFE',
  MOTOR = 'MOTOR',
  UNKNOWN = 'UNKNOWN',
  NORMAL_MASTER_POLICY = 'NORMAL_MASTER_POLICY',
  EB = 'EB',
}

export enum MasterPolicyCategoryEnum {
  MasterPolicy = 'MASTER_POLICY',
  GroupPolicy = 'Group_Policy',
}

export enum MultiplierType {
  FIXED_VALUE = 'FIXED_VALUE',
  BY_FORMULA = 'BY_FORMULA',
}

/**
 * 前端用到的flag, 区分个单与团单
 */
export enum PolicyTypeEnums {
  // Tab key of antd must be string, so
  Individual = '1', // 个人
  MasterPolicy = '2', // master policy, 原团单
}

export interface SelectEnumOption {
  label?: string;
  value?: string | number;
  // 级联选项
  children?: SelectEnumOption[];
}

export enum SelfDefinedSchemaType {
  UI,
  Property,
}

/** GoodsCategoryId === ProductCategoryEnum */
export enum GoodsCategoryId {
  /** 定期险 */
  TERM_LIFE = '11',
  /** 终生险 */
  WHOLE_LIFE = '12',
  /** 两全险 */
  ENDOWMENT = '13',
  /** 年金 */
  ANNUITY = '14',
  /** 豁免险 */
  WAIVER = '15',
  /** 重疾 */
  CRITICAL_ILLNESS = '16',
  /** 医疗" */
  HOSPITAL_MEDICAL = '17',
  /** 意外 */
  ACCIDENT = '18',
  /** 投连险 */
  UNIT_LINKED = '19',
  /** 万能 */
  UNIVERSAL = '20',
  /** 变额年金 */
  VARIABLE_ANNUITY = '21',
  /** 车险 */
  AUTO = '22',
  /** 财产险 */
  PROPERTY = '24',
  /** Group Employee Benefit */
  GROUP_EMPLOYEE_BENEFIT = '33',
  /** bundle */
  BUNDLE = '80',
}

export enum MinimumInvestmentPeriodType {
  FIXED_YEAR = 'FIXED_YEAR',
}

export enum PremiumFrequencyTypeEnum {
  SINGLE = 'SINGLE',
}

export enum UploadResultEnum {
  FULL = 'FULL',
  FAILED = 'FAILED',
  SUCCESS = 'SUCCESS',
}

export enum MasterAgreementSubCategoryEnum {
  EB = 'EB',
}

export enum PolicyHolderAndInsuredEnum {
  Policyholder = 'Policyholder',
  Insured = 'Insured',
}

export enum HolderAndInsuredEnum {
  HOLDER = 'HOLDER',
  INSURED = 'INSURED',
}

export enum DistributionMethod {
  CASH_PAYOUT = 'CASH_PAYOUT',
  REINVESTMENT = 'REINVESTMENT',
}

export enum DownloadPoliciesEnum {
  pending = 'pending',
  success = 'success',
  fail = 'fail',
}

export enum AddAccountType {
  FillInTheExistingAccount = 'FillInTheExistingAccount',
  ManualEntry = 'ManualEntry',
}

export enum RuleDecisionEnum {
  ACCEPT = 'ACCEPT',
  AUTO = 'AUTO',
  DECLINE = 'DECLINE',
  DOWN_SELL_SA = 'DOWN_SELL_SA',
  EXCLUSION = 'EXCLUSION',
  EXTRA_LOADING = 'EXTRA_LOADING',
  FAIL = 'FAIL',
  MANUAL = 'MANUAL',
  MANUAL_UNBLOCK = 'MANUAL_UNBLOCK',
  PASS = 'PASS',
  POSTPONE = 'POSTPONE',
  RETURN = 'RETURN',
}

export const RuleDecisionEnumMap = {
  [RuleDecisionEnum.ACCEPT]: 'Accept',
  [RuleDecisionEnum.AUTO]: 'Auto',
  [RuleDecisionEnum.DECLINE]: 'Decline',
  [RuleDecisionEnum.DOWN_SELL_SA]: 'Down Sell SA',
  [RuleDecisionEnum.EXCLUSION]: 'Exclusion',
  [RuleDecisionEnum.EXTRA_LOADING]: 'Extra Loading',
  [RuleDecisionEnum.FAIL]: 'Fail',
  [RuleDecisionEnum.MANUAL]: 'Manual',
  [RuleDecisionEnum.MANUAL_UNBLOCK]: 'Manual&Unblock',
  [RuleDecisionEnum.PASS]: 'Pass',
  [RuleDecisionEnum.POSTPONE]: 'Postpone',
  [RuleDecisionEnum.RETURN]: 'Return',
};

export enum ComplianceResultEnum {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
  STANDARD = 'STANDARD',
  DECLINED = 'DECLINED',
}

export const ComplianceResultEnumMap = {
  [ComplianceResultEnum.ACCEPTED]: 'Accepted',
  [ComplianceResultEnum.PENDING]: 'Pending',
  [ComplianceResultEnum.REJECTED]: 'Rejected',
  [ComplianceResultEnum.STANDARD]: 'Standard',
  [ComplianceResultEnum.DECLINED]: 'Declined',
};

export enum UwDetailPath {
  NEW = 'uw-worksheet',
  OLD = 'uw-operation',
}

export enum ScopeEnum {
  LIBRARY = 'LIBRARY',
  INSTANCE = 'INSTANCE',
}

export enum AttachmentRelationScopeEnum {
  POLICY = '1',
  POLICYHOLDER = '2',
  INSURED = '3',
  OBJECT = '4',
}

export enum AttachmentRefTypeEnum {
  POLICYHOLDER = 'POLICY_HOLDER',
  INSURED = 'INSURANT',
  OBJECT = 'AUTO',
  POLICY = 'POLICY',
  ISSUANCE = 'ISSUANCE',
}

export enum MasterPolicyUploadModule {
  BatchUpload = 'BatchUpload',
  MasterPolicy = 'MasterPolicy',
}

export enum VehicleOriginEnum {
  CAR_DEALER = 'CAR_DEALER',
  OTHER = 'OTHER',
}

export enum VehicleColorEnum {
  Red = '1',
  White = '4',
  Black = '3',
  Green = '2',
}

export enum OptMode {
  SAVE = 'save',
  EDIT = 'edit',
}

// POLICY STATUS
export enum PolicyStatusUpperEnum {
  POLICY_EFFECT = 'POLICY_EFFECT',
  TERMINATION = 'TERMINATION',
  LAPSED = 'LAPSED',
  EXPIRY = 'EXPIRY',
}

export enum BusinessTypeEnum {
  POLICY_QUERY = 'POLICY_QUERY',
  PROPOSAL_QUERY = 'PROPOSAL_QUERY',
}

export enum MasterAgreementStatus {
  WAITING_FOR_ISSUANCE = 'WAITING_FOR_ISSUANCE',
  WAITING_FOR_COMPLIANCE_CHECK = 'WAITING_FOR_COMPLIANCE_CHECK',
  DATA_ENTRY_IN_PROGRESS = 'DATA_ENTRY_IN_PROGRESS',
  LAPSED = 'LAPSED',
  TERMINATION = 'TERMINATION',
  POLICY_EFFECT = 'POLICY_EFFECT',
}

export enum DueDateCompareEnum {
  AFTER = '15',
  SAME_AS = '14',
  BEFORE = '13',
}

export enum PolicyTypeNameEnum {
  NORMAL = 'NORMAL',
  GROUP_POLICY = 'GROUP_POLICY',
  BY_EVENT = 'BY_EVENT',
  BY_EVENT_STANDALONE = 'BY_EVENT_STANDALONE',
  RELATIONAL = 'RELATIONAL',
  MASTER_POLICY = 'MASTER_POLICY',
}

export enum IssuanceTaskTypeEnum {
  NEW_BUSINESS = 'NEW_BUSINESS',
  RENEW = 'RENEW',
}
