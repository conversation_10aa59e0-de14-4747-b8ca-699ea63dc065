import React from 'react';

import { UploadFile } from 'antd/lib/upload/interface.d';

import { ColumnEditingType } from '@zhongan/nagrand-ui';

import {
  BizDict,
  DataTypeEnum,
  FieldType,
  Mode,
  YesOrNo,
} from 'genesis-web-component/lib/interface/enum.interface';

import { TypeEnums } from '@uw/interface/enum.interface';

export enum MultipleType {
  Multiple = 'multiple',
}
export interface FieldDataType {
  [index: string]: unknown;
  label?: React.ReactElement | string;
  key?: string | string[];
  options?: BizDict[] | unknown[];
  placeholder?: string | [string, string];
  type?: FieldType | MultipleType.Multiple | DataTypeEnum;
  col?: number;
  format?: string;
  children?: FieldDataType[];
  render?: () => JSX.Element;
  readOnly?: boolean; // form展示功能使用（非操作）
  bordered?: boolean;
  className?: string;
  readPretty?: boolean;
}

export interface CreatePolictType {
  paymentMethod?: string;
  premiumCollectionTime?: moment.Moment;
  premiumCollection?: YesOrNo;
  policyFiles: UploadFile[];
}

export interface FileType extends UploadFile {
  creator?: string;
  gmtCreated?: string;
  comments?: string;
  id?: number;
  optType?: Mode;
  fileUniqueCode?: string;
  canEdit?: YesOrNo;
  fileUrl?: string;
  curMaterPolicyNo?: string;
}

export interface Relationship {
  type?: string;
  relationNo?: string;
}

export interface NbConfigurationFields<T> {
  title?: string;
  inlineEdit: boolean;
  typeEnum?: TypeEnums;
  columns: ColumnEditingType<T>[];
  tooltip?: string;
  tips?: string;
}
