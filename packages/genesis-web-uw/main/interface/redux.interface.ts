import { Theme } from '@zhongan/nagrand-ui';

export interface ReduxProps {
  enums?: Record<
    string,
    {
      child: undefined | string[];
      dictDesc: undefined | string;
      dictKeyName: undefined | string;
      dictValue: string;
      dictValueName: string;
      itemExtend1: string;
      itemName: string;
    }[]
  >;
  locale?: string;
  permissions?: Record<string, boolean>;
  tenant?: string;
  state?: {
    clientConfig: {
      security: boolean;
    };
  };
  theme?: Theme;
  messageDuration?: number;
}
