import { Dispatch, ReactNode, SetStateAction } from 'react';

import { UploadFile } from 'antd/es/upload/interface';

import { Form } from '@formily/core';
import { Schema } from '@formily/react';

import {
  BizDict,
  Mode,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  AttachmentSaveParamType,
  FactorsType,
  IssuancePremium,
  MedicalExaminationType,
  PlaceholderEnum,
  QuotationProductInfoType,
  UwHistoryTypeV2,
} from 'genesis-web-service';

export type SetState<T> = Dispatch<SetStateAction<T>>;

export type UnknownObjectRecord = Record<string, unknown>;

export type StringObjectRecord = Record<string, string>;

export type InferObjectRecord<T> = Record<string, T>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SchemaType = Schema<any, any, any, any, any, any, any, any, any>;

export interface AttachmentParamType extends AttachmentSaveParamType {
  optType?: Mode;
}
export interface AttachmentUpdateType {
  attachments?: AttachmentParamType[];
  deletedNoteIds?: number[];
  attachmentType: string;
}
export interface ErrorType {
  message: string;
}

export const PDF = ['pdf'];
export const DOC = ['doc', 'docx'];
export const IMG = ['jpg', 'jpeg', 'png'];
export const XLS = ['xlsx', 'xls'];

export const NEWBUSINESS = 'NewBusiness';

export interface MedicalExaminationItemType
  extends Omit<MedicalExaminationType, 'medicalReport'> {
  medicalReport?: UploadFile[];
}

export interface ExceptionResponse {
  message: string;
}
export type MessageType = 'error' | 'success' | 'warn';

export interface SalesChannelList {
  channelCode: string;
  goodsId: number;
  partnerType: PlaceholderEnum;
  partyId: number;
}
export interface SortInfoType {
  field: string;
  order: string;
}

export interface ProposalTabelData {
  [key: string]: Proposal[];
}

export interface ProposalTableDataSource {
  accumulatedConfig: (Proposal & { key: string })[];
  separatedConfig: (Proposal & { key: string })[];
}

export interface UwFactorsType extends FactorsType {
  value: string;
  [index: string]: unknown;
}

export interface Proposal {
  channelCode: string;
  goodsCodes: string[];
  key: string;
}
export interface GoodsObj {
  [value: number]: {
    enumItemName?: string;
    itemName?: string;
    value?: number;
  };
}

export interface PendingCaseItem {
  bizModule: string;
  creator: string;
  deleted: boolean;
  effectivePeriod: number;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  modifier: string;
  pendingCaseType: string;
  reminderFrequency: number;
}
export interface PendingCaseResponseType {
  PENDING_FOR_CUSTOMER: PendingCaseItem[];
}
export type UwHistoryTableType = UwHistoryTypeV2 & {
  rowSpan?: number;
};

export interface VerificationFactorsType extends FactorsType {
  value: string;
  [index: string]: unknown;
}

export interface PremiumStructureItemType {
  title: string;
  key: keyof IssuancePremium;
}

export type PickQuotationProductListInfoType = Pick<
  Partial<QuotationProductInfoType>,
  | 'liabilityList'
  | 'productDiscountList'
  | 'productTaxList'
  | 'campaignDiscountList'
  | 'extraLoadingList'
  | 'productName'
>;

export interface CoverageTypeItem {
  coverageTypeName: string;
  liabilityId: string;
  interestModelBaseList: {
    interestName: string;
    interestId: string;
    isOptional: number;
  }[];
  mutexLiabilityIds: string[];
}

export interface ObjectInfoType extends Record<string, unknown> {
  objectCategory?: string;
  objectSubCategory?: string;
}

export interface CoverageData {
  title?: string;
  objectTypeList?: {
    objectCategory?: string;
    objectSubCategory?: string;
  }[];
  sumInsured?: string;
  rate?: string;
  annualPremium?: string;
  periodStandardPremium?: string;
  limitPerOccurance?: string;
  aggregateLimit?: string;
  deductibleAmount?: string;
  interestList?: CoverageData[];
  liabilityRefId?: string;
  liabilityId?: string;
  liabilityInfo?: Record<string, unknown>;
  interestId?: string;
  [propName: string]: unknown;
}
export interface IssuanceInsuredMachineryEquipment {
  remark?: string;
  equipmentInfoList?: Record<string, unknown>[];
}

export interface GoodsObjType {
  channelType: string;
  packages: BizDict[];
}

// 已导出到 web-component
export interface CommonSectionProps<T extends Record<string, unknown>> {
  title: string | ReactNode;
  form?: Form<T>;
  id: string;
  render: SchemaType;
}

export interface AttachmentRequestParam {
  goodsId: number;
  goodsCode: string;
  goodsCategoryId: number;
  packageCode: string;
}

export interface RefDetail {
  refType: string;
  refId: number;
  holderCustomerId: number;
  ifInsureObject?: boolean;
}

export interface ToolBarContentConfig {
  key: string;
  title: string;
  component: ReactNode;
  isModal?: boolean;
}
