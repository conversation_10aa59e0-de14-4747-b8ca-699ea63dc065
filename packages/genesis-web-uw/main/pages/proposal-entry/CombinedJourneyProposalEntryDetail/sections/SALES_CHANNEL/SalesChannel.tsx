import {
  Ref,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import {
  Field,
  createForm,
  onFieldInputValueChange,
  onFieldReact,
  onFormInitialValuesChange,
  onFormValuesChange,
} from '@formily/core';
import { FormProvider } from '@formily/react';

import { useAsyncEffect, useDebounceFn, useUpdateEffect } from 'ahooks';
import { useAtom, useAtomValue } from 'jotai';
import { filter, find, head, isEmpty, size, unionBy } from 'lodash-es';

import { SectionContainer, SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { MdmIssuanceStatus } from 'genesis-web-service';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import { useProposalEntryState } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.Provider';
import {
  hasEditAuthAtom,
  isPosProposalEntryScenarioAtom,
  isReadPrettyAtom,
  masterSalesChannelListAtom,
  salesChannelAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import {
  AgreementListItem,
  ChannelListItem,
  queryAgreement,
  queryChannel,
} from '@uw/pages/proposal-entry/hooks/request';

import { AGREEMENT_NO_PATH } from '../../constant';
import { useValidateFunc } from '../../hooks/useValidateFunc';
import { SectionProps } from '../interface';
import { SchemaField } from './SchemaField';

const resetChannelValues = {
  agreementCode: undefined,
  channelType: undefined,
  channelCode: undefined,
  channelName: undefined,
  mobilePhone: undefined,
  email: undefined,
};

const SalesChannel = forwardRef(
  (
    { title, id, render, order, form: antForm }: SectionProps,
    ref: Ref<unknown>
  ) => {
    const { t } = useTranslation(['uw', 'common']);
    const hasEditAuth = useAtomValue(hasEditAuthAtom);
    const readPretty = useAtomValue(isReadPrettyAtom);
    const isPosProposalEntryScenario = useAtomValue(
      isPosProposalEntryScenarioAtom
    );
    const masterSalesChannelList = useAtomValue(masterSalesChannelListAtom);
    const [salesChannel, setSalesChannel] = useAtom(salesChannelAtom);
    const agreementListRef = useRef<AgreementListItem[]>();
    const allChannelNameListRef = useRef<ChannelListItem[]>();
    const allChannelCodeListRef = useRef<ChannelListItem[]>();
    const channelTypes = useBizDictByKey('channelType');
    const { issuanceStatus, detail } = useProposalEntryState();
    const { run: debounceDispatch } = useDebounceFn(formInst => {
      setSalesChannel({ ...formInst.values });
    });
    const disabled =
      issuanceStatus === MdmIssuanceStatus.WAITING_FOR_ISSUANCE ||
      issuanceStatus === MdmIssuanceStatus.EFFECTIVE ||
      isPosProposalEntryScenario; // pos 场景下不允许编辑 sale channel

    const globalPattern = useMemo(
      () =>
        readPretty
          ? 'readPretty'
          : !hasEditAuth || disabled
            ? 'disabled'
            : 'editable',
      [readPretty, disabled, hasEditAuth]
    );

    const form = useMemo(
      () =>
        createForm({
          pattern: globalPattern,
          initialValues: detail?.policySalesChannel,
          effects() {
            onFormInitialValuesChange(formInst => {
              // 不对 salesChannel 进行操作时，也可以获取到表单信息 更改之后触发下方的 onFormValuesChange

              debounceDispatch(formInst);
            });
            onFormValuesChange(formInst => {
              formInst.validate();
              debounceDispatch(formInst);
            });
          },
        }),
      [globalPattern, detail?.policySalesChannel]
    );

    useEffect(() => {
      form.setPattern(
        readPretty
          ? 'readPretty'
          : !hasEditAuth || disabled
            ? 'disabled'
            : 'editable'
      );
    }, [hasEditAuth, readPretty, form]);

    const queryChannelByAgreement = useCallback(
      async (field: Field) => {
        if (field?.value) {
          const channelCodeField = form.query('channelCode').take() as Field;
          const channelNameField = form.query('channelName').take() as Field;
          try {
            field.setLoading(true);
            channelCodeField.setLoading(true);
            channelNameField.setLoading(true);
            const list = await queryChannel({ agreementCode: field.value });
            form.setFieldState('channelCode', {
              dataSource: list,
            });
            form.setFieldState('channelName', {
              dataSource: list?.map(channel => ({
                ...channel,
                value: channel.name,
                label: channel.name,
              })),
            });
            if (size(list) === 1) {
              const currChannel = head(list);
              form.setValues({
                channelCode: currChannel?.value,
                channelName: currChannel?.name,
                channelType: currChannel?.salesChannelType,
                mobilePhone: currChannel?.hotLine,
                email: currChannel?.email,
              });
            }
          } finally {
            field.setLoading(false);
            channelCodeField.setLoading(false);
            channelNameField.setLoading(false);
          }
        }
      },
      [form]
    );

    const queryAgreementByChannel = useCallback(
      async (
        field: Field,
        options: { fieldKey: string; salesCode: string }
      ) => {
        const agreementCodeField = form.query('agreementCode').take() as Field;
        const channelCodeField = form.query('channelCode').take() as Field;
        const channelNameField = form.query('channelName').take() as Field;

        try {
          const { fieldKey, salesCode } = options;
          agreementCodeField.setLoading(true);
          channelCodeField.setLoading(true);
          channelNameField.setLoading(true);
          const list = await queryAgreement(salesCode);
          form.setFieldState('agreementCode', {
            dataSource: list,
            value: size(list) === 1 ? head(list)?.value : undefined,
          });
          const currChannelList = await queryChannel({
            salesCodeOrNameLike: salesCode,
          });
          const currChannel = find(currChannelList, [
            fieldKey === 'channelCode' ? 'code' : 'name',
            field.value,
          ]);
          form.setValues({
            channelName: currChannel?.name,
            channelType: currChannel?.salesChannelType,
            mobilePhone: currChannel?.hotLine,
            email: currChannel?.email,
          });
          if (currChannel) {
            form.setFieldState('channelCode', {
              dataSource: [currChannel],
            });
            form.setFieldState('channelName', {
              dataSource: [
                {
                  ...currChannel,
                  label: currChannel.name,
                  value: currChannel.name,
                },
              ],
            });
          }
        } finally {
          agreementCodeField.setLoading(false);
          channelCodeField.setLoading(false);
          channelNameField.setLoading(false);
        }
      },
      [form]
    );

    useEffect(() => {
      form.addEffects('agreementCode', () => {
        onFieldReact('agreementCode', field => {
          const { value } = field as Field;
          const channelCode = form.getFieldState('channelCode')?.value;
          if (!channelCode) {
            form.setFieldState('agreementCode', {
              dataSource: value
                ? filter(agreementListRef.current, ['value', value])
                : agreementListRef.current,
            });
          }
        });

        onFieldInputValueChange('agreementCode', async field => {
          await queryChannelByAgreement(field);
        });
      });

      return () => {
        form.removeEffects('agreementCode');
      };
    }, [form, queryChannelByAgreement]);

    useEffect(() => {
      form.addEffects('channel', () => {
        onFieldReact('channelCode', (field, innerForm) => {
          const { value } = field as Field;
          const agreementCode = form.getFieldState('agreementCode')?.value;
          if (isEmpty(value) && isEmpty(agreementCode)) {
            innerForm.setFieldState('channelCode', {
              dataSource: allChannelCodeListRef.current,
            });
          }
        });
        onFieldInputValueChange('channelCode', async (field, innerForm) => {
          if (!isEmpty(field.value)) {
            await queryAgreementByChannel(field, {
              fieldKey: 'channelCode',
              salesCode: field.value,
            });
          } else {
            innerForm.setFieldState('agreementCode', {
              dataSource: agreementListRef.current,
            });
          }
        });
        onFieldReact('channelName', (field, innerForm) => {
          const { value } = field as Field;
          const agreementCode = form.getFieldState('agreementCode')?.value;
          const channelCode = form.getFieldState('channelCode')?.value;
          if (
            isEmpty(value) ||
            (isEmpty(agreementCode) && isEmpty(channelCode))
          ) {
            innerForm.setFieldState('channelName', {
              dataSource: allChannelNameListRef.current,
            });
          }
        });

        onFieldInputValueChange('channelName', async (field, innerForm) => {
          if (isEmpty(field.value)) {
            innerForm.setFieldState('agreementCode', {
              dataSource: agreementListRef.current,
            });
            form.setValues({
              agreementCode: undefined,
              channelType: undefined,
              channelCode: undefined,
              mobilePhone: undefined,
              email: undefined,
            });
          }

          if (!field.value) {
            form.setValues({
              agreementCode: undefined,
              channelCode: undefined,
            });
            return;
          }

          const agreementCodeField = form
            .query('agreementCode')
            .take() as Field;
          const channelCodeField = form.query('channelCode').take() as Field;

          let channels;
          try {
            field.setLoading(true);
            agreementCodeField.setLoading(true);
            channelCodeField.setLoading(true);

            channels = await queryChannel({
              salesCodeOrNameLike: field.value,
            });
          } finally {
            field.setLoading(false);
            agreementCodeField.setLoading(false);
            channelCodeField.setLoading(false);
          }
          const channelList = filter(channels, ['name', field.value]);
          if (size(channelList) === 1) {
            const { code, salesChannelType, hotLine, email } = head(
              channelList
            ) as ChannelListItem;
            form.setValues({
              channelCode: code,
              channelType: salesChannelType,
              mobilePhone: hotLine,
              email,
            });
            queryAgreementByChannel(form.query('channelName').take() as Field, {
              fieldKey: 'channelName',
              salesCode: code,
            });
          } else {
            // 重新选择channelName,如果存在多个channelList需要清空其他联动项防止有旧值
            form.setValues({
              agreementCode: undefined,
              channelCode: undefined,
            });
            if (size(channelList) > 0) {
              form.setFieldState('channelCode', {
                dataSource: channelList,
              });
            } else {
              form.setFieldState('channelCode', {
                dataSource: channels,
              });
            }
          }
        });
      });

      return () => {
        form.removeEffects('channel');
      };
    }, [form, queryAgreementByChannel]);

    useUpdateEffect(() => {
      form.setValues({
        ...(salesChannel
          ? { ...resetChannelValues, ...salesChannel }
          : resetChannelValues),
      });
    }, [salesChannel]);

    useEffect(() => {
      const channelType = channelTypes?.map(
        ({ dictValueName, enumItemName }) => ({
          label: dictValueName,
          value: enumItemName,
        })
      );
      form.setFieldState('channelType', {
        dataSource: channelType,
      });
    }, [form]);

    useAsyncEffect(async () => {
      try {
        let masterChannelList: ChannelListItem[] = [];
        let masterAgreementList: string[] = [];
        if (masterSalesChannelList) {
          masterChannelList = masterSalesChannelList.channel;
          masterAgreementList = masterSalesChannelList.agreement;
        }
        const [allChannel, allAgreement] = await Promise.all([
          queryChannel(),
          queryAgreement(),
        ]);
        const channel =
          size(masterChannelList) > 0 ? masterChannelList : allChannel;
        const agreement =
          size(masterAgreementList) > 0
            ? allAgreement?.filter(agreement =>
                masterAgreementList.includes(agreement.agreementCode)
              )
            : allAgreement;
        const channelNameList = unionBy(channel, 'name').map(item => ({
          ...item,
          label: item.name,
          value: item.name,
        }));
        agreementListRef.current = agreement;
        allChannelCodeListRef.current = channel;
        allChannelNameListRef.current = channelNameList;

        const agreementNo = antForm.getFieldValue(AGREEMENT_NO_PATH);
        const pattern = readPretty
          ? 'readPretty'
          : !isEmpty(agreementNo) && size(masterChannelList) === 1
            ? 'disabled'
            : globalPattern;
        form.setFieldState('channelCode', {
          dataSource: channel,
          pattern,
        });
        form.setFieldState('channelName', {
          dataSource: channelNameList,
          pattern,
        });
        form.setFieldState('agreementCode', {
          dataSource: agreement,
          pattern,
        });
      } catch (error) {
        message.error(error);
      }
    }, [
      form,
      masterSalesChannelList,
      globalPattern,
      readPretty,
      antForm.getFieldValue(AGREEMENT_NO_PATH),
    ]);

    useImperativeHandle(ref, () => ({
      async onSubmit() {
        return form.submit();
      },
    }));

    const validateSalesChannel = useCallback(
      () =>
        new Promise((res, rej) =>
          form
            .validate()
            .then(() => res(true))
            .catch(() =>
              // eslint-disable-next-line prefer-promise-reject-errors
              rej({
                section: id,
              })
            )
        ),
      [form, id]
    );

    useValidateFunc({ id, promise: validateSalesChannel, order });

    return (
      <SectionContainer>
        <section id={id}>
          <SimpleSectionHeader weight={500} type={'h4'} className="mb-4">
            {title}
          </SimpleSectionHeader>
          <FormProvider form={form}>
            <SchemaField
              scope={{
                t,
                // fetchAgreement,
                // fetchChannel,
              }}
              schema={render}
            />
          </FormProvider>
        </section>
      </SectionContainer>
    );
  }
);

export { SalesChannel };
