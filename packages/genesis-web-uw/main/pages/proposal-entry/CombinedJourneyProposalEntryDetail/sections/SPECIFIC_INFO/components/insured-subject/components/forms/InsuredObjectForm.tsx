import { FC, MutableRefObject, useMemo } from 'react';

import { Form } from 'antd';
import { NamePath } from 'antd/es/form/interface';

import cls from 'clsx';
import { findIndex, isNumber, sortBy } from 'lodash-es';

import { Divider } from 'genesis-web-component/lib/components';
import { Switcher } from 'genesis-web-component/lib/components/Switcher';
import { FactorsType, ObjectComponentEnum } from 'genesis-web-service';

import {
  INSURED_OBJECT_LIST,
  InsuredObjectProps,
  addUniqueKeyField,
  useGetIsMaxAllowedNumber,
  useObjectInfo,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/components/insured-subject';
import { ObjectBasicInfo } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/sub-sections/OBJECT_INFO/components';

import { CommonList, PersonList, ProductList } from '../lists';
import { ObjectTypeFormItems } from './ObjectTypeFormItems';

interface Props extends InsuredObjectProps {
  fieldName?: NamePath;
  insuredDrawerVisible?: boolean;
  nestedInBg?: boolean; // 在location object下的表格需要特殊header底色
  fileUniqueCodeListRef?: MutableRefObject<string[]>;
  newRowListRef?: MutableRefObject<string[]>;
  isAloneObjects?: boolean;
}
interface ObjectComponentFactorType {
  componentType: ObjectComponentEnum;
  factors: FactorsType[];
}
type UnionSingleComponentFactorType = FactorsType & {
  componentType: ObjectComponentEnum;
};
// 按title的i18n首字母排序
const MultipleObjectComponentTypes = [
  /** Employee Category */
  ObjectComponentEnum.EMPLOYEE_GROUP,
  /** Machinery Equipments */
  ObjectComponentEnum.MACHINERY_EQUIPMENT,
  /** Person list */
  ObjectComponentEnum.EMPLOYEE,
  /** Product list */
  ObjectComponentEnum.PRODUCT,
  /** Property of Another Person */
  ObjectComponentEnum.PROPERTY,
  /** Electronic Equipments */
  ObjectComponentEnum.ELECTRONIC_EQUIPMENT,
  /** Precious Item */
  ObjectComponentEnum.PRECIOUS_ITEM,
  /** Trip */
  ObjectComponentEnum.TRIP,
];

const getFormItemName = (curKey: string, fieldName?: NamePath) =>
  isNumber(fieldName) ? [fieldName, curKey] : curKey;

export const InsuredObjectForm: FC<Props> = ({
  form,
  fieldName,
  insuredObjectTypes,
  isLocation,
  insuredDrawerVisible,
  nestedInBg = false,
  fileUniqueCodeListRef,
  newRowListRef,
  isAloneObjects,
}) => {
  const { categorizedFactors, enumsMap, categorizedEnumsMap } = useObjectInfo();

  const fieldKey = isNumber(fieldName)
    ? [INSURED_OBJECT_LIST, fieldName, 'objectType']
    : 'objectType';
  const objectType = Form.useWatch(fieldKey, form);
  const getIsMaxAllowedNumber = useGetIsMaxAllowedNumber();
  const isMaxAllowedNumber = getIsMaxAllowedNumber(objectType);

  // 区分开可添加多条数据以表格形式展示的的object component 和 只有一条数据以表单形式展示的object component
  const {
    singleObjectComponents: singleObjectComponentsInfo,
    multipleObjectComponents: multipleObjectComponentsInfo,
  } = useMemo(() => {
    const objectComponentFactorMap = categorizedFactors[objectType];
    const singleObjectComponents: ObjectComponentFactorType[] = [];
    const multipleObjectComponents: ObjectComponentFactorType[] = [];
    Object.entries(objectComponentFactorMap ?? {}).forEach(([key, factors]) => {
      const componentFactor = {
        componentType: key,
        factors,
      } as ObjectComponentFactorType;
      if (key === ObjectComponentEnum.PRODUCT && isMaxAllowedNumber) {
        singleObjectComponents.push(componentFactor);
      } else if (
        MultipleObjectComponentTypes.includes(key as ObjectComponentEnum)
      ) {
        multipleObjectComponents.push(componentFactor);
      } else {
        singleObjectComponents.push(componentFactor);
      }
    });
    return {
      // 把纯表单展示的component要素合并到一起展示在页面上方，componentType用于处理nest form情况
      singleObjectComponents: singleObjectComponents?.reduce(
        (cur, next) => [
          ...cur,
          ...next.factors.map(factor => ({
            ...factor,
            componentType: next.componentType,
          })),
        ],
        [] as UnionSingleComponentFactorType[]
      ),
      multipleObjectComponents: sortBy(multipleObjectComponents, object =>
        findIndex(
          MultipleObjectComponentTypes,
          sortObject => object.componentType === sortObject
        )
      ),
    };
  }, [categorizedFactors, isMaxAllowedNumber, objectType]);

  return (
    <>
      <ObjectTypeFormItems
        form={form!}
        insuredObjectTypes={insuredObjectTypes}
        isLocation={isLocation}
        fieldName={fieldName}
        isAloneObjects={isAloneObjects}
      />

      <ObjectBasicInfo
        curTabData={{}}
        categorizedFactors={addUniqueKeyField(singleObjectComponentsInfo)}
        // 用于处理根据component分块处理formItem的
        nestFieldNameKey={'componentType'}
        enumsMap={enumsMap}
        categorizedEnumsMap={categorizedEnumsMap?.[objectType]}
        form={form!}
        fieldName={fieldName}
        colSpan={6}
        needExtensionPath
      />
      {multipleObjectComponentsInfo.map((objectComponent, index) => (
        <div key={`${objectComponent.componentType}-${index}`}>
          <Divider className={cls('!my-md', index !== 0 && '!mt-10')} />
          <Switcher.Alone
            visible={
              ![
                // Person/Product list有自己的组件
                ObjectComponentEnum.EMPLOYEE,
                ObjectComponentEnum.PRODUCT,
              ].includes(objectComponent.componentType)
            }
          >
            <Form.Item
              name={getFormItemName(objectComponent.componentType, fieldName)}
            >
              <CommonList
                title={objectComponent.componentType}
                factors={objectComponent.factors}
                enumsMap={
                  categorizedEnumsMap?.[objectType]?.[
                    objectComponent.componentType
                  ]
                }
                insuredDrawerVisible={insuredDrawerVisible}
                nestedInBg={nestedInBg}
              />
            </Form.Item>
          </Switcher.Alone>
          <Switcher.Alone
            visible={
              objectComponent.componentType === ObjectComponentEnum.PRODUCT &&
              !isMaxAllowedNumber
            }
          >
            {/* 由于后端定位不到product, employee 所以强行弄个FormItem给后端空的数据结构，匪夷所思 */}
            <Form.Item
              name={getFormItemName(objectComponent.componentType, fieldName)}
            >
              <ProductList
                form={form!}
                categorizedFactors={objectComponent.factors.map(item => ({
                  ...item,
                  componentType: objectComponent.componentType,
                }))}
                enumsMap={
                  categorizedEnumsMap?.[objectType]?.[
                    objectComponent.componentType
                  ]
                }
                categorizedEnumsMap={categorizedEnumsMap?.[objectType]}
                fieldName={fieldName}
                isLocation={isLocation}
                nestedInBg={nestedInBg}
                objectType={objectType}
                fileUniqueCodeListRef={fileUniqueCodeListRef}
                newRowListRef={newRowListRef}
              />
            </Form.Item>
          </Switcher.Alone>
          <Switcher.Alone
            visible={
              objectComponent.componentType === ObjectComponentEnum.EMPLOYEE
            }
          >
            {/* 由于后端定位不到product, employee 所以强行弄个FormItem给后端空的数据结构，匪夷所思 */}
            <Form.Item
              name={getFormItemName(objectComponent.componentType, fieldName)}
            >
              <PersonList
                form={form!}
                categorizedFactors={objectComponent.factors}
                enumsMap={
                  categorizedEnumsMap?.[objectType]?.[
                    objectComponent.componentType
                  ]
                }
                fieldName={fieldName}
                isLocation={isLocation}
                nestedInBg={nestedInBg}
                objectType={objectType}
                fileUniqueCodeListRef={fileUniqueCodeListRef}
                newRowListRef={newRowListRef}
              />
            </Form.Item>
          </Switcher.Alone>
        </div>
      ))}
    </>
  );
};
