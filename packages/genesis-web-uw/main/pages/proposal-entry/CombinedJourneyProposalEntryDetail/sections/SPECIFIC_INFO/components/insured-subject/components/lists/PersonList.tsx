import {
  FC,
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, PaginationProps } from 'antd';
import { NamePath } from 'antd/es/form/interface';
import { ColumnProps } from 'antd/es/table';

import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import { find, isEmpty, size } from 'lodash-es';

import {
  EditableTable,
  FilterDropdownInput,
  Icon,
  PaginationType,
  TextBody,
  message,
} from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface';
import {
  CommercialLineService,
  CommercialLineTypes,
  FactorsType,
  ObjectComponentEnum,
  ObjectType,
  PolicyStage,
  PosFileType,
  ProposalCommonService,
  ProposalCommonTypes,
  ProposalService,
} from 'genesis-web-service';

import { useAppElement2TableColumns } from '@uw/hook/useAppElementsToFields';
import { embedProposalEntryPropsAtom } from '@uw/pages/embed/proposal-entry/atom';
import styles from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.module.scss';
import {
  activeTabKeyAtom,
  asComponentAtom,
  commercialLinePolicyInsuredObjectListAtom,
  hasEditAuthAtom,
  isPosProposalEntryScenarioAtom,
  isQueryAtom,
  isReadPrettyAtom,
  policyListAtom,
  temporaryIdAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { ClearAllButton } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/ClearAll';
import { TemplateDownload } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/TemplateDownload';
import { UploadFile } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/UploadFile';
import { useSpecificInfo } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/SpecificInfo.Provider';
import {
  INSURED_OBJECT_LIST,
  getFieldKey,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/components/insured-subject';

interface PersonListProps {
  categorizedFactors: FactorsType[] | undefined;
  form: FormInstance;
  enumsMap: Record<string, BizDict[]>;
  isLocation?: boolean;
  fieldName?: NamePath;
  nestedInBg?: boolean; // 在location object下的表格需要特殊header底色
  objectType?: ObjectType;
  fileUniqueCodeListRef?: MutableRefObject<string[]>;
  newRowListRef?: MutableRefObject<string[]>;
}

const initialPagination = {
  current: 1,
  pageSize: 10,
};

export const PersonList: FC<PersonListProps> = ({
  categorizedFactors,
  enumsMap,
  form,
  fieldName,
  isLocation,
  nestedInBg = false,
  objectType,
  fileUniqueCodeListRef,
  newRowListRef,
}) => {
  const { t } = useTranslation();
  const appElement2TableColumns = useAppElement2TableColumns(
    categorizedFactors!,
    enumsMap,
    {
      isNeedNoColumn: false,
    }
  );

  const readPretty = useAtomValue(isReadPrettyAtom);
  const hasEditAuth = useAtomValue(hasEditAuthAtom);
  const asComponent = useAtomValue(asComponentAtom);
  const stage = asComponent ? PolicyStage.UNDERWRITING : undefined;
  const isQuery = useAtomValue(isQueryAtom);
  const { uniqueKey } = useSpecificInfo();
  const commercialLinePolicyInsuredObjectList = useAtomValue(
    commercialLinePolicyInsuredObjectListAtom
  );
  const isPosProposalEntryScenario = useAtomValue(
    isPosProposalEntryScenarioAtom
  );
  const embedProposalEntryProps = useAtomValue(embedProposalEntryPropsAtom);
  const employeeObjectService =
    embedProposalEntryProps?.service?.employeeObject;

  const businessData = embedProposalEntryProps?.service?.businessData;

  const getEmployeeObjectList =
    employeeObjectService?.getEmployeeObjectList ??
    CommercialLineService.getEmployeeObjectList;
  const addEmployeeObject =
    employeeObjectService?.addEmployeeObject ??
    CommercialLineService.addEmployeeObject;

  const updateEmployeeObject =
    employeeObjectService?.updateEmployeeObject ??
    CommercialLineService.updateEmployeeObject;

  const deleteEmployeeObject =
    employeeObjectService?.deleteEmployeeObject ??
    CommercialLineService.deleteEmployeeObject;

  const submitService =
    isPosProposalEntryScenario && businessData?.submitBusinessData
      ? (data: Record<string, unknown>) => {
          return businessData?.submitBusinessData({
            ...data,
            posFileType: PosFileType.LiabilityPerson,
          });
        }
      : ProposalCommonService.uploadFile;
  const uploadFileAndCheckData =
    isPosProposalEntryScenario && businessData?.uploadFileAndCheckData
      ? (formData: FormData) => {
          formData.append('posFileType', PosFileType.LiabilityPerson);
          formData.append('objectType', objectType ?? '');
          return businessData?.uploadFileAndCheckData(formData);
        }
      : undefined;
  const downloadBusinessData =
    isPosProposalEntryScenario && businessData?.downloadBusinessData
      ? (data?: Record<string, unknown>) =>
          businessData?.downloadBusinessData(PosFileType.LiabilityPerson, data)
      : ProposalCommonService.downloadBusinessData;
  const clearAllData =
    isPosProposalEntryScenario && businessData?.clearAllBusinessData
      ? // @ts-expect-error 受限于 clearAllData 原回调参数
        (businessType, _temporaryId, uniqueKey) => {
          return businessData?.clearAllBusinessData(businessType, {
            uniqueKey,
          });
        }
      : ProposalCommonService.clearAllData;
  const downloadBusinessTemplate =
    isPosProposalEntryScenario && businessData?.downloadBusinessTemplate
      ? (
          ...[
            _businessType,
            _goodsId,
            _planId,
            _packageId,
            _temporaryId,
            objectType,
          ]: Parameters<typeof ProposalCommonService.downloadBusinessTemplate>
        ) =>
          businessData?.downloadBusinessTemplate(PosFileType.LiabilityPerson, {
            objectType,
          })
      : ProposalCommonService.downloadBusinessTemplate;

  const activeTabKey = useAtomValue(activeTabKeyAtom);
  const policyList = useAtomValue(policyListAtom);

  const [goodsId, planId, packageId] = useMemo(() => {
    const goods = policyList.find(policy => policy.uniqueKey === activeTabKey)
      ?.goodsList?.[0];
    return [goods?.goodsId, goods?.planId, goods?.packageId];
  }, [activeTabKey, policyList]);

  const objectUniqueKey: string = Form.useWatch(
    getFieldKey('uniqueKey', fieldName),
    form
  );
  const temporaryId = useAtomValue(temporaryIdAtom);

  const [submitting, setSubmitting] = useState(false);
  const [pagination, setPagination] = useState(initialPagination);
  const [queryPagination, setQueryPagination] = useState<
    { paginationType?: PaginationType } & PaginationProps
  >(initialPagination);

  // 正在编辑的数据，当editRecord存在时，upload与clear all要disabled,否则点击add new之后，再点击upload按钮，会导致add new按钮一直disabled
  const [editRecord, setEditRecord] =
    useState<Partial<CommercialLineTypes.EmployeeObject>>();
  const [filters, setFilters] =
    useState<Partial<CommercialLineTypes.EmployeeObject>>(null);

  const [dataSource, setDataSource] =
    useState<CommercialLineTypes.EmployeeObject[]>();

  const onFilterObj = useCallback<
    (dataIndex: string) => {
      onFilter?: ColumnProps['onFilter'];
    }
  >(
    dataIndex =>
      isQuery
        ? {
            onFilter: (value, record) =>
              record[dataIndex]
                .toString()
                .toLowerCase()
                .includes((value as string).toLowerCase()),
          }
        : {},
    [isQuery]
  );
  const columns = useMemo(() => {
    const cols = appElement2TableColumns?.map(column => {
      if (
        column.dataIndex === 'name' ||
        column.dataIndex === 'certificateNum'
      ) {
        return {
          ...column,
          filterIcon: <Icon type="search" />,
          filterDropdown: props => <FilterDropdownInput {...props} />,
          ...onFilterObj(column.dataIndex),
        };
      }

      return column;
    });
    return [
      {
        dataIndex: 'index',
        editable: false,
        title: t('No.'),
        render: (txt, record, index) =>
          ((pagination.current ?? initialPagination.current) - 1) *
            (pagination.pageSize ?? initialPagination.pageSize) +
          index +
          1,
      },
      ...cols,
    ];
  }, [
    appElement2TableColumns,
    onFilterObj,
    pagination.current,
    pagination.pageSize,
  ]);

  const {
    run: getPersonObjectList,
    loading: isLoading,
    data,
  } = useRequest(
    (
      page: number,
      size: number,
      filterParams?: Partial<CommercialLineTypes.EmployeeObject>
    ) =>
      getEmployeeObjectList(
        temporaryId as unknown as number,
        objectUniqueKey,
        page - 1,
        size,
        filterParams,
        stage
      ),
    {
      manual: true,
      onSuccess: data => {
        setDataSource(data?.content);
        setEditRecord(null);
      },
      onError: err => message.error(err?.message),
    }
  );

  useEffect(() => {
    // proposal query | policy query从大接口获取数据不需要再重新请求
    if (isQuery) {
      let data =
        (find(commercialLinePolicyInsuredObjectList[uniqueKey], [
          'uniqueKey',
          objectUniqueKey,
        ])?.[
          ObjectComponentEnum.EMPLOYEE
        ] as CommercialLineTypes.EmployeeObject[]) ?? [];
      if (isLocation) {
        const locationUniqueKey = form?.getFieldsValue()?.uniqueKey;
        data =
          (find(
            find(commercialLinePolicyInsuredObjectList[uniqueKey], [
              'uniqueKey',
              locationUniqueKey,
            ])?.[INSURED_OBJECT_LIST],
            ['uniqueKey', objectUniqueKey]
          )?.[
            ObjectComponentEnum.EMPLOYEE
          ] as CommercialLineTypes.EmployeeObject[]) ?? [];
      }
      setDataSource(data);
      setQueryPagination({
        ...initialPagination,
        total: size(data),
        paginationType: PaginationType.Frontend,
      });
    } else if (objectUniqueKey && (temporaryId || isPosProposalEntryScenario)) {
      getPersonObjectList(
        initialPagination.current,
        initialPagination.pageSize
      );
    }
    setPagination(initialPagination);
    setFilters(null);
  }, [
    objectUniqueKey,
    temporaryId,
    isQuery,
    commercialLinePolicyInsuredObjectList,
    uniqueKey,
    isLocation,
  ]);

  const savePerson = (
    record: CommercialLineTypes.EmployeeObject,
    editingKey: string
  ) => {
    setSubmitting(true);
    const req =
      editingKey === 'add'
        ? addEmployeeObject(
            temporaryId,
            objectUniqueKey,
            record,
            stage,
            goodsId,
            planId,
            packageId,
            objectType
          )
        : updateEmployeeObject(
            record.id as string,
            {
              ...record,
              stage,
            },
            temporaryId,
            goodsId,
            planId,
            packageId,
            objectType
          );
    return req
      .then((res: string) => {
        if (newRowListRef?.current) {
          newRowListRef.current.push(res);
        }

        getPersonObjectList(pagination.current, pagination.pageSize, filters);
      })
      .catch(error => {
        message.error(error.message);
        throw error;
      })
      .finally(() => setSubmitting(false));
  };

  const handleDelete = (
    idx: number,
    record: CommercialLineTypes.EmployeeObject
  ) => {
    setSubmitting(true);
    deleteEmployeeObject(record.id as string, stage)
      .then(() => {
        getPersonObjectList(1, pagination.pageSize, filters);
        setPagination({
          current: 1,
          pageSize: pagination.pageSize,
        });
      })
      .catch(error => message.error(error.message))
      .finally(() => setSubmitting(false));
  };

  const successCallBack = (fileUniqueCode: string) => {
    setPagination(initialPagination);
    getPersonObjectList(
      initialPagination.current,
      initialPagination.pageSize,
      filters
    );

    if (fileUniqueCodeListRef?.current) {
      fileUniqueCodeListRef.current.push(fileUniqueCode);
    }
  };

  return (
    <EditableTable
      className={nestedInBg && styles.objectTable}
      loading={isLoading}
      readonly={readPretty || !hasEditAuth}
      rowKey="id"
      scroll={{ x: 'max-content' }}
      columns={columns ?? []}
      dataSource={dataSource ?? []}
      title={<TextBody weight={700}>{t('Person List')}</TextBody>}
      addBtnProps={{
        type: 'default',
        ghost: false,
        disabled: submitting || isLoading,
        handleAdd: setEditRecord,
      }}
      handleCancel={() => setEditRecord(null)}
      setDataSource={setDataSource}
      addBtnLeftSection={
        !readPretty && (
          <>
            <UploadFile
              disabled={!!editRecord}
              businessType={ProposalCommonTypes.BusinessType.LiabilityPerson}
              uniqueKey={objectUniqueKey}
              submitCallback={successCallBack}
              submitStep={ProposalCommonTypes.UploadStep.Persistence}
              stage={stage}
              objectType={objectType}
              relatedService={{
                uploadFileAndCheckData,
                uploadFile: ProposalService.uploadAttachment,
                checkFile: ProposalCommonService.uploadFile,
                downloadBusinessData,
                submitService,
                clearAllData,
              }}
            />
            <TemplateDownload
              downloadBusinessTemplateService={downloadBusinessTemplate}
              objectType={objectType}
              businessType={ProposalCommonTypes.BusinessType.LiabilityPerson}
              stage={stage}
            />
            <ClearAllButton
              clearAllDataService={clearAllData}
              businessType={ProposalCommonTypes.BusinessType.LiabilityPerson}
              objectUniqueKey={objectUniqueKey}
              successCallBack={successCallBack}
              disabled={!!editRecord}
            />
          </>
        )
      }
      handleConfirm={savePerson}
      deleteBtnProps={{
        handleDelete,
        disabled: () => submitting,
      }}
      editBtnProps={{
        disabled: () => submitting,
        handleEdit: setEditRecord,
      }}
      pagination={{
        size: 'small',
        paginationType: PaginationType.Backend,
        total: data?.totalElements,
        ...queryPagination,
        current: pagination.current,
        pageSize: pagination.pageSize,
      }}
      onChange={(pag, filtersParams) => {
        // filtersParams返回的name，certificateNum为数组，解构给后端
        const transFiltersParams = {
          ...filtersParams,
          name: filtersParams?.name?.[0],
          certificateNum: filtersParams?.certificateNum?.[0],
        };
        const pageInfo = isEmpty(pag) ? initialPagination : pag;
        setFilters(transFiltersParams);
        setPagination({
          current: pageInfo.current,
          pageSize: pageInfo.pageSize,
        });
        if (!isQuery) {
          getPersonObjectList(
            pageInfo.current,
            pageInfo.pageSize,
            transFiltersParams
          );
        }
      }}
    />
  );
};
