import { useCallback } from 'react';
import { useParams } from 'react-router-dom';

import { useAtomValue } from 'jotai';
import useSWR from 'swr';

import { ObjectType } from 'genesis-web-service';

import { goodsIdAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { queryPackages } from '@uw/pages/proposal-entry/hooks/request';

import { IS_MAX_ALLOWED_NUMBER_ARR } from '../constants';

export const useGetIsMaxAllowedNumber = (): ((
  objectType: ObjectType
) => boolean) => {
  const curGoodsId = Number(useAtomValue(goodsIdAtom));
  const { mode } = useParams();
  const { data: packageObj } = useSWR(
    isNaN(curGoodsId) ? null : { goodsId: Number(curGoodsId), mode },
    ({ goodsId }: { goodsId: number }) => queryPackages({ goodsId })
  );
  const getIsMaxAllowedNumber = useCallback<
    (objectType: ObjectType) => boolean
  >(
    objectType => {
      if (IS_MAX_ALLOWED_NUMBER_ARR.includes(objectType)) {
        const selectPackage = packageObj?.packages?.[0];
        const selectObjectLimit =
          selectPackage?.packageSchemaApplicationElements?.objectLimits?.find(
            item => item?.code === objectType
          );
        return selectObjectLimit?.subCateLimits[0].maxAllowedNumber === 1;
      }
      return false;
    },
    [packageObj]
  );

  return getIsMaxAllowedNumber;
};
