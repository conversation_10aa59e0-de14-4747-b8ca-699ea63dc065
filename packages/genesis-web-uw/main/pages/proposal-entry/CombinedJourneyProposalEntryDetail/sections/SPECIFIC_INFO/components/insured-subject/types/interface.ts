import { MutableRefObject } from 'react';

import { FormInstance } from 'antd';

import { FactorsType, PolicyCommonTypes } from 'genesis-web-service';

import { CategorizedFactorsType } from '../../hooks/useGetCurrentSectionFactors';
import { SectionProps } from '../../interface';

export interface InsuredObjectProps extends Partial<SectionProps> {
  insuredObjectTypes: PolicyCommonTypes.QueryObjectInfo[] | undefined;
  isLocation?: boolean;
  outForm?: FormInstance;
  showTitle?: boolean;
  fileUniqueCodeListRef?: MutableRefObject<string[]>;
  newRowListRef?: MutableRefObject<string[]>;
  categorizedFactors: FactorsType[] | CategorizedFactorsType;
}
