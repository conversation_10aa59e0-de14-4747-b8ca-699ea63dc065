import { useCallback, useMemo } from 'react';

import { isUndefined } from 'lodash-es';

import { ObjectComponentEnum, ObjectType } from 'genesis-web-service';

import { CommercialLineObjectType } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { i18nFn } from '@uw/util/i18nFn';

import { CategorizedFactorsType } from '../../../../hooks/useGetCurrentSectionFactors';

export const useGetLocationTitle = (
  categorizedFactors: CategorizedFactorsType
) => {
  // Address的投保要素需要写死取ObjectType: ADDRESS, ObjectComponent: ADDRESS 去获取要素
  const addressFactors = useMemo(
    () =>
      categorizedFactors?.[ObjectType.ADDRESS]?.[
        ObjectComponentEnum.ADDRESS
      ]?.map(factor => ({
        ...factor,
        componentType: ObjectComponentEnum.ADDRESS,
      })),
    [categorizedFactors]
  );
  const getAddressInfo = useCallback(
    (location: CommercialLineObjectType) => {
      const addressInfo = addressFactors
        ?.map(factor => location?.[factor.factorCode])
        .filter(value => !!value);

      return addressInfo?.join(',');
    },
    [addressFactors]
  );

  const getLocationTitle = (
    location: CommercialLineObjectType,
    locationIndex?: number
  ) => {
    const address = getAddressInfo(
      location?.[ObjectComponentEnum.ADDRESS] as CommercialLineObjectType
    );
    return i18nFn('{{index}}. Location: {{title}}', ['uw', 'common'], {
      index: `${isUndefined(locationIndex) ? undefined : locationIndex + 1}`,
      title: address,
    });
  };

  return { getLocationTitle, getAddressInfo, addressFactors };
};
