import { FC, ReactNode, createContext, useContext } from 'react';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { ObjectComponentEnum, ObjectType } from 'genesis-web-service';

import { CategorizedFactorsType } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/hooks';

interface IObjectInfoProvider {
  /** 分类因子数据 - Categorized factors data */
  categorizedFactors: CategorizedFactorsType;

  /** 枚举映射表 - Enums mapping table */
  enumsMap: Record<string, BizDict[]>;

  /** 按标的类型和组件类型分类的枚举映射 - Categorized enums mapping by object type and component type */
  categorizedEnumsMap: Record<
    ObjectType,
    Record<ObjectComponentEnum, Record<string, BizDict[]>>
  >;

  /** 子组件 - Child components */
  children?: ReactNode;
}

export const ObjectContext = createContext(
  null as unknown as IObjectInfoProvider
);

export function useObjectInfo(): IObjectInfoProvider {
  return useContext(ObjectContext) || {};
}

export const ObjectInfoProvider: FC<IObjectInfoProvider> = ({
  children,
  ...rest
}) => (
  <ObjectContext.Provider value={{ ...rest }}>
    {children}
  </ObjectContext.Provider>
);
