import { useCallback } from 'react';

import { useSetAtom } from 'jotai';
import { size } from 'lodash-es';

import { specialInfoAnchorMenuAtom } from '../../../atom/section';
import { ProposalEntryPageSections } from '../../interface';

export const useSpecificInfoMenuHide = () => {
  const setSpecialInfoAnchorMenu = useSetAtom(specialInfoAnchorMenuAtom);

  const specificInfoMenuHide = useCallback(
    (keys: ProposalEntryPageSections[]) => {
      setSpecialInfoAnchorMenu(currentMenu => {
        return currentMenu?.map(item => {
          if (size(item.anchorItems)) {
            const anchorItems = item.anchorItems?.map(anchorItem => {
              if (size(anchorItem.children)) {
                const children = anchorItem.children?.filter(child =>
                  keys.every(
                    section => String(child.key)?.indexOf(section) === -1
                  )
                );
                return {
                  ...anchorItem,
                  children,
                };
              }
              return anchorItem;
            });

            return {
              ...item,
              anchorItems,
            };
          }

          return item;
        });
      });
    },
    [setSpecialInfoAnchorMenu]
  );

  return specificInfoMenuHide;
};
