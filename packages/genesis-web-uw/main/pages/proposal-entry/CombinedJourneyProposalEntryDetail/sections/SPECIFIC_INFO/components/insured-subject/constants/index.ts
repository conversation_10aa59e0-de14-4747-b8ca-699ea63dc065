import { ProposalEntryPageSections } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/interface';

/** 保险标的列表字段名 - Insured Object List Field Name */
export const INSURED_OBJECT_LIST = 'insuredObjectList';

/** 支持最大数量限制的产品类型 - Product Types Supporting Max Quantity Limit */
export const PRODUCT_TYPES_WITH_MAX_QUANTITY_LIMIT = [
  'OUTDOOR_SUPPLIES', // 户外用品
  'RAW_MATERIALS_PRODUCTS_MERCHANDISES', // 原材料商品
];

/** 向后兼容的旧常量名 - Legacy constant name for backward compatibility */
export const IS_MAX_ALLOWED_NUMBER_ARR = PRODUCT_TYPES_WITH_MAX_QUANTITY_LIMIT;

/** 需要动态获取标的标题的页面模块列表 - Page Sections Requiring Dynamic Object Titles */
export const SECTIONS_WITH_DYNAMIC_OBJECT_TITLES = [
  ProposalEntryPageSections.MULTIPLE_OBJECT_INFO, // 多对象信息
  ProposalEntryPageSections.SINGLE_PLAN_MULTI_OBJECT, // 单方案多标的
  ProposalEntryPageSections.SINGLE_PLAN_SINGLE_OBJECT, // 单方案单标的
] as const;

/** 向后兼容的旧常量名 - Legacy constant name for backward compatibility */
export const NEED_DYNAMIC_OBJECT_TITLE_SECTIONS =
  SECTIONS_WITH_DYNAMIC_OBJECT_TITLES;
