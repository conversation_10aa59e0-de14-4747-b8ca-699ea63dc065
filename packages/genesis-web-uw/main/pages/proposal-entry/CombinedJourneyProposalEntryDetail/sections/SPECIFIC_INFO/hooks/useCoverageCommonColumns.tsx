import { useCallback, useMemo } from 'react';
import { Provider } from 'react-redux';

import { Button, Form, FormInstance } from 'antd';
import { NamePath } from 'antd/es/form/interface';

import { useAtomValue } from 'jotai';
import { isEmpty } from 'lodash-es';

import { Modal } from '@zhongan/nagrand-ui';

import { FieldType } from 'genesis-web-component/lib/interface';
import { PolicyProductListType, ProposalEntryTypes } from 'genesis-web-service';

import { ClaimStack } from '@uw/pages/policy-query-pages/Detail/Components-Tabs/Product-Libility-Info/components/ClaimStack';
import { usePolicyInfo } from '@uw/pages/policy-query-pages/DetailV2/hooks/usePolicyInfo';
import { useZoneId } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/hooks/useZoneId';
import store from '@uw/redux/store';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { i18nFn } from '@uw/util/i18nFn';

import { isReadPrettyAtom, queryDetailMetaDataAtom } from '../../../atom';

// 定义扩展的产品记录类型
interface ExtendedProductRecord extends ProposalEntryTypes.ProductInfo {
  policyProductId?: string | number;
  issuanceProductId?: string;
}

// 列配置常量
const COLUMN_CONSTANTS = {
  WIDTH: {
    DATE: 300,
    CLAIM_STACK: 200,
  },
  FIELD_TYPE: FieldType.TimePicker,
} as const;

// Modal 配置常量
const MODAL_CONFIG = {
  width: 600,
  icon: null,
  className: '&_.antd-modal-confirm-paragraph:max-w-full',
  closable: false,
  okText: i18nFn('Close'),
} as const;

export const useCoverageCommonColumns = () => {
  const { policyNo, proposalNo } = useAtomValue(queryDetailMetaDataAtom) ?? {};
  const { policyInfo } = usePolicyInfo();
  const zoneId = useZoneId();
  const readPretty = useAtomValue(isReadPrettyAtom);

  // 优化：使用更高效的对象构建方式，避免对象展开
  const productIdMap = useMemo(() => {
    if (!policyInfo?.policyProductList?.length) {
      return {};
    }

    const map: Record<string, PolicyProductListType> = {};
    policyInfo.policyProductList.forEach(product => {
      if (product.productId) {
        map[product.productId] = product;
      }
    });
    return map;
  }, [policyInfo?.policyProductList]);

  // 优化：提取样式对象到组件外部，避免重复创建
  const titleStyle = useMemo(
    () => ({
      fontSize: 'var(--font-size-lg)',
      fontWeight: '700' as const,
      margin: '6px 0 var(--gap-lg)',
    }),
    []
  );

  const handleClaimStackModal = useCallback(
    (
      record: ProposalEntryTypes.LiabilityInfo,
      productRecord: ExtendedProductRecord
    ) => {
      Modal.info({
        ...MODAL_CONFIG,
        title: i18nFn('Claim Stack'),
        content: (
          <Provider store={store}>
            <div>
              <p style={titleStyle}>
                {i18nFn('Stack Liability Name', undefined, {
                  liabilityName: record?.liabilityName,
                })}
              </p>
              <ClaimStack
                policyNo={policyNo || ''}
                productId={productRecord?.productId?.toString() || ''}
                policyProductId={
                  productRecord?.policyProductId?.toString() || ''
                }
                issuanceProductId={
                  productRecord?.issuanceProductId?.toString() || ''
                }
                liabilityId={record?.liabilityId?.toString() || ''}
                issuanceNo={proposalNo || ''}
              />
            </div>
          </Provider>
        ),
      });
    },
    [policyNo, proposalNo, titleStyle]
  );

  // 优化：提取公共的日期字段渲染逻辑
  const renderDateField = useCallback(
    (fieldName: 'effectiveDate' | 'expiryDate', field: { name: number }) => (
      <Form.Item name={[field.name, fieldName]} noStyle>
        {getFields({
          type: COLUMN_CONSTANTS.FIELD_TYPE,
          placeholder: i18nFn('Please select'),
          showTime: true,
          readPretty,
          zoneId,
        })}
      </Form.Item>
    ),
    [readPretty, zoneId]
  );

  const getCommonCols = useCallback(
    ({
      form,
      namePath,
      isLiability,
      productInfoNamePath,
      productsIndex,
    }: {
      form: FormInstance;
      namePath: NamePath;
      productInfoNamePath?: NamePath;
      isLiability?: boolean;
      productsIndex?: number;
    }) => {
      return [
        ...(!isEmpty(policyNo) || !isEmpty(proposalNo)
          ? [
              {
                title: i18nFn('Effective Date'),
                key: 'effectiveDate',
                dataIndex: 'effectiveDate',
                width: COLUMN_CONSTANTS.WIDTH.DATE,
                render: (_: unknown, field: { name: number }) => {
                  const effectiveDate = form.getFieldValue([
                    ...namePath,
                    field.name,
                    'effectiveDate',
                  ]);
                  if (!effectiveDate) {
                    return i18nFn('--');
                  }

                  return renderDateField('effectiveDate', field);
                },
              },
              {
                title: i18nFn('Expiry Date'),
                key: 'expiryDate',
                dataIndex: 'expiryDate',
                width: COLUMN_CONSTANTS.WIDTH.DATE,
                render: (_: unknown, field: { name: number }) => {
                  const expiryDate = form.getFieldValue([
                    ...namePath,
                    field.name,
                    'expiryDate',
                  ]);
                  if (!expiryDate) {
                    return i18nFn('--');
                  }
                  return renderDateField('expiryDate', field);
                },
              },
              {
                title: i18nFn('Claim Stack'),
                dataIndex: 'claimStackList',
                width: COLUMN_CONSTANTS.WIDTH.CLAIM_STACK,
                render: (_: unknown, field: { name: number }) => {
                  if (!isLiability) {
                    return i18nFn('--');
                  }

                  const record = form.getFieldValue([
                    ...namePath,
                    field.name,
                  ]) as ProposalEntryTypes.LiabilityInfo;

                  const productRecord = form.getFieldValue([
                    ...productInfoNamePath,
                    productsIndex,
                  ]) as ExtendedProductRecord;

                  // 安全地合并产品记录数据
                  const safeProductRecord: ExtendedProductRecord = {
                    ...(productIdMap?.[productRecord?.productId] ?? {}),
                    ...productRecord,
                  };

                  return (
                    <Button
                      type="link"
                      disabled={false}
                      onClick={() =>
                        handleClaimStackModal(record, safeProductRecord)
                      }
                    >
                      {i18nFn('Detail')}
                    </Button>
                  );
                },
              },
            ]
          : []),
      ];
    },
    [renderDateField, handleClaimStackModal, productIdMap, policyNo, proposalNo]
  );

  return {
    getCommonCols,
    productIdMap,
  };
};
