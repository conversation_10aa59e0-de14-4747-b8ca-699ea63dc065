import React, {
  FC,
  ForwardRefExoticComponent,
  RefAttributes,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';

import { Affix, Space } from 'antd';

import { useAsyncEffect, useDeepCompareEffect } from 'ahooks';
import cls from 'clsx';
import { useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai';
import { filter, head, map } from 'lodash-es';

import { Icon, Tabs } from '@zhongan/nagrand-ui';

import { PageTemplateTypes, ProposalEntryTypes } from 'genesis-web-service';

import { SectionRender, useQueryBothSections } from '@uw/hook/useQuerySchema';
import { InferObjectRecord } from '@uw/interface/common.interface';
import { Mode, SaveMode } from '@uw/interface/enum.interface';
import {
  activeTab<PERSON>ey<PERSON>tom,
  applicationElementConfig<PERSON>tom,
  errorActiveTabKeyAtom,
  isPageReadyAtom,
  pageCodeAtom,
  policyListAtom,
  specialInfoAnchorMenuAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { AnchorConnector } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/config';
import { processSections } from '@uw/util/renderSections';

import { useSaveProposal } from '../../hooks/request';
import { filterSectionsByApplication } from '../../utils/filterSectionsByApplication';
import { ProposalEntryPageSections, SectionProps } from '../interface';
import { SpecificInfoProvider } from './SpecificInfo.Provider';
import { useAssemblePolicyListModel, useSyncInputFeeConfig } from './hooks';
import { usePolicyVersionUpdate } from './hooks/usePolicyVersionUpdate';
import { sections, sectionsSchema } from './sub-sections';

const getGoodsName = (goodsList: ProposalEntryTypes.PolicyGoodsItem[]) =>
  map(goodsList, goods => goods.goodsName).join(' & ');

export const SpecificInfo: FC<SectionProps> = ({
  proposalDetail,
  form,
  relatedFirstSection,
  asComponent,
  id,
  showHeaderInfo,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const location = useLocation();
  const { mode } = useParams();
  const policyList = useAtomValue(policyListAtom);
  const [activeTabKey, setActiveTabKey] = useAtom(activeTabKeyAtom);
  const setSpecialInfoAnchorMenu = useSetAtom(specialInfoAnchorMenuAtom);
  const pageCode = useAtomValue(pageCodeAtom);
  const errorActiveTabKey = useAtomValue(errorActiveTabKeyAtom);
  const applicationElementConfig = useAtomValue(applicationElementConfigAtom);
  const isPageReady = useAtomValue(isPageReadyAtom);
  const [renderMap, setRenderMap] = useState<
    Record<string, Record<string, SectionRender>>
  >({});
  const [sectionsMap, setSectionsMap] = useState<
    Record<string, PageTemplateTypes.SectionDetailResponse[]>
  >({});
  const { querySection, updateSectionAndRender } = useQueryBothSections({
    basicPageCode: pageCode as PageTemplateTypes.PageType,
    basicSection: ProposalEntryPageSections.SPECIFIC_INFO,
    schemas: sectionsSchema,
    needGetInitSection: false,
  });

  useAsyncEffect(async () => {
    policyList.forEach(async item => {
      const goodsInfo = head(item?.goodsList);
      if (goodsInfo?.goodsId && goodsInfo.packageId) {
        const factors =
          goodsInfo?.goodsId && goodsInfo.packageId
            ? [
                {
                  factor: 'goodsId',
                  factorValue: +goodsInfo?.goodsId,
                },
                {
                  factor: 'packageId',
                  factorValue: +goodsInfo?.packageId,
                },
              ]
            : [];

        const sections = await querySection({
          factors,
          pageCode,
        });
        setSectionsMap(prev => ({
          ...prev,
          [item.uniqueKey]: sections,
        }));
        const render = await updateSectionAndRender(
          sections,
          ProposalEntryPageSections.SPECIFIC_INFO
        );

        setRenderMap(prev => ({
          ...prev,
          [item.uniqueKey]: render,
        }));
      }
    });
  }, [pageCode, policyList, querySection]);

  useAssemblePolicyListModel();
  useSyncInputFeeConfig({
    form,
  });

  const { handleSaveProposal } = useSaveProposal();

  // 存在goods tab时将goods tab信息转换显示在anchor menu
  // 点击anchor是需要切换tab,因此 anchor menu 需要包含 goods tab key信息，用AnchorConnector链接goods tab key与section
  useDeepCompareEffect(() => {
    if (policyList?.length) {
      const anchorItems = policyList.map(detail => {
        const goodsName = getGoodsName(detail.goodsList);

        const sectionElements = filter(
          filterSectionsByApplication(
            (applicationElementConfig[detail.uniqueKey] ?? {})
              .applicationElementConfig,
            processSections(
              sectionsMap[detail.uniqueKey],
              sections as InferObjectRecord<
                FC<SectionProps>
              > as InferObjectRecord<FC<unknown>>
            ).componentSections
          ),
          section =>
            section?.section !== ProposalEntryPageSections.TOTAL_PREMIUM &&
            section?.section !==
              ProposalEntryPageSections.MOTOR_FLEET_POLICY_TOTAL_PREMIUM
        );

        return {
          key: detail.uniqueKey,
          href: `#${detail.uniqueKey}`,
          title: goodsName,
          disable: true,
          children: sectionElements?.map(({ section }) => ({
            key: `${detail.uniqueKey}${AnchorConnector}${section}`,
            title: t(section),
            href: `#${detail.uniqueKey}${AnchorConnector}${section}`,
          })),
        };
      });
      const anchorMenus = [
        {
          title: t('Specific Info'),
          anchorItems,
        },
      ];

      setSpecialInfoAnchorMenu(anchorMenus);
    } else {
      setSpecialInfoAnchorMenu([]);
    }
  }, [policyList, applicationElementConfig, sectionsMap, sections]);

  const timer = useRef<NodeJS.Timeout>();

  // 点击anchor时hash会发生变化，监听hash的变化，切换tab
  // 优化逻辑并处理 useSaveProposal 中 window.location.href = `#${ProposalEntryPageSections.SPECIFIC_INFO}`的情况
  useDeepCompareEffect(() => {
    const hash = location.hash?.slice?.(1);
    const hasAnchor = hash?.includes(AnchorConnector);
    let activeKey = head(policyList)?.uniqueKey;
    const hashList = hash?.split(AnchorConnector);
    if (hasAnchor) {
      const hashActiveKey = hashList?.[0];
      if (
        policyList?.some(policy => {
          return policy?.uniqueKey === hashActiveKey;
        })
      ) {
        activeKey = hashActiveKey;
      }
    }

    if (hashList?.length > 1) {
      let count = 0;
      const maxTries = 10;
      clearInterval(timer.current);
      // 用于校验跳转定位
      timer.current = setInterval(() => {
        count++;
        if (document.getElementById(hash) || count >= maxTries) {
          // 保证切换tab后元素能加载出来
          document.getElementById(hash)?.scrollIntoView();
          clearInterval(timer.current);
        }
      }, 100);
    }
    setActiveTabKey(activeKey as string);
  }, [location.hash, policyList]);
  // 因为已经pos/uw 场景分单,所以不需要考虑
  const handleChangeTab = async (activeKey: string) => {
    // isPageReady false, 页面 ready
    // tab 出来后一般都会 ready,这里做下兜底 没有 ready 时不要调保存接口,防止数据丢失
    setActiveTabKey(activeKey);
    if (!isPageReady && mode !== Mode.View) {
      try {
        await handleSaveProposal(form, SaveMode.ONLY_SAVE);
      } catch (error) {
        // 即使保存失败，仍然允许切换 tab
      }
    }
  };

  const affixRef = React.useRef();

  useEffect(() => {
    const handler = () => {
      affixRef.current?.updatePosition();
    };
    window.addEventListener('scroll', handler, true);
    return () => window.removeEventListener('scroll', handler, true);
  }, [policyList?.length]);

  usePolicyVersionUpdate(form);

  return (
    <div
      id={id}
      className={cls(
        policyList?.length > 1 &&
          `&>*_.antd-space-item-first-child:!rounded-none &>*_.antd-space-item-first-child:!rounded-b-lg`
      )}
    >
      {policyList?.length > 1 && (
        <div className="bg-white rounded-t-lg pb-0 &_.antd-tabs-nav-list_.antd-tabs-ink-bar:!h-0.5 &_.antd-affix:bg-white &_.antd-affix:mx-[-8px] &_.antd-affix:!pt-xs &_.antd-affix:!h-14 &_.antd-affix:!w-full &_.antd-affix:!shadow-[0_8px_12px_0px_rgba(16,42,67,0.06)]">
          <Affix ref={affixRef} offsetTop={showHeaderInfo ? 97 : 48}>
            <Tabs
              type="line-with-bg"
              activeKey={activeTabKey}
              onChange={handleChangeTab}
              items={policyList?.map(goods => ({
                key: goods.uniqueKey,
                label: (
                  <div
                    className={
                      errorActiveTabKey === goods.uniqueKey ? 'text-error' : ''
                    }
                  >
                    {errorActiveTabKey === goods.uniqueKey ? (
                      <Icon className="mr-xs" type="close-fill" />
                    ) : (
                      <></>
                    )}
                    {map(goods?.goodsList, 'goodsName')?.join('&')}
                  </div>
                ),
              }))}
            />
          </Affix>
        </div>
      )}
      <div>
        {policyList?.map(goods => (
          <section
            key={goods.uniqueKey}
            className={goods.uniqueKey === activeTabKey ? '' : 'hidden'}
          >
            <SpecificInfoProvider uniqueKey={goods.uniqueKey}>
              <Space
                direction="vertical"
                size={16}
                style={{ width: '100%' }}
                className="specific-section"
              >
                {filter(
                  filterSectionsByApplication(
                    (applicationElementConfig[goods.uniqueKey] ?? {})
                      .applicationElementConfig,
                    processSections(
                      sectionsMap[goods.uniqueKey],
                      sections as InferObjectRecord<
                        FC<SectionProps>
                      > as InferObjectRecord<FC<unknown>>
                    ).componentSections
                  ),
                  section =>
                    section?.section !== ProposalEntryPageSections.TOTAL_PREMIUM
                )?.map(section => {
                  const ComponentObj =
                    section?.ComponentObj as unknown as ForwardRefExoticComponent<
                      SectionProps & RefAttributes<unknown>
                    >;

                  return (
                    <ComponentObj
                      title={section?.title}
                      id={`${goods.uniqueKey}${AnchorConnector}${section?.section}`}
                      form={form}
                      key={`${section?.section}_${goods.uniqueKey}`}
                      proposalDetail={proposalDetail}
                      render={
                        renderMap?.[goods.uniqueKey]?.[section?.section]?.render
                      }
                      sectionCode={section?.section}
                      relatedFirstSection={relatedFirstSection}
                      asComponent={asComponent}
                      order={100 + section?.order}
                    />
                  );
                })}
              </Space>
              {filter(
                filterSectionsByApplication(
                  (applicationElementConfig[activeTabKey] ?? {})
                    .applicationElementConfig,
                  processSections(
                    sectionsMap[activeTabKey],
                    sections as InferObjectRecord<
                      FC<SectionProps>
                    > as InferObjectRecord<FC<unknown>>
                  ).componentSections
                ),
                ['section', ProposalEntryPageSections.TOTAL_PREMIUM]
              )?.map(section => {
                const ComponentObj =
                  section?.ComponentObj as unknown as ForwardRefExoticComponent<
                    SectionProps &
                      RefAttributes<SectionProps & RefAttributes<unknown>>
                  >;

                return (
                  <ComponentObj
                    title={section?.title}
                    id={`${goods.uniqueKey}${AnchorConnector}${section?.section}`}
                    form={form}
                    key={`${section?.section}_${goods.uniqueKey}`}
                    proposalDetail={proposalDetail}
                    render={
                      renderMap?.[goods.uniqueKey]?.[section?.section]?.render
                    }
                    sectionCode={section?.section}
                    relatedFirstSection={relatedFirstSection}
                    asComponent={asComponent}
                    order={100 + section?.order}
                  />
                );
              })}
            </SpecificInfoProvider>
          </section>
        ))}
      </div>
    </div>
  );
};
