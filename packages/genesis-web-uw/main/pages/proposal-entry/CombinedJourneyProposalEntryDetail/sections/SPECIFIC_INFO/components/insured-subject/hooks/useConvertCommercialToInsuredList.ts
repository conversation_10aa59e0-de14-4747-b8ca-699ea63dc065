import { useCallback, useEffect } from 'react';

import { useAtomValue, useSetAtom } from 'jotai';
import { cloneDeep, isObject, size } from 'lodash-es';

import {
  ObjectComponentEnum,
  ProposalCombinedTypes,
} from 'genesis-web-service';
import { Types } from 'genesis-web-shared';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import {
  CommercialLineObjectType,
  commercialLinePolicyInsuredObjectListAtom,
  policyInsuredObjectListAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { INSURED_OBJECT_LIST } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/components/insured-subject';

import { useSpecificInfo } from '../../../SpecificInfo.Provider';

const uploadedObjectConponents = [
  ObjectComponentEnum.EMPLOYEE,
  ObjectComponentEnum.PRODUCT,
];

export const useConvertCommercialToInsuredList = () => {
  const { uniqueKey } = useSpecificInfo();
  const commercialLinePolicyInsuredObjectList = useAtomValue(
    commercialLinePolicyInsuredObjectListAtom
  );
  const objectComponents = useBizDictByKey('objectComponent');

  const setPolicyInsuredObjectList = useSetAtom(policyInsuredObjectListAtom);
  const objectComponentEnums = objectComponents?.map(
    bizDict => bizDict.enumItemName
  );

  const updateProductObjects = useCallback<
    (
      componentType: ObjectComponentEnum,
      componentInfoList: ProposalCombinedTypes.PolicyInsuredObjectComponentInfoType
    ) => ProposalCombinedTypes.PolicyInsuredObjectComponentInfoType
  >((componentType, componentInfoList) => {
    if (
      componentType === ObjectComponentEnum.PRODUCT &&
      isObject(componentInfoList) &&
      !Array.isArray(componentInfoList)
    ) {
      return [
        componentInfoList,
      ] as ProposalCombinedTypes.PolicyInsuredObjectComponentInfoType;
    }
    return componentInfoList;
  }, []);

  const handleObjectStructure = useCallback<
    (
      objects: Partial<CommercialLineObjectType>
    ) => ProposalCombinedTypes.PolicyInsuredObjectType
  >(
    objects =>
      Object.entries(objects)?.reduce((cur, next) => {
        const [key, value] = next;
        const newObject = {
          ...cur,
        };
        if (objectComponentEnums?.includes(key)) {
          newObject.insuredComponentList = [
            ...(newObject.insuredComponentList ?? []),
            {
              componentType: key as ObjectComponentEnum,
              componentInfoList: updateProductObjects(
                key as ObjectComponentEnum,
                value as ProposalCombinedTypes.PolicyInsuredObjectComponentInfoType
              ),
            },
          ].filter(item => {
            // 如果是需要上传的数据，不作过滤
            if (uploadedObjectConponents.includes(item.componentType)) {
              return true;
            }
            return Types.isObject(item.componentInfoList)
              ? !!Object.values(item.componentInfoList).filter(Types.isNotBlank)
                  .length
              : !!size(item.componentInfoList);
          });
          delete newObject[key];
        } else {
          newObject[key] = value;
        }
        return newObject;
      }, {} as ProposalCombinedTypes.PolicyInsuredObjectType),
    []
  );

  useEffect(() => {
    const curTabInsuredObjects = commercialLinePolicyInsuredObjectList?.[
      uniqueKey
    ]?.reduce((cur, commercialLineObject) => {
      const objects: Partial<CommercialLineObjectType> =
        cloneDeep(commercialLineObject);
      delete objects[INSURED_OBJECT_LIST];
      return [
        ...cur,
        {
          ...handleObjectStructure(objects),
          refObjectSerialsNoList: commercialLineObject[
            INSURED_OBJECT_LIST
          ]?.map(subObject => subObject.uniqueKey),
        },
        ...(commercialLineObject[INSURED_OBJECT_LIST] ?? []).map(
          insuredObject => handleObjectStructure(insuredObject)
        ),
      ] as ProposalCombinedTypes.PolicyInsuredObjectType[];
    }, [] as ProposalCombinedTypes.PolicyInsuredObjectType[]);

    setPolicyInsuredObjectList(prev => ({
      ...prev,
      [uniqueKey]: curTabInsuredObjects, //undefined 则清除默认值 这里设置成 undefined 之后,会重新触发 useAssemblePolicyListModel 设置默认值
    }));
  }, [
    commercialLinePolicyInsuredObjectList,
    handleObjectStructure,
    setPolicyInsuredObjectList,
    uniqueKey,
  ]);
};
