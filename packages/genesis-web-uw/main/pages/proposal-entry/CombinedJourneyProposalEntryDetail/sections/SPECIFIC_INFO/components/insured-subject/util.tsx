import { NamePath } from 'antd/es/form/interface';

import { head, isNumber, size, uniqWith } from 'lodash-es';

import { message } from '@zhongan/nagrand-ui';

import { DataTypeEnum } from 'genesis-web-component/lib/interface';
import { FactorsType, ProposalCombinedTypes } from 'genesis-web-service';

import { IsExtension, IsRequiredEnum } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';

import { INSURED_OBJECT_LIST } from './constants';

export const addUniqueKeyField = (
  factors: FactorsType[] = []
): FactorsType[] => [
  ...factors,
  {
    factorCode: 'uniqueKey',
    objectCategory: head(factors)?.objectCategory as number,
    objectSubCategory: head(factors)?.objectSubCategory as number,
    bizDictKey: '',
    configured: true,
    dataType: DataTypeEnum.INPUT,
    factorName: '',
    isExtension: IsExtension.No,
    isShowInApplicationForm: 1,
    isRequired: IsRequiredEnum.NO,
    hidden: true,
    options: [],
  },
];

export const validateObject = (
  objectList: ProposalCombinedTypes.PolicyInsuredObjectType[]
): boolean => {
  // 至少要有一个标的
  if (size(objectList) <= 0) {
    message.warning(
      i18nFn(
        'At least one item must be created under the address, please check.'
      )
    );
    return false;
  }
  // Object type + name要保证唯一性
  const uniqObjectList = uniqWith(
    objectList,
    (objectA, objectB) =>
      objectA.objectType === objectB.objectType &&
      objectA.insuredName === objectB.insuredName
  );

  if (size(uniqObjectList) !== size(objectList)) {
    message.warning(
      i18nFn('The object names cannot be the same, please check.')
    );
    return false;
  }
  return true;
};

export const getFieldKey = (curKey: string, fieldName?: NamePath) =>
  isNumber(fieldName) ? [INSURED_OBJECT_LIST, fieldName, curKey] : [curKey];
