import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, Row } from 'antd';
import { NamePath } from 'antd/es/form/interface';

import { useAtomValue } from 'jotai';
import { find, isEmpty, isNumber, last, size } from 'lodash-es';
import { v4 as uuidV4 } from 'uuid';

import { FactorField } from 'genesis-web-component/lib/hook';
import {
  BizDict,
  DataTypeEnum,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import { ObjectType, PolicyCommonTypes } from 'genesis-web-service';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import { IsRequiredEnum } from '@uw/interface/enum.interface';
import {
  hasEditAuthAtom,
  isReadPrettyAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import {
  INSURED_OBJECT_LIST,
  getFieldKey,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/components/insured-subject';
import { FormItem } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/sub-sections/OBJECT_INFO/components/FormItem';

interface Props {
  insuredObjectTypes: PolicyCommonTypes.QueryObjectInfo[] | undefined;
  isLocation?: boolean;
  form: FormInstance;
  fieldName?: NamePath;
  isAloneObjects?: boolean;
}

const getObjectTypes = (
  value: string,
  objectInfos?: PolicyCommonTypes.QueryObjectInfo[]
) => {
  let selectedOptions: PolicyCommonTypes.ObjectType[] = [];
  for (let i = 0; i < size(objectInfos); i += 1) {
    const category = objectInfos?.[i];
    if (category?.code === value) {
      selectedOptions = category.objectTypes;
      break;
    } else {
      selectedOptions = getObjectTypes(value, category?.children);
      if (size(selectedOptions) > 0) {
        break;
      }
    }
  }
  return selectedOptions;
};

export const ObjectTypeFormItems: FC<Props> = ({
  fieldName,
  insuredObjectTypes = [],
  isLocation,
  form,
  isAloneObjects,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [objectTypes, setObjectTypes] = useState<BizDict[]>();
  const objectTypeEnums = useBizDictByKey('objectType');
  const readPretty = useAtomValue(isReadPrettyAtom);
  const hasEditAuth = useAtomValue(hasEditAuthAtom);

  const objectCategory: string[] = Form.useWatch(
    getFieldKey('objectCategory', fieldName),
    form
  );
  const objectUniqueKey: string[] = Form.useWatch(
    getFieldKey('uniqueKey', fieldName),
    form
  );

  useEffect(() => {
    const lastValue = last(objectCategory);
    const selectedOptions = getObjectTypes(
      lastValue as string,
      insuredObjectTypes
    );
    const objTypes = selectedOptions
      ?.filter(object =>
        isLocation ? object.requireAddress : !object.requireAddress
      )
      ?.map(object => object.objectType);
    setObjectTypes(
      objectTypeEnums?.filter(object =>
        objTypes?.includes(object.enumItemName as ObjectType)
      )
    );
  }, [
    fieldName,
    form,
    insuredObjectTypes,
    isLocation,
    objectCategory,
    objectTypeEnums,
  ]);

  const objectTypeRelatedFields = useMemo<
    FactorField<PolicyCommonTypes.QueryObjectInfo | BizDict>[]
  >(
    () => [
      {
        placeholder: t('Please select'),
        col: 6,
        label: t('Insured Object Category'),
        type: FieldType.Cascader,
        dataType: DataTypeEnum.CASCADER,
        factorCode: 'objectCategory',
        fieldNames: { label: 'name', value: 'code', children: 'children' },
        options: insuredObjectTypes,
        isRequired:
          (isAloneObjects && !size(insuredObjectTypes)) || readPretty
            ? IsRequiredEnum.NO
            : IsRequiredEnum.YES,
        changeOnSelect: true,
        onChange: (values: string[]) => {
          form.setFieldValue(getFieldKey('insuredName', fieldName), undefined);
          form.setFieldValue(getFieldKey('objectType', fieldName), undefined);
          form.setFieldValue(getFieldKey('category1', fieldName), values?.[0]);
          form.setFieldValue(getFieldKey('category2', fieldName), values?.[1]);
          form.setFieldValue(getFieldKey('category3', fieldName), values?.[2]);
        },
      },
      {
        placeholder: t('Please select'),
        col: 6,
        label: t('Insured Object Type'),
        type: FieldType.Select,
        dataType: DataTypeEnum.SELECT,
        options: objectTypes,
        factorCode: 'objectType',
        isRequired: readPretty ? IsRequiredEnum.NO : IsRequiredEnum.YES,
        onChange: (value: string) => {
          if (isEmpty(objectUniqueKey)) {
            form.setFieldValue(getFieldKey('uniqueKey', fieldName), uuidV4());
          }
          form.setFieldValue(
            getFieldKey('insuredName', fieldName),
            find(objectTypes, ['value', value])?.label
          );
          form.validateFields(getFieldKey('insuredName', fieldName), {
            recursive: true,
          });
        },
      },
      {
        placeholder: t('Please input'),
        col: 6,
        label: t('Insured Object Name'),
        type: FieldType.Input,
        dataType: DataTypeEnum.INPUT,
        options: undefined,
        factorCode: 'insuredName',
        isRequired: readPretty ? IsRequiredEnum.NO : IsRequiredEnum.YES,
      },
    ],
    [
      t,
      insuredObjectTypes,
      isAloneObjects,
      readPretty,
      objectTypes,
      form,
      fieldName,
      objectUniqueKey,
    ]
  );

  const fieldKey = useCallback<(field: NamePath) => NamePath>(
    field =>
      isNumber(fieldName)
        ? [INSURED_OBJECT_LIST, fieldName, field.factorCode]
        : field.factorCode,
    [fieldName]
  );

  return (
    <Row
      gutter={[16, 0]}
      className={isAloneObjects && !size(insuredObjectTypes) ? 'hidden' : ''}
    >
      {objectTypeRelatedFields?.map(item => (
        <FormItem
          fieldName={fieldName}
          fieldKey={fieldKey(item)}
          field={item}
          options={item.options}
          enumsMap={{}}
          colSpan={6}
          readPretty={readPretty}
          disabled={!hasEditAuth}
          needOneOptionDefaultValue={true}
        />
      ))}
    </Row>
  );
};
