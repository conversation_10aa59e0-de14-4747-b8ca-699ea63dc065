import { FC, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { useAtomValue } from 'jotai';

import { EditableTable, TextBody } from '@zhongan/nagrand-ui';

import { FactorsType, UnknownObjectRecord } from 'genesis-web-service';

import { useAppElement2TableColumns } from '@uw/hook/useAppElementsToFields';
import { BizDict } from '@uw/interface/enum.interface';
import styles from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.module.scss';
import {
  hasEditAuthAtom,
  isReadPrettyAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';

interface Props {
  factors: FactorsType[];
  enumsMap: Record<string, BizDict[]>;
  id?: string;
  value?: UnknownObjectRecord[];
  onChange?: (value: UnknownObjectRecord[]) => void;
  insuredDrawerVisible?: boolean;
  title: string;
  nestedInBg?: boolean; // 在location object下的表格需要特殊header底色
}

export const CommonList: FC<Props> = ({
  factors,
  enumsMap,
  id,
  value = [],
  onChange,
  insuredDrawerVisible,
  title,
  nestedInBg = false,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const hasEditAuth = useAtomValue(hasEditAuthAtom);
  const readPretty = useAtomValue(isReadPrettyAtom);

  const columns = useAppElement2TableColumns<UnknownObjectRecord>(
    factors,
    enumsMap,
    { isNeedNoColumn: false, isNeedExtensionPath: true }
  );
  const setData = (values: UnknownObjectRecord[]) => {
    const formattedValue = values.map(({ uniqueKey, ...value }) => ({
      ...value,
      uniqueKey: uniqueKey && uniqueKey !== 'add' ? uniqueKey : value.key,
    }));
    onChange?.(formattedValue);
  };

  useEffect(() => {
    if (!insuredDrawerVisible) {
      setData(value?.filter(item => item.key !== 'add'));
    }
  }, [insuredDrawerVisible]);
  return (
    <div id={id}>
      <EditableTable<UnknownObjectRecord>
        className={nestedInBg && styles.objectTable}
        title={<TextBody weight={700}>{t(title)}</TextBody>}
        rowKey="uniqueKey"
        scroll={{ x: 'max-content' }}
        columns={columns}
        dataSource={value}
        setDataSource={setData}
        readonly={!hasEditAuth || readPretty}
        pagination={false}
        addBtnProps={{
          type: 'default',
          ghost: false,
        }}
      />
    </div>
  );
};
