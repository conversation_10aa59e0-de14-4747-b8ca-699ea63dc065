import {
  FC,
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, PaginationProps } from 'antd';
import { NamePath } from 'antd/es/form/interface';
import { ColumnProps } from 'antd/es/table';

import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import { find, isEmpty, size } from 'lodash-es';

import {
  EditableTable,
  FilterDropdownCheckbox,
  FilterDropdownInput,
  Icon,
  PaginationType,
  TextBody,
  message,
} from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface';
import {
  CommercialLineService,
  CommercialLineTypes,
  FactorsType,
  ObjectComponentEnum,
  ObjectType,
  PolicyStage,
  PosFileType,
  ProposalCommonService,
  ProposalCommonTypes,
  ProposalService,
} from 'genesis-web-service';

import { useAppElement2TableColumns } from '@uw/hook/useAppElementsToFields';
import { embedProposalEntryPropsAtom } from '@uw/pages/embed/proposal-entry/atom';
import styles from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.module.scss';
import {
  activeTabKeyAtom,
  asComponentAtom,
  commercialLinePolicyInsuredObjectListAtom,
  hasEditAuthAtom,
  isPosProposalEntryScenarioAtom,
  isQueryAtom,
  isReadPrettyAtom,
  policyListAtom,
  temporaryIdAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { ClearAllButton } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/ClearAll';
import { TemplateDownload } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/TemplateDownload';
import { UploadFile } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/UploadFile';
import { useSpecificInfo } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/SpecificInfo.Provider';
import {
  INSURED_OBJECT_LIST,
  getFieldKey,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/components/insured-subject';

interface ProductListProps {
  categorizedFactors: FactorsType[] | undefined;
  form: FormInstance;
  enumsMap: Record<string, BizDict[]>;
  fieldName?: NamePath;
  isLocation?: boolean;
  nestedInBg?: boolean; // 在location object下的表格需要特殊header底色
  categorizedEnumsMap?: Record<ObjectComponentEnum, Record<string, BizDict[]>>;
  objectType?: ObjectType;
  fileUniqueCodeListRef?: MutableRefObject<string[]>;
  newRowListRef?: MutableRefObject<string[]>;
}

const initialPagination = {
  current: 1,
  pageSize: 10,
};

export const ProductList: FC<ProductListProps> = ({
  categorizedFactors,
  enumsMap,
  form,
  fieldName,
  isLocation,
  nestedInBg = false,
  objectType,
  fileUniqueCodeListRef,
  newRowListRef,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const appElement2TableColumns = useAppElement2TableColumns(
    categorizedFactors!,
    enumsMap,
    {
      isNeedNoColumn: false,
    }
  );

  const readPretty = useAtomValue(isReadPrettyAtom);
  const hasEditAuth = useAtomValue(hasEditAuthAtom);
  const asComponent = useAtomValue(asComponentAtom);
  const isQuery = useAtomValue(isQueryAtom);
  const activeTabKey = useAtomValue(activeTabKeyAtom);
  const policyList = useAtomValue(policyListAtom);
  const embedProposalEntryProps = useAtomValue(embedProposalEntryPropsAtom);
  const isPosProposalEntryScenario = useAtomValue(
    isPosProposalEntryScenarioAtom
  );
  const { uniqueKey } = useSpecificInfo();
  const commercialLinePolicyInsuredObjectList = useAtomValue(
    commercialLinePolicyInsuredObjectListAtom
  );

  const [goodsId, planId, packageId] = useMemo(() => {
    const goods = policyList.find(policy => policy.uniqueKey === activeTabKey)
      ?.goodsList?.[0];
    return [goods?.goodsId, goods?.planId, goods?.packageId];
  }, [activeTabKey, policyList]);
  const productObjectService = embedProposalEntryProps?.service?.productObject;
  const businessData = embedProposalEntryProps?.service?.businessData;
  const queryProductObjectList =
    productObjectService?.getProductObjectList ??
    CommercialLineService.getProductObjectList;
  const addProductObject =
    productObjectService?.addProductObject ??
    CommercialLineService.addProductObject;
  const updateProductObject =
    productObjectService?.updateProductObject ??
    CommercialLineService.updateProductObject;
  const deleteProductObject =
    productObjectService?.deleteProductObject ??
    CommercialLineService.deleteProductObject;

  const submitService =
    isPosProposalEntryScenario && businessData?.submitBusinessData
      ? (data: Record<string, unknown>) => {
          return businessData?.submitBusinessData({
            ...data,
            posFileType: PosFileType.LiabilityProduct,
          });
        }
      : ProposalCommonService.uploadFile;
  const uploadFileAndCheckData =
    isPosProposalEntryScenario && businessData?.uploadFileAndCheckData
      ? (formData: FormData) => {
          formData.append('posFileType', PosFileType.LiabilityProduct);
          formData.append('objectType', objectType ?? '');
          return businessData?.uploadFileAndCheckData(formData);
        }
      : undefined;
  const downloadBusinessData =
    isPosProposalEntryScenario && businessData?.downloadBusinessData
      ? (data?: Record<string, unknown>) =>
          businessData?.downloadBusinessData(PosFileType.LiabilityProduct, data)
      : ProposalCommonService.downloadBusinessData;
  const clearAllData =
    isPosProposalEntryScenario && businessData?.clearAllBusinessData
      ? () => businessData?.clearAllBusinessData(PosFileType.LiabilityProduct)
      : ProposalCommonService.clearAllData;
  const downloadBusinessTemplate =
    isPosProposalEntryScenario && businessData?.downloadBusinessTemplate
      ? (
          ...[
            _businessType,
            _goodsId,
            _planId,
            _packageId,
            _temporaryId,
            objectType,
          ]: Parameters<typeof ProposalCommonService.downloadBusinessTemplate>
        ) =>
          businessData?.downloadBusinessTemplate(PosFileType.LiabilityProduct, {
            objectType,
          })
      : ProposalCommonService.downloadBusinessTemplate;

  const objectUniqueKey: string = Form.useWatch(
    getFieldKey('uniqueKey', fieldName),
    form
  );
  const temporaryId = useAtomValue(temporaryIdAtom);
  const stage = asComponent ? PolicyStage.UNDERWRITING : undefined;

  // 正在编辑的数据，当editRecord存在时，upload与clear all要disabled,否则点击add new之后，再点击upload按钮，会导致add new按钮一直disabled
  const [editRecord, setEditRecord] =
    useState<Partial<CommercialLineTypes.EmployeeObject>>();
  const [submitting, setSubmitting] = useState(false);
  const [pagination, setPagination] = useState(initialPagination);
  const [queryPagination, setQueryPagination] = useState<
    { paginationType?: PaginationType } & PaginationProps
  >(initialPagination);
  const [filters, setFilters] =
    useState<Partial<CommercialLineTypes.ProductObject>>(null);

  const [dataSource, setDataSource] =
    useState<CommercialLineTypes.ProductObject[]>();

  const onFilterObj = useCallback<
    (dataIndex: string) => {
      onFilter?: ColumnProps['onFilter'];
    }
  >(
    dataIndex =>
      isQuery
        ? {
            onFilter: (value, record) =>
              record[dataIndex]
                .toString()
                .toLowerCase()
                .includes((value as string).toLowerCase()),
          }
        : {},
    [isQuery]
  );

  const columns = useMemo<ColumnProps[]>(() => {
    const cols = appElement2TableColumns?.map(column => {
      if (column.dataIndex === 'productType') {
        return {
          ...column,
          filterDropdown: props => (
            <FilterDropdownCheckbox
              {...props}
              filterOptions={
                enumsMap?.productType?.map(item => ({
                  label: item.dictValueName,
                  value: item.enumItemName ?? item.dictValue,
                })) ?? []
              }
            />
          ),
          ...onFilterObj(column.dataIndex),
        };
      }

      if (column.dataIndex === 'productName' || column.dataIndex === 'brand') {
        return {
          ...column,
          filterIcon: <Icon type="search" />,
          filterDropdown: props => <FilterDropdownInput {...props} />,
          ...onFilterObj(column.dataIndex),
        };
      }

      return column;
    });
    return [
      {
        dataIndex: 'index',
        editable: false,
        title: t('No.'),
        render: (txt, record, index) =>
          ((pagination.current ?? initialPagination.current) - 1) *
            (pagination.pageSize ?? initialPagination.pageSize) +
          index +
          1,
      },
      ...cols,
    ];
  }, [
    appElement2TableColumns,
    enumsMap?.productType,
    pagination.current,
    pagination.pageSize,
    onFilterObj,
  ]);
  const {
    run: getProductObjectList,
    loading: isLoading,
    data,
  } = useRequest(
    (
      page: number,
      size: number,
      filterParams?: Partial<CommercialLineTypes.ProductObject>
    ) =>
      queryProductObjectList(
        +temporaryId,
        objectUniqueKey,
        page - 1,
        size,
        filterParams,
        stage
      ),
    {
      manual: true,
      onSuccess: data => {
        setDataSource(data?.content);
        setEditRecord(undefined);
      },
      onError: err => message.error(err?.message),
    }
  );

  useEffect(() => {
    // proposal query | policy query从大接口获取数据不需要再重新请求
    if (isQuery) {
      let data =
        (find(commercialLinePolicyInsuredObjectList[uniqueKey], [
          'uniqueKey',
          objectUniqueKey,
        ])?.[
          ObjectComponentEnum.PRODUCT
        ] as CommercialLineTypes.ProductObject[]) ?? [];
      if (isLocation) {
        const locationUniqueKey = form?.getFieldsValue()?.uniqueKey;
        data =
          (find(
            find(commercialLinePolicyInsuredObjectList[uniqueKey], [
              'uniqueKey',
              locationUniqueKey,
            ])?.[INSURED_OBJECT_LIST],
            ['uniqueKey', objectUniqueKey]
          )?.[
            ObjectComponentEnum.PRODUCT
          ] as CommercialLineTypes.ProductObject[]) ?? [];
      }
      setDataSource(data);
      setQueryPagination({
        ...initialPagination,
        total: size(data),
        paginationType: PaginationType.Frontend,
      });
    } else if (objectUniqueKey && (temporaryId || isPosProposalEntryScenario)) {
      getProductObjectList(
        initialPagination.current,
        initialPagination.pageSize
      );
    }

    setPagination(initialPagination);
    setFilters(null);
  }, [
    objectUniqueKey,
    temporaryId,
    isQuery,
    commercialLinePolicyInsuredObjectList,
    uniqueKey,
    isLocation,
    isPosProposalEntryScenario,
    form,
    getProductObjectList,
  ]);

  const saveProduct = (
    record: CommercialLineTypes.ProductObject,
    editingKey: string
  ) => {
    setSubmitting(true);
    const req =
      editingKey === 'add'
        ? addProductObject(
            temporaryId,
            objectUniqueKey,
            record,
            stage,
            goodsId,
            planId,
            packageId,
            objectType
          )
        : updateProductObject(
            record.id as string,
            {
              ...record,
              stage,
            },
            temporaryId,
            goodsId,
            planId,
            packageId,
            objectType
          );
    return req
      .then((res: string) => {
        if (newRowListRef?.current) {
          newRowListRef.current.push(res);
        }

        getProductObjectList(pagination.current, pagination.pageSize, filters);
      })
      .catch(error => {
        message.error(error.message);
        throw error;
      })
      .finally(() => setSubmitting(false));
  };

  const handleDelete = (
    idx: number,
    record: CommercialLineTypes.ProductObject
  ) => {
    setSubmitting(true);
    deleteProductObject(record.id as string, stage)
      .then(() => {
        getProductObjectList(1, pagination.pageSize, filters);
        setPagination({
          current: 1,
          pageSize: pagination.pageSize,
        });
      })
      .catch(error => message.error(error.message))
      .finally(() => setSubmitting(false));
  };

  const successCallBack = (fileUniqueCode: string) => {
    setPagination(initialPagination);
    getProductObjectList(
      initialPagination.current,
      initialPagination.pageSize,
      filters
    );

    if (fileUniqueCodeListRef?.current) {
      fileUniqueCodeListRef?.current?.push(fileUniqueCode);
    }
  };

  return (
    <>
      <EditableTable
        className={nestedInBg && styles.objectTable}
        readonly={readPretty || !hasEditAuth}
        loading={isLoading}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        columns={columns ?? []}
        dataSource={dataSource ?? []}
        title={<TextBody weight={700}>{t('Product List')}</TextBody>}
        handleCancel={() => setEditRecord(undefined)}
        addBtnProps={{
          type: 'default',
          ghost: false,
          disabled: submitting || isLoading,
          handleAdd: setEditRecord,
        }}
        setDataSource={setDataSource}
        addBtnLeftSection={
          !readPretty && (
            <>
              <UploadFile
                disabled={!!editRecord || !hasEditAuth}
                businessType={ProposalCommonTypes.BusinessType.LiabilityProduct}
                uniqueKey={objectUniqueKey}
                submitCallback={successCallBack}
                submitStep={ProposalCommonTypes.UploadStep.Persistence}
                stage={stage}
                objectType={objectType}
                relatedService={{
                  uploadFileAndCheckData,
                  uploadFile: ProposalService.uploadAttachment,
                  checkFile: ProposalCommonService.uploadFile,
                  downloadBusinessData,
                  submitService,
                  clearAllData,
                }}
              />
              <TemplateDownload
                downloadBusinessTemplateService={downloadBusinessTemplate}
                businessType={ProposalCommonTypes.BusinessType.LiabilityProduct}
                objectType={objectType}
                stage={stage}
              />
              <ClearAllButton
                clearAllDataService={clearAllData}
                businessType={ProposalCommonTypes.BusinessType.LiabilityProduct}
                objectUniqueKey={objectUniqueKey}
                successCallBack={successCallBack}
                disabled={!!editRecord || !hasEditAuth}
              />
            </>
          )
        }
        handleConfirm={saveProduct}
        deleteBtnProps={{
          handleDelete,
          disabled: () => submitting,
        }}
        editBtnProps={{
          disabled: () => submitting,
          handleEdit: setEditRecord,
        }}
        pagination={{
          size: 'small',
          paginationType: PaginationType.Backend,
          total: data?.totalElements,
          ...queryPagination,
          current: pagination.current,
          pageSize: pagination.pageSize,
        }}
        onChange={(pag, filtersParams) => {
          // filtersParams返回的productName，brand为数组，解构给后端
          // 后端要求查询里的productType字段加list
          const transFiltersParams = {
            productTypeList: filtersParams?.productType,
            productName: filtersParams?.productName?.[0],
            brand: filtersParams?.brand?.[0],
          };
          const pageInfo = isEmpty(pag) ? initialPagination : pag;
          setFilters(transFiltersParams);
          setPagination({
            current: pageInfo.current,
            pageSize: pageInfo.pageSize,
          });
          if (!isQuery) {
            getProductObjectList(
              pageInfo.current,
              pageInfo.pageSize,
              transFiltersParams
            );
          }
        }}
      />
    </>
  );
};
