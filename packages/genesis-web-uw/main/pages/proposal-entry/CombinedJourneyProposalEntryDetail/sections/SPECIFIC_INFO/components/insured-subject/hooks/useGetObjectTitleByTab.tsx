import { useTranslation } from 'react-i18next';

import { head } from 'lodash-es';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import { useGetCurrentSectionObjectFactors } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/hooks/useGetCurrentSectionFactors';

export interface GetCurrTitleProps {
  uniqueKey: string;
}

/**
 * 根据当前Tab UniqueKey 获取标的的标题
 *
 * @description
 * 只考虑标的 不考虑其他要素
 * 和useGetCurrentSectionFactors有一些重复取objectElementList的逻辑
 * 是为了给AnchorModule组件使用
 */
const useGetObjectTitleByTab = () => {
  const { t } = useTranslation(['uw', 'common']);
  const objectTypeEnums = useBizDictByKey('objectType');
  const getObjectFactors = useGetCurrentSectionObjectFactors();

  return ({ uniqueKey }: GetCurrTitleProps) => {
    const newFactors = getObjectFactors({ uniqueKey });
    const firstObjectKey = head(Object.keys(newFactors));

    const title = t('{{ objectType }} Info', {
      objectType: objectTypeEnums?.find(
        item => item.enumItemName === firstObjectKey
      )?.dictValueName,
    });

    return title;
  };
};

export { useGetObjectTitleByTab };
