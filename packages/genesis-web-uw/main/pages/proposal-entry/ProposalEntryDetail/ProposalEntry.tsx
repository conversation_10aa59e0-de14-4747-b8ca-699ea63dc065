import React, { FC, useEffect, useMemo, useReducer } from 'react';
import { useParams } from 'react-router-dom';

import { Form } from 'antd';

import { Provider } from 'jotai';

import { PageTemplateTypes } from 'genesis-web-service';

import { usePermission } from '@uw/hook/permission';
import { Mode, SubCategoryEnum } from '@uw/interface/enum.interface';
import { ProposalEntryStateProvider } from '@uw/pages/proposal-entry/ProposalEntryDetail/ProposalEntry.Provider';
import { Permission } from '@uw/util/PermissionKeys';

import {
  ProposalEntryDetailAction,
  ProposalEntryDetailContext,
  ProposalEntryDetailReducer,
  ProposalEntryDetailState,
  UPDATE_HASEDITPERMISSION,
  UPDATE_PAGE_SECTION,
  UPDATE_SUB_CATRGORY,
  UPDATE_TEMPORARY_ID,
  initialContextValue,
} from './ProposalEntryDetailProvider';
import { ProposalEntryLayout } from './ProposalEntryLayout';
import { useQueryProposalDetail } from './hooks/request';

export const ProposalEntry: FC = () => {
  const [state, dispatch] = useReducer<
    React.Reducer<ProposalEntryDetailState, ProposalEntryDetailAction>
  >(ProposalEntryDetailReducer, initialContextValue);

  const form = Form.useForm()?.[0];

  const { proposalDetail } = useQueryProposalDetail(dispatch, form);
  const editAuth = !!usePermission(Permission.NewBusinessProposalTaskPoolEdit);

  const { mode, subCategory, temporaryId } = useParams();

  useEffect(() => {
    if (!dispatch) return;
    // 都为 undefined 代表 proposal entry 页面
    if (dispatch) {
      dispatch({
        type: UPDATE_HASEDITPERMISSION,
        hasEditAuth: editAuth && mode !== Mode.View,
      });
    }
  }, [editAuth, mode, dispatch]);

  useEffect(() => {
    if (!dispatch) return;
    if (dispatch) {
      dispatch({
        type: UPDATE_SUB_CATRGORY,
        subCategory,
      });
    }
  }, [subCategory, dispatch]);

  useEffect(() => {
    if (!dispatch) return;
    // 都为 undefined 代表 proposal entry 页面
    if (dispatch) {
      dispatch({
        type: UPDATE_TEMPORARY_ID,
        temporaryId,
      });
    }
  }, [dispatch, temporaryId]);

  const pageType = useMemo(() => {
    // 兼容后续多产品线 采用 switch
    switch (subCategory) {
      // 寿险
      case SubCategoryEnum.LIFE:
        return PageTemplateTypes.PageType.PROPOSAL_ENTRY_LIFE;
      // 车险
      case SubCategoryEnum.MOTOR:
        return PageTemplateTypes.PageType.PROPOSAL_ENTRY_AUTO;
      default:
        return PageTemplateTypes.PageType.PROPOSAL_ENTRY_LIFE;
    }
  }, [subCategory]);

  useEffect(() => {
    if (!dispatch) return;
    if (dispatch) {
      dispatch({
        type: UPDATE_PAGE_SECTION,
        pageType,
      });
    }
  }, [dispatch, pageType]);

  return (
    <Provider>
      <ProposalEntryDetailContext.Provider
        value={{
          state,
          dispatch,
        }}
      >
        <ProposalEntryStateProvider
          currency={proposalDetail?.policyBasicInfo?.currency}
          form={form}
        >
          <ProposalEntryLayout
            form={form}
            proposalDetail={proposalDetail}
            pageType={pageType}
          />
        </ProposalEntryStateProvider>
      </ProposalEntryDetailContext.Provider>
    </Provider>
  );
};
