/* eslint-disable no-lonely-if */
import { FormInstance } from 'antd';

import {
  cloneDeep,
  concat,
  filter,
  find,
  includes,
  map,
  omit,
  size,
  uniq,
} from 'lodash-es';

import {
  AccountPayerRemark,
  AccountRecord,
  CustomerPartyTypeEnum,
  InferObjectRecord,
} from 'genesis-web-service';

import { ProposalEntryDetailState } from '../ProposalEntryDetailProvider';
import { ProposalEntryPageSections } from '../sections/interface';
import { AccountFormNames } from '../sections/page.config';
import {
  CommonPersonOrCompanyRecord,
  PartyTypeMapToCustomerQueryTypeEnum,
} from './interface';
import { acceptEmptyKeyOrData, hasRemark, removeUndefinedValue } from './util';

// 如果当前角色存在 same as 时，提交给后端时不需要下面字段
const uselessKeys = ['partyType', 'person', 'company'];

export const deleteUselessData = (
  state: CommonPersonOrCompanyRecord[],
  sameAs: boolean
): CommonPersonOrCompanyRecord[] => {
  if (sameAs) return state;
  let newState = cloneDeep(state);
  // not same as

  // 找到 partyType
  const customerType = newState?.[0]
    ?.partyType as NonNullable<CustomerPartyTypeEnum>;
  if (!customerType) return state;
  // 根据 partyType 获取 对应的key company 或者 person
  const key = PartyTypeMapToCustomerQueryTypeEnum?.[customerType];
  if (!key) return state;
  const account = newState?.[0]?.[key]?.account ?? [];
  // 移除只有非必要 key 的 account 数据
  newState = acceptEmptyKeyOrData(key, newState, account);
  return newState;
};

export const updateAccount = (
  state: CommonPersonOrCompanyRecord[]
): CommonPersonOrCompanyRecord[] => {
  let newState = cloneDeep(state);
  // not same as

  // 找到 partyType
  const customerType = newState?.[0]
    ?.partyType as NonNullable<CustomerPartyTypeEnum>;
  if (!customerType) return state;
  // 根据 partyType 获取 对应的key company 或者 person
  const key = PartyTypeMapToCustomerQueryTypeEnum?.[customerType];
  if (!key) return state;
  const account = newState?.[0]?.[key]?.account ?? [];
  // 移除只有非必要 key 的 account 数据
  newState = acceptEmptyKeyOrData(key, newState, account);
  return newState;
};

// 将 firstAccount renewalAccount 通过 same as 加入到对应的 state 中
const operatAccount = (
  accountList: AccountRecord[],
  firstAccount: AccountRecord,
  renewalAccount: AccountRecord,
  isSameAsFirst: boolean
): AccountRecord[] => {
  let account = cloneDeep(accountList);
  // 如果已有 account 以 form 为主 进行覆盖操作
  // https://jira.zaouter.com/browse/GIS-98514 如果选择了已有账户（对比uniqKey）需要给已有账户打上First标签
  const selectedAccount = find(account, ['uniqueKey', firstAccount.uniqueKey]);
  if (hasRemark(account, AccountPayerRemark.FIRST) || selectedAccount) {
    account = map(account, accountItem => {
      const payerTypes = accountItem?.payerTypes ?? [];
      if (includes(payerTypes, AccountPayerRemark.FIRST)) {
        return {
          ...accountItem,
          ...firstAccount,
          payerTypes,
        };
      }
      // 如果当前循环到的账号就是First Account 并且还没有First 这个payerType，打上标签
      if (selectedAccount?.uniqueKey === accountItem.uniqueKey) {
        payerTypes.push(AccountPayerRemark.FIRST);
        return {
          ...accountItem,
          ...firstAccount,
          payerTypes,
        };
      }
      return accountItem;
    });
  } else {
    // firstAccount 填写有值
    if (size(removeUndefinedValue(firstAccount))) {
      // 如果没有 进行 push 操作
      account.push({
        ...firstAccount,
        payerTypes: [AccountPayerRemark.FIRST],
      });
    }
  }

  // 如果 renewal same as first 那么只需在对应的 payerTypes 加入 AccountPayerRemark.RENEWAL
  if (isSameAsFirst) {
    // 重新赋值 account
    account = map(account, accountItem => {
      const payerTypes = accountItem?.payerTypes ?? [];
      // payerTypes 中 包含 FIRST 这个标记，就往这项 account 数据中 push
      if (includes(payerTypes, AccountPayerRemark.FIRST)) {
        return {
          ...accountItem,
          // 将 RENEWAL 合并进包含 FIRST 这个标记的 account 中， 然后对标记进行去重处理
          payerTypes: uniq(concat(payerTypes, [AccountPayerRemark.RENEWAL])),
        };
      }
      return accountItem;
    });
    // same as 到此为止
    return account;
  }

  // RENEWAL 没有 same as FRIST

  // account 数据中包含 RENEWAL 标记（代表为历史数据）
  if (hasRemark(account, AccountPayerRemark.RENEWAL)) {
    // 历史数据中 FIRST 和 RENEWAL 为同一个 account 时，找到相同的那个 account 去移除 RENEWAL 标记

    account = map(account, accountItem => {
      const payerTypes = accountItem?.payerTypes ?? [];

      // 当前项 account 中是否同时包含 FIRST 和 RENEWAL 标记
      const hasBothRemark = [
        AccountPayerRemark.RENEWAL,
        AccountPayerRemark.FIRST,
      ]?.every(remark => includes(payerTypes, remark));

      // -先不要删
      // 如果包含，移除 RENEWAL 标记
      // if (hasBothRemark) {
      //   const newPayerTypes = filter(
      //     payerTypes,
      //     item => item !== AccountPayerRemark.RENEWAL
      //   );
      //   return {
      //     ...accountItem,
      //     payerTypes: newPayerTypes,
      //   };
      // }
      // 如果包含 并且不是同时含有 FIRST 和 RENEWAL 进行替换处理
      if (includes(payerTypes, AccountPayerRemark.RENEWAL) && !hasBothRemark) {
        return {
          ...accountItem,
          ...renewalAccount,
        };
      }
      return accountItem;
    });
    return account;
  }

  // account 数据中不包含 RENEWAL 标记（代表为新增数据）并且 renewalAccount 填写有值
  if (size(removeUndefinedValue(firstAccount))) {
    // 如果没有 进行 push 操作
    account.push({
      ...renewalAccount,
      payerTypes: [AccountPayerRemark.RENEWAL],
    });
  }
  return account;
};

// 当指定的人，没有 same as 时，清除其余人上的 account 的对应标记
const clearAccountRemark = (
  state: CommonPersonOrCompanyRecord[] | undefined,
  remark: AccountPayerRemark
): CommonPersonOrCompanyRecord[] =>
  map(state, item => {
    // 找到 partyType
    const customerType = item?.partyType;
    if (!customerType) return item;
    // 根据 partyType 获取 对应的key company 或者 person
    const key = PartyTypeMapToCustomerQueryTypeEnum?.[customerType];
    if (!key) return item;
    // 设置 account
    const account = map(item?.[key]?.account ?? [], accountInfo => {
      const newAccountInfo = cloneDeep(accountInfo);
      // 获取内部的 payerTypes
      let payerTypes = newAccountInfo?.payerTypes ?? [];
      // 如果包含要过滤掉
      if (includes(payerTypes, remark)) {
        // 过滤下 重新赋值
        payerTypes = filter(payerTypes, payerType => payerType !== remark);
      }
      // 为空时删除掉 payerTypes 属性
      if (!payerTypes?.[0]) {
        delete newAccountInfo.payerTypes;
      } else {
        // 重新复制 payerTypes
        newAccountInfo.payerTypes = payerTypes;
      }
      return newAccountInfo;
    });
    return {
      ...item,
      [key]: {
        ...(item?.[key] ?? {}),
        account,
      },
    };
  });

const replaceStateInfo = ({
  replacedStateParams,
  removeRemarkStateParamsA,
  removeRemarkStateParamsB,
  firstAccount,
  renewalAccount,
  isSameAsFirst,
}: {
  replacedStateParams: CommonPersonOrCompanyRecord[];
  removeRemarkStateParamsA: CommonPersonOrCompanyRecord[];
  removeRemarkStateParamsB: CommonPersonOrCompanyRecord[];
  firstAccount: AccountRecord;
  renewalAccount: AccountRecord;
  isSameAsFirst: boolean;
}): {
  replacedState: CommonPersonOrCompanyRecord[];
  removeRemarkStateA: CommonPersonOrCompanyRecord[];
  removeRemarkStateB: CommonPersonOrCompanyRecord[];
} => {
  const replacedState = replacedStateParams?.map(item => {
    // 找到 partyType
    const customerType = item?.partyType;
    if (!customerType) return item;
    // 根据 partyType 获取 对应的key company 或者 person
    const key = PartyTypeMapToCustomerQueryTypeEnum?.[customerType];
    if (!key) return item;
    // 设置 account
    const account = operatAccount(
      item?.[key]?.account ?? [],
      firstAccount,
      renewalAccount,
      isSameAsFirst
    );
    return {
      ...item,
      [key]: {
        ...(item?.[key] ?? {}),
        account,
      },
    };
  });
  let removeRemarkStateA = clearAccountRemark(
    removeRemarkStateParamsA,
    AccountPayerRemark.FIRST
  );
  removeRemarkStateA = clearAccountRemark(
    removeRemarkStateA,
    AccountPayerRemark.RENEWAL
  );
  let removeRemarkStateB = clearAccountRemark(
    removeRemarkStateParamsB,
    AccountPayerRemark.FIRST
  );
  removeRemarkStateB = clearAccountRemark(
    removeRemarkStateB,
    AccountPayerRemark.RENEWAL
  );

  return {
    replacedState,
    removeRemarkStateA,
    removeRemarkStateB,
  };
};

// 判断各种 same as 关系，执行不同的 情况
const joinInAccountInfo = ({
  paramsState,
  firstValues,
  isSameAsFirst,
  renewalValues,
}: {
  paramsState: ProposalEntryDetailState;
  isSameAsFirst: boolean;
  renewalValues: AccountRecord;
  firstValues: AccountRecord;
}): InferObjectRecord<CommonPersonOrCompanyRecord[]> => {
  let { premiumPayerInfo, policyHolderInfo, insuredInfo } = paramsState || {};
  const payerSameAsSection = premiumPayerInfo?.[0]?.sameAs?.section;
  const holderSameAsSection = policyHolderInfo?.[0]?.sameAs?.section;
  const insuredSameAsSection = insuredInfo?.[0]?.sameAs?.section;

  const firstAccount = firstValues;
  const renewalAccount = isSameAsFirst ? firstValues : renewalValues;

  // 优先找 payer 的 same as 关系
  if (!payerSameAsSection) {
    const { replacedState, removeRemarkStateA, removeRemarkStateB } =
      replaceStateInfo({
        replacedStateParams: premiumPayerInfo,
        removeRemarkStateParamsA:
          policyHolderInfo as CommonPersonOrCompanyRecord[],
        removeRemarkStateParamsB: insuredInfo,
        firstAccount,
        renewalAccount,
        isSameAsFirst,
      });

    premiumPayerInfo = replacedState;
    policyHolderInfo = removeRemarkStateA;
    insuredInfo = removeRemarkStateB;
    // payer same as holder
  } else if (payerSameAsSection === ProposalEntryPageSections.POLICY_HOLDER) {
    // holder not same as
    if (!holderSameAsSection) {
      const { replacedState, removeRemarkStateA, removeRemarkStateB } =
        replaceStateInfo({
          replacedStateParams:
            policyHolderInfo as CommonPersonOrCompanyRecord[],
          removeRemarkStateParamsA: premiumPayerInfo,
          removeRemarkStateParamsB: insuredInfo,
          firstAccount,
          renewalAccount,
          isSameAsFirst,
        });

      policyHolderInfo = replacedState;
      premiumPayerInfo = removeRemarkStateA;
      insuredInfo = removeRemarkStateB;
    } else if (holderSameAsSection === ProposalEntryPageSections.INSURED) {
      const { replacedState, removeRemarkStateA, removeRemarkStateB } =
        replaceStateInfo({
          replacedStateParams: insuredInfo,
          removeRemarkStateParamsA: premiumPayerInfo,
          removeRemarkStateParamsB:
            policyHolderInfo as CommonPersonOrCompanyRecord[],
          firstAccount,
          renewalAccount,
          isSameAsFirst,
        });

      insuredInfo = replacedState;
      premiumPayerInfo = removeRemarkStateA;
      policyHolderInfo = removeRemarkStateB;
    }
  } else if (payerSameAsSection === ProposalEntryPageSections.INSURED) {
    // insured not same as
    if (!insuredSameAsSection) {
      const { replacedState, removeRemarkStateA, removeRemarkStateB } =
        replaceStateInfo({
          replacedStateParams: insuredInfo,
          removeRemarkStateParamsA: premiumPayerInfo,
          removeRemarkStateParamsB:
            policyHolderInfo as CommonPersonOrCompanyRecord[],
          firstAccount,
          renewalAccount,
          isSameAsFirst,
        });

      insuredInfo = replacedState;
      premiumPayerInfo = removeRemarkStateA;
      policyHolderInfo = removeRemarkStateB;
    } else if (
      insuredSameAsSection === ProposalEntryPageSections.POLICY_HOLDER
    ) {
      const { replacedState, removeRemarkStateA, removeRemarkStateB } =
        replaceStateInfo({
          replacedStateParams:
            policyHolderInfo as CommonPersonOrCompanyRecord[],
          removeRemarkStateParamsA: premiumPayerInfo,
          removeRemarkStateParamsB: insuredInfo,
          firstAccount,
          renewalAccount,
          isSameAsFirst,
        });

      policyHolderInfo = replacedState;
      premiumPayerInfo = removeRemarkStateA;
      insuredInfo = removeRemarkStateB;
    }
  }

  return {
    issuancePayerList: premiumPayerInfo,
    proposalHolder: policyHolderInfo as CommonPersonOrCompanyRecord[],
    policyInsurantList: insuredInfo,
  };
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const saveOrSubmitProposalDetailInfo = (
  paramsState: ProposalEntryDetailState,
  form: FormInstance
) => {
  const firstValues = form.getFieldValue(AccountFormNames.FirstAccount);
  const isSameAsFirst = form.getFieldValue(
    AccountFormNames.SameAsInitialAccount
  );
  const renewalValues = form.getFieldValue(AccountFormNames.RenewalAccount);

  form.setFieldValue(AccountFormNames.FirstAccount, firstValues);
  form.setFieldValue(AccountFormNames.RenewalAccount, renewalValues);
  let { issuancePayerList, policyInsurantList, proposalHolder } =
    joinInAccountInfo({
      paramsState,
      firstValues,
      isSameAsFirst,
      renewalValues,
    });

  const isPayerSameAs = !!issuancePayerList?.[0]?.sameAs?.section;

  const isInsuredSameAs = !!policyInsurantList?.[0]?.sameAs?.section;

  const isHolderSameAs = !!proposalHolder?.[0]?.sameAs?.section;

  proposalHolder = deleteUselessData(proposalHolder, isHolderSameAs);
  // payerRemark,
  issuancePayerList = deleteUselessData(issuancePayerList, isPayerSameAs);
  // [],
  policyInsurantList = deleteUselessData(policyInsurantList, isInsuredSameAs);
  // insuredRemark,

  const syncHolderByPartyType = () => {
    const insuredPartyType = policyInsurantList?.[0]?.partyType;

    const initialInsured = paramsState?.initialInsuredInfo?.[0];

    // 如果没有存初始的投被保人信息(partyType必须要存在)，说明是新增的情况这个时候不需要删除相关字段
    // 如果投保人选择same as，并且切换了custom type那么需要清空数据
    if (
      isHolderSameAs &&
      initialInsured?.partyType &&
      initialInsured.partyType !== insuredPartyType
    ) {
      return proposalHolder.map(holder => omit(holder, uselessKeys));
    }

    return proposalHolder;
  };

  return {
    proposalHolder: syncHolderByPartyType(),
    issuancePayerList,
    policyInsurantList,
  };
};
