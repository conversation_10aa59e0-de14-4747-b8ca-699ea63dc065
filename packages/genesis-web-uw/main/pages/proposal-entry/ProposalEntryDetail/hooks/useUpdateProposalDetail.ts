import { Dispatch, useCallback } from 'react';
import { useParams } from 'react-router-dom';

import { FormInstance } from 'antd';

import { ProposalEntryTypes } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { Mode, SubCategoryEnum } from '@uw/interface/enum.interface';

import {
  BACKEND_EXTRA_INFO,
  ProposalEntryDetailAction,
  UPDATE_ATTACHMENT_LIST,
  UPDATE_BASEINFO_PLANID,
  UPDATE_BASICINFO,
  UPDATE_COMMENT_LIST,
  UPDATE_EXTERNALRULE,
  UPDATE_GOODS_ID,
  UPDATE_GOODS_LIST,
  UPDATE_INITIALINSUREDINFO,
  UPDATE_INITIALPOLICYHOLDERINFO,
  UPDATE_INITIALPOLICYRENTER,
  UPDATE_INITIALPREMIUMPAYERINFO,
  UPDATE_INSUREDINFO,
  UPDATE_ISSUANCE_STATUS,
  UPDATE_PACKAGE_ID,
  UPDATE_POLICYHOLDERINFO,
  UPDATE_POLICYRENTERINFO,
  UPDATE_POLICY_CAMPAIGN_LIST,
  UPDATE_PREMIUMPAYERINFO,
  UPDATE_PRODUCTDATANFO,
  UPDATE_RELATION_LIST,
  UPDATE_TAXPREMIUMINFO,
} from '../ProposalEntryDetailProvider';
import { getProviderInitValueFormRes } from '../utils/hasPartyType';
import { setInitAccountInfo } from '../utils/setInitAccountInfo';
import { updateLifeState } from '../utils/updateLifeState';
import { updateMotorState } from '../utils/updateMotorState';

export type UpdateProposalDetailType = (
  proposalEntryInfo: ProposalEntryTypes.SaveProposalRequestBody,
  form: FormInstance,
  extraInfo: {
    subCategory: SubCategoryEnum;
    isUwMotor?: boolean;
  }
) => void;

export const useUpdateProposalDetail = (
  dispatch: Dispatch<ProposalEntryDetailAction>
): {
  updateProposalDetail: UpdateProposalDetailType;
} => {
  const { mode } = useParams();

  const updateProposalDetail: UpdateProposalDetailType =
    useCallback<UpdateProposalDetailType>(
      async (
        proposalEntryInfo: ProposalEntryTypes.SaveProposalRequestBody,
        form: FormInstance,
        extraInfo
      ) => {
        if (mode === Mode.Add) return;
        if (dispatch) {
          // switch 减少不必要的 dispatch
          switch (extraInfo.subCategory) {
            case SubCategoryEnum.LIFE:
              updateLifeState(dispatch, proposalEntryInfo);
              break;
            case SubCategoryEnum.MOTOR:
              updateMotorState(
                dispatch,
                proposalEntryInfo,
                extraInfo.isUwMotor
              );
              break;
            default:
              // 有不确定 subCategory 情况
              updateLifeState(dispatch, proposalEntryInfo);
              updateMotorState(
                dispatch,
                proposalEntryInfo,
                extraInfo.isUwMotor
              );
              break;
          }

          // external rule 手动核保弹窗信息
          dispatch({
            type: UPDATE_EXTERNALRULE,
            externalRule: proposalEntryInfo?.externalRule,
          });

          // policy status
          dispatch({
            type: UPDATE_ISSUANCE_STATUS,
            issuanceStatus: proposalEntryInfo?.issuanceStatus,
          });

          // basicInfo
          dispatch({
            type: UPDATE_BASICINFO,
            basicInfo: {
              // GIS-87513，后端system source只落库到了baseinfo里，沟通后让把additionalInfo也取出来放到baseinfo中，正常baseinfo就是全量的了
              ...proposalEntryInfo?.policyBasicInfo?.additionalInfo,
              ...proposalEntryInfo?.policyBasicInfo,
            },
          });

          // goodsId
          dispatch({
            type: UPDATE_GOODS_ID,
            goodsId: proposalEntryInfo?.policyBasicInfo?.goodsId as number,
          });

          // packageId
          dispatch({
            type: UPDATE_PACKAGE_ID,
            packageId: proposalEntryInfo?.policyBasicInfo?.packageId as number,
          });

          // 从 proposal 的tail 中获取 planId
          dispatch({
            type: UPDATE_BASEINFO_PLANID,
            planId: proposalEntryInfo?.policyBasicInfo?.planId,
          });

          // 后端返回的字段，前端用不到，需要在提交时存到对应的结构内作为入参给到后端
          dispatch({
            type: BACKEND_EXTRA_INFO,
            extraInfo: {
              taskId: proposalEntryInfo?.taskId,
              issuanceNo: proposalEntryInfo?.policyBasicInfo?.issuanceNo,
              bizApplyNo: proposalEntryInfo?.policyBasicInfo?.bizApplyNo,
              policyBasicInfo: proposalEntryInfo?.policyBasicInfo,
              extensions: {
                ...proposalEntryInfo?.extensions,
                ...proposalEntryInfo?.policyBasicInfo?.extensions,
              },
            },
          });

          // holder
          dispatch({
            type: UPDATE_POLICYHOLDERINFO,
            policyHolderInfo: getProviderInitValueFormRes(
              proposalEntryInfo?.policyHolder
            ),
          });

          // initial holder
          dispatch({
            type: UPDATE_INITIALPOLICYHOLDERINFO,
            initialPolicyHolderInfo: getProviderInitValueFormRes(
              proposalEntryInfo?.policyHolder
            ),
          });

          // initial renter
          dispatch({
            type: UPDATE_INITIALPOLICYRENTER,
            initialPolicyRenterInfo: getProviderInitValueFormRes(
              proposalEntryInfo?.policyRenter
            )?.[0],
          });

          dispatch({
            type: UPDATE_POLICYRENTERINFO,
            policyRenterInfo: getProviderInitValueFormRes(
              proposalEntryInfo?.policyRenter
            )?.[0],
          });

          // insured
          dispatch({
            type: UPDATE_INSUREDINFO,
            insuredInfo: proposalEntryInfo?.policyInsurantList ?? [],
          });

          // initial insured
          dispatch({
            type: UPDATE_INITIALINSUREDINFO,
            initialInsuredInfo: proposalEntryInfo?.policyInsurantList ?? [],
          });

          // payer
          dispatch({
            type: UPDATE_PREMIUMPAYERINFO,
            premiumPayerInfo: proposalEntryInfo?.policyPayerList ?? [],
          });

          // initial payer
          dispatch({
            type: UPDATE_INITIALPREMIUMPAYERINFO,
            initialPremiumPayerInfo: proposalEntryInfo?.policyPayerList ?? [],
          });

          // 多 goods 数据
          dispatch({
            type: UPDATE_GOODS_LIST,
            policyGoodsList:
              proposalEntryInfo?.policyGoodsList?.map(item => ({
                ...item,
                effectiveDate:
                  item?.effectiveDate &&
                  (dateFormatInstance.l10nMoment(
                    item?.effectiveDate,
                    proposalEntryInfo.policyBasicInfo?.zoneId
                  ) as unknown as string),
                expiryDate:
                  item?.expiryDate &&
                  (dateFormatInstance.l10nMoment(
                    item?.expiryDate,
                    proposalEntryInfo.policyBasicInfo?.zoneId
                  ) as unknown as string),
              })) ?? [],
          });

          // product data info
          dispatch({
            type: UPDATE_PRODUCTDATANFO,
            productDataInfo: proposalEntryInfo?.policyProductList?.map(
              ({ uniqueKey, ...rest }) => {
                const goodsId = proposalEntryInfo?.policyGoodsList?.find(
                  ({ goodsModuleInfo }) =>
                    goodsModuleInfo?.PRODUCT?.includes(uniqueKey)
                )?.goodsId;
                return { uniqueKey, ...rest, goodsId };
              }
            ),
          });

          // calculate info
          dispatch({
            type: UPDATE_TAXPREMIUMINFO,
            taxPremiumInfo: proposalEntryInfo?.policyBatchCalculateResult,
          });

          // comments list
          dispatch({
            type: UPDATE_COMMENT_LIST,
            commentList: proposalEntryInfo?.comments ?? [],
          });

          // attachment
          dispatch({
            type: UPDATE_ATTACHMENT_LIST,
            issuanceAttachmentList:
              proposalEntryInfo?.policyAttachmentList ?? [],
          });

          // policyCampaignList
          dispatch({
            type: UPDATE_POLICY_CAMPAIGN_LIST,
            policyCampaignList: proposalEntryInfo?.policyCampaignList ?? [],
          });

          // policyRelationList
          dispatch({
            type: UPDATE_RELATION_LIST,
            policyRelationList: proposalEntryInfo?.policyRelationList ?? [],
          });

          // account form
          setInitAccountInfo(proposalEntryInfo, form);
        }
      },
      [mode, dispatch]
    );

  return {
    updateProposalDetail,
  };
};
