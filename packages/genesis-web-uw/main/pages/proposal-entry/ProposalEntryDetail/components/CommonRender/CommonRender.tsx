import { FC, useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import { isEmpty, size } from 'lodash-es';

import { Table } from '@zhongan/nagrand-ui';

import { CustomerPartyTypeEnum, FactorsType } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';

import { ProposalEntryDetailContext } from '../../ProposalEntryDetailProvider';
import { getTableSource } from '../../hooks/getTableSource';
import { CommonPersonOrCompanyRecord, KeyMaybe } from '../../hooks/interface';
import { useCustomerTableColumns } from '../../sections/hooks/config';
import {
  IndividualTableInfo,
  OrganizationTableInfo,
} from '../../sections/interface';

interface Props {
  show: boolean;
  data: CommonPersonOrCompanyRecord[];
  setOpenDrawer: SetState<boolean>;
  handleDelete: () => void;
  stateKey: KeyMaybe;
  customerType?: CustomerPartyTypeEnum;
  applicationFactors?: FactorsType[];
}

export const CommonRender: FC<Props> = ({
  show,
  data,
  setOpenDrawer,
  handleDelete,
  stateKey,
  customerType,
  applicationFactors,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const { state } = useContext(ProposalEntryDetailContext);

  const { individualColumns, companyColumns } = useCustomerTableColumns(
    setOpenDrawer,
    handleDelete,
    applicationFactors
  );

  const tableRender = useMemo(
    () =>
      customerType === CustomerPartyTypeEnum.COMPANY ? (
        <Table
          columns={companyColumns}
          dataSource={
            getTableSource(
              stateKey,
              state?.[stateKey] as CommonPersonOrCompanyRecord[]
            ) as unknown as OrganizationTableInfo[]
          }
          pagination={false}
        />
      ) : (
        <Table
          columns={individualColumns}
          dataSource={
            getTableSource(
              stateKey,
              state?.[stateKey] as CommonPersonOrCompanyRecord[]
            ) as unknown as IndividualTableInfo[]
          }
          pagination={false}
        />
      ),
    [customerType, companyColumns, stateKey, state, individualColumns]
  );

  const render = useMemo(() => {
    // 未选择 customer type 不展示
    if (!show) {
      return null;
    }
    // 数据为空 ||  数据中只有一个 partyType 字段      必须要有编辑权限
    if ((isEmpty(data) || size(data?.[0]) === 1) && state?.hasEditAuth) {
      return (
        <Button
          onClick={() => {
            setOpenDrawer(true);
          }}
          disabled={!state?.hasEditAuth}
        >
          {t('+ Add New')}
        </Button>
      );
    }

    return tableRender;
  }, [data, setOpenDrawer, show, state?.hasEditAuth, t, tableRender]);

  return <section className={show ? 'mt-md' : ''}>{render}</section>;
};
