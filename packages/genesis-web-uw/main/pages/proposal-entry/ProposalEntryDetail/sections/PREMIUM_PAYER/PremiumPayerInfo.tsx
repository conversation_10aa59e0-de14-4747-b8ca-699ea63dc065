import {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Drawer, Modal, Space } from 'antd';

import {
  DataField,
  createForm,
  onFieldValueChange,
  onFormInit,
} from '@formily/core';
import { FormProvider } from '@formily/react';

import { useUpdateEffect } from 'ahooks';
import { isEmpty } from 'lodash-es';
import { v4 as uuidV4 } from 'uuid';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { AddressHelpDrawerModule as Address } from 'genesis-web-component/lib/components/AddressHelpDrawerModule';
import { DrawerModule } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer';
import { getInitData } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer/util';
import { useApplicationElementsEnums } from 'genesis-web-component/lib/hook';
import { useAddressComHelp } from 'genesis-web-component/lib/hook/addressComHelpHook';
import { FactorEnum } from 'genesis-web-component/lib/interface/enum.interface';
import {
  CustomerPartyTypeEnum,
  FactorsType,
  SameAsType,
  UnknownObjectRecord,
} from 'genesis-web-service';

import { Warning } from '@uw/assets/new-icons';
import { useBizDictAntd } from '@uw/hook/useBizDict';

import styles from '../../ProposalEntry.module.scss';
import {
  ProposalEntryDetailContext,
  UPDATE_INITIALPREMIUMPAYERINFO,
  UPDATE_PREMIUMPAYERINFO,
} from '../../ProposalEntryDetailProvider';
import { CommonRender } from '../../components/CommonRender';
import { getSubmitData } from '../../hooks/getSubmitData';
import { getTableSource } from '../../hooks/getTableSource';
import { KeyMaybeEnum } from '../../hooks/interface';
import { useRelatedState } from '../../hooks/useRelatedState';
import { useSameAsCheckBox } from '../../hooks/useSameAsCheckBox';
import { judgeManualUnderwriting, payerRemark } from '../../hooks/util';
import { removeUnNeedAccountToState } from '../../utils/removeUnNeedAccountToState';
import { useDrawerModuleConfig } from '../hooks/useDrawerModuleConfig';
import { useGetCurrentSectionFactors } from '../hooks/useGetCurrentSectionFactors';
import { useGetSameAsInfo } from '../hooks/useGetSameAsInfo';
import { ProposalEntryPageSections, SectionProps } from '../interface';
import { SchemaField } from './SchemaField';

const { confirm } = Modal;
export const PremiumPayerInfo: FC<SectionProps> = ({
  title,
  id,
  render,
  sectionCode,
  relatedFirstSection,
  asComponent,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { isInitialPayerSameAs } = useRelatedState();
  const {
    personFactors,
    companyFactors,
    factors: applicationFactors,
  } = useGetCurrentSectionFactors(ProposalEntryPageSections.PREMIUM_PAYER);

  const { organizationIdTypeEnum, certiTypeEnum, occupationCategoryTree } =
    useDrawerModuleConfig();

  const customerTypeEnums = useBizDictAntd('partyType');
  const { state, dispatch } = useContext(ProposalEntryDetailContext);
  const { isShowSameAs, sectionTitle } = useSameAsCheckBox(
    relatedFirstSection,
    ProposalEntryPageSections.PREMIUM_PAYER
  );

  const { enumsPersonMap, enumsCompanyMap } = useApplicationElementsEnums(
    state?.applicationElementEnumsConfig,
    FactorEnum.PAYER
  );

  const [customerType, setCustomerType] = useState<CustomerPartyTypeEnum>(
    CustomerPartyTypeEnum.INDIVIDUAL
  );
  const [openDrawer, setOpenDrawer] = useState(false);
  const [payerSameAs, setPayerSameAs] = useState(isInitialPayerSameAs);

  const isManualUnderwriting = judgeManualUnderwriting(
    asComponent,
    sectionCode
  );
  useEffect(() => {
    setPayerSameAs(isInitialPayerSameAs);
  }, [isInitialPayerSameAs]);
  const handleDelete = useCallback(() => {
    dispatch({
      type: UPDATE_INITIALPREMIUMPAYERINFO,
      initialPremiumPayerInfo: [
        { partyType: state?.initialPremiumPayerInfo?.[0]?.partyType },
      ],
    });
  }, [dispatch, state?.initialPremiumPayerInfo]);

  const { rootSameAsModule, rootSameAsModuleState } = useGetSameAsInfo(
    ProposalEntryPageSections.PREMIUM_PAYER
  );

  const customerTypeChange = (field: DataField) => {
    setCustomerType(field.value);
    if (field.value) {
      dispatch({
        type: UPDATE_INITIALPREMIUMPAYERINFO,
        initialPremiumPayerInfo: [{ partyType: field.value }],
      });
    }
  };

  const enumsMap = useMemo(
    () =>
      customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? enumsPersonMap
        : enumsCompanyMap,
    [enumsPersonMap, enumsCompanyMap, customerType]
  );

  const form = useMemo(
    () =>
      createForm({
        validateFirst: true,
        disabled: !state?.hasEditAuth,
        initialValues: {
          customerType: !isEmpty(state?.initialPremiumPayerInfo)
            ? state?.initialPremiumPayerInfo?.[0]?.partyType
            : CustomerPartyTypeEnum.INDIVIDUAL,
          sameAs: isInitialPayerSameAs,
        },
        effects() {
          onFormInit(formInst => {
            setCustomerType(formInst.values.customerType);
            dispatch({
              type: UPDATE_PREMIUMPAYERINFO,
              // init 判断 setPayerSameAs 初始值 为 true 打上 sameAs relatedFirstSection 标志 否则仍为初始值
              premiumPayerInfo: formInst.values?.sameAs
                ? [
                    {
                      sameAs: { section: relatedFirstSection } as SameAsType,
                    },
                  ]
                : state?.initialPremiumPayerInfo,
            });
          });
          onFieldValueChange('customerType', field => {
            if (
              !field.value ||
              field.value === state?.initialPremiumPayerInfo?.[0]?.partyType
            )
              return;
            if (
              isEmpty(state?.initialPremiumPayerInfo) ||
              Object.keys(state?.initialPremiumPayerInfo?.[0])?.length === 1
            ) {
              customerTypeChange(field);
              return;
            }
            confirm({
              width: 500,
              icon: <Warning />,
              title: t('Confirm'),
              content: t(
                'All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?'
              ),
              onOk: () => customerTypeChange(field),
              onCancel: () => {
                form.setFieldState('customerType', {
                  value: state?.initialPremiumPayerInfo?.[0]?.partyType,
                });
              },
              okText: t('Yes'),
              cancelText: t('No'),
            });
          });
          onFieldValueChange('sameAs', field => {
            setPayerSameAs(field.value);
            // 在每次取消 same as 的时候 清空 premiumPayerInfo 列表
            if (!field.value && isShowSameAs) {
              dispatch({
                type: UPDATE_PREMIUMPAYERINFO,
                premiumPayerInfo: [],
              });
              payerRemark?.forEach(item => {
                removeUnNeedAccountToState({
                  dispatch,
                  otherState: rootSameAsModuleState,
                  payerType: item,
                  rootModule:
                    rootSameAsModule as unknown as ProposalEntryPageSections,
                });
              });
            }
          });
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      state?.hasEditAuth,
      state?.initialPremiumPayerInfo,
      isInitialPayerSameAs,
      dispatch,
      sectionTitle,
      relatedFirstSection,
      isShowSameAs,
    ]
  );

  const handleClose = () => {
    setOpenDrawer(false);
  };

  const handleSubmit = useCallback(
    values => {
      const premiumPayerInfo = getSubmitData({
        key: KeyMaybeEnum.PremiumPayerInfo,
        customerType: customerType as CustomerPartyTypeEnum,
        oldValues: state?.premiumPayerInfo?.[0],
        values,
      });

      dispatch({
        type: UPDATE_INITIALPREMIUMPAYERINFO,
        initialPremiumPayerInfo: [premiumPayerInfo],
      });
      handleClose();
    },
    [customerType, dispatch, state?.premiumPayerInfo]
  );

  const handleCancel = () => {
    confirm({
      width: 500,
      icon: <Warning />,
      title: t('Confirm'),
      content: t('Are you sure to cancel?'),
      onOk: () => handleClose(),
      okText: t('Yes'),
      cancelText: t('No'),
    });
  };

  useUpdateEffect(() => {
    // if true  add the sameAs relatedFirstSection
    if (isShowSameAs) {
      if (payerSameAs) {
        dispatch({
          type: UPDATE_PREMIUMPAYERINFO,
          premiumPayerInfo: [
            {
              sameAs: {
                section: relatedFirstSection,
              },
              uniqueKey: uuidV4(),
            },
          ],
        });
      } else {
        // 在每次取消 same as 的时候 清空 premiumPayerInfo 列表
        dispatch({
          type: UPDATE_PREMIUMPAYERINFO,
          premiumPayerInfo: [],
        });
      }
    }
  }, [dispatch, isShowSameAs, payerSameAs, relatedFirstSection]);

  const initData = useMemo(
    () =>
      getInitData(
        (
          getTableSource(
            KeyMaybeEnum.PremiumPayerInfo,
            state?.[KeyMaybeEnum.PremiumPayerInfo]
          ) as UnknownObjectRecord[]
        )?.[0]
      ),
    [state]
  );

  const {
    addressOpen,
    setaddressOpen,
    addressForm,
    addAddress,
    handledAddressFields,
    ref,
    extraAreaProps,
    validateCascaderRules,
    setCascaderValue,
    setFields,
    initialValue,
  } = useAddressComHelp({
    factors:
      customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? personFactors
        : companyFactors,
    enumsMap,
    customerType,
    disabled: !state?.hasEditAuth,
  });

  return (
    <>
      <section className={styles.proposalEntrySection} id={id}>
        <SimpleSectionHeader
          weight={500}
          type={'h5'}
          style={{ marginBottom: styles.gapMd }}
        >
          {title}
        </SimpleSectionHeader>
        <section className={styles.formWrapper}>
          <FormProvider form={form}>
            <SchemaField
              scope={{
                t,
                customerTypeEnums,
                gapMd: styles.gapMd,
                isShowSameAs,
                relatedFirstSection,
                sectionTitle,
              }}
              schema={render}
            />
          </FormProvider>
        </section>
        <CommonRender
          show={(customerType as CustomerPartyTypeEnum) && !payerSameAs}
          setOpenDrawer={setOpenDrawer}
          handleDelete={handleDelete}
          customerType={customerType}
          stateKey={KeyMaybeEnum.PremiumPayerInfo}
          data={state?.premiumPayerInfo}
          applicationFactors={applicationFactors as FactorsType[]}
        />
      </section>
      <Divider category="body" />
      {openDrawer && (
        <DrawerModule
          readonly={!state?.hasEditAuth}
          open={openDrawer}
          type={customerType as CustomerPartyTypeEnum}
          drawerTitle={title}
          handleCancel={handleCancel}
          handleSubmit={handleSubmit}
          factors={
            customerType === CustomerPartyTypeEnum.INDIVIDUAL
              ? personFactors
              : companyFactors
          }
          enumsMap={enumsMap}
          sectionCode={sectionCode}
          initData={initData}
          occupationCategoryTree={occupationCategoryTree}
          organizationIdTypeEnum={organizationIdTypeEnum}
          certiTypeEnum={certiTypeEnum}
          zoneId={state.basicInfo?.zoneId as string}
          isManualUnderwriting={isManualUnderwriting}
          ref={ref}
          extraAreaProps={extraAreaProps}
          setOutFields={setFields}
        />
      )}
      {addressOpen && (
        <Drawer
          width={720}
          onClose={() => setaddressOpen(false)}
          open={addressOpen}
          title={t('Add New')}
          footer={
            <Space className="float-right">
              <Button type="primary" onClick={addAddress}>
                {t('Add')}
              </Button>
              <Button onClick={() => setaddressOpen(false)}>
                {t('Cancel')}
              </Button>
            </Space>
          }
        >
          <Address
            initialValue={initialValue}
            form={addressForm}
            selfCustomFields={handledAddressFields}
            validateCascaderRules={validateCascaderRules}
            onCascaderValueChange={setCascaderValue}
          />
        </Drawer>
      )}
    </>
  );
};
