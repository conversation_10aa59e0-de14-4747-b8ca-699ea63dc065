import React, { FC, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Col, Form, Row } from 'antd';

import { useAtomValue } from 'jotai';
import { isEmpty } from 'lodash-es';

import {
  AccountPayerRemark,
  PaymentOrCollection,
  PremiumFrequencyType,
  TransactionType,
} from 'genesis-web-service';

import { PaymentAccount } from '@uw/components/PaymentAccount';
import { TextBody } from '@uw/components/Text';
import { paymentFrequencyAtom } from '@uw/pages/proposal-entry/ProposalEntryDetail/store';

import { removeUndefinedValue } from '../../../../hooks/util';
import { AccountFormNames } from '../../../page.config';
import { AccountProps } from '../../interface';

const RowGutter = 40;

export const RenewalAccount: FC<AccountProps> = ({
  form,
  fillInAccountList,
  hasEditAuth,
  title = 'Installment / Renewal Premium Payment',
}) => {
  const paymentFrequency = useAtomValue(paymentFrequencyAtom);
  const { t } = useTranslation(['uw', 'common']);
  const [sameAsChecked, setSameAsChecked] = useState(
    form?.getFieldValue(AccountFormNames.SameAsInitialAccount) ?? false
  );
  const readonly = useMemo(() => !hasEditAuth, [hasEditAuth]);

  const sameAsPrevForm = useCallback(
    (checked: boolean) => {
      if (!checked) {
        form?.setFieldValue(AccountFormNames.RenewalAccount, {});
      } else {
        form?.setFieldValue(
          AccountFormNames.RenewalAccount,
          form.getFieldValue(AccountFormNames.FirstAccount) ?? {}
        );
      }
      form?.setFieldValue(AccountFormNames.SameAsInitialAccount, checked);
      setSameAsChecked(checked);
    },
    [form]
  );

  const renewalAccountInitialValue = useMemo(() => {
    if (
      isEmpty(
        removeUndefinedValue(
          form?.getFieldValue(AccountFormNames.RenewalAccount)
        )
      )
    ) {
      return fillInAccountList?.find(item =>
        item?.payerTypes?.includes(AccountPayerRemark.RENEWAL)
      );
    }
    return form?.getFieldValue(AccountFormNames.RenewalAccount);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fillInAccountList, form?.getFieldValue(AccountFormNames.RenewalAccount)]);
  // https://jira.zaouter.com/browse/GIS-126679 当缴费频率=single时，录单页面的续期/续保缴费方式的部分自动隐藏
  if (paymentFrequency === PremiumFrequencyType.Single) {
    form.setFieldValue(AccountFormNames.RenewalAccount, {});
    if (sameAsChecked) {
      form.setFieldValue(AccountFormNames.SameAsInitialAccount, false);
      setSameAsChecked(false);
    }
    return null;
  }
  return (
    <>
      <TextBody weight={700} type="h5" className="mb-xs">
        {t(title)}
      </TextBody>
      <Row gutter={RowGutter}>
        <Col span={8}>
          <Form.Item
            name={AccountFormNames.SameAsInitialAccount}
            valuePropName="checked"
          >
            <Checkbox
              onChange={event => {
                sameAsPrevForm(event?.target?.checked);
              }}
              checked={sameAsChecked}
              disabled={readonly}
            >
              {t('Same as Initial Premium Payment')}
            </Checkbox>
          </Form.Item>
        </Col>
      </Row>
      {!sameAsChecked && (
        <PaymentAccount
          totalAccountList={fillInAccountList}
          form={form}
          hasEditAuth={hasEditAuth}
          initialValue={renewalAccountInitialValue}
          transactionType={TransactionType.Renewal}
          paymentOrCollection={PaymentOrCollection.Collection}
          valueField={AccountFormNames.RenewalAccount}
          needFillIn
        />
      )}
    </>
  );
};
