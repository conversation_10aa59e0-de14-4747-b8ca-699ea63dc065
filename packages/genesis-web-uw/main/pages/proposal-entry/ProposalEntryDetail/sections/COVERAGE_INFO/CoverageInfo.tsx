import {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Button, FormInstance } from 'antd';

import { useSetAtom } from 'jotai';

import { Icon, SimpleSectionHeader, TextBody } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { TextLink } from 'genesis-web-component/lib/components/TextLink';

import { getDictLabel, useBizDictByKey } from '@uw/biz-dict/hooks';
import {
  BizDict,
  ProductTypeEnum,
  SaveMode,
} from '@uw/interface/enum.interface';
import { paymentFrequencyAtom } from '@uw/pages/proposal-entry/ProposalEntryDetail/store';
import { getAmountCurrencyString } from '@uw/util/formatAmountCurrency';

import styles from '../../ProposalEntry.module.scss';
import { ProposalEntryDetailContext } from '../../ProposalEntryDetailProvider';
import { TaxPremiumPopup } from '../../components/TaxPremiumPopup';
import { useSaveProposal } from '../../hooks/request';
import { useCoverageProductInfo } from '../hooks/useCoverageProductInfo';
import { ILPProducts, LifeGoodsCategory, SectionProps } from '../interface';
import { CoverageMainProduct, CoverageRiderProduct } from './sub-sections';

export const CoverageInfo: FC<SectionProps> = ({ title, form, id }) => {
  const [t] = useTranslation(['uw', 'common']);
  const { state } = useContext(ProposalEntryDetailContext);
  const { productsMap, mainProductMap, ridersMap } = useCoverageProductInfo();

  const premiumFrequencyTypes = useBizDictByKey('premiumFrequencyType');
  const { handleSaveProposal } = useSaveProposal();
  const setPaymentFrequency = useSetAtom(paymentFrequencyAtom);

  const { mainProductData, riderData, selectedProducts } = useMemo(() => {
    const mainProducts =
      state?.productDataInfo?.filter(
        product =>
          (product.productType as unknown as ProductTypeEnum) ===
          ProductTypeEnum.MAIN
      ) ?? [];
    const riderProducts =
      state?.productDataInfo?.filter(
        product =>
          (product.productType as unknown as ProductTypeEnum) !==
          ProductTypeEnum.MAIN
      ) ?? [];
    const mainProductCombine = mainProducts?.[0]
      ? mainProducts
      : Object.values(mainProductMap ?? {});

    return {
      mainProductData: mainProductCombine,
      riderData: riderProducts,
      selectedProducts: [...mainProductCombine, ...riderProducts],
    };
  }, [mainProductMap, state?.productDataInfo]);
  useEffect(() => {
    const mainProductPaymentFrequency =
      mainProductData?.[0]?.premiumFrequencyType;
    setPaymentFrequency(mainProductPaymentFrequency);
  }, [mainProductData?.[0]?.premiumFrequencyType, setPaymentFrequency]);
  // 根据主险判断tax & premium的显示字段
  const mainProductIsILP = useMemo(
    () =>
      ILPProducts.includes(
        mainProductData?.[0]?.productCategory as LifeGoodsCategory
      ),
    [mainProductData]
  );

  const [showModal, setShowModal] = useState(false);
  const handleQueryDetail = useCallback(() => {
    setShowModal(true);
  }, []);
  const handleCloseModal = useCallback(() => {
    setShowModal(false);
  }, []);

  const taxPremiumInfo = useMemo(() => {
    const currentGoodsPremium =
      state?.taxPremiumInfo?.policyCalculateResultRespList?.[0]; // 单goods取第一个即可
    return currentGoodsPremium?.policyCalculateDetail?.[0]
      ?.policyCalculatePlanPremium;
  }, [state?.taxPremiumInfo?.policyCalculateResultRespList]);

  return (
    <section className={styles.proposalEntrySection} id={id}>
      <SimpleSectionHeader
        weight={500}
        type={'h5'}
        style={{ marginBottom: styles.gapMd }}
      >
        {title}
      </SimpleSectionHeader>
      <section className={styles.formWrapper}>
        <CoverageMainProduct
          mainProductData={mainProductData}
          productsMap={productsMap}
          form={form as FormInstance}
        />
        <CoverageRiderProduct
          riderData={riderData}
          selectedProducts={selectedProducts}
          productsMap={productsMap}
          ridersMap={ridersMap}
          form={form as FormInstance}
        />
      </section>

      <section className={styles.totalPremiumContent}>
        {taxPremiumInfo?.periodFinalPremium && (
          <>
            <TextBody type="caption">{t('Total Premium Amount')}</TextBody>
            <TextLink onClick={handleQueryDetail}>
              {getAmountCurrencyString(
                taxPremiumInfo?.periodFinalPremium,
                state?.basicInfo?.currency
              )}{' '}
              /{' '}
              {getDictLabel(
                premiumFrequencyTypes as BizDict[],
                mainProductData?.[0]?.premiumFrequencyType
              )}
            </TextLink>
          </>
        )}
        <Button
          className={styles.calculateBtn}
          onClick={() => {
            handleSaveProposal(form!, SaveMode.CALCULATE);
          }}
          disabled={!state?.hasEditAuth}
        >
          <Icon type="reload" />
          {t('Recount')}
        </Button>
        <TaxPremiumPopup
          isILP={mainProductIsILP}
          showModal={showModal}
          handleCloseModal={handleCloseModal}
        />
      </section>
      <Divider category="body" />
    </section>
  );
};
