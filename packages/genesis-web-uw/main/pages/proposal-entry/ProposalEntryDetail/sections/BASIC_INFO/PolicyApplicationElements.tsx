import React, { FC, useEffect } from 'react';
import { Col, Form, FormInstance, Row } from 'antd';
import { Input } from '@zhongan/nagrand-ui';
import { useApplicationElementsEnums } from 'genesis-web-component/lib/hook';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import {
  DataTypeEnum,
  DataTypeFieldTypeMap,
  FactorEnum,
  IsExtension,
  IsRequiredEnum,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  ProposalEntryDetailState,
  ProposalEntryDetailAction,
  UPDATE_APPLICATION_ELEMENT_CONFIG,
} from '@uw/pages/proposal-entry/ProposalEntryDetail/ProposalEntryDetailProvider';
import {
  handleApplicationElementsFormRules,
  handleRequired,
} from '@uw/util/handleApplicationElements';
import { getFields } from '@uw/util/getFieldsQueryForm';
import {
  handleFactorDisplayName,
  handleFactorMessage,
} from 'genesis-web-component/lib/util/objectDisplayValue';
import { getAppElementsOptions } from '@uw/hook/useAppElementsToFields';
import { IsUpdateful } from '@uw/interface/enum.interface';
import { FactorsType, MultiCurrency, YesOrNo } from 'genesis-web-service';
import { get } from 'lodash-es';
import { newMultiCurrencyRateData } from '@uw/util/handleMultiCurrency';
import { useProposalEntryState } from '@uw/pages/proposal-entry/ProposalEntryDetail/ProposalEntry.Provider';

import { useGetCurrentSectionFactors } from '../hooks/useGetCurrentSectionFactors';
import { useSetSystemSourceDefaultValue } from './hooks/useSetSystemSourceDefaultValue';

interface SectionProps {
  form: FormInstance;
  state: ProposalEntryDetailState;
  dispatch: React.Dispatch<ProposalEntryDetailAction>;
  sectionCode: string;
  initValues: {
    policyApplicationElements: {
      additionalInfo?: Record<string, string | number>;
      extensions?: Record<string, string | number>;
    };
  };
  asComponent?: boolean;
  multiCurrencyList?: MultiCurrency[] | undefined;
  zoneId?: string;
}

export const PolicyApplicationElements: FC<SectionProps> = ({
  form,
  sectionCode,
  state,
  dispatch,
  initValues,
  asComponent,
  multiCurrencyList,
  zoneId,
}) => {
  const { factors: elements } = useGetCurrentSectionFactors(sectionCode);

  const { enumsMap } = useApplicationElementsEnums(
    state?.applicationElementEnumsConfig,
    FactorEnum.POLICY
  );

  useSetSystemSourceDefaultValue({
    enumsMap,
    elements: elements as FactorsType[],
    form,
  });

  const issueWithGreenCard = Form.useWatch(
    ['policyApplicationElements', 'additionalInfo', 'issueWithGreenCard'],
    form
  );

  useEffect(() => {
    const data = { ...state.applicationElementConfig };
    const index = data.policyElementList?.findIndex(
      item => item.factorCode === 'greenCardNo'
    );
    const required =
      issueWithGreenCard === YesOrNo.YES
        ? IsRequiredEnum.YES
        : IsRequiredEnum.NO;
    if (
      (elements as FactorsType[])?.find(
        item => item.factorCode === 'issueWithGreenCard'
      ) &&
      (elements as FactorsType[])?.find(
        item => item.factorCode === 'greenCardNo'
      ) &&
      data.policyElementList[index] &&
      data.policyElementList[index].isRequired !== required
    ) {
      data.policyElementList[index].isRequired = required;
      dispatch({
        type: UPDATE_APPLICATION_ELEMENT_CONFIG,
        applicationElementConfig: data,
      });
    }
  }, [issueWithGreenCard, state.applicationElementConfig, elements]);

  const { hiddenBaseCurrency } = useProposalEntryState();

  return (
    <section>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        disabled={!state?.hasEditAuth}
        name="policyApplicationElements"
      >
        <Row className="ant-form-row !flex-wrap">
          {(elements as FactorsType[])?.map(field => {
            const path = [
              'policyApplicationElements',
              field.isExtension === IsExtension.Yes
                ? 'extensions'
                : 'additionalInfo',
            ];
            if (field.factorCode === 'nonStandardTariff') {
              path.push('additionalInfo');
            }
            path.push(field.factorCode);
            return (
              <Col
                className="flex-initial w-[calc(33.3%-26.67px)]"
                key={field.factorCode}
              >
                <Form.Item
                  label={handleFactorDisplayName(field)}
                  required={handleRequired(field)}
                  rules={handleApplicationElementsFormRules(field)}
                  name={path}
                  initialValue={
                    [DataTypeEnum.DATE, DataTypeEnum.DATETIME].includes(
                      +field.dataType
                    )
                      ? dateFormatInstance.l10nMoment(
                          get(initValues, path),
                          zoneId
                        )
                      : get(initValues, path)
                  }
                >
                  {getFields({
                    type: DataTypeFieldTypeMap[Number(field.dataType)],
                    placeholder: handleFactorMessage(
                      field.dataType as DataTypeEnum
                    ),
                    options: getAppElementsOptions(
                      enumsMap?.[field.factorCode],
                      field
                    ),
                    disabled:
                      ['systemSource'].includes(field.factorCode) ||
                      !state?.hasEditAuth ||
                      (field.isUpdatableWhenUw === IsUpdateful.NO &&
                        asComponent),
                  })}
                </Form.Item>
              </Col>
            );
          })}
          {newMultiCurrencyRateData(multiCurrencyList, hiddenBaseCurrency)?.map(
            rateData => (
              <Col
                className="flex-initial w-[calc(33.3%-26.67px)]"
                key={rateData.label}
              >
                <Form.Item label={rateData.label}>
                  <Input disabled={true} value={rateData.text} />
                </Form.Item>
              </Col>
            )
          )}
        </Row>
      </Form>
    </section>
  );
};
