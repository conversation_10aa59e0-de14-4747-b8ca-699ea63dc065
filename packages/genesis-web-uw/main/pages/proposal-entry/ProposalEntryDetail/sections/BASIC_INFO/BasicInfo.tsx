import { FC, useCallback, useContext, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import {
  Field,
  createForm,
  onFieldReact,
  onFieldValueChange,
  onFormInit,
  onFormValuesChange,
} from '@formily/core';
import { FormProvider } from '@formily/react';
import { IAction, action } from '@formily/reactive';

import { useAsyncEffect } from 'ahooks';
import { isArray, size } from 'lodash-es';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import {
  ProposalEntryTypes,
  ProposalService,
  YesOrNo,
} from 'genesis-web-service';
import { dateFormatInstance, useL10n } from 'genesis-web-shared/lib/l10n';

import { useTenantTimeZone } from '@uw/hook/useTenantTimeZone';
import { BizDict, TodoOrDoneStatus } from '@uw/interface/enum.interface';
import {
  queryCampaign,
  queryGoods,
  queryPackages,
} from '@uw/pages/proposal-entry/hooks/request';
import { messagePopup } from '@uw/util/messagePopup';
import { transferItemToOption } from '@uw/util/transferBizDictToOption';

import styles from '../../ProposalEntry.module.scss';
import {
  ProposalEntryDetailContext,
  UPDATE_BASEINFO_PLANID,
  UPDATE_BASICINFO,
  UPDATE_GOODS_CATEGORY_ID,
  UPDATE_PRODUCTDATANFO,
  UPDATE_PRODUCTNFO,
  UPDATE_PRODUCT_INFO_CONFIG,
  UPDATE_QUESTIONNAIRE_COVERAGE,
} from '../../ProposalEntryDetailProvider';
import { useGetGoodsType } from '../../hooks/useGetGoodsType';
import { useGetCategoryList } from '../hooks/useGetCategoryList';
import { GoodsCategoryId, SectionProps } from '../interface';
import { PolicyApplicationElements } from './PolicyApplicationElements';
import { SchemaField } from './SchemaField';
import { useGetIssuanceNo } from './hooks/useGetIssuanceNo';
import { useUpdateGoodsList } from './hooks/useUpdateGoodsList';

export const BasicInfo: FC<SectionProps> = ({
  title,
  id,
  proposalDetail,
  render,
  form,
  sectionCode,
  asComponent,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const tenantZoneId = useTenantTimeZone();

  // 获取 issuanceNo
  const issuanceNo = useGetIssuanceNo(proposalDetail);
  const { isSameGoods } = useGetGoodsType(GoodsCategoryId.AUTO);
  const { state, dispatch } = useContext(ProposalEntryDetailContext);
  const initZoneId = (state?.basicInfo || {}).zoneId || tenantZoneId;
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [searchParams] = useSearchParams();

  useAsyncEffect(async () => {
    if (!state?.basicInfo?.goodsId || !state?.planId) {
      return;
    }
    try {
      const res = await ProposalService.queryCoverage(
        state?.basicInfo?.goodsId as number,
        {
          planId: state?.planId,
        }
      );
      dispatch({ type: UPDATE_PRODUCTNFO, productInfo: res.products });

      dispatch({
        type: UPDATE_PRODUCT_INFO_CONFIG,
        productDataInitConfig: res.products,
      });
      if (
        !isArray(state?.productDataInfo) &&
        size(state?.productDataInfo) !== 0
      ) {
        if (!state?.productDataInfo) {
          dispatch({
            type: UPDATE_PRODUCTDATANFO,
            productDataInfo: res?.products
              // 过滤掉非必填
              ?.filter(product => product?.required === YesOrNo.YES),
          });
        }
      }

      dispatch({
        type: UPDATE_QUESTIONNAIRE_COVERAGE,
        questionnaireCoverageInfo: res?.questionnaire?.map((item, index) => ({
          ...item,
          // 首期问卷后端不返回状态信息  前端写死为 TODO 状态
          status: TodoOrDoneStatus.TODO,
          key: index + 1,
        })) as unknown as ProposalEntryTypes.CustomerUwList[],
      });
      // 保存 goodsCategoryId
      dispatch({
        type: UPDATE_GOODS_CATEGORY_ID,
        goodsCategoryId: res?.goodsCategoryId,
      });
    } catch (e) {
      messagePopup((e as Error)?.toString(), 'error');
    }
  }, [state?.basicInfo?.goodsId, state?.planId]);

  const [isMulti, setIsMulti] = useState<boolean>(false);
  const [multiCurrencyOptions, setMultiCurrencyOptions] = useState<
    {
      label: string | number;
      value: string | number;
    }[]
  >([]);

  const formatNow = dateFormat.formatTz(new Date(), initZoneId);

  // 获取 categoryList
  const categoryList = useGetCategoryList();

  // 实时将 basicInfo 中的 goods 相关数据同步进 policyGoodsList 中
  useUpdateGoodsList();

  const handleClearProductInfo = useCallback(() => {
    dispatch({
      type: UPDATE_PRODUCTDATANFO,
      productDataInfo: undefined,
    });
    dispatch({
      type: UPDATE_PRODUCTNFO,
      productInfo: undefined,
    });
  }, [dispatch]);

  const basicInfoForm = useMemo(
    () =>
      createForm({
        disabled: !state?.hasEditAuth,
        initialValues: {
          goodsId: +searchParams.get('goodsId'),
          ...proposalDetail?.policyBasicInfo,
          issuanceNo,
          zoneId: initZoneId,
          proposalDate:
            proposalDetail?.policyBasicInfo?.proposalDate ?? formatNow,
          applicationDate:
            proposalDetail?.policyBasicInfo?.applicationDate ?? formatNow,
        },
        effects() {
          onFormInit(formInst => {
            // 不对 basicInfo 进行操作时，也可以获取到表单信息 更改之后触发下方的 onFormValuesChange
            dispatch({
              type: UPDATE_BASICINFO,
              basicInfo: formInst.getFormState().values,
            });
          });
          onFormValuesChange(formInst => {
            const { values } = formInst.getFormState();
            dispatch({
              type: UPDATE_BASICINFO,
              basicInfo: values,
            });
          });
          onFieldValueChange('goodsId', (field, formInst) => {
            const goodsId = field.value;
            const goodsSource = field.dataSource;
            const curGoodsInfo = goodsSource?.find(
              sourceItem => sourceItem?.value === goodsId
            );
            const goodsCategoryId = curGoodsInfo?.goodsCategoryId;
            dispatch({
              type: UPDATE_GOODS_CATEGORY_ID,
              goodsCategoryId,
            });
            handleClearProductInfo();
            formInst.setFieldState('packageId', formState => {
              // eslint-disable-next-line no-param-reassign
              formState.value = undefined;
            });
            formInst.setFieldState('campaignCode', formState => {
              // eslint-disable-next-line no-param-reassign
              formState.value = undefined;
            });
          });
          onFieldReact('goodsId', (field, formInst) => {
            const goodsId = field.value;
            const goodsSource = field.dataSource;
            const curGoodsInfo = goodsSource?.find(
              sourceItem => sourceItem?.value === goodsId
            );
            const goodsCode = curGoodsInfo?.goodsCode;
            formInst.setFieldState('goodsCode', { value: goodsCode });
          });
          onFieldValueChange('packageId', (field, formInst) => {
            const packageId = field.query('packageId').get('value');
            const packageDicts = field.query('packageId').get('dataSource');
            const curPackageInfo = packageDicts?.find(
              packageItem => packageItem.value === packageId
            );
            const planId = curPackageInfo?.planId as number;
            handleClearProductInfo();
            dispatch({
              type: UPDATE_BASEINFO_PLANID,
              planId,
            });
            formInst.setFieldState('planId', { value: planId });
            const currency = field
              .query('packageId')
              .get('dataSource')
              ?.find(
                packageItem => packageItem.value === packageId
              )?.multiCurrency;
            setMultiCurrencyOptions(transferItemToOption(currency));
            setIsMulti(currency?.length > 1);
            formInst.setFieldState('currency', formState => {
              // 多币种展示空可选，单币种展示值不可编辑
              // eslint-disable-next-line no-param-reassign
              formState.value =
                currency?.length > 1 ? undefined : currency?.[0];
            });
          });
          onFieldReact('packageId', (field, formInst) => {
            const packageId = field.query('packageId').get('value');
            const packageDicts = field.query('packageId').get('dataSource');
            const curPackageInfo = packageDicts?.find(
              packageItem => packageItem.value === packageId
            );
            const packageCode = curPackageInfo?.packageCode;
            formInst.setFieldState('packageCode', { value: packageCode });
          });
        },
      }),
    [
      state?.hasEditAuth,
      proposalDetail?.policyBasicInfo,
      issuanceNo,
      dispatch,
      handleClearProductInfo,
      categoryList,
      searchParams.get('goodsId'),
      initZoneId,
    ]
  );

  const formatProposalRequestDate = useCallback(
    (proposalRequestDate: string) =>
      dateFormat.formatTz(
        dateFormatInstance.l10nMoment(proposalRequestDate),
        state.basicInfo?.zoneId
      ),
    [dateFormat, state.basicInfo]
  );

  const fetchGoodsList = (field: Field) => {
    const proposalDate = field.query('proposalDate').get('value');
    const goodsId = field.value;
    queryGoods({
      categoryList,
      proposalRequestDate: formatProposalRequestDate(proposalDate),
    }).then(
      (action as IAction)?.bound?.(data => {
        // eslint-disable-next-line no-param-reassign
        field.dataSource = data?.goodsOpts;
        if (
          field.dataSource &&
          !(data?.goodsOpts as BizDict[])?.find(
            goods => goods.enumItemName === goodsId
          ) &&
          goodsId
        ) {
          messagePopup(t('Goods is not on sale, please check.'), 'warn');
          // eslint-disable-next-line no-param-reassign
          field.value = undefined;
        }
      })
    );
  };

  const fetchPackageOptions = (field: Field) => {
    const goodsID = field.query('goodsId').get('value');
    queryPackages({ goodsId: goodsID }).then(
      (action as IAction)?.bound?.(data => {
        // eslint-disable-next-line no-param-reassign
        field.dataSource = data.packageOpts;
      })
    );
  };

  const fetchCampaign = (field: Field) => {
    const goodsId = field.query('goodsId').get('value') as number;
    const packageId = field.query('packageId').get('value');
    const planId = field
      .query('packageId')
      .get('dataSource')
      ?.find(packageItem => packageItem.value === packageId)?.planId as number;
    const category = field.query('campaignType').get('value') as string;
    queryCampaign({
      goodsId,
      planId,
      category,
    }).then(
      (action as IAction)?.bound?.(data => {
        // eslint-disable-next-line no-param-reassign
        field.dataSource = data;
      })
    );
  };

  return (
    <section className={styles.proposalEntrySection} id={id}>
      <SimpleSectionHeader
        weight={500}
        type={'h5'}
        style={{ marginBottom: styles.gapMd }}
      >
        {title}
      </SimpleSectionHeader>
      <section className={styles.formWrapper}>
        <FormProvider form={basicInfoForm}>
          <SchemaField
            scope={{
              t,
              fetchGoodsList,
              fetchPackageOptions,
              fetchCampaign,
              isSameGoods,
              isMulti,
              multiCurrencyOptions,
            }}
            schema={render}
          />
        </FormProvider>
      </section>

      {form && (
        <PolicyApplicationElements
          form={form}
          state={state}
          dispatch={dispatch}
          sectionCode={sectionCode as string}
          initValues={{
            policyApplicationElements: {
              additionalInfo: proposalDetail?.policyBasicInfo as
                | Record<string, string | number>
                | undefined,
              extensions: proposalDetail?.policyBasicInfo?.extensions,
            },
          }}
          asComponent={asComponent}
          zoneId={state.basicInfo?.zoneId}
        />
      )}
      <Divider category="body" className="!mb-0" />
    </section>
  );
};
