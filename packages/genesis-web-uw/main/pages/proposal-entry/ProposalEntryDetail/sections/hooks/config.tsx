import {
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useMemo,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Popconfirm, Space } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import { Icon } from '@zhongan/nagrand-ui';

import { handleFactorDisplayName } from 'genesis-web-component/lib/util/objectDisplayValue';
import { FactorsType } from 'genesis-web-service';

import { useDict } from '@uw/hook/useDict';
import { i18nFn } from '@uw/util/i18nFn';

import { ProposalEntryDetailContext } from '../../ProposalEntryDetailProvider';
import { IndividualTableInfo, OrganizationTableInfo } from '../interface';
import {
  companyInfoTableColumns,
  individualInfoTableColumns,
} from '../page.config';

export const useCustomerTableColumns = (
  setOpenDrawer: Dispatch<SetStateAction<boolean>>,
  handleDelete: () => void,
  applicationFactors?: FactorsType[]
) => {
  const { t } = useTranslation(['uw', 'common']);
  const { state } = useContext(ProposalEntryDetailContext);
  const [genderMap, certiTypeMap, organziationIdTypeMap] = useDict([
    'gender',
    'certiType',
    'organizationIdType',
  ]);
  const handleEdit = useCallback(() => {
    setOpenDrawer(true);
  }, []);
  const actionCol = useMemo(
    () => ({
      title: i18nFn('Actions'),
      dataIndex: 'action',
      align: 'right',
      fixed: 'right',
      render: () => (
        <Space>
          {state?.hasEditAuth ? (
            <>
              <Icon
                type="edit"
                style={{ cursor: 'pointer' }}
                onClick={handleEdit}
              />
              <Popconfirm
                title={t('Confirm to delete?')}
                onConfirm={handleDelete}
              >
                <Icon type="delete" style={{ cursor: 'pointer' }} />
              </Popconfirm>
            </>
          ) : (
            <>
              <Icon
                type="view"
                style={{ cursor: 'pointer' }}
                onClick={handleEdit}
              />
            </>
          )}
        </Space>
      ),
    }),
    [handleDelete, handleEdit, state?.hasEditAuth]
  );
  const applicationFactorMap = useMemo(
    () =>
      applicationFactors?.reduce(
        (acc, factor) => {
          acc[factor.factorCode] = handleFactorDisplayName(factor);
          return acc;
        },
        {} as Record<string, string>
      ),
    [applicationFactors]
  );
  const individualColumns = useMemo(
    () => [
      ...individualInfoTableColumns(
        certiTypeMap,
        genderMap,
        applicationFactorMap
      ),
      actionCol as ColumnProps<IndividualTableInfo>,
    ],
    [actionCol, certiTypeMap, genderMap, applicationFactors]
  );
  const companyColumns = useMemo(
    () => [
      ...companyInfoTableColumns(organziationIdTypeMap, applicationFactorMap),
      actionCol as ColumnProps<OrganizationTableInfo>,
    ],
    [actionCol, organziationIdTypeMap, applicationFactors]
  );
  return {
    individualColumns,
    companyColumns,
  };
};
