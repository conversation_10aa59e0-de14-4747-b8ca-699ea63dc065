import { FC, useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { FormProvider } from '@formily/react';

import { TextBody } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { ProposalEntryTypes, YesOrNo } from 'genesis-web-service';

import { PeriodUnitMap } from '@uw/interface/enum.interface';
import { ProposalEntryDetailContext } from '@uw/pages/proposal-entry/ProposalEntryDetail/ProposalEntryDetailProvider';

import { SectionProps } from '../interface';
import { PremiumInfoSchemaField } from './SchemaField';

export const PremiumInfo: FC<SectionProps> = ({
  form,
  schema,
  productDetail,
  disabled,
  isILP,
  bizDicts,
  isMainProduct,
  title,
  id,
}) => {
  const { state } = useContext(ProposalEntryDetailContext);
  const [t] = useTranslation(['uw', 'common']);
  const extendedInvestmentPeriodVisible: boolean =
    productDetail?.flexiblePremiumAllocationExtendedInvestment
      ?.allowExtendedInvestmentPeriod === YesOrNo.YES;

  const premiumPeriodTypes = useMemo(
    () =>
      bizDicts?.premiumPeriodType?.filter(bizDict =>
        productDetail?.premiumPeriodTypeList.includes(bizDict.value as string)
      ),
    [productDetail?.premiumPeriodTypeList, bizDicts?.premiumPeriodType]
  );

  const premiumFrequencyTypes = useMemo(
    () =>
      bizDicts?.premiumFrequencyType?.filter(bizDict =>
        productDetail?.premiumFrequencyTypeList?.includes(
          bizDict.value as string
        )
      ),
    [bizDicts?.premiumFrequencyType, productDetail?.premiumFrequencyTypeList]
  );

  const extendedInvestmentPeriodMap = useMemo(() => {
    const investmentPeriodMap = new Map();
    productDetail?.flexiblePremiumAllocationExtendedInvestment?.extendedInvestmentPeriodMap?.forEach(
      (premiumPeriodItem: ProposalEntryTypes.ExtendedInvestmentPeriodMap) =>
        investmentPeriodMap.set(
          premiumPeriodItem.refPremiumPeriodId,
          premiumPeriodItem.extendedInvestmentPeriodMap
        )
    );

    return investmentPeriodMap;
  }, [productDetail?.flexiblePremiumAllocationExtendedInvestment]);

  const getCurRefPremiumPeriodId = (
    premiumPeriodType: ProposalEntryTypes.PremiumPeriodType,
    premiumPeriod: number
  ) =>
    premiumPeriodType === ProposalEntryTypes.PremiumPeriodType.WHOLELIFE
      ? productDetail?.premiumPeriodTypeMap?.[premiumPeriodType]?.[0]
          .refPremiumPeriodId
      : productDetail?.premiumPeriodTypeMap?.[premiumPeriodType].find(
          (value: ProposalEntryTypes.RefPremiumPeriodGroupType) =>
            value.value === premiumPeriod
        )?.refPremiumPeriodId;

  const getExtendedInvestmentPeriodType = (
    premiumPeriod: number,
    premiumPeriodType: ProposalEntryTypes.PremiumPeriodType
  ) => {
    if (premiumPeriodType) {
      return bizDicts?.extendedInvestmentPeriodType?.filter(bizDict =>
        Object.keys(
          extendedInvestmentPeriodMap.get(
            getCurRefPremiumPeriodId(premiumPeriodType, premiumPeriod)
          ) || {}
        ).includes(bizDict.value as string)
      );
    }
    return [];
  };
  const getExtendedInvestmentPeriodValue = (
    premiumPeriod: number,
    premiumPeriodType: ProposalEntryTypes.PremiumPeriodType,
    minimumInvestmentPeriodType: ProposalEntryTypes.PremiumPeriodType
  ) => {
    if (minimumInvestmentPeriodType) {
      return extendedInvestmentPeriodMap
        .get(getCurRefPremiumPeriodId(premiumPeriodType, premiumPeriod))
        ?.[minimumInvestmentPeriodType].map((value: number) => ({
          label: value,
          value,
        }));
    }
    return [];
  };

  return (
    <>
      <section id={id}>
        <TextBody weight={700} className="mb-md">
          {title}
        </TextBody>
        <FormProvider form={form}>
          <PremiumInfoSchemaField
            scope={{
              t,
              isMainProduct,
              currency: state?.basicInfo?.currency,
              productDetail,
              PeriodUnitMap,
              isILP,
              disabled,
              premiumPeriodTypes,
              premiumFrequencyTypes,
              extendedInvestmentPeriodMap,
              extendedInvestmentPeriodVisible,
              getExtendedInvestmentPeriodType,
              getExtendedInvestmentPeriodValue,
            }}
            schema={schema}
          />
        </FormProvider>
      </section>
      <Divider category="body" />
    </>
  );
};
