import { Schema } from '@formily/react';

export const schema = new Schema({
  type: 'object',
  properties: {
    FormLayout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelCol: 6,
        wrapperCol: 24,
        layout: 'vertical',
        colon: false,
      },
      properties: {
        row1: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-validator': [],
          'x-component-props': {
            columnGap: 40,
            maxColumns: 3,
          },
          'x-designable-id': 'row1',
          'x-index': 0,
          properties: {
            premiumPeriodType: {
              type: 'string',
              title: "{{t('Premium Period Type')}}",
              required: '{{!isILP}}',
              'x-decorator': 'FormItem',
              'x-component': 'SearchSelect',
              'x-designable-id': 'premiumPeriodType',
              'x-index': 0,
              'x-component-props': {
                placeholder: "{{t('Please select')}}",
              },
              'x-reactions': {
                fulfill: {
                  state: {
                    dataSource: '{{premiumPeriodTypes}}',
                  },
                },
              },
            },
            premiumPeriod: {
              type: 'string',
              title: "{{t('Premium Period')}}",
              required: '{{!isILP}}',
              'x-decorator': 'FormItem',
              'x-component': 'SearchSelect',
              'x-designable-id': 'premiumPeriod',
              'x-index': 1,
              'x-component-props': {
                placeholder: "{{t('Please select')}}",
              },
              'x-reactions': {
                dependencies: ['premiumPeriodType'],
                when: '{{$deps[0] === "WHOLELIFE" || $deps[0] === "SINGLE"}}',
                fulfill: {
                  state: {
                    disabled: true,
                    value: null,
                  },
                },
                otherwise: {
                  state: {
                    disabled: '{{disabled}}',
                    value: null,
                    dataSource: `{{((productDetail?.premiumPeriodTypeMap || {})[$deps[0]])?.map(value => ({
                      label: value.value,
                      value: value.value
                    }))}}`,
                  },
                },
              },
            },
            premiumFrequencyType: {
              type: 'string',
              title: "{{t('Premium Frequency')}}",
              required: '{{!isILP}}',
              'x-decorator': 'FormItem',
              'x-component': 'SearchSelect',
              'x-designable-id': 'premiumFrequencyType',
              'x-index': 2,
              'x-component-props': {
                placeholder: "{{t('Please select')}}",
              },
              'x-reactions': [
                {
                  dependencies: ['premiumPeriodType'],
                  when: '{{$deps[0] === "SINGLE"}}',
                  fulfill: {
                    state: {
                      disabled: true,
                      value: 'SINGLE',
                      dataSource: '{{premiumFrequencyTypes}}',
                    },
                  },
                },
                {
                  dependencies: ['premiumPeriodType'],
                  when: '{{$deps[0] === "YEARFULL"}}',
                  fulfill: {
                    state: {
                      disabled: '{{disabled}}',
                      dataSource: `{{premiumFrequencyTypes?.map((item) => item.value === "SINGLE" ? ({ ...item, disabled: true }) : item)}}`,
                      value: `{{$form.values.premiumFrequencyType === "SINGLE" ? null : $form.values.premiumFrequencyType}}`,
                    },
                  },
                },
                {
                  dependencies: ['premiumPeriodType'],
                  when: '{{["SUIFULL", "MONTHFULL", "WHOLELIFE"].includes($deps[0])}}',
                  fulfill: {
                    state: {
                      disabled: '{{disabled}}',
                      dataSource: '{{premiumFrequencyTypes}}',
                    },
                  },
                },
                {
                  dependencies: ['premiumPeriodType'],
                  when: '{{!$deps[0]}}',
                  fulfill: {
                    state: {
                      disabled: '{{disabled}}',
                      dataSource: [],
                      value: null,
                    },
                  },
                },
              ],
            },
            plannedPremiumAmount: {
              type: 'string',
              title: "{{t('Planned Premium Amount')}}",
              'x-decorator': 'FormItem',
              'x-component': 'NumberPicker',
              'x-designable-id': 'plannedPremiumAmount',
              'x-index': 3,
              'x-visible': '{{isILP}}',
              'x-component-props': {
                placeholder: "{{t('Please input')}}",
                addonBefore: '{{ currency }}',
              },
              'x-reactions': {
                dependencies: ['calculationMethod', 'plannedCalculateMethod'],
                when: `{{$deps[0] === 3 || $deps[1] === 3}}`,
                fulfill: {
                  state: {
                    disabled: true,
                  },
                },
                otherwise: {
                  state: {
                    disabled: '{{disabled}}',
                  },
                },
              },
            },
            minimumInvestmentPeriodType: {
              type: 'string',
              title: "{{t('Minimum Investment Period Type')}}",
              'x-decorator': 'FormItem',
              'x-component': 'SearchSelect',
              'x-designable-id': 'minimumInvestmentPeriodType',
              'x-index': 4,
              'x-visible': '{{extendedInvestmentPeriodVisible}}',
              'x-component-props': {
                placeholder: "{{t('Please select')}}",
              },
              'x-reactions': {
                dependencies: ['premiumPeriod', 'premiumPeriodType'],
                fulfill: {
                  state: {
                    disabled: '{{disabled}}',
                    dataSource: `{{getExtendedInvestmentPeriodType($deps[0], $deps[1])}}`,
                    value: null,
                  },
                },
              },
            },

            minimumInvestmentPeriodValue: {
              type: 'number',
              title: "{{t('Minimum Investment Period Value')}}",
              'x-decorator': 'FormItem',
              'x-component': 'SearchSelect',
              'x-designable-id': 'minimumInvestmentPeriodValue',
              'x-index': 5,
              'x-visible': '{{extendedInvestmentPeriodVisible}}',
              'x-component-props': {
                placeholder: "{{t('Please select')}}",
              },
              'x-reactions': {
                dependencies: [
                  'premiumPeriod',
                  'premiumPeriodType',
                  'minimumInvestmentPeriodType',
                ],
                fulfill: {
                  state: {
                    disabled: '{{disabled}}',
                    dataSource: `{{getExtendedInvestmentPeriodValue($deps[0], $deps[1],$deps[2])}}`,
                    value: null,
                  },
                },
              },
            },
            periodStandardPremium: {
              type: 'string',
              title: "{{t('Premium')}}",
              'x-decorator': 'FormItem',
              'x-component': 'NumberPicker',
              'x-designable-id': 'periodStandardPremium',
              'x-index': 6,
              'x-visible': '{{!isILP && isMainProduct}}',
              'x-component-props': {
                placeholder: "{{t('Please input')}}",
                addonBefore: '{{ currency }}',
              },
              'x-reactions': {
                dependencies: ['calculationMethod'],
                when: `{{$deps[0] !== 2 }}`,
                fulfill: {
                  state: {
                    disabled: true,
                    required: false,
                  },
                },
                otherwise: {
                  state: {
                    disabled: '{{disabled}}',
                    required: true, // 保额算保费
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});
