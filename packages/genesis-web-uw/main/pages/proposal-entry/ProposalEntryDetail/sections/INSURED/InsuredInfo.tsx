import {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Drawer, Modal, Space } from 'antd';

import {
  DataField,
  createForm,
  onFieldInputValueChange,
  onFieldValueChange,
} from '@formily/core';
import { FormProvider } from '@formily/react';

import { useUpdateEffect } from 'ahooks';
import { isEmpty, isUndefined, size } from 'lodash-es';
import { v4 as uuidV4 } from 'uuid';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { AddressHelpDrawerModule as Address } from 'genesis-web-component/lib/components/AddressHelpDrawerModule';
import { DrawerModule } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer';
import { getInitData } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer/util';
import { SectionWrapperWithBg } from 'genesis-web-component/lib/components/SectionWrapper';
import {
  useApplicationElementsEnums,
  useQueryDynamicByMatrix,
} from 'genesis-web-component/lib/hook';
import { useAddressComHelp } from 'genesis-web-component/lib/hook/addressComHelpHook';
import {
  CustomerPartyTypeEnum,
  ElementTopic,
  FactorsType,
  SchemaDefType,
  UnknownObjectRecord,
} from 'genesis-web-service';

import { Warning } from '@uw/assets/new-icons';
import { useBizDictAntd } from '@uw/hook/useBizDict';
import {
  FactorEnum,
  InsuredAndPolicyHolder,
  SubCategoryEnum,
} from '@uw/interface/enum.interface';
import { useQueryDynamicFields } from '@uw/pages/proposal-entry/ProposalEntryDetail/sections/hooks/useQueryDynamicDetail';

import styles from '../../ProposalEntry.module.scss';
import {
  ProposalEntryDetailContext,
  UPDATE_BENFICIARY,
  UPDATE_INITIALINSUREDINFO,
  UPDATE_INSUREDINFO,
} from '../../ProposalEntryDetailProvider';
import { CommonRender } from '../../components/CommonRender';
import { NewCommonRender } from '../../components/NewCommonRender';
import { getSubmitData } from '../../hooks/getSubmitData';
import { getTableSource } from '../../hooks/getTableSource';
import { KeyMaybeEnum } from '../../hooks/interface';
import { useRelatedState } from '../../hooks/useRelatedState';
import { useSameAsCheckBox } from '../../hooks/useSameAsCheckBox';
import { insuredRemark, judgeManualUnderwriting } from '../../hooks/util';
import { removeUnNeedAccountToState } from '../../utils/removeUnNeedAccountToState';
import { useDrawerModuleConfig } from '../hooks/useDrawerModuleConfig';
import { useGetCurrentSectionFactors } from '../hooks/useGetCurrentSectionFactors';
import { useGetSameAsInfo } from '../hooks/useGetSameAsInfo';
import { ProposalEntryPageSections, SectionProps } from '../interface';
import { SchemaField } from './SchemaField';
import { useDisabledSameAs } from './hooks/useDIsabledSameAs';

const dynamicParams = {
  elementTopic: ElementTopic.INSURED,
  schemaDefType: SchemaDefType.CUSTOMER,
};

export const InsuredInfo: FC<SectionProps> = ({
  title,
  id,
  render,
  sectionCode,
  relatedFirstSection,
  asComponent,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const { state, dispatch } = useContext(ProposalEntryDetailContext);
  const { organizationIdTypeEnum, certiTypeEnum, occupationCategoryTree } =
    useDrawerModuleConfig();
  const { isInsuredSameAs } = useRelatedState();
  const {
    personFactors,
    companyFactors,
    factors: applicationFactors,
  } = useGetCurrentSectionFactors(ProposalEntryPageSections.INSURED);

  const { enumsPersonMap, enumsCompanyMap } = useApplicationElementsEnums(
    state?.applicationElementEnumsConfig,
    FactorEnum.INSURANT
  );
  const customerTypeEnums = useBizDictAntd('partyType');
  const { isShowSameAs, sectionTitle } = useSameAsCheckBox(
    relatedFirstSection,
    ProposalEntryPageSections.INSURED
  );
  const [insuredSameAs, setInsuredSameAs] = useState(isInsuredSameAs);
  const [customerType, setCustomerType] = useState<
    CustomerPartyTypeEnum | undefined
  >(CustomerPartyTypeEnum.INDIVIDUAL);

  const isManualUnderwriting = judgeManualUnderwriting(
    asComponent,
    sectionCode
  );

  // - Matrix打通
  const { data = [] } = useQueryDynamicFields(dynamicParams);
  const dynamicFields = useMemo(
    () =>
      data.reduce(
        (acc, curr) => acc.concat(curr?.source || []),
        [] as string[]
      ),
    [data]
  );

  const queryDynamicDetail = useQueryDynamicByMatrix({
    dynamicFields: data,
    ...dynamicParams,
  });

  // 初始化 insuredSameAs
  useEffect(() => {
    setInsuredSameAs(isInsuredSameAs);
  }, [isInsuredSameAs]);

  // 初始化 customerType
  useEffect(() => {
    setCustomerType(
      !isEmpty(state?.initialInsuredInfo?.[0])
        ? (state?.initialInsuredInfo?.[0]?.partyType as CustomerPartyTypeEnum)
        : CustomerPartyTypeEnum.INDIVIDUAL
    );
  }, [state?.initialInsuredInfo]);

  const { rootSameAsModule, rootSameAsModuleState } = useGetSameAsInfo(
    ProposalEntryPageSections.INSURED
  );

  const handleDelete = useCallback(() => {
    dispatch({ type: UPDATE_INSUREDINFO, insuredInfo: [] });
    // 点击删除的时候同时清空受益人
    dispatch({ type: UPDATE_BENFICIARY, proposalBeneficiary: undefined });
  }, [dispatch]);

  const sameAsChange = useCallback(
    (isSameAs: boolean) => {
      if (!isUndefined(isSameAs) && isShowSameAs) {
        if (dispatch) {
          dispatch({
            type: UPDATE_INSUREDINFO,
            insuredInfo: [],
          });
          // 每次 same as 变更的时候 同时需要清空 beneficiary
          dispatch({
            type: UPDATE_BENFICIARY,
            proposalBeneficiary: undefined,
          });
          insuredRemark?.forEach(item => {
            removeUnNeedAccountToState({
              dispatch,
              otherState: rootSameAsModuleState,
              payerType: item,
              rootModule: rootSameAsModule,
            });
          });
        }
      }
    },
    [dispatch, isShowSameAs, rootSameAsModule, rootSameAsModuleState]
  );
  const customerTypeChange = (field: DataField) => {
    setCustomerType(field.value);
    if (field.value) {
      dispatch({
        type: UPDATE_INSUREDINFO,
        insuredInfo: [{ partyType: field.value }],
      });
    }
  };
  const form = useMemo(
    () =>
      createForm({
        validateFirst: true,
        initialValues: {
          customerType,
          sameAs: insuredSameAs ?? isInsuredSameAs,
        },
        disabled: !state?.hasEditAuth,
        effects() {
          onFieldInputValueChange('customerType', field => {
            if (field.value === customerType) return;
            if (
              isEmpty(state?.insuredInfo) ||
              Object.keys(state?.insuredInfo?.[0] ?? {})?.length === 1
            ) {
              customerTypeChange(field);
              return;
            }
            Modal.confirm({
              width: 500,
              icon: <Warning />,
              title: t('Confirm'),
              content: t(
                'Switching customer types will clear existing customer data, please confirm.'
              ),
              onOk: () => customerTypeChange(field),
              onCancel: () => {
                form.setFieldState('customerType', { value: customerType });
              },
              okText: t('Yes'),
              cancelText: t('No'),
            });
          });
          onFieldValueChange('sameAs', field => {
            // same as 为 undefined 不进行处理
            if (isUndefined(field.value)) return;
            // 在每次取消 same as 的时候 清空 insuredInfo 列表
            // same as 为 false 并且 insuredInfo 中的 partyType 有值的时候，打开对话框
            if (field.value && !!state?.insuredInfo?.[0]?.partyType) {
              setModalOpen(true);
              setCustomerType(customerType);
              dispatch({
                type: UPDATE_INSUREDINFO,
                insuredInfo: state?.insuredInfo,
              });
              // 暂时不变更 same as 的值
              // eslint-disable-next-line no-param-reassign
              field.value = false;
            } else {
              // 变更 same as 的值
              setInsuredSameAs(field.value);
              sameAsChange(false);
            }
          });
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      dispatch,
      state?.insuredInfo,
      state?.hasEditAuth,
      isInsuredSameAs,
      isShowSameAs,
      insuredSameAs,
      setModalOpen,
      sectionTitle,
      customerType,
      relatedFirstSection,
    ]
  );

  // insured 处的是否禁用要随着 holder 的客户类型改变
  useDisabledSameAs(form);

  const handleClose = () => {
    setOpenDrawer(false);
  };
  const handleSubmit = useCallback(
    values => {
      const insuredInfo = getSubmitData({
        key: KeyMaybeEnum.InsuredInfo,
        customerType,
        oldValues: state?.insuredInfo?.[0],
        values,
      }) as UnknownObjectRecord;

      dispatch({
        type: UPDATE_INSUREDINFO,
        insuredInfo: [insuredInfo],
      });
      dispatch({
        type: UPDATE_INITIALINSUREDINFO,
        initialInsuredInfo: [insuredInfo],
      });
      handleClose();
    },
    [customerType, state?.insuredInfo, dispatch]
  );

  useUpdateEffect(() => {
    // if true  add the sameAs
    if (isShowSameAs) {
      // 这里的处理主要是解决初始化时会覆盖接口数据导致关联的Beneficiary（insuredInfo中的beneficiaryType丢失）渲染有误
      const insuredInfo = state?.insuredInfo;
      const { sameAs, partyType } = insuredInfo?.[0] || {};
      const isSameInsuredInfo =
        size(insuredInfo) === 1 &&
        sameAs?.section === relatedFirstSection &&
        partyType === customerType;
      if (insuredSameAs && !isSameInsuredInfo) {
        dispatch({
          type: UPDATE_INSUREDINFO,
          // TODO: insuredInfo 与 beneficiary 关联 需要在 save 时额外处理
          insuredInfo: [
            {
              sameAs: {
                section: relatedFirstSection,
              },
              uniqueKey: uuidV4(),
              partyType: customerType as CustomerPartyTypeEnum,
            },
          ],
        });
      }
    }
  }, [
    dispatch,
    insuredSameAs,
    isShowSameAs,
    relatedFirstSection,
    customerType,
  ]);

  const factors = useMemo(
    () =>
      (customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? personFactors
        : companyFactors
      ).map(item => ({
        ...item,
        orderNum: item?.orderNo || item.orderNum,
      })),
    [companyFactors, customerType, personFactors]
  );

  const enumsMap = useMemo(
    () =>
      customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? enumsPersonMap
        : enumsCompanyMap,
    [enumsPersonMap, customerType, enumsCompanyMap]
  );

  const initData = useMemo(
    () =>
      getInitData(
        (
          getTableSource(
            KeyMaybeEnum.InsuredInfo,
            state?.[KeyMaybeEnum.InsuredInfo]
          ) as UnknownObjectRecord[]
        )?.[0]
      ),
    [state]
  );

  const {
    addressOpen,
    setaddressOpen,
    addressForm,
    addAddress,
    handledAddressFields,
    ref,
    extraAreaProps,
    validateCascaderRules,
    setCascaderValue,
    setFields,
    initialValue,
  } = useAddressComHelp({
    factors,
    enumsMap,
    customerType,
    disabled: !state?.hasEditAuth,
  });

  return (
    <>
      <section className={styles.proposalEntrySection} id={id}>
        {state.subCategory === SubCategoryEnum.MOTOR ? (
          <section className="relative">
            <SectionWrapperWithBg
              title={t(sectionCode || '')}
              id={id}
              readOnly={!state?.hasEditAuth}
              dividerClassName={'!mt10 !mb-0'}
              extraButtons={
                (isEmpty(state?.insuredInfo) ||
                  size(state?.insuredInfo?.[0]) === 1) &&
                state?.hasEditAuth && (
                  <Button
                    onClick={() => {
                      setOpenDrawer(true);
                    }}
                    className="right-lg top-md"
                    disabled={!state?.hasEditAuth}
                  >
                    {t('+ Add New')}
                  </Button>
                )
              }
            >
              <FormProvider form={form}>
                <SchemaField
                  scope={{
                    t,
                    isShowSameAs,
                    relatedFirstSection,
                    customerTypeEnums,
                    insuredSameAs,
                    sectionTitle,
                    disabled: !state?.hasEditAuth,
                  }}
                  schema={render}
                />
              </FormProvider>
              <NewCommonRender
                show={(customerType as CustomerPartyTypeEnum) && !insuredSameAs}
                customerType={customerType}
                setOpenDrawer={setOpenDrawer}
                handleDelete={handleDelete}
                stateKey={KeyMaybeEnum.InsuredInfo}
                data={state?.insuredInfo}
                roleType={InsuredAndPolicyHolder.Insured}
              />
            </SectionWrapperWithBg>
          </section>
        ) : (
          <>
            <SimpleSectionHeader
              weight={500}
              type={'h5'}
              style={{ marginBottom: styles.gapMd }}
            >
              {title}
            </SimpleSectionHeader>
            <section className={styles.formWrapper}>
              <FormProvider form={form}>
                <SchemaField
                  scope={{
                    t,
                    isShowSameAs,
                    relatedFirstSection,
                    customerTypeEnums,
                    insuredSameAs,
                    sectionTitle,
                    disabled: !state?.hasEditAuth,
                  }}
                  schema={render}
                />
              </FormProvider>
            </section>
            <CommonRender
              show={(customerType as CustomerPartyTypeEnum) && !insuredSameAs}
              customerType={customerType}
              setOpenDrawer={setOpenDrawer}
              handleDelete={handleDelete}
              stateKey={KeyMaybeEnum.InsuredInfo}
              data={state?.insuredInfo}
              applicationFactors={applicationFactors as FactorsType[]}
            />
            <Divider category="body" />
          </>
        )}
      </section>
      <Modal
        width={500}
        title={<Warning />}
        open={modalOpen}
        onOk={() => {
          setCustomerType(undefined);
          sameAsChange(true);
          setInsuredSameAs(true);
          setModalOpen(false);
        }}
        onCancel={() => {
          setModalOpen(false);
        }}
        cancelText={t('No')}
        okText={t('Yes')}
        closable={false}
        keyboard={false}
        maskClosable={false}
      >
        {t(
          'Insured Info will be cleared and replaced by policyholder Info. Are you sure to proceed?'
        )}
      </Modal>
      {openDrawer && (
        <DrawerModule
          readonly={!state?.hasEditAuth}
          open={openDrawer}
          type={customerType}
          drawerTitle={title}
          handleCancel={handleClose}
          handleSubmit={handleSubmit}
          addBtnClassName={styles.rightAddButton}
          sectionCode={sectionCode}
          factors={factors}
          enumsMap={enumsMap}
          initData={initData}
          occupationCategoryTree={occupationCategoryTree}
          organizationIdTypeEnum={organizationIdTypeEnum}
          certiTypeEnum={certiTypeEnum}
          queryDynamicDetail={queryDynamicDetail}
          matrixFields={dynamicFields}
          isGettingAddress={true}
          zoneId={state.basicInfo?.zoneId as string}
          isManualUnderwriting={isManualUnderwriting}
          ref={ref}
          extraAreaProps={extraAreaProps}
          setOutFields={setFields}
        />
      )}
      {addressOpen && (
        <Drawer
          width={720}
          onClose={() => setaddressOpen(false)}
          open={addressOpen}
          title={t('Add New')}
          footer={
            <Space className="float-right">
              <Button type="primary" onClick={addAddress}>
                {t('Add')}
              </Button>
              <Button onClick={() => setaddressOpen(false)}>
                {t('Cancel')}
              </Button>
            </Space>
          }
        >
          <Address
            initialValue={initialValue}
            form={addressForm}
            selfCustomFields={handledAddressFields}
            validateCascaderRules={validateCascaderRules}
            onCascaderValueChange={setCascaderValue}
          />
        </Drawer>
      )}
    </>
  );
};
