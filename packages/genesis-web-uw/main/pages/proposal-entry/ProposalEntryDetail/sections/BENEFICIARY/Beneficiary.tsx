import {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import AntdIcon from '@ant-design/icons';
import { But<PERSON>, Collapse, Form, RadioChangeEvent } from 'antd';
import { ColumnType } from 'antd/lib/table';

import { cloneDeep } from 'lodash-es';

import { Icon, Table, TextBody } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components/Divider';
import { DrawerModule } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer';
import { getInitData } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer/util';
import { useApplicationElementsEnums } from 'genesis-web-component/lib/hook';
import {
  FactorEnum,
  FieldType,
  Mode,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  CustomerPartyTypeEnum,
  UnknownObjectRecord,
} from 'genesis-web-service';

import { Error } from '@uw/assets/new-icons';
import { useBizDict } from '@uw/hook/useBizDict';
import { BeneficiaryTypeEnum } from '@uw/interface/enum.interface';
import AvatarBgColor from '@uw/pages/policy-query-pages/components/AvatarBgColor';
import { getFields } from '@uw/util/getFieldsQueryForm';

import {
  BeneficiaryType,
  ProposalEntryDetailContext,
  UPDATE_BENFICIARY,
  UPDATE_INSUREDINFO,
} from '../../ProposalEntryDetailProvider';
import { getSubmitData } from '../../hooks/getSubmitData';
import { getTableSource } from '../../hooks/getTableSource';
import {
  CommonPersonOrCompanyRecord,
  KeyMaybeEnum,
} from '../../hooks/interface';
import { judgeManualUnderwriting } from '../../hooks/util';
import { useDrawerModuleConfig } from '../hooks/useDrawerModuleConfig';
import { useGetCurrentSectionFactors } from '../hooks/useGetCurrentSectionFactors';
import { ProposalEntryPageSections } from '../interface';
import { useCustomerTableColumns } from './hooks/config';
import styles from './index.module.scss';

export interface BeneficiaryProps {
  record: BeneficiaryType;
  sectionCode?: string;
  asComponent?: boolean;
}

export const Beneficiary: FC<BeneficiaryProps> = ({
  record,
  sectionCode,
  asComponent,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { state, dispatch } = useContext(ProposalEntryDetailContext);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [form] = Form.useForm();
  const beneficiaryType = useBizDict('beneficiaryType');
  const { organizationIdTypeEnum, certiTypeEnum, occupationCategoryTree } =
    useDrawerModuleConfig();
  const [collapseActivekeys, setCollapseActivekeys] = useState([
    record?.beneficiaryType,
  ]);
  const { personFactors } = useGetCurrentSectionFactors(
    ProposalEntryPageSections.BENEFICIARY
  );

  const { enumsPersonMap } = useApplicationElementsEnums(
    state.applicationElementEnumsConfig,
    FactorEnum.BENEFICIARY
  );

  const isManualUnderwriting = judgeManualUnderwriting(
    asComponent,
    sectionCode
  );

  const handleDelete = useCallback(
    (index: number) => {
      const proposalBeneficiary = cloneDeep(state?.proposalBeneficiary);
      // 删除当前下标
      proposalBeneficiary?.splice(index, 1);
      dispatch({ type: UPDATE_BENFICIARY, proposalBeneficiary });
    },
    [dispatch, state?.proposalBeneficiary]
  );

  const { beneficiaryTableColumns, editIndex, setEditIndex, mode, setMode } =
    useCustomerTableColumns(setOpenDrawer, handleDelete, record);

  const isDesignateBeneficiary = useMemo(
    () => record?.beneficiaryType === BeneficiaryTypeEnum.DESIGNATE_BENEFICIARY,
    [record?.beneficiaryType]
  );

  // 当前被保险人的受益人们
  const currentBeneficaryList = useMemo(
    () =>
      state?.proposalBeneficiary?.filter(
        beneficiary => beneficiary?.uniqueKey === record?.uniqueKey
      ),
    [record?.uniqueKey, state?.proposalBeneficiary]
  );

  const handleClose = useCallback(() => {
    setOpenDrawer(false);
    setMode(Mode.Add);
    setEditIndex(undefined);
  }, [setEditIndex, setMode]);

  const handleSubmit = useCallback(
    values => {
      let totalProposalBeneficiary = cloneDeep(state?.proposalBeneficiary);
      const beneficiaryInfo = getSubmitData({
        key: KeyMaybeEnum.ProposalBeneficiary,
        customerType: CustomerPartyTypeEnum.INDIVIDUAL,
        values,
      });
      if (mode === Mode.Add) {
        // 新增总受益人列表
        totalProposalBeneficiary?.push({
          ...(beneficiaryInfo as BeneficiaryType),
          beneficiaryRatio: 0,
          uniqueKey: record?.uniqueKey as string,
          partyType: CustomerPartyTypeEnum.INDIVIDUAL,
        });
      } else {
        // 过滤出来非当前被保人
        totalProposalBeneficiary = totalProposalBeneficiary?.filter(
          beneficiary => beneficiary?.uniqueKey !== record?.uniqueKey
        );
        // 更改当前受益人列表
        const currentBeneficiaryList = cloneDeep(currentBeneficaryList)?.map(
          (beneficiary, index) => {
            // 找到对应的受益人
            if (index === editIndex) {
              return {
                ...(beneficiaryInfo as BeneficiaryType),
                beneficiaryRatio: beneficiary?.beneficiaryRatio,
                uniqueKey: record?.uniqueKey as string,
                partyType: CustomerPartyTypeEnum.INDIVIDUAL,
              };
            }
            return beneficiary;
          }
        );
        // 然后合并所有被保人
        totalProposalBeneficiary = totalProposalBeneficiary?.concat(
          currentBeneficiaryList as CommonPersonOrCompanyRecord[]
        );
      }
      dispatch({
        type: UPDATE_BENFICIARY,
        proposalBeneficiary: totalProposalBeneficiary,
      });
      handleClose();
    },
    [
      state?.proposalBeneficiary,
      mode,
      dispatch,
      handleClose,
      record?.uniqueKey,
      currentBeneficaryList,
      editIndex,
    ]
  );

  // 更改受益人类型的时候将数据保存到 context 中
  const handleSwitchChange = useCallback(
    (value: BeneficiaryTypeEnum) => {
      const copyedInsuredInfo = cloneDeep(state?.insuredInfo);
      dispatch({
        type: UPDATE_INSUREDINFO,
        insuredInfo: copyedInsuredInfo?.map(insured => {
          const newInsured = cloneDeep(insured);
          if (insured?.uniqueKey === record?.uniqueKey) {
            newInsured.beneficiaryType = value;
          }
          return newInsured;
        }),
      });
      dispatch({
        type: UPDATE_BENFICIARY,
        proposalBeneficiary:
          value === BeneficiaryTypeEnum.LEGAL_BENEFICIARY ? undefined : [],
      });
    },
    [dispatch, record?.uniqueKey, state?.insuredInfo]
  );

  useEffect(() => {
    if (!record?.beneficiaryType) {
      handleSwitchChange(BeneficiaryTypeEnum.LEGAL_BENEFICIARY);
    }
  }, [record?.beneficiaryType]);

  const header = useMemo(
    () => (
      <div className={styles.header}>
        <AvatarBgColor
          name={(record?.person?.fullName as string) || t('No Data')}
          style={{
            width: 40,
            height: 40,
            lineHeight: '40px',
            marginRight: styles.gapMd,
          }}
        />
        <TextBody weight={700} style={{ marginRight: styles.gapMd }}>
          {record?.person?.fullName}
        </TextBody>
        {getFields({
          type: FieldType.Radio,
          defaultValue:
            record?.beneficiaryType ?? BeneficiaryTypeEnum.LEGAL_BENEFICIARY,
          childClassName: styles.radio,
          options: beneficiaryType,
          disabled: !state?.hasEditAuth,
          onChange: (e: RadioChangeEvent) => {
            handleSwitchChange(e?.target?.value);
            setCollapseActivekeys([e?.target?.value as BeneficiaryTypeEnum]);
          },
        })}
      </div>
    ),
    [
      beneficiaryType,
      handleSwitchChange,
      record?.beneficiaryType,
      record?.person?.fullName,
      state?.hasEditAuth,
      t,
    ]
  );

  const renderRate = useMemo(() => {
    const rateAmount = currentBeneficaryList?.reduce(
      // eslint-disable-next-line no-return-assign, no-param-reassign
      (prev, next) => (prev += Number(next?.beneficiaryRatio)),
      0
    );
    const errorMessage = t('Total Beneficiary ratio should be equal to 100%');
    const normalMessage = t('Beneficiary ratio is');
    return (
      <>
        {Number(rateAmount) !== 100 ? (
          <AntdIcon style={{ fontSize: styles.fontSizeLg }} component={Error} />
        ) : (
          <Icon type="done" />
        )}
        <span style={{ margin: '0 6px' }}>
          {Number(rateAmount) !== 100 ? errorMessage : normalMessage}
        </span>
        {Number(rateAmount) === 100 && (
          <span className={styles.percent}>{Number(rateAmount)}%</span>
        )}
      </>
    );
  }, [currentBeneficaryList, t]);
  const handleCollapseChange = useCallback(
    (key: string | string[]) => {
      setCollapseActivekeys(key as BeneficiaryTypeEnum[]);
    },
    [setCollapseActivekeys]
  );
  return (
    <>
      <Collapse
        className={styles.beneficiary}
        expandIconPosition="end"
        activeKey={collapseActivekeys as string[]}
        onChange={handleCollapseChange}
      >
        <Collapse.Panel
          style={{ marginBottom: styles.gapMd }}
          collapsible={
            record?.beneficiaryType ===
            BeneficiaryTypeEnum.DESIGNATE_BENEFICIARY
              ? 'icon'
              : 'disabled'
          }
          header={header}
          key={BeneficiaryTypeEnum.DESIGNATE_BENEFICIARY}
        >
          {isDesignateBeneficiary && (
            <div className={styles.content}>
              <Divider category="body" />
              <Button
                onClick={() => {
                  setOpenDrawer(true);
                  setEditIndex(currentBeneficaryList?.length);
                  setMode(Mode.Add);
                }}
                disabled={!state?.hasEditAuth}
              >
                {t('+ Add New')}
              </Button>
              {currentBeneficaryList?.[0] && (
                <>
                  <Form form={form} layout="vertical">
                    <Table
                      columns={
                        beneficiaryTableColumns as ColumnType<BeneficiaryType>[]
                      }
                      dataSource={
                        getTableSource(
                          KeyMaybeEnum.ProposalBeneficiary,
                          state?.[
                            KeyMaybeEnum.ProposalBeneficiary
                          ] as CommonPersonOrCompanyRecord[],
                          currentBeneficaryList
                        ) as CommonPersonOrCompanyRecord[]
                      }
                      style={{ margin: `${styles.gapMd} 0` }}
                      pagination={false}
                    />
                  </Form>
                  <div className={styles.rate}>{renderRate}</div>
                </>
              )}
            </div>
          )}
        </Collapse.Panel>
      </Collapse>
      {openDrawer && (
        <DrawerModule
          readonly={!state?.hasEditAuth}
          open={openDrawer}
          type={CustomerPartyTypeEnum.INDIVIDUAL}
          drawerTitle={t('Beneficiary')}
          handleCancel={handleClose}
          handleSubmit={handleSubmit}
          sectionCode={sectionCode}
          factors={personFactors}
          enumsMap={enumsPersonMap}
          initData={getInitData(
            (
              getTableSource(
                KeyMaybeEnum.ProposalBeneficiary,
                state?.[
                  KeyMaybeEnum.ProposalBeneficiary
                ] as CommonPersonOrCompanyRecord[],
                currentBeneficaryList
              ) as CommonPersonOrCompanyRecord[]
            )?.[editIndex as number] as UnknownObjectRecord
          )}
          occupationCategoryTree={occupationCategoryTree}
          organizationIdTypeEnum={organizationIdTypeEnum}
          certiTypeEnum={certiTypeEnum}
          zoneId={state.basicInfo?.zoneId as string}
          isManualUnderwriting={isManualUnderwriting}
        />
      )}
    </>
  );
};
