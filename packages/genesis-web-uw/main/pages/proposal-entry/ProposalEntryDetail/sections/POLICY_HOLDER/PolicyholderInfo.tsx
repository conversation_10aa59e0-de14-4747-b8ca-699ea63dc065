import {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { <PERSON><PERSON>, Drawer, Modal, Space } from 'antd';

import {
  DataField,
  Field,
  createForm,
  onFieldInputValueChange,
  onFieldValueChange,
  onFormInit,
} from '@formily/core';
import { FormProvider } from '@formily/react';

import { useUpdateEffect } from 'ahooks';
import { head, isEmpty } from 'lodash-es';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { AddressHelpDrawerModule as Address } from 'genesis-web-component/lib/components/AddressHelpDrawerModule';
import { DrawerModule } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer';
import { DrawerInitData } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer/interface';
import { getInitData } from 'genesis-web-component/lib/components/MasterAgreement/components/CommonDrawer/util';
import { SectionWrapperWithBg } from 'genesis-web-component/lib/components/SectionWrapper';
import {
  useApplicationElementsEnums,
  useQueryDynamicByMatrix,
} from 'genesis-web-component/lib/hook';
import { useAddressComHelp } from 'genesis-web-component/lib/hook/addressComHelpHook';
import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import {
  CustomerPartyTypeEnum,
  ElementTopic,
  FactorsType,
  SchemaDefType,
} from 'genesis-web-service';

import { Warning } from '@uw/assets/new-icons';
import { useBizDictAntd } from '@uw/hook/useBizDict';
import {
  FactorEnum,
  InsuredAndPolicyHolder,
  SubCategoryEnum,
} from '@uw/interface/enum.interface';
import { useQueryDynamicFields } from '@uw/pages/proposal-entry/ProposalEntryDetail/sections/hooks/useQueryDynamicDetail';

import styles from '../../ProposalEntry.module.scss';
import {
  ProposalEntryDetailContext,
  UPDATE_POLICYHOLDERINFO,
} from '../../ProposalEntryDetailProvider';
import { CommonRender } from '../../components/CommonRender';
import { NewCommonRender } from '../../components/NewCommonRender';
import { sectionReduxKeyMap } from '../../hooks/getRootSameAsModule';
import { getSubmitData } from '../../hooks/getSubmitData';
import { getTableSource } from '../../hooks/getTableSource';
import {
  CommonPersonOrCompanyRecord,
  KeyMaybeEnum,
  PartyTypeMapToCustomerQueryTypeEnum,
} from '../../hooks/interface';
import { useRelatedState } from '../../hooks/useRelatedState';
import { useSameAsCheckBox } from '../../hooks/useSameAsCheckBox';
import { holderRemark, judgeManualUnderwriting } from '../../hooks/util';
import { isRoleEmpty } from '../../utils/isRoleEmpty';
import { removeUnNeedAccountToState } from '../../utils/removeUnNeedAccountToState';
import { useDrawerModuleConfig } from '../hooks/useDrawerModuleConfig';
import { useGetCurrentSectionFactors } from '../hooks/useGetCurrentSectionFactors';
import { useGetSameAsInfo } from '../hooks/useGetSameAsInfo';
import { ProposalEntryPageSections, SectionProps } from '../interface';
import { SchemaField } from './SchemaField';

const dynamicParams = {
  elementTopic: ElementTopic.POLICYHOLDER,
  schemaDefType: SchemaDefType.CUSTOMER,
};

const { confirm } = Modal;

export const PolicyholderInfo: FC<SectionProps> = ({
  title,
  id,
  render,
  sectionCode,
  relatedFirstSection,
  asComponent,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { state, dispatch } = useContext(ProposalEntryDetailContext);
  const customerTypeEnums = useBizDictAntd('partyType');
  const { isPolicyHolderSameAs } = useRelatedState();
  const { mode } = useParams();
  const [policyHolderSameAs, setPolicyHolderSameAs] =
    useState(isPolicyHolderSameAs);
  useEffect(() => {
    setPolicyHolderSameAs(isPolicyHolderSameAs);
  }, [isPolicyHolderSameAs]);
  const { organizationIdTypeEnum, certiTypeEnum, occupationCategoryTree } =
    useDrawerModuleConfig();
  const {
    personFactors,
    companyFactors,
    factors: applicationFactors,
  } = useGetCurrentSectionFactors(ProposalEntryPageSections.POLICY_HOLDER);
  const { enumsCompanyMap, enumsPersonMap } = useApplicationElementsEnums(
    state?.applicationElementEnumsConfig,
    FactorEnum.HOLDER
  );
  const [diffData, setDiffData] = useState<DrawerInitData | undefined>(
    undefined
  );
  const { isShowSameAs, sectionTitle } = useSameAsCheckBox(
    relatedFirstSection,
    ProposalEntryPageSections.POLICY_HOLDER
  );
  const [openDrawer, setOpenDrawer] = useState(false);
  const [customerType, setCustomerType] = useState<
    CustomerPartyTypeEnum | undefined
  >();
  const isManualUnderwriting = judgeManualUnderwriting(
    asComponent,
    sectionCode
  );

  const handleDelete = useCallback(() => {
    dispatch({ type: UPDATE_POLICYHOLDERINFO, policyHolderInfo: [] });
  }, [dispatch]);

  const { rootSameAsModule, rootSameAsModuleState } = useGetSameAsInfo(
    ProposalEntryPageSections.POLICY_HOLDER
  );

  // - Matrix打通
  const { data = [] } = useQueryDynamicFields(dynamicParams);
  const dynamicFields = useMemo(
    () =>
      data.reduce(
        (acc, curr) => acc.concat(curr?.source || []),
        [] as string[]
      ),
    [data]
  );

  const queryDynamicDetail = useQueryDynamicByMatrix({
    dynamicFields: data,
    ...dynamicParams,
  });

  const customerTypeChange = (field: DataField) => {
    setCustomerType(field.value);
    if (field.value) {
      dispatch({
        type: UPDATE_POLICYHOLDERINFO,
        policyHolderInfo: [{ partyType: field.value }],
      });
    }
  };

  const enumsMap = useMemo(
    () =>
      customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? enumsPersonMap
        : enumsCompanyMap,
    [enumsPersonMap, enumsCompanyMap, customerType]
  );

  const form = useMemo(
    () =>
      createForm({
        disabled: !state?.hasEditAuth,
        validateFirst: true,
        initialValues: {
          customerType,
          sameAs: policyHolderSameAs,
        },
        effects() {
          onFormInit(formInst => {
            setCustomerType(formInst.values.customerType);
          });
          onFieldInputValueChange('customerType', field => {
            if (field.value === customerType) return;
            if (
              isEmpty(state?.policyHolderInfo) ||
              Object.keys(state?.policyHolderInfo?.[0] ?? {})?.length === 1
            ) {
              customerTypeChange(field);
              return;
            }
            confirm({
              width: 500,
              icon: <Warning />,
              title: t('Confirm'),
              content: t(
                'Switching customer types will clear existing customer data, please confirm.'
              ),
              onOk: () => customerTypeChange(field),
              onCancel: () => {
                form.setFieldState('customerType', { value: customerType });
              },
              okText: t('Yes'),
              cancelText: t('No'),
            });
          });
          onFieldValueChange('sameAs', (field, formInst) => {
            setPolicyHolderSameAs(field.value);
            // 在每次取消 same as 的时候 清空 policyHolderInfo 列表
            if (!formInst.values.sameAs && isShowSameAs) {
              setCustomerType(undefined);
              dispatch({
                type: UPDATE_POLICYHOLDERINFO,
                policyHolderInfo: [],
              });
              holderRemark?.forEach(item => {
                removeUnNeedAccountToState({
                  dispatch,
                  otherState: rootSameAsModuleState,
                  payerType: item,
                  rootModule:
                    rootSameAsModule as unknown as ProposalEntryPageSections,
                });
              });
            }
          });
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      state?.hasEditAuth,
      state?.initialPolicyHolderInfo,
      state?.policyHolderInfo,
      policyHolderSameAs,
      isShowSameAs,
      sectionTitle,
      relatedFirstSection,
      customerType,
    ]
  );

  const closeDrawer = () => {
    setOpenDrawer(false);
  };

  const handleClose = () => {
    confirm({
      width: 500,
      icon: <Warning />,
      title: t('Confirm'),
      content: t('Are you sure to cancel?'),
      onOk: () => closeDrawer(),
      okText: t('Yes'),
      cancelText: t('No'),
    });
  };

  // 如果删除了投保人信息并且投保人是same as状态的话需要清除投保信息&取消勾选
  useEffect(() => {
    if (
      isRoleEmpty(state?.insuredInfo) &&
      state?.policyHolderInfo?.[0]?.sameAs?.section
    ) {
      setPolicyHolderSameAs(false);
      form.setValuesIn('sameAs', false);
      dispatch({
        type: UPDATE_POLICYHOLDERINFO,
        policyHolderInfo: [],
      });
    }
  }, [state?.insuredInfo]);

  const handleSubmit = useCallback(
    values => {
      const policyholder = getSubmitData({
        key: KeyMaybeEnum.PolicyHolderInfo,
        customerType: customerType as CustomerPartyTypeEnum,
        oldValues: state?.policyHolderInfo?.[0],
        values,
      });
      dispatch({
        type: UPDATE_POLICYHOLDERINFO,
        policyHolderInfo: [policyholder],
      });
      closeDrawer();
    },
    [customerType, dispatch, state]
  );

  useEffect(() => {
    const initHolderInfo = head(state?.initialPolicyHolderInfo);
    if (isEmpty(initHolderInfo)) {
      customerTypeChange({ value: CustomerPartyTypeEnum.INDIVIDUAL } as Field);
    } else {
      setCustomerType(initHolderInfo.partyType);
    }
  }, [state?.initialPolicyHolderInfo]);

  useUpdateEffect(() => {
    // if true  add the sameAs
    if (isShowSameAs) {
      if (policyHolderSameAs) {
        // 初始化走到这里，会修改state.policyHolderInfo，进而让initData没了
        const policyHolderInfo = state?.initialPolicyHolderInfo;
        const partyType = policyHolderInfo?.[0]?.partyType;
        const key = partyType && PartyTypeMapToCustomerQueryTypeEnum[partyType];
        const info: Record<string, unknown> = {
          sameAs: {
            section: relatedFirstSection,
          },
        };
        if (
          key &&
          policyHolderInfo?.[0]?.[key] &&
          !isEmpty(policyHolderInfo?.[0]?.[key])
        ) {
          info[key] = policyHolderInfo?.[0]?.[key];
          info.partyType = partyType;
        }
        dispatch({
          type: UPDATE_POLICYHOLDERINFO,
          policyHolderInfo: [info],
        });
      }
    }
  }, [isShowSameAs, policyHolderSameAs, relatedFirstSection]);

  const factors = useMemo(
    () =>
      (customerType === CustomerPartyTypeEnum.INDIVIDUAL
        ? personFactors
        : companyFactors
      ).map(item => ({
        ...item,
        orderNum: item?.orderNo || item.orderNum,
      })),
    [companyFactors, customerType, personFactors]
  );

  const sectionKey = useMemo(
    () => relatedFirstSection && sectionReduxKeyMap[relatedFirstSection],
    [relatedFirstSection]
  );
  /** same as diff */
  useEffect(() => {
    if (form.values.sameAs && sectionKey) {
      const sameAs = state?.[sectionKey] as CommonPersonOrCompanyRecord[];
      setCustomerType(head(sameAs)?.partyType);
      const initData = getInitData(
        head(getTableSource(sectionKey, sameAs)) as Record<string, unknown>
      );
      setDiffData(initData);
    } else {
      setDiffData(undefined);
    }
  }, [form.values.sameAs, relatedFirstSection, sectionKey, state]);

  const initData = useMemo(
    () =>
      getInitData(
        head(
          getTableSource(
            KeyMaybeEnum.PolicyHolderInfo,
            state?.[
              KeyMaybeEnum.PolicyHolderInfo
            ] as CommonPersonOrCompanyRecord[]
          )
        ) as Record<string, unknown>
      ),
    [state]
  );

  const {
    addressOpen,
    setaddressOpen,
    addressForm,
    addAddress,
    handledAddressFields,
    ref,
    extraAreaProps,
    validateCascaderRules,
    setCascaderValue,
    setFields,
    initialValue,
  } = useAddressComHelp({
    factors,
    enumsMap,
    customerType,
    disabled: !state?.hasEditAuth,
  });

  return (
    <>
      <section className={styles.proposalEntrySection} id={id}>
        {state.subCategory === SubCategoryEnum.MOTOR ? (
          <section className="relative">
            <SectionWrapperWithBg
              title={title as string}
              id={id}
              dividerClassName={'!mt10 !mb-0'}
              readOnly={!state?.hasEditAuth}
              extraButtons={
                isRoleEmpty(state?.policyHolderInfo) &&
                state?.hasEditAuth &&
                customerType &&
                !policyHolderSameAs && (
                  <Button
                    onClick={() => {
                      setOpenDrawer(true);
                    }}
                    className="right-lg top-md"
                    disabled={!state?.hasEditAuth}
                  >
                    {t('+ Add New')}
                  </Button>
                )
              }
            >
              <FormProvider form={form}>
                <SchemaField
                  scope={{
                    t,
                    customerTypeEnums,
                    isShowSameAs,
                    sectionTitle,
                    relatedFirstSection,
                  }}
                  schema={render}
                />
              </FormProvider>
              <NewCommonRender
                show={!isRoleEmpty(state?.policyHolderInfo)}
                customerType={customerType}
                setOpenDrawer={setOpenDrawer}
                handleDelete={handleDelete}
                stateKey={KeyMaybeEnum.PolicyHolderInfo}
                data={
                  form.values.sameAs
                    ? state.insuredInfo
                    : (state.policyHolderInfo as CommonPersonOrCompanyRecord[])
                }
                roleType={InsuredAndPolicyHolder.PolicyHolder}
              />
            </SectionWrapperWithBg>
          </section>
        ) : (
          <>
            <SimpleSectionHeader
              weight={500}
              type={'h5'}
              style={{ marginBottom: styles.gapMd }}
            >
              {title}
            </SimpleSectionHeader>
            <section className={styles.formWrapper}>
              <FormProvider form={form}>
                <SchemaField
                  scope={{
                    t,
                    customerTypeEnums,
                    gapMd: styles.gapMd,
                    isShowSameAs,
                    sectionTitle,
                    relatedFirstSection,
                  }}
                  schema={render}
                />
              </FormProvider>
            </section>
            <CommonRender
              show={
                (customerType as CustomerPartyTypeEnum) && !policyHolderSameAs
              }
              setOpenDrawer={setOpenDrawer}
              handleDelete={handleDelete}
              customerType={customerType}
              stateKey={KeyMaybeEnum.PolicyHolderInfo}
              data={state.policyHolderInfo as CommonPersonOrCompanyRecord[]}
              applicationFactors={applicationFactors as FactorsType[]}
            />
            <Divider category="body" />
          </>
        )}
      </section>
      {openDrawer && (
        <DrawerModule
          open={openDrawer}
          mode={mode as Mode}
          readonly={!state?.hasEditAuth}
          type={customerType as CustomerPartyTypeEnum}
          drawerTitle={title}
          handleCancel={handleClose}
          handleSubmit={handleSubmit}
          addBtnClassName={styles.rightAddButton}
          sectionCode={sectionCode}
          factors={factors}
          enumsMap={enumsMap}
          initData={initData}
          occupationCategoryTree={occupationCategoryTree}
          organizationIdTypeEnum={organizationIdTypeEnum}
          certiTypeEnum={certiTypeEnum}
          diffData={diffData}
          matrixFields={dynamicFields}
          queryDynamicDetail={queryDynamicDetail}
          isGettingAddress={true}
          ref={ref}
          zoneId={state.basicInfo?.zoneId as string}
          isManualUnderwriting={isManualUnderwriting}
          extraAreaProps={extraAreaProps}
          setOutFields={setFields}
        />
      )}
      {addressOpen && (
        <Drawer
          width={720}
          onClose={() => setaddressOpen(false)}
          open={addressOpen}
          title={t('Add New')}
          footer={
            <Space className="float-right">
              <Button type="primary" onClick={addAddress}>
                {t('Add')}
              </Button>
              <Button onClick={() => setaddressOpen(false)}>
                {t('Cancel')}
              </Button>
            </Space>
          }
        >
          <Address
            initialValue={initialValue}
            form={addressForm}
            selfCustomFields={handledAddressFields}
            validateCascaderRules={validateCascaderRules}
            onCascaderValueChange={setCascaderValue}
          />
        </Drawer>
      )}
    </>
  );
};
