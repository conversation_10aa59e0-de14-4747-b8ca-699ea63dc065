import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Row, Select, Space } from 'antd';

import BigNumber from 'bignumber.js';
import { size } from 'lodash-es';

import { AmountInput } from 'genesis-web-component/lib/components/AmountInput';
import { Switcher } from 'genesis-web-component/lib/components/Switcher';
import { ProposalEntryTypes } from 'genesis-web-service';

import { useDict } from '@uw/biz-dict/hooks';
import { AmountInputWithAutoPrecision } from '@uw/components/AmountInputWithAutoPrecision';
import { useProposalEntryState } from '@uw/pages/proposal-entry/ProposalEntryDetail/ProposalEntry.Provider';

import { convertCampaignUserType } from '../../../utils/createGoodsProductsMap';
import { AdjAnnualMode } from '../interface';

interface CampaignRenderProps {
  field: ProposalEntryTypes.UsableUserInputFee;
  disabled: boolean;
  onValueChange: (
    value: {
      value?: string | undefined;
      discountType?: ProposalEntryTypes.CampaignDiscountType | undefined;
      mode?: AdjAnnualMode | undefined;
      calculationBasisValue?: string | undefined;
    },
    field?: ProposalEntryTypes.UsableUserInputFee
  ) => void;
  renderFeeEditField: (
    _: any,
    field: ProposalEntryTypes.UsableUserInputFee
  ) => JSX.Element;
  extraPremium?: number;
}

export const CampaignRender: FC<CampaignRenderProps> = ({
  field,
  onValueChange,
  disabled,
  renderFeeEditField,
  extraPremium,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const campaignUserInputTypeMap = useDict('campaignUserInputType')?.[0];

  const { currency } = useProposalEntryState();
  const { campaignUserInputTypeList } = field;
  const options = campaignUserInputTypeList?.map(type => {
    const value = convertCampaignUserType(type, 'toDiscountType');
    const label = campaignUserInputTypeMap[type] || type;
    return { label, value };
  });
  const [type, setType] = useState(field?.discountType);

  useEffect(() => {
    setType(field?.discountType);
  }, [field?.discountType]);

  const min = field?.minValue
    ? BigNumber(field.minValue).multipliedBy(100).toNumber()
    : undefined;
  const max = field?.maxValue
    ? BigNumber(field.maxValue).multipliedBy(100).toNumber()
    : undefined;

  return (
    <>
      <Row className="w-[460px]">
        <Switcher expression={!!field?.fixedValue}>
          {/** 优先判断fixedValue  后端说他不好改  fixedValue需要以value的形式传给后端 类似formulaValue */}
          <Switcher.If>
            <AmountInputWithAutoPrecision
              disabled
              value={extraPremium}
              currency={currency}
              className="w-[408px]"
            />
          </Switcher.If>

          <Switcher.Else>
            <Switcher expression={size(options) !== 0}>
              <Switcher.If>
                <Select
                  className="w-40 mr-2"
                  placeholder={t('Please select')}
                  disabled={disabled}
                  value={type}
                  allowClear={true}
                  onChange={(
                    discountType: ProposalEntryTypes.CampaignDiscountType
                  ) => {
                    onValueChange({ discountType }, { ...field, value: null });
                    setType(discountType);
                  }}
                  options={options}
                />
                <div className="w-60">
                  <Switcher.Alone
                    visible={
                      type === ProposalEntryTypes.CampaignDiscountType.Amount
                    }
                  >
                    {renderFeeEditField('', {
                      ...field,
                      className: '!w-60',
                      // https://jira.zaouter.com/browse/GIS-109974 amount的时候不用minValue, maxValue进行校验
                      minValue: undefined,
                      maxValue: undefined,
                    })}
                  </Switcher.Alone>

                  <Switcher.Alone
                    visible={
                      type ===
                      ProposalEntryTypes.CampaignDiscountType.Percentage
                    }
                  >
                    <Space.Compact>
                      <AmountInputWithAutoPrecision
                        className="!w-[50%]"
                        disabled
                        value={extraPremium}
                        currency={currency}
                      />
                      {/** by rate 下 value要特殊转换百分比 */}
                      <Switcher
                        expression={
                          ![
                            ProposalEntryTypes.RateSourceType.ENUMERATION,
                          ].includes(field.rateSource)
                        }
                      >
                        <Switcher.If>
                          <AmountInput
                            disabled={
                              disabled ||
                              field.rateSource ===
                                ProposalEntryTypes.RateSourceType.BY_FORMULA
                            }
                            currency={currency}
                            min={min}
                            className="!w-[50%]"
                            max={max}
                            addonAfter={'%'}
                            precision={2}
                            placeholder={t('Please input')}
                            value={
                              field.value
                                ? BigNumber(field.value)
                                    .multipliedBy(100)
                                    .toNumber()
                                : null
                            }
                            onChange={value => {
                              const params = value
                                ? BigNumber(value).dividedBy(100)
                                : undefined;
                              onValueChange({ value: params }, field);
                            }}
                          />
                        </Switcher.If>
                        <Switcher.Else>
                          {/** ENUMERATION类型需要复用renderFeeEditField 复用这个renderFeeEditField 是万恶之源 */}
                          {renderFeeEditField('', field)}
                        </Switcher.Else>
                      </Switcher>
                    </Space.Compact>
                  </Switcher.Alone>
                </div>
              </Switcher.If>

              <Switcher.Else>
                <div className="w-[407px]">
                  {renderFeeEditField('', {
                    ...field,
                    value: field?.value || extraPremium!,
                  })}
                </div>
              </Switcher.Else>
            </Switcher>
          </Switcher.Else>
        </Switcher>
      </Row>
    </>
  );
};
