import React, {
  useEffect,
  useImperative<PERSON>andle,
  useLayoutEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';

import { Form } from 'antd';

import { useAtomValue, useSetAtom } from 'jotai';
import { cloneDeep, debounce, head, isEmpty, omit } from 'lodash-es';
import useSWR from 'swr';

import {
  CommercialLineService,
  type IProposalStore,
  PosCancelParams,
  PosFileType,
  ProposalCombinedService,
  ProposalCombinedTypes,
  ProposalEntryTypes,
} from 'genesis-web-service';
import { CommonRespWithPagination } from 'genesis-web-service/lib/common.interface';
import { PageTemplateTypes } from 'genesis-web-service/lib/policy/page-template-service/page-template.interface';
import { CommercialLineTypes } from 'genesis-web-service/lib/policy/proposal-service/commercial-line.interface';

import { ebInsuredAtom } from '@uw/atom/atom';
import { embedProposalEntryProps<PERSON>tom } from '@uw/pages/embed/proposal-entry/atom';
import { mergePickedKeys } from '@uw/pages/embed/proposal-entry/utils/mergePickedKeys';
import { ProposalEntryStateProvider } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.Provider';
import {
  ProposalEntryDetailAction,
  ProposalEntryDetailContext,
  ProposalEntryDetailReducer,
  ProposalEntryDetailState,
  initialContextValue,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntryDetailProvider';
import { ProposalEntryLayout } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntryLayout';
import {
  ProposalEntryScenario,
  extraSectionConditionsAtom,
  multiGoodsFeeConfigAtom,
  pageCodeAtom,
  proposalEntryDetailAtom,
  proposalEntryScenarioAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { useGetSaveValues } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useGetSaveValues';
import { useProposalValidation } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useProposalValidation';
import { updateQueryInfo } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useQueryProposalDetail';
import { useUpdateProposalDetail } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useUpdateProposalDetail';
import { covertSameAsFromRelationshipWithPolicyholder } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/util';
import { usePremiumFeeConfig } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/sub-sections/COVERAGE_PLAN/hooks/usePremiumFeeConfig';
import { useUserInputFee } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/sub-sections/COVERAGE_PLAN/hooks/useUserInputFee';
import {
  UwOperationRefProvider,
  useUwOperationRef,
} from '@uw/pages/uw-pages/uwOperationPage/UwOperation.Provider';

type PolicyListItem =
  ProposalCombinedTypes.SaveCombinedProposalRequestBody['policyList'][number];
type PolicyListItemKeys = keyof PolicyListItem;

type ExcludedPolicyListKeys =
  | 'comments'
  | 'policyComments'
  | 'policyAttachmentList'
  | 'uniqueKey';

type PolicyListRequiredKeys = Exclude<
  PolicyListItemKeys,
  ExcludedPolicyListKeys
>;

// pos 结构无 policyList ,所以需要从 policyList 中取出一些数据放在外层
// 如果缺少字段 _assertPolicyListKeysComplete 会报错
const needPickKeysFromPolicyList = [
  'policyRenter',
  'policyPlanList',
  'policyBeneficiaryList',
  'claimStackConfigList',
  'policyInsuredObjectList',
  'policyPayerList',
  'policySpecialAgreementList',
  'policyProductList',
  'policyPaymentPlanList',
  'policyClaimStackList',
  'policyCampaignList',
  'coverageRelationList',
  'policyUnnamedInsuredList',
  'unnamedInsuredFlag',
  'policyBatchCalculateResult',
] as const;

// 这将展示我们数组中缺少的键（如果有）
type MissingPolicyListKeys = Exclude<
  PolicyListRequiredKeys,
  (typeof needPickKeysFromPolicyList)[number]
>;

// MissingPolicyListKeys 应该是 never
// 使用条件类型通过编译器显式检查
type AssertPolicyListKeysComplete<T> = [T] extends [never]
  ? true
  : { error: 'Missing required keys'; missing: T };
type IsComplete = AssertPolicyListKeysComplete<MissingPolicyListKeys>;

// 添加强制检查：如果数组不完整，这里会编译时报错
export const _assertPolicyListKeysComplete: true = true as IsComplete;

enum RuleCode {
  payerChange = 'PAYER_CHANGE',
}
interface Rules {
  // 用于区块 disabled 规则
  disabledRules?: {
    sectionCode: string;
    disabledAll: boolean; // 默认 false, 如果为 true 则 partialFields 无效
    partialFields: string[];
  };
  // 用于一些预定义的特定规则, 比如 payer 的特殊逻辑
  otherRules?: RuleCode[];
}

// 文件上传业务数据服务接口,因为 eb named insured 和 product object 都复用相同接口,参数不同,所以这里抽出来
interface BusinessDataService {
  clearAllBusinessData: (
    posFileType: PosFileType,
    options?: { planCode?: string; uniqueKey?: string }
  ) => Promise<void>;
  downloadBusinessTemplate: (
    posFileType: PosFileType,
    data?: Record<string, unknown>
  ) => Promise<void>;
  downloadBusinessData: (
    posFileType: PosFileType,
    data?: Record<string, unknown>
  ) => Promise<void>;
  uploadFileAndCheckData: (
    formData: FormData,
    posFileType: PosFileType
  ) => Promise<void>;
  submitBusinessData: (data: {
    fileUniqueCode: string;
    uniqueKey: string;
    posFileType: PosFileType;
  }) => Promise<unknown>; // 替换 unknown 为具体的返回类型
  cancelUpload: (data: PosCancelParams) => Promise<unknown>;
}

// EB 被保人服务接口
interface EBInsuredService {
  queryEbInsuredList: (
    page: number,
    size: number,
    temporaryId: number,
    data: Record<string, unknown>
  ) => Promise<CommonRespWithPagination<unknown>>; // 替换 unknown 为具体的返回类型

  updateEbInsured: (
    temporaryId: number,
    goodsId: number,
    planId: number,
    packageId: number,
    data: Record<string, unknown>
  ) => Promise<void>;

  deleteEbInsured: (rowId: string) => Promise<void>;
}

// 产品对象服务接口
interface ProductObjectService {
  getProductObjectList: (
    ...data: Parameters<typeof CommercialLineService.getProductObjectList>
  ) => Promise<CommonRespWithPagination<CommercialLineTypes.ProductObject>>;

  addProductObject: (
    ...data: Parameters<typeof CommercialLineService.addProductObject>
  ) => Promise<void>;

  deleteProductObject: (
    ...data: Parameters<typeof CommercialLineService.deleteProductObject>
  ) => Promise<void>;

  updateProductObject: (
    ...data: Parameters<typeof CommercialLineService.updateProductObject>
  ) => Promise<void>;
}

interface EmployeeObjectService {
  getEmployeeObjectList: (
    ...data: Parameters<typeof CommercialLineService.getEmployeeObjectList>
  ) => Promise<CommonRespWithPagination<CommercialLineTypes.EmployeeObject>>;

  addEmployeeObject: (
    ...data: Parameters<typeof CommercialLineService.addEmployeeObject>
  ) => Promise<void>;

  updateEmployeeObject: (
    ...data: Parameters<typeof CommercialLineService.updateEmployeeObject>
  ) => Promise<void>;

  deleteEmployeeObject: (
    ...data: Parameters<typeof CommercialLineService.deleteEmployeeObject>
  ) => Promise<void>;
}

export interface EmbedProposalEntryIProps {
  readPretty?: boolean;
  scenario?: ProposalEntryScenario;
  forwardRef?: React.RefObject<unknown>;
  hasEditAuth: boolean;
  applicationNo: string;
  proposalData: ProposalCombinedTypes.SaveCombinedProposalRequestBody;
  rules?: Rules;
  proposalStore: IProposalStore;
  setLoading?: (loading: boolean) => void; // TODO: ready 时通知父组件
  service?: {
    businessData?: BusinessDataService;
    ebInsured?: EBInsuredService;
    productObject: ProductObjectService;
    employeeObject: EmployeeObjectService;
    getBuildFee?: () => Promise<
      ProposalCombinedTypes.UsableUserInputFeeResponse[]
    >;
  };
  setQuickMenu?: (menu: unknown) => void; // 添加类型
}

const EmbedProposalEntry = (props: EmbedProposalEntryIProps) => {
  const {
    scenario = ProposalEntryScenario.POS,
    hasEditAuth,
    applicationNo,
    proposalData,
    forwardRef,
    proposalStore,
    readPretty,
  } = props;
  const [form] = Form.useForm();
  const [proposalDetail, setProposalDetail] = useState(
    covertSameAsFromRelationshipWithPolicyholder(proposalData)
  );
  const { validateSaveValues } = useProposalValidation();

  const setPageCode = useSetAtom(pageCodeAtom);
  const setEbInsured = useSetAtom(ebInsuredAtom);
  const setProposalEntryScenario = useSetAtom(proposalEntryScenarioAtom);
  const setExtraSectionConditionsAtom = useSetAtom(extraSectionConditionsAtom);
  const setProposalEntryDetailAtom = useSetAtom(proposalEntryDetailAtom);
  const setEmbedProposalEntryProps = useSetAtom(embedProposalEntryPropsAtom);
  const { getSaveValues } = useGetSaveValues();
  const multiGoodsFeeConfig = useAtomValue(multiGoodsFeeConfigAtom);
  const { patchPremiumData } = usePremiumFeeConfig(undefined);
  const comparisonRules = useUserInputFee();
  const { updateProposalDetail } = useUpdateProposalDetail();
  const actionRef = useUwOperationRef();
  useLayoutEffect(() => {
    actionRef.current.handleSave = async () => {
      await proposalStore.submitData({
        isFormValid: false,
        isRuleValid: false,
      });
    };
  }, [actionRef, proposalStore]);

  useImperativeHandle(forwardRef, () => ({
    setProposalDetail: async (
      data: ProposalCombinedTypes.SaveCombinedProposalRequestBody
    ) => {
      await comparisonRules(data);
      setProposalDetail(data);
    },
    validateForm: async () => {
      // promise.all 会导致已经填写了但是校验失败的情况,所以这里分开校验
      await validateSaveValues({
        hideHash: true,
      });
      await form.validateFields();
    },
    getValues: () => {
      if (isEmpty(multiGoodsFeeConfigAtom)) {
        // TODO: wgd 防止数据丢失
        throw new Error('multiGoodsFeeConfigAtom is empty');
      }
      const savedValues = getSaveValues({
        form,
        detail: proposalDetail,
        keepCalculatedSumInsured: true,
      });
      const savedParams = patchPremiumData(
        (multiGoodsFeeConfig as ProposalEntryTypes.GoodsUsableUserInputFee[]) ??
          [],
        savedValues,
        true
      );
      // 计算出来的 sa 不保留,用于算费参数
      const calculateSavedValues = getSaveValues({
        form,
        detail: proposalDetail,
      });
      const calculateParams = patchPremiumData(
        (multiGoodsFeeConfig as ProposalEntryTypes.GoodsUsableUserInputFee[]) ??
          [],
        calculateSavedValues,
        true
      );

      needPickKeysFromPolicyList.forEach(key => {
        savedParams[key] = head(savedParams?.policyList)?.[key];
        calculateParams[key] = head(calculateParams?.policyList)?.[key];
      }, []);

      // 这里处理是回了还原接口返回的 claimStackTemplateList 和其内部 claimStackList 的 id 和 uniqueKey
      // 因在页面处理过于复杂,所以这里做 merge 处理
      const processClaimStackTemplates = (
        templates?: ProposalEntryTypes.ClaimStackTemplate[],
        originalTemplates?: ProposalEntryTypes.ClaimStackTemplate[]
      ): ProposalEntryTypes.ClaimStackTemplate[] | undefined => {
        if (isEmpty(templates) || isEmpty(originalTemplates)) return templates;

        // 处理 templateList - 仅合并id和uniqueKey
        const mergedTemplates = mergePickedKeys(templates, originalTemplates, {
          matchKey: 'templateCode',
          keys: ['uniqueKey', 'id'],
        });

        // 处理每个模板下的 claimStackList - 仅合并id和uniqueKey
        mergedTemplates?.forEach(template => {
          const templateObj = template;

          if (isEmpty(templateObj?.claimStackList)) return;

          const originalTemplate = originalTemplates?.find(
            original => original?.templateCode === templateObj.templateCode
          );

          templateObj.claimStackList = mergePickedKeys(
            templateObj.claimStackList?.map(item => ({
              ...item,
              stackCodeAndType: item?.stackCode + item?.stackValueType,
            })),
            originalTemplate?.claimStackList?.map(item => ({
              ...item,
              stackCodeAndType: item?.stackCode + item?.stackValueType,
            })),
            {
              matchKey: 'stackCodeAndType',
              keys: ['uniqueKey', 'id'],
            }
          );
        });

        return mergedTemplates;
      };

      // 处理产品和责任级别的 claimStackTemplateList
      savedParams?.policyProductList?.forEach(product => {
        const originalProduct = proposalDetail?.policyProductList?.find(
          origProduct => origProduct.productId === product.productId
        );
        if (!isEmpty(product?.claimStackTemplateList)) {
          // 处理产品级别的 claimStackTemplateList
          product.claimStackTemplateList = processClaimStackTemplates(
            product.claimStackTemplateList,
            originalProduct?.claimStackTemplateList
          );
        }

        // 处理责任级别的 claimStackTemplateList
        if (isEmpty(product?.policyProductLiabilityList)) return;
        product.policyProductLiabilityList.forEach(liability => {
          // 找到原始责任数据
          const originalLiability =
            originalProduct?.policyProductLiabilityList?.find(
              origLiability =>
                origLiability.liabilityId === liability.liabilityId
            );

          // 处理责任级别的 claimStackTemplateList
          liability.claimStackTemplateList = processClaimStackTemplates(
            liability.claimStackTemplateList,
            originalLiability?.claimStackTemplateList
          );
        });
      });

      const proposalWithoutPolicyList = cloneDeep(
        omit(savedParams, 'policyList')
      );
      const calculateProposalWithPolicyList = cloneDeep(
        omit(calculateParams, 'policyList')
      );

      return {
        saveValues: mergePickedKeys(proposalWithoutPolicyList, proposalDetail, {
          keys: ['id'],
        }),
        calculateValues: mergePickedKeys(
          calculateProposalWithPolicyList,
          proposalDetail,
          { keys: ['id'] }
        ),
      };
    },
  }));
  const goodsId = proposalDetail?.policyBasicInfo?.goodsId;
  const { data: templateData, isLoading } = useSWR(
    goodsId ? ['proposal/combined/template', goodsId] : null,
    () =>
      ProposalCombinedService.queryTemplate({
        goodsId: proposalDetail?.policyBasicInfo?.goodsId,
      })
  );
  const pageType = templateData?.pageCode as PageTemplateTypes.PageType;
  // setQuickMenu 可能会多次触发,这里 debounce 一下
  const setQuickMenu = useMemo(() => {
    if (proposalStore?.setQuickMenu) {
      return debounce(menu => {
        proposalStore?.setQuickMenu?.(menu);
      }, 1000);
    }
  }, [proposalStore]);
  const processedProposalDetail = useMemo(
    () =>
      updateQueryInfo(
        covertSameAsFromRelationshipWithPolicyholder(proposalDetail) ?? {}
      ),
    [proposalDetail]
  );

  useEffect(() => {
    if (pageType && scenario && proposalDetail) {
      const sectionConditions = [
        {
          factor: 'module',
          factorValue: scenario,
        },
      ];
      // TODO: wgd 为不影响测试,POS_QUERY,POS 同时传,等下迭代后端修复后再去掉
      if (readPretty) {
        sectionConditions.push({
          factor: 'module',
          factorValue: ProposalEntryScenario.POS_QUERY,
        });
      }
      setExtraSectionConditionsAtom(sectionConditions);
      setPageCode(pageType); // special info section 内部会用到
      setProposalEntryScenario(scenario); // 区分嵌入场景做一些特殊逻辑,比如隐藏试算按钮等
      updateProposalDetail(processedProposalDetail); // 将 proposalDetail 更新到 context
      setProposalEntryDetailAtom(proposalDetail); // 接口返回的原始的数据
      setEmbedProposalEntryProps({
        ...props,
        setQuickMenu,
      });
      const { queryEbInsuredList, updateEbInsured, deleteEbInsured } =
        props?.service?.ebInsured ?? {};
      setEbInsured(prev => {
        return {
          ...prev,
          relatedService: {
            queryInsuredList: queryEbInsuredList,
            saveInsuredRecord: updateEbInsured,
            deleteInsuredRecord: deleteEbInsured,
          },
        };
      });
    }
  }, [
    processedProposalDetail,
    proposalDetail,
    pageType,
    scenario,
    setQuickMenu,
    setEbInsured,
  ]);
  if (isLoading) {
    return null;
  }
  return (
    <ProposalEntryStateProvider
      form={form}
      detail={processedProposalDetail}
      detailLoading={!proposalDetail}
    >
      <ProposalEntryLayout
        isShowAnchor={false}
        hasEditAuth={hasEditAuth}
        asComponent={true}
        applicationNo={applicationNo}
        form={form}
        pageType={pageType}
        isEmbed
        proposalDetail={processedProposalDetail}
        isUw={true}
      />
    </ProposalEntryStateProvider>
  );
};

const EmbedProposalEntryContainer = (props: EmbedProposalEntryIProps) => {
  const [state, dispatch] = useReducer<
    React.Reducer<ProposalEntryDetailState, ProposalEntryDetailAction>
  >(ProposalEntryDetailReducer, initialContextValue);
  return (
    <ProposalEntryDetailContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <EmbedProposalEntry {...props} />
    </ProposalEntryDetailContext.Provider>
  );
};

export default (props: EmbedProposalEntryIProps) => {
  return (
    <UwOperationRefProvider>
      <EmbedProposalEntryContainer {...props} />
    </UwOperationRefProvider>
  );
};
