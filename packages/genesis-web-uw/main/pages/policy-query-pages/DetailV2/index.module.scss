@import '@uw/styles/variables.scss';

.tabs-wrapper {
  :global {

    // 只影响 .main-tabs 下的 antd tabs
    .outer-tabs>.#{$antd-prefix}-tabs-content-holder {
      overflow-y: auto;
      background-color: var(--layout-body-background);

      >.#{$antd-prefix}-tabs-content {
        background-color: var(--white);
        margin: var(--gap-xs);
        padding: 1.5rem var(--gap-md);
        width: auto;
        border-radius: 0.5rem;
        min-height: calc(100% - (2 * var(--gap-xs)));
      }
    }

    .outer-tabs .#{$antd-prefix}-tabs-tab-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-root) !important;
    }
  }
}