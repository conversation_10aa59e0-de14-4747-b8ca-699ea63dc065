import { FC, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';

import clsx from 'clsx';
import { useAtomValue } from 'jotai';
import { head, isArray, isUndefined } from 'lodash-es';
import { mutate } from 'swr';

import { PageHeader, StatusTag, Tabs } from '@zhongan/nagrand-ui';

import { PageTemplateTypes } from 'genesis-web-service';
import { LabeledValue } from 'genesis-web-shared/lib/util/interface';

import { CommonInfoDisplay } from '@uw/components/CommonInfoDisplay';
import {
  ToolBarsContent,
  ToolBarsPanel,
  ToolPanelsProvider,
} from '@uw/components/ToolBarsPanel';
import { ProposalStatusEnum } from '@uw/interface/enum.interface';
import { Mode } from '@uw/interface/enum.interface';
import { useInsuranceDetail } from '@uw/pages/policy-query-pages/DetailV2/hooks/useInsuranceDetail';
import { useQueryPageSections } from '@uw/pages/policy-query-pages/DetailV2/hooks/useQueryPageSections';
import { queryDetailMetaDataAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { getQueryProposalDetailSWRKey } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/swr-key';

import { PolicyStatus } from '../Detail/Components-Detail/PolicyInfo/Box/PolicyStatus';
import { querySectionDisplayStatusAtom } from './atom';
import { usePolicyInfo } from './hooks/usePolicyInfo';
import {
  toolBarKeySectionMap,
  useToolBarsContent,
} from './hooks/useToolBarsContent';
import styles from './index.module.scss';
import { PolicyDetailSection, usePolicyDetailSectionMapping } from './sections';

export interface IInsuranceQueryDetail {
  pageCode: PageTemplateTypes.PageType;
  viewPageCode: PageTemplateTypes.PageType;
  module: PageTemplateTypes.PageType;
  businessNumber: string;
}

// 统一处理保单和投保单的详情展示
export const InsuranceQueryDetail: FC<IInsuranceQueryDetail> = props => {
  const { viewPageCode, module, businessNumber } = props;
  const { t } = useTranslation();
  const { isPolicyQuery } = useAtomValue(queryDetailMetaDataAtom) ?? {};
  const querySectionDisplayStatus = useAtomValue(querySectionDisplayStatusAtom);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const newSearchParams = new URLSearchParams(searchParams.toString());
  const [currentTab, setCurrentTab] = useState<PolicyDetailSection>(
    (newSearchParams.get('currentTab') as PolicyDetailSection) || undefined
  );
  const policyDetailSectionMapping = usePolicyDetailSectionMapping();
  const [pageCondition, setPageCondition] = useState<
    PageTemplateTypes.FactorRecord[]
  >([
    {
      factor: 'module',
      factorValue: module,
    },
  ]);

  const { data: policyDetail, isLoading } = useInsuranceDetail();
  const { policyInfo } = usePolicyInfo();

  const [loading, setLoading] = useState(true);

  // pagecode 是POLICY_QUERY 和 PROPOSAL_QUERY的情况下，新增
  useEffect(() => {
    const packageId = head(policyDetail?.policyGoodsList)?.packageId;
    const exist = pageCondition.find(
      condition => condition.factor === 'packageId'
    );
    if (packageId && !exist) {
      setPageCondition([
        {
          factor: 'module',
          factorValue: module,
        },
        {
          factor: 'packageId',
          factorValue: +packageId!,
        },
      ]);
    }
    if (!isUndefined(policyDetail) && !isLoading) {
      setLoading(false);
    }
  }, [policyDetail, isLoading]);

  const {
    queryDetailTabSections,
    queryDetailHeaderSections,
    queryDetailHeaderMoreInfoSection,
  } = useQueryPageSections({
    localSections: { ...policyDetailSectionMapping },
    module: PageTemplateTypes.ModuleType.QUERY,
    factors: pageCondition,
    pageCode: viewPageCode,
    needSubSection: true,
  });

  const toolBarContentConfig = useToolBarsContent(
    policyDetail?.basicViewInfo?.issuanceNo as string
  );

  const handleTabChange = (activeKey: PolicyDetailSection) => {
    setCurrentTab(activeKey);
    newSearchParams.set('currentTab', activeKey);
    setSearchParams(newSearchParams, { replace: true });
  };

  const basicInfo = useMemo(() => {
    const headerMoreInfo = policyDetail?.headerMoreInfo;
    const submissionNo = {
      label: t('Submission No.'),
      value: headerMoreInfo?.submissionNo,
    };
    const insuranceNo = isPolicyQuery
      ? {
          label: t('Proposal No.'),
          value: headerMoreInfo?.proposalNo,
        }
      : {
          label: t('Policy No.'),
          value: headerMoreInfo?.policyNo,
          hidden: policyDetail?.issuanceStatus !== ProposalStatusEnum.EFFECTIVE, // 非EFFECTIVE的proposal不展示policyNo
        };
    const goodsName = {
      label: t('Goods Name'),
      value: headerMoreInfo?.goodsName,
    };
    const holderFullName = {
      label: t('Policy Holder'),
      value: headerMoreInfo?.holderFullName,
    };
    const fields = [submissionNo, insuranceNo, goodsName, holderFullName];
    return fields.filter(
      field => !!field?.value && !field.hidden
    ) as LabeledValue<string>[]; // 没有值就不展示
  }, [policyDetail]);
  const title = useMemo(() => {
    return (
      <div className="flex">
        <span className="text-textQuaternary mr-2">
          {isPolicyQuery ? t('Policy No.') : t('Proposal No.')}
          {businessNumber}
        </span>
        {policyDetail?.policyVersion?.version && (
          <div className="mr-2">
            <StatusTag
              statusI18n={`V${policyDetail?.policyVersion?.version}`}
            />
          </div>
        )}
        <PolicyStatus policyInfo={policyInfo?.policyBaseObject} />
      </div>
    );
  }, [isPolicyQuery, businessNumber, policyDetail, t, policyInfo]);

  const backUrl = useMemo(
    () => (isPolicyQuery ? '/uw/query-policy/search' : '/uw/proposal/search'),
    [isPolicyQuery]
  );

  const { mode } = useParams();
  const policyVersion = searchParams.get('policyVersion');

  useEffect(() => {
    // GIS-123832进到这里把这个key的swr缓存请一下，否则刚进来就有值会导致policyList丢
    // 下面是useQueryProposalDetail的key
    mutate(
      mode !== Mode.Add
        ? getQueryProposalDetailSWRKey({ mode, policyVersion })
        : null,
      undefined,
      { revalidate: false }
    );
  }, []);

  const tabItems = useMemo(() => {
    return queryDetailTabSections.map(
      ({
        section,
        title,
        wrapperClassName,
        contentClassName,
        ComponentObj,
      }) => ({
        key: section,
        label: title,
        className: clsx('bg-white rounded-lg', wrapperClassName),
        // 为了让每一个tab里的数据有无来设置filteredSections，这里让tab默认渲染
        forceRender: true,
        children: (
          <section className={clsx('rounded-lg bg-white', contentClassName)}>
            <ComponentObj />
          </section>
        ),
      })
    );
  }, [queryDetailTabSections]);

  return (
    <ToolPanelsProvider
      toolbarConfigs={queryDetailHeaderSections}
      toolBarKeySectionMap={toolBarKeySectionMap}
    >
      <section className="flex h-full flex-col overflow-hidden read-pretty-label">
        <PageHeader
          title={title}
          back={{
            title: t('Back to Search'),
            onClick: () => navigate(backUrl, { state: location.state }),
          }}
          action={<ToolBarsPanel expandedCount={4} />}
        />
        {/* 在pageCondition稳定下来之前不要加载，容易出很多问题 */}
        {!loading && (
          <section className="flex overflow-hidden flex-1">
            <div className="relative flex-1 flex w-0">
              <div className="flex h-full w-full">
                <div
                  className={clsx(
                    'relative flex-1 flex w-full flex-col',
                    styles.tabsWrapper
                  )}
                >
                  {isArray(queryDetailHeaderMoreInfoSection) && (
                    <CommonInfoDisplay basicInfo={basicInfo} />
                  )}
                  <Tabs
                    className="outer-tabs bg-white h-[calc(100%_-_49px)] flex-1"
                    defaultActiveKey="1"
                    type="line-with-bg"
                    activeKey={currentTab}
                    onChange={key =>
                      handleTabChange(key as PolicyDetailSection)
                    }
                    items={tabItems?.filter(
                      item => querySectionDisplayStatus[item.key] !== false
                    )}
                  />
                </div>
              </div>
            </div>
            <ToolBarsContent contentConfig={toolBarContentConfig} />
          </section>
        )}
      </section>
    </ToolPanelsProvider>
  );
};
