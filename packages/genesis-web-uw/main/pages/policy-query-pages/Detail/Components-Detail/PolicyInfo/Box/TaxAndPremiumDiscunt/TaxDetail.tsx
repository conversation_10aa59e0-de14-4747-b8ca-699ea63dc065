import React, { FC, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Tabs } from 'antd';

import { isEmpty } from '@formily/shared';

import { useAsyncEffect } from 'ahooks';
import Bignumber from 'bignumber.js';
import useSWR from 'swr';

import { Table } from '@zhongan/nagrand-ui';

import {
  OldPolicyService,
  PolicyProductInvestmentListType,
  PolicyProductTaxInfoType,
  YesOrNo,
} from 'genesis-web-service';
import { QueryService } from 'genesis-web-service';
import {
  PolicyBaseObjectType,
  PolicyProductListType,
} from 'genesis-web-service/lib/query/query.interface';
import { amountFormatInstance } from 'genesis-web-shared/lib/l10n';

import {
  InstallmentTypeEnum,
  PremiumItemEnum,
  PremiumType,
  ProductTabType,
  ProductTypeEnum,
  taxDetailMap,
} from '@uw/interface/enum.interface';
import { selectEnums } from '@uw/redux/selector';
import { getRenewalFlagTxt } from '@uw/util/getRenewalFlagTxt';
import { handleMultiCurrency } from '@uw/util/handleMultiCurrency';
import { handleSortData } from '@uw/util/handleSortData';

import { PremiumLayerTable } from './PremiumLayerTable';
import styles from './Tax.module.scss';
import {
  campaignDiscountColumns,
  campaignDiscountInnerColumns,
  extraLoadingColumns,
  extraLoadingInnerColumns,
  productDiscountColumns,
  productDiscountInnerColumns,
  serviceFeeColumns,
  serviceFeeInnerColumns,
  taxDetailColumns,
  taxDetailInnerColumns,
} from './tableColumns';

const { TabPane } = Tabs;

export type TaxInfo = PolicyBaseObjectType &
  Record<string, string> & {
    policyProductInvestmentPlanList: {
      productCode: string;
      productName: string;
      productInvestmentId: string | number | (string | number)[];
      premiumType: string;
      productId: number;
      currency: string;
    }[];
  };

interface Props {
  isProduct?: boolean;
  taxInfo: PolicyBaseObjectType & Record<string, unknown>;
  policyProductList?: (PolicyProductListType & Record<string, unknown>)[];
  withoutTotal?: boolean;
  renewalFlag: boolean;
  hiddenBaseCurrency?: boolean;
}

export const TaxDetail: FC<Props> = ({
  taxInfo,
  policyProductList,
  isProduct,
  renewalFlag,
  withoutTotal,
  hiddenBaseCurrency,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const enums = useSelector(selectEnums);
  const [taxInfoOrders, setTaxInfoOrders] = useState<
    PolicyProductTaxInfoType[]
  >([]);
  const premiumTxt = useCallback(
    (hideTotal?: boolean) =>
      getRenewalFlagTxt(
        taxInfo?.installmentType as InstallmentTypeEnum,
        renewalFlag,
        hideTotal
      ),
    [taxInfo?.installmentType, renewalFlag]
  );

  const isShowTab = (tabKey: string) =>
    !!policyProductList?.find(item =>
      Object.keys(item)?.find(subKey => subKey === tabKey)
    );

  // table 涉及到接口请求 只要包含请求中需要的字段才会展示 table 组件 否则不进行没有意义的接口请求
  const showPremiumLayerTable = useMemo(() => {
    const hasPolicyProductId = !!taxInfo?.policyProductId;
    const hasPolicyId = !!taxInfo?.policyId;
    const investmentList =
      taxInfo?.policyProductInvestmentPlanList as PolicyProductInvestmentListType[];
    const hasPolicyProductInvestmentList =
      !!investmentList?.length &&
      investmentList?.some(
        item => item?.premiumType === PremiumType.PLANNED_PREMIUM
      );
    const isSaBayLayerYes = taxInfo?.saByLayer === YesOrNo.YES;

    return (
      hasPolicyProductId &&
      hasPolicyId &&
      hasPolicyProductInvestmentList &&
      isSaBayLayerYes
    );
  }, [
    taxInfo?.policyId,
    taxInfo?.policyProductId,
    taxInfo?.policyProductInvestmentPlanList,
    taxInfo?.saByLayer,
  ]);

  useAsyncEffect(async () => {
    const productId = isProduct
      ? (taxInfo as unknown as PolicyProductListType).productId
      : policyProductList?.find(
          product => product.productType === ProductTypeEnum.MAIN
        )?.productId;
    if (productId) {
      try {
        const orders = await OldPolicyService.queryPremiumItems(productId);
        // 这里ADJ_NET_PREMIUM的order后端说那边不好改，要手动写死在NET_PREMIUM后
        const netPremiumOrder = orders?.find(
          item => item.premiumItem === PremiumItemEnum.NET_PREMIUM
        );
        if (netPremiumOrder) {
          orders.push({
            premiumItem: PremiumItemEnum.ADJ_NET_PREMIUM,
            order: netPremiumOrder.order + 0.5,
          });
        }

        setTaxInfoOrders(orders ?? []);
      } catch (e) {
        // console.log(e)
      }
    }
  }, [isProduct, taxInfo, policyProductList]);

  const handleGetTaxValue = useCallback<(responseKey: string[]) => string>(
    responseKey =>
      (taxInfo?.[responseKey?.find(resp => taxInfo?.[resp]) ?? ''] as string) ??
      '',
    [taxInfo]
  );
  const getShowListData = (type: string) =>
    policyProductList?.filter(
      item => (item?.[type] as Record<string, unknown>[])?.[0]
    );

  const getValue = useCallback(
    (responseKey: string[]) => {
      if (
        responseKey.some(key =>
          [
            'periodCampaignDiscount',
            'premiumDiscount',
            'periodNoClaimDiscount',
          ].includes(key)
        )
      ) {
        const value: string | number = handleGetTaxValue(responseKey);
        if (value) {
          const originalValue = String(value);
          const numValue = Number(value);
          // 保持原始字符串的小数位数 https://jira.zaouter.com/browse/GIS-128341
          const absOriginalValue = originalValue.replace(/^-/, '');
          return (
            <>
              {amountFormatInstance.getAmountCurrencyString(
                absOriginalValue,
                taxInfo?.currency as string,
                {
                  symbol: numValue > 0 ? '-' : '', // https://jira.zaouter.com/browse/GIS-92502
                }
              )}
            </>
          );
        }
      }
      let value;
      if (responseKey.includes('installmentPremiumBeforeTaxAndServiceFee')) {
        if (responseKey.includes('coverageTotalDeltaNetPremium')) {
          const delNetPrem = new Bignumber(
            handleGetTaxValue(['coverageTotalDeltaNetPremium']) || 0
          );
          const netPremValue = handleGetTaxValue([
            'installmentPremiumBeforeTaxAndServiceFee',
          ]);
          const netPrem = new Bignumber(netPremValue);

          if (netPrem.isNaN()) {
            value = undefined;
          } else {
            // 保留原始小数点位数
            const decimalPlaces = (netPremValue.split('.')[1] || '').length;
            value = netPrem.minus(delNetPrem).toFixed(decimalPlaces);
          }
        } else {
          value = handleGetTaxValue([
            'installmentPremiumBeforeTaxAndServiceFee',
          ]);
        }
      } else {
        value = handleGetTaxValue(responseKey);
      }

      return (
        <>
          {amountFormatInstance.getAmountCurrencyString(
            value,
            taxInfo?.currency as string
          )}
        </>
      );
    },
    [handleGetTaxValue, taxInfo?.currency]
  );

  const productIds = useMemo(() => {
    return taxInfo?.productId
      ? [+taxInfo?.productId]
      : policyProductList?.map(item => item.productId);
  }, [policyProductList, taxInfo?.productId]);

  const { data: adjProductsConfig } = useSWR(
    isEmpty(productIds) ? null : `wrapper/market/product/${productIds}`,
    () =>
      QueryService.queryMarketProduct({
        productIds: productIds!,
        queryProductLevelAgreements: true,
      })
  );

  const showAdjNetPremium = useMemo(() => {
    return adjProductsConfig?.some?.(item => {
      return (
        item?.productLevelAgreements?.adjustNetPremiumAgreement
          ?.isAutoAdjustNetPremium === YesOrNo.YES ||
        item?.productLevelAgreements?.adjustNetPremiumAgreement
          ?.isManualAdjustNetPremium === YesOrNo.YES
      );
    });
  }, [adjProductsConfig]);

  return (
    <div>
      <div className={styles.taxDetailWrapper}>
        <div className={styles.header}>{t('Details')}</div>
        {handleSortData(Object.keys(taxDetailMap), taxInfoOrders)
          .filter(
            item =>
              handleGetTaxValue(taxDetailMap[item.key].responseKey) &&
              (item.key === PremiumItemEnum.ADJ_NET_PREMIUM
                ? showAdjNetPremium
                : true)
          )
          .map(item => {
            const { responseKey } = taxDetailMap[item.key];
            return (
              <div className={styles.taxItemWrapper}>
                <div className={styles.title}>
                  {taxDetailMap[item.key].i18n}
                </div>
                <div className={styles.value}>{getValue(responseKey)}</div>
              </div>
            );
          })}
        <div className={styles.footer}>
          <div className={styles.title}>
            {t(
              `${premiumTxt(withoutTotal)}${
                taxInfo?.installmentType === InstallmentTypeEnum.INSTALLMENT
                  ? ''
                  : ` (${t('Total')})`
              }`
            )}
          </div>
          <div className={styles.value}>
            {handleMultiCurrency(
              taxInfo?.currency,
              taxInfo?.multiCurrency,
              taxInfo?.includingTaxAndDiscountPremium,
              taxInfo?.baseCurrencyIncludingTaxAndDiscountPremium,
              hiddenBaseCurrency
            )}
          </div>
        </div>
      </div>
      {!isProduct && (
        <>
          <Tabs>
            {isShowTab(ProductTabType.PolicyTaxFeeDetailList) && (
              <TabPane tab={t('Tax Details')} key="1">
                <Table
                  scroll={{ x: 'max-content' }}
                  rowKey={record => record.productId}
                  columns={taxDetailColumns(
                    taxInfo?.multiCurrency?.premiumCurrency
                  )}
                  expandType={'nestedTable'}
                  expandable={{
                    defaultExpandAllRows: true,
                    expandedRowRender: record => (
                      <Table
                        columns={taxDetailInnerColumns(
                          taxInfo?.multiCurrency?.premiumCurrency
                        )}
                        dataSource={record.policyTaxFeeDetailList}
                        pagination={false}
                      />
                    ),
                  }}
                  dataSource={getShowListData(
                    ProductTabType.PolicyTaxFeeDetailList
                  )}
                  pagination={false}
                />
              </TabPane>
            )}
            {isShowTab(ProductTabType.PremiumDiscountDetailList) && (
              <TabPane tab={t('Product Discount')} key="2">
                <Table
                  scroll={{ x: 'max-content' }}
                  rowKey={record => record.productId}
                  columns={productDiscountColumns(
                    taxInfo?.multiCurrency?.premiumCurrency
                  )}
                  expandType={'nestedTable'}
                  expandable={{
                    defaultExpandAllRows: true,
                    expandedRowRender: record => (
                      <Table
                        columns={productDiscountInnerColumns(
                          taxInfo?.multiCurrency?.premiumCurrency ||
                            record?.currency,
                          enums?.premiumDiscountType
                        )}
                        dataSource={record.premiumDiscountDetailList}
                        pagination={false}
                      />
                    ),
                  }}
                  dataSource={getShowListData(
                    ProductTabType.PremiumDiscountDetailList
                  )}
                  pagination={false}
                />
              </TabPane>
            )}
            {isShowTab(ProductTabType.CampaignDiscountDetailList) && (
              <TabPane tab={t('Campaign Discount')} key="3">
                <Table
                  scroll={{ x: 'max-content' }}
                  rowKey={record => record.productId}
                  columns={campaignDiscountColumns(
                    taxInfo?.multiCurrency?.premiumCurrency
                  )}
                  expandType={'nestedTable'}
                  expandable={{
                    defaultExpandAllRows: true,
                    expandedRowRender: record => (
                      <Table
                        columns={campaignDiscountInnerColumns(
                          taxInfo?.multiCurrency?.premiumCurrency ||
                            record?.currency
                        )}
                        dataSource={record.campaignDiscountDetailList}
                        pagination={false}
                      />
                    ),
                  }}
                  dataSource={getShowListData(
                    ProductTabType.CampaignDiscountDetailList
                  )}
                  pagination={false}
                />
              </TabPane>
            )}
            {isShowTab(ProductTabType.PolicyProductExtraFeeDetailList) && (
              <TabPane tab={t('query-Extra Loading')} key="4">
                <Table
                  scroll={{ x: 'max-content' }}
                  rowKey={record => record.productId}
                  columns={extraLoadingColumns(
                    taxInfo?.multiCurrency?.premiumCurrency
                  )}
                  expandType={'nestedTable'}
                  expandable={{
                    defaultExpandAllRows: true,
                    expandedRowRender: record => (
                      <Table
                        columns={extraLoadingInnerColumns(
                          taxInfo?.multiCurrency?.premiumCurrency ||
                            record?.currency
                        )}
                        dataSource={record.policyProductExtraFeeDetailList}
                        pagination={false}
                      />
                    ),
                  }}
                  dataSource={getShowListData(
                    ProductTabType.PolicyProductExtraFeeDetailList
                  )}
                  pagination={false}
                />
              </TabPane>
            )}
            {isShowTab(ProductTabType.ServiceFeeDetailList) && (
              <TabPane tab={t('query-Service Fee')} key="5">
                <Table
                  scroll={{ x: 'max-content' }}
                  rowKey={record => record.productId}
                  columns={serviceFeeColumns}
                  dataSource={getShowListData(
                    ProductTabType.ServiceFeeDetailList
                  )}
                  expandType={'nestedTable'}
                  expandable={{
                    defaultExpandAllRows: true,
                    expandedRowRender: record => (
                      <Table
                        columns={serviceFeeInnerColumns(record?.currency)}
                        dataSource={record.serviceFeeDetailList}
                        pagination={false}
                      />
                    ),
                  }}
                  pagination={false}
                />
              </TabPane>
            )}
          </Tabs>
        </>
      )}
      {showPremiumLayerTable && <PremiumLayerTable taxInfo={taxInfo} />}
    </div>
  );
};
