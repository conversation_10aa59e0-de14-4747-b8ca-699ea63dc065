import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { InfoCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

import { StatusTag } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { PolicyInfo } from 'genesis-web-service';

import { dictMap } from '@uw/hook/useBizDict';
import { PolicyStatusEnum } from '@uw/interface/enum.interface';
import { RenderEnums } from '@uw/pages/policy-query-pages/components/RenderEnums';
import { selectEnums } from '@uw/redux/selector';
import { getStatusTagType } from '@uw/util/getStatusTagType';

export const PolicyStatus: FC<{ policyInfo: PolicyInfo }> = ({
  policyInfo,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const { policyStatus, statusChangeReason, statusChangeCause } =
    policyInfo ?? {};
  const enums: Record<string, BizDict[]> = useSelector(selectEnums);
  const policyStatusI18n = dictMap(enums.policyStatus, policyStatus);

  const statusI18n = () => {
    if (
      ![PolicyStatusEnum.TERMINATION, PolicyStatusEnum.LAPSED].includes(
        policyStatus
      )
    ) {
      return policyStatusI18n;
    }
    return (
      <>
        {policyStatusI18n}
        <Tooltip
          title={
            <>
              {policyStatus === PolicyStatusEnum.LAPSED
                ? t('Lapsed Reason')
                : t('Termination Reason')}
              :
              <br />
              {statusChangeReason ? (
                <RenderEnums
                  enums={enums.cancellationReason}
                  value={statusChangeReason}
                  keyName="dictValue"
                />
              ) : (
                <RenderEnums
                  enums={enums.cancellationReason}
                  value={statusChangeCause}
                  keyName="enumItemName"
                />
              )}
            </>
          }
          placement="top"
        >
          <InfoCircleOutlined className="pl-gapXs pr-gapXs background-none" />
        </Tooltip>
      </>
    );
  };

  return (
    <div>
      {policyStatusI18n && (
        <StatusTag
          statusI18n={statusI18n()}
          type={getStatusTagType(policyStatus)}
        />
      )}
      {/** 看起来并没什么用 */}
      {/* {policyStatus !== PolicyStatusEnum.TERMINATION &&
        // "Terminated due to POS -Freelook"
        policyTerminateReasonI18n && (
          <span
            style={{ maxWidth: 'unset' }}
            className={`status ${gray} ml-gapXs`}
          >
            {`${
              policyStatus === PolicyStatusEnum.LAPSED
                ? t('Lapsed Reason')
                : t('Termination Reason')
            }: ${policyTerminateReasonI18n}`}
          </span>
        )} */}
    </div>
  );
};
