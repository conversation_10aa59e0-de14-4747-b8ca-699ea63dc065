import React, { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColumnsType } from 'antd/es/table';

import { Table } from '@zhongan/nagrand-ui';

import { StackBasesType, claimService } from 'genesis-web-service';

import { useDict } from '@uw/biz-dict/hooks';

interface ColumnsDataType extends StackBasesType {
  rowSpan: number;
}
type ClaimStackParams = {
  policyNo?: string;
  policyProductId: string;
  issuanceProductId: string;
  productId: string;
  liabilityId: string;
  issuanceNo?: string;
};
export const ClaimStack: FC<ClaimStackParams> = ({
  policyNo,
  liabilityId,
  productId,
  policyProductId,
  issuanceProductId,
  issuanceNo,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [loading, setLoading] = useState(false);

  const [stackTypeMap, mainConditionTypeMap, claimPeriodTypeMap, stackUnitMap] =
    useDict(
      ['stackType', 'mainConditionType', 'claimPeriodType', 'stackUnit'],
      'dictValue'
    );

  const [stacks, setStacks] = useState<ColumnsDataType[]>([]);
  const columns: ColumnsType<ColumnsDataType> = [
    {
      title: t('Msg_claim_insured_name'),
      dataIndex: 'insuredName',
      width: 136,
      key: 'insuredName',
      render: (value: string, row) => ({
        children: value,
        props: {
          rowSpan: row.rowSpan,
        },
      }),
    },
    {
      title: t('Stack Name'),
      width: 200,
      dataIndex: 'stackName',
      key: 'stackName',
    },
    {
      title: t('Stack Type'),
      dataIndex: 'stackTypeCode',
      width: 160,
      key: 'stackTypeCode',
      render: (text: string) => {
        if (!text) return t('--');
        return stackTypeMap?.[text] || text;
      },
    },
    {
      title: t('Period Type'),
      dataIndex: 'periodTypeCode',
      width: 160,
      key: 'periodTypeCode',
      render: (text: string) => {
        if (!text) return t('--');
        return claimPeriodTypeMap?.[text] || text;
      },
    },
    {
      title: t('Main Condition Type'),
      dataIndex: 'mainConditionTypeCode',
      width: 200,
      key: 'mainConditionTypeCode',
      render: (text: string) => {
        if (!text) return t('--');
        return mainConditionTypeMap?.[text] || text;
      },
    },
    {
      title: t('Stack Unit'),
      dataIndex: 'stackUnitCode',
      width: 136,
      key: 'stackUnitCode',
      render: (text: string) => {
        if (!text) return t('--');
        return stackUnitMap?.[text] || text;
      },
    },
    {
      title: t('Stack Value'),
      width: 112,
      dataIndex: 'stackValue',
      key: 'stackValue',
      fixed: 'right',
      render: text => text || t('--'),
    },
  ];

  const fetchClaimStackResponse = useCallback(() => {
    const baseParams = {
      liabilityId,
      productId,
      policyProductId: policyProductId || issuanceProductId,
    };
    // policyProductId存在时，保单查询；不存在时，投保单查询
    if (policyProductId) {
      return claimService.queryPoliciesStacks({ ...baseParams, policyNo });
    }

    return claimService.queryIssuancesStacks({ ...baseParams, issuanceNo });
  }, [
    policyNo,
    liabilityId,
    productId,
    issuanceNo,
    policyProductId,
    issuanceProductId,
  ]);

  const getClaimStackList = useCallback(async () => {
    setLoading(true);
    const newData: ColumnsDataType[] = [];

    const res = await fetchClaimStackResponse();
    res?.map(stacksItem =>
      stacksItem.stackBases?.map(
        (stack: StackBasesType, stackIndex: number) => {
          const rowSpan = stackIndex === 0 ? stacksItem.stackBases.length : 0;
          newData.push({
            ...stack,
            rowSpan,
            insuredName: stacksItem.insuredName,
          });
          return null;
        }
      )
    );
    setLoading(false);
    setStacks(newData);
  }, [policyNo, liabilityId, productId, policyProductId, issuanceProductId]);

  useEffect(() => {
    getClaimStackList();
  }, [liabilityId, productId, policyProductId, getClaimStackList]);

  return (
    <Table
      scroll={{ x: 'max-content', y: 310 }}
      pagination={false}
      columns={columns}
      dataSource={stacks}
      loading={loading}
    />
  );
};
