import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EyeOutlined } from '@ant-design/icons';
import { Button, Modal, message } from 'antd';

import clsx from 'clsx';
import { useAtomValue } from 'jotai';
import { size } from 'lodash-es';

import { ColumnsType, Table } from '@zhongan/nagrand-ui';

import { ComplianceRule } from 'genesis-web-component/lib/components';
import {
  ComplianceRuleResult,
  ComplianceService,
  HistoryType,
  PolicyService,
  ProposalService,
  TaskInfo,
} from 'genesis-web-service';
import { ComplianceDecisionEnum } from 'genesis-web-service/lib/compliance/compliance.interface';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { useFilterTabSectionByData } from '@uw/pages/policy-query-pages/DetailV2/hooks/useFilterTabSectionByData';
import { PolicyDetailSection } from '@uw/pages/policy-query-pages/DetailV2/sections';
import { queryDetailMetaDataAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';

import { TaskStatus } from './TaskStatus';
import styles from './style.module.scss';

type RuleExt = TaskInfo & {
  businessNo: string;
  businessType: string;
  rowSpan: number;
};

const EmpyStatus = '-';

export const ComplianceInfo: React.FC<{
  policyNo: string;
  issuanceNo: string;
  className?: string;
}> = ({ policyNo, issuanceNo, className }) => {
  const [t] = useTranslation(['uw', 'common']);
  const [dataSource, setDataSource] = useState<RuleExt[]>([]);
  const [loading, setLoading] = useState(false);
  const [ruleVisible, setRuleVisible] = useState(false);
  const [ruleResults, setRuleResults] = useState<ComplianceRuleResult[]>([]);
  const { isPolicyQuery } = useAtomValue(queryDetailMetaDataAtom) || {};

  const {
    l10n: { dateFormat },
  } = useL10n();

  const render = (text: string) =>
    text
      ? dateFormat
          .l10nMoment(text, dateFormat.defaultTimeZone)
          .format('YYYY-MM-DD HH:mm:ss')
      : t('No record');

  const queryPolicyInfo = async () => {
    setLoading(true);
    let result: RuleExt[] = [];
    try {
      let res;
      if (isPolicyQuery) {
        res = policyNo ? await PolicyService.getQueryPolicy(policyNo) : [];
      } else {
        res = issuanceNo
          ? await ProposalService.getQueryProposal(issuanceNo)
          : [];
      }
      res?.forEach(item => {
        const rules = item?.taskInfoList?.map((rule, index) => ({
          ...rule,
          businessNo: item.businessNo,
          businessType: item.businessType,
          rowSpan: index === 0 ? (item.taskInfoList?.length ?? 1) : 0,
        }));
        result = result.concat(rules);
      });
      setDataSource(result);
    } catch (error) {
      message.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryPolicyInfo();
  }, [policyNo, issuanceNo]);

  const showRuleModal = (ruleResult: ComplianceRuleResult[]) => {
    setRuleVisible(true);
    setRuleResults(ruleResult);
  };

  const handleComplianceTaskNoClick = useCallback(
    (complianceTaskNo: string) => {
      ComplianceService.getCaseDetailByCaseNo(complianceTaskNo).then(res => {
        if (res.id) {
          window.open(`/compliance/case/${res.id}`, '_blank');
        }
      });
    },
    []
  );

  const columns: ColumnsType<RuleExt> = [
    {
      title: t('Business Type'),
      dataIndex: 'businessType',
      width: 150,
      render: (text, record) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan,
        },
      }),
    },
    {
      title: t('Business No'),
      dataIndex: 'businessNo',
      width: 220,
      render: (text, record) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan,
        },
      }),
    },
    {
      title: t('History Type'),
      dataIndex: 'historyType',
      width: 150,
    },
    {
      title: t('Compliance Task No.'),
      dataIndex: 'complianceTaskNo',
      width: 180,
      render: (text, record) => {
        if (record.complianceTaskNo && text && text !== '-') {
          return (
            <a
              href="javascript: void(0)"
              onClick={e => {
                e.preventDefault();
                handleComplianceTaskNoClick(record.complianceTaskNo);
              }}
              target="_blank"
            >
              {text}
            </a>
          );
        }
        return text || t('--');
      },
    },
    {
      title: t('Task Status'),
      dataIndex: 'taskStatus',
      width: 180,
      render: status => (
        <span>
          {status === EmpyStatus && <span>{status}</span>}
          <TaskStatus taskStatus={status} />
        </span>
      ),
    },
    {
      title: t('Current Handler'),
      dataIndex: 'currentHandler',
      width: 180,
    },
    {
      title: t('Create Date'),
      dataIndex: 'createDate',
      width: 180,
      render: text => render(text),
    },
    {
      title: t('Completed Date'),
      dataIndex: 'completeDate',
      width: 180,
      render: text => render(text),
    },
    {
      title: t('Compliance Decision'),
      dataIndex: 'complianceDecision',
      width: 180,
      fixed: 'right',
      render: (text, record) => (
        <span className={styles.complianceDecisionFlex}>
          <span
            className={
              styles[
                `complianceDecision${ComplianceDecisionEnum[record.complianceDecisionType]}`
              ]
            }
          >
            {text || t('No record')}
          </span>
          {record.historyTypeCode === HistoryType.Auto && (
            <EyeOutlined
              className={styles.complianceEye}
              onClick={() => showRuleModal(record.ruleResults)}
            />
          )}
        </span>
      ),
    },
  ];

  useFilterTabSectionByData({
    noDataFlag: size(dataSource) === 0,
    sectionKey: PolicyDetailSection.COMPLIANCE_INFO,
  });

  return (
    <div className={clsx(styles.complianceInfo, className)}>
      <div className={styles.complianceInfoTitle}>{t('Compliance Info')}</div>
      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        pagination={false}
        loading={loading}
      />
      {ruleVisible && (
        <Modal
          title={t('Compliance Decision Details')}
          open={ruleVisible}
          width={1000}
          onCancel={() => setRuleVisible(false)}
          footer={
            <Button
              key="submit"
              type="primary"
              onClick={() => setRuleVisible(false)}
            >
              {t('Close')}
            </Button>
          }
        >
          <ComplianceRule
            loadingResults={false}
            ruleResults={ruleResults}
          ></ComplianceRule>
        </Modal>
      )}
    </div>
  );
};
