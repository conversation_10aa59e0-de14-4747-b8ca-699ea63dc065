/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { camelCase, countBy, find, head, keyBy, merge, size } from 'lodash-es';

import {
  BizDict,
  DataTypeEnum,
} from 'genesis-web-component/lib/interface/enum.interface';
import { handleFactorValue } from 'genesis-web-component/lib/util/handleDictOptions';
import { handleFactorDisplayName } from 'genesis-web-component/lib/util/objectDisplayValue';
import {
  AdditionalEquipmentType,
  CarOwerDriverType,
  DesTableType,
  FactorsType,
  LoanType,
  PolicyInsuredObjectType,
  QueryService,
  TabsDataSourceType,
  TripType,
  policyInsuredAttachListType,
} from 'genesis-web-service';
import { useDict } from 'genesis-web-shared/lib/hook';
import { amountFormatInstance, useL10n } from 'genesis-web-shared/lib/l10n';
import { handleDownloadResponse } from 'genesis-web-shared/lib/util/downloadFile';

import UploadFileItem from '@uw/components/UploadFileItem';
import {
  dictMap,
  occupationBizDictI18n,
  useBizDict,
} from '@uw/hook/useBizDict';
import { IsCurrencyAmountTypeEnum } from '@uw/interface/enum.interface';
import { UserType } from '@uw/pages/policy-query-pages/Detail/Components-Detail/Relationship/NewAllRelationInfo/components/User-Info-Drawer/request';
import { useConfigPlateNo } from '@uw/pages/proposal-entry/ProposalEntryDetail/sections/VEHICLE_INFO/hooks/request';
import { AdditionalEquipment } from '@uw/pages/uw-pages/uwOperationPage/components/UwObjectInfo/AdditionalEquipment';

import { VehicleLoan } from '../VehicleLoan';
import { CarOwerDriver } from '../carOwerDriver';
import {
  ObjectSubSubject,
  insuredTypeMap,
  keyName,
  objectIconObj,
} from '../interface';
import styles from '../style.module.scss';
import { VehicleInfo } from '../vehicleInfo';

type ObjectMapType = Record<number, FactorsType[]>;
interface AttachmentType {
  documentType: string;
  attachmentUrl: string;
}
const delObjectInfosList = ['bodyImg', 'frontImg', 'petImg'];

const needFilterFileds = [
  'plateNo1',
  'plateNo2',
  'plateNo3',
  'plateNo4',
  'plateRegistrationZone',
];

export const useInsuredObjectInfoData = (
  policyInsuredObject: PolicyInsuredObjectType &
    Record<string, unknown[] | string>,
  policyZoneId: string,
  objectInfos: Record<number, FactorsType[]>,
  enumsMap: Record<string, BizDict[]> | undefined,
  keyType?: string,
  currency?: string
): [string, TabsDataSourceType] => {
  const [objectSubCategory] = useDict('objectSubCategory', 'dictValue');

  const attachmentDocumentEnums = useBizDict('attachmentDocumentType');
  const relationshipEnum = useBizDict('relationship');

  const { data: plateFilter } = useConfigPlateNo();

  const title = useMemo(
    () =>
      (insuredTypeMap as Record<string, string>)[
        policyInsuredObject.insuredType as string
      ],
    [policyInsuredObject, insuredTypeMap]
  );
  const { t } = useTranslation(['uw', 'common']);
  const { l10n } = useL10n();
  const countryMap = useMemo<Record<string, string> | undefined>(
    () =>
      enumsMap?.country?.reduce((cur, next) => {
        const res: Record<string, string> = { ...cur };
        res[next.enumItemName] = next.dictValueName as string;
        return res;
      }, {}),
    [enumsMap]
  );
  // 判断是否是car owner || driver
  const isCarOwnerOrDrvier = useCallback<(object: string) => boolean>(
    object =>
      +object === ObjectSubSubject.CAR_OWNER ||
      +object === ObjectSubSubject.DRIVER,
    []
  );
  // 判断是否是hotel, FELLOW_TRAVELLER, ticket, trip
  const isHotelOrFellow = useCallback<(object: string) => boolean>(
    object =>
      +object === ObjectSubSubject.TICKET ||
      +object === ObjectSubSubject.TRIP ||
      +object === ObjectSubSubject.FELLOW_TRAVELLER ||
      +object === ObjectSubSubject.HOTEL,
    []
  );
  // 判断是否是Vehicle Loan
  const isVehicleLoan = useCallback<(object: string) => boolean>(
    object => +object === ObjectSubSubject.VEHICLE_LOAN,
    []
  );
  // 判断是否是AdditionalEquipment
  const isAdditionalEquipment = useCallback<(object: string) => boolean>(
    object => +object === ObjectSubSubject.ADDITIONAL_EQUIPMENT,
    []
  );

  // 判断是否有attachmentList

  const attachmentList = useCallback<
    (
      attachData: Record<string, unknown>
    ) => Record<string, AttachmentType>[] | undefined
  >(
    object =>
      (object?.issuanceAttachmentList || object?.policyInsureAttachList) as
        | Record<string, AttachmentType>[]
        | undefined,
    []
  );
  const handleObjectValue = useCallback<
    (
      objectValue: string | string[],
      factorCode: string,
      dataType: string,
      attachList: unknown,
      factor: FactorsType,
      isCurrencyAmount?: number
    ) => Promise<string | JSX.Element | undefined>
  >(
    async (
      objectValue,
      factorCode,
      dataType,
      attachList,
      factor,
      isCurrencyAmount
    ) => {
      if (+dataType === DataTypeEnum.FILE && typeof objectValue === 'object') {
        let urlList = [
          {
            blobUrl: '',
            name: '',
          },
        ];
        try {
          if (objectValue.length > 0) {
            const attachValueObject = attachList?.reduce(
              (out, cur) => ({
                ...out,
                [cur?.attachmentUrl]: cur?.attachmentName,
              }),
              {}
            );

            const res = await Promise.all(
              objectValue.map(item => QueryService.downloadFile(item))
            );

            const newRes = res.map(item => ({
              configuration: item,
              name: attachValueObject?.[
                item.config.url?.substring(item.config.url.lastIndexOf('/') + 1)
              ],
            }));

            const blobList = await Promise.all(
              newRes
                .filter(item => item?.configuration?.data)
                .map(item =>
                  handleDownloadResponse(item?.configuration, item?.name)
                )
            );
            urlList = blobList?.map(blobItem => ({
              blobUrl: window.URL.createObjectURL(blobItem?.blob),
              name: blobItem?.filename,
            }));
          }
        } catch (e: unknown) {}

        if (objectValue && urlList?.[0]?.blobUrl) {
          return urlList.map(item => (
            <div style={{ padding: `${styles.gapMd} 0` }}>
              <UploadFileItem
                showHover={false}
                style={{ background: styles.whiteColor, width: '405px' }}
                fileInfo={{
                  uid: objectValue,
                  name: item.name,
                  url: item.blobUrl,
                  type: 'IMAGE',
                }}
              />
            </div>
          ));
        }

        if (!urlList?.[0]?.blobUrl) {
          return objectValue?.map(item => <p>{item}</p>);
        }

        return objectValue ?? t('--');
      }

      if (factorCode === 'relationshipWithPolicyholder') {
        return dictMap(relationshipEnum, objectValue as string);
      }

      if (factorCode.indexOf('country') > -1) {
        return countryMap?.[objectValue as string] ?? objectValue;
      }
      if (isCurrencyAmount === IsCurrencyAmountTypeEnum.Yes) {
        return amountFormatInstance.getAmountCurrencyString(
          objectValue as string,
          currency ?? ''
        );
      }
      if (['occupationCode', 'driverOccupation'].includes(factorCode)) {
        const occupation = await occupationBizDictI18n([objectValue as string]);
        return occupation ?? objectValue;
      }
      return handleFactorValue(
        objectValue as string,
        factor,
        enumsMap,
        policyZoneId
      );
    },
    [policyZoneId, countryMap, enumsMap]
  );

  const getTabData = useCallback<(object: string) => unknown[]>(
    object => {
      const key = keyName[+object];
      const oldData =
        policyInsuredObject?.[
          objectIconObj(keyType)[+object].dataKey as string
        ];

      const tabData = (oldData as unknown[])?.map(item => {
        const attachData: Record<string, policyInsuredAttachListType> = {};
        // @TODO  proposal 跟 policy的业务字段应该在外面判断
        if (attachmentList(item)) {
          attachmentList(item)?.forEach(attach => {
            if (attachData[attach.documentType]) {
              attachData[attach.documentType] = [
                ...attachData[attach.documentType],
                attach.attachmentUrl,
              ];
            } else {
              attachData[attach.documentType] = [attach.attachmentUrl];
            }
          });
        }
        return { ...item, ...attachData };
      });

      if (isHotelOrFellow(object)) {
        return (tabData as unknown[])
          ?.map(item =>
            (item as Record<string, TripType[]>)?.[key]?.map(
              (tripItem: TripType) => ({
                ...tripItem,
                ...(+object === ObjectSubSubject.TRIP
                  ? {
                      destinationZoneId:
                        (item as Record<string, string>).destinationTimeZone ||
                        policyZoneId,
                      departureZoneId:
                        (item as Record<string, string>).departureTimeZone ||
                        policyZoneId,
                    }
                  : {}),
              })
            )
          )
          ?.flat();
      }

      if (+object === ObjectSubSubject.CLAIM_EXPERIENCE) {
        return (tabData as unknown[])?.map(
          item => (item as Record<string, unknown[]>)?.[key]
        );
      }
      if (+object === ObjectSubSubject.BUILDING) {
        return (tabData as Record<string, string>[])?.map(item => {
          const buildingObject = {
            ...item,
          };
          if (item.address1) {
            const addressObj = {
              address11: item.address1,
              address12: item.address2,
              address13: item.address3,
              address14: item.address4,
              address15: item.address5,
            };
            buildingObject.buildingAddress = addressObj;
          }
          return buildingObject;
        });
      }
      return tabData as unknown[];
    },
    [policyInsuredObject, policyZoneId]
  );

  const dataSource = useMemo(() => {
    if (objectInfos) {
      const newObjectInfos: Record<number, FactorsType[] | ObjectMapType> = {};
      Object.keys(objectInfos).forEach(object => {
        if (isCarOwnerOrDrvier(object)) {
          newObjectInfos[ObjectSubSubject.DRIVER] = {
            ...(newObjectInfos[ObjectSubSubject.DRIVER] ?? {}),
            [object]: objectInfos[+object],
          };
        } else {
          newObjectInfos[+object] = objectInfos[+object];
        }
      });
      const driverFactorList = newObjectInfos?.[ObjectSubSubject.DRIVER]?.[
        ObjectSubSubject.DRIVER
      ] as FactorsType[];
      const staticDriverFactor: string[] = [];
      const extraDriverFactorList: FactorsType[] = [];
      const driverFields = ['driverType', 'driverBirthday', 'driverGender'];
      driverFactorList?.forEach(factor => {
        const index = driverFields.findIndex(
          item => item === factor.factorCode
        );
        if (index !== -1) {
          staticDriverFactor[index] = handleFactorDisplayName(factor);
        } else if (factor.factorCode !== 'driverName') {
          extraDriverFactorList.push(factor);
        }
      });

      return Object.keys(newObjectInfos)
        ?.filter(object => objectIconObj(keyType)[+object])
        .map(object => ({
          id: object,
          tabsTitle: {
            icon: objectIconObj(keyType)[+object].icon,
            label: isCarOwnerOrDrvier(object)
              ? t('Car Owner & Driver & Renter')
              : objectSubCategory?.[object],
          },
          objectKeys: getTabData(object)?.map(
            item =>
              (item as Record<string, string>)?.[
                (objectIconObj(keyType)[+object]?.numberKey as string) ??
                  `${camelCase(objectSubCategory?.[+object]?.trim() || '')}No`
              ]
          ),
          tabsBody: getTabData(object)?.map((item, index) => {
            // Car Owner & Driver 特殊处理
            if (isCarOwnerOrDrvier(object)) {
              const newItem = item as Record<
                string,
                Record<string, unknown> | Record<string, unknown>[]
              >;
              const { carOwner } = newItem;
              const policyInsuredAutoDriverList =
                newItem?.[`${keyType}InsuredAutoDriverList`];
              const {
                name,
                nameCombine,
                nameCombine2,
                nameCombine3,
                gender,
                birthday,
                customerType,
                idType,
                idNumber,
                numberOfVehicleOwned,
                relationshipWithPolicyholder,
                extensions = {},
              } = (carOwner as Record<string, string>) || {};
              const ObjectMap: Record<string, string> = objectInfos[9]?.reduce(
                (curObj, nextFactor) => ({
                  ...curObj,
                  [nextFactor.factorCode]: handleFactorDisplayName(nextFactor),
                }),
                {}
              );
              const extraNameList = [];
              const extraNameValueList = [];
              if (nameCombine2) {
                extraNameList.push(t('Car Owner Name2'));
                extraNameValueList.push(nameCombine2);
              }
              if (nameCombine3) {
                extraNameList.push(t('Car Owner Name3'));
                extraNameValueList.push(nameCombine3);
              }

              const extraDriverNameList: string[] = [];
              policyInsuredAutoDriverList?.forEach(listItem => {
                if (listItem.name || listItem?.nameCombine) {
                  extraDriverNameList[0] = t('Driver Name');
                }
                if (listItem?.nameCombine2) {
                  extraDriverNameList[1] = t('Driver Name2');
                }
                if (listItem?.nameCombine3) {
                  extraDriverNameList[2] = t('Driver Name3');
                }
              });
              let dataHeadLength = 0;
              const originRenter =
                policyInsuredObject?.policyRenterResponse ??
                head(policyInsuredObject?.issuanceInsuredAutoList)
                  ?.proposalRenterResponse;
              const renter = merge({}, originRenter, originRenter?.extensions);
              const carOwerDriverData: CarOwerDriverType[] = [
                {
                  id: 1,
                  title: t('Car Owner'),
                  column: [
                    t('Car Owner Name'),
                    ...extraNameList,
                    t('Car Owner Gender'),
                    t('Car Owner Birthday'),
                  ],
                  data: [
                    {
                      id: '1-1',
                      head: [
                        (name as string) ?? nameCombine ?? '--',
                        ...extraNameValueList,
                        (dictMap(enumsMap?.gender ?? [], gender) as string) ??
                          '--',
                        (l10n.dateFormat.getDateTimeString(
                          birthday,
                          policyZoneId
                        ) as string) ?? '--',
                      ],
                      body: [
                        {
                          label:
                            ObjectMap?.customerType || t('Customer Type'),
                          value: customerType,
                        },
                        {
                          label: ObjectMap?.idType || t('Car Owner ID Type'),
                          value: dictMap(enumsMap?.certiType ?? [], idType),
                        },
                        {
                          label:
                            ObjectMap?.idNumber || t('Car Owner ID Number'),
                          value: idNumber,
                        },
                        {
                          label:
                            ObjectMap?.numberOfVehicleOwned ||
                            t('Number of Vehicle Owned'),
                          value: numberOfVehicleOwned,
                        },
                        {
                          label: t('Relationship with Policyholder'),
                          value: dictMap(
                            relationshipEnum,
                            relationshipWithPolicyholder
                          ),
                        },
                        ...Object.keys(
                          extensions as Record<string, string>
                        ).map(keyItem => ({
                          label: keyItem,
                          value: (extensions as Record<string, string>)[
                            keyItem
                          ],
                        })),
                      ],
                    },
                  ],
                },
                {
                  id: 2,
                  title: t('Driver'),
                  column: [
                    ...extraDriverNameList,
                    t('Msg_query_no'),
                    ...staticDriverFactor,
                  ],
                  data: (
                    policyInsuredAutoDriverList as Record<
                      string,
                      string | number | Record<string, string>
                    >[]
                  )?.map((listItem, index) => {
                    const extraDriverNameValueList: string[] = [];
                    const name1 = (listItem?.nameCombine ||
                      listItem?.name) as string;
                    if (name1) {
                      extraDriverNameValueList[0] = name1;
                    }
                    if (listItem?.nameCombine2) {
                      extraDriverNameValueList[1] =
                        listItem?.nameCombine2 as string;
                    }
                    if (listItem?.nameCombine3) {
                      extraDriverNameValueList[2] =
                        listItem?.nameCombine3 as string;
                    }
                    const head = [
                      // - extraDriverNameValueList没数据UI空占位一下
                      ...(extraDriverNameValueList.length === 0
                        ? ['']
                        : extraDriverNameValueList),
                      index + 1,
                      dictMap(
                        enumsMap?.driverType ?? [],
                        listItem.driverType as string
                      ),
                      l10n.dateFormat.getDateTimeString(
                        listItem.driverBirthday,
                        policyZoneId
                      ),
                      dictMap(
                        enumsMap?.gender ?? [],
                        listItem.gender as string
                      ),
                    ];

                    dataHeadLength =
                      head.length > dataHeadLength
                        ? head.length
                        : dataHeadLength;

                    return {
                      id: `2-${index + 1}`,
                      head,
                      body:
                        (extraDriverFactorList as FactorsType[])?.map(
                          async factor => {
                            let { factorCode } = factor;
                            if (factor.factorCode === 'occupationCode') {
                              factorCode = 'driverOccupation';
                            }
                            if (
                              factor.factorCode ===
                              'driverRelationshipWithPolicyholder'
                            ) {
                              factorCode = 'relationshipWithPolicyholder';
                            }

                            return {
                              label: handleFactorDisplayName(factor),
                              value: await handleObjectValue(
                                typeof (listItem as Record<string, string>)?.[
                                  factorCode
                                ] !== 'undefined'
                                  ? (listItem as Record<string, string>)?.[
                                      factorCode
                                    ]
                                  : (
                                      listItem as Record<
                                        string,
                                        Record<string, string>
                                      >
                                    )?.extensions?.[factorCode],
                                factorCode,
                                factor.dataType as string,
                                attachmentList(listItem),
                                factor,
                                factor.isCurrencyAmount
                              ),
                            };
                          }
                        ) ?? [],
                    };
                  }),
                },
              ];

              const isCompany = renter?.userType === UserType.COMPANY;
              const factors =
                newObjectInfos?.[
                  isCompany
                    ? ObjectSubSubject.ORG_RENTER
                    : ObjectSubSubject.INDIVIDUAL_RENTER
                ];
              // - 因为renter只有一个 但是现在结构上看起来不支持一个？
              if (renter && size(getTabData(object)) - 1 === index && factors) {
                const addressConstant = [
                  'address11',
                  'address12',
                  'address13',
                  'address14',
                  'address15',
                  'address16',
                  'address17',
                  'address18',
                  'zipCode',
                  'country',
                ];
                const addressKey = isCompany
                  ? 'companyAddressType'
                  : 'addressType';
                const addressTypeDictKey = isCompany
                  ? 'organizationAddressType'
                  : 'addressType';
                // -BA说不展示一些字段 or 特殊处理过的一些字段
                const phoneConstant = ['phoneType', 'phoneNo'];
                const additionalConstant = [
                  'userType',
                  'countryCode',
                  'contactPersonName',
                  ...phoneConstant,
                  addressKey,
                  addressTypeDictKey,
                ];
                const effectiveFactors = (factors as FactorsType[])?.filter(
                  ({ factorCode }) =>
                    !addressConstant
                      .concat(additionalConstant)
                      .includes(factorCode)
                );
                const effective = effectiveFactors?.map(
                  ({ factorCode }) => factorCode
                );
                const codeCounts = countBy(factors, 'factorCode');
                const phone = keyBy(renter?.phoneList || [], 'phoneType');
                const phoneBasicKey =
                  codeCounts?.phoneType > 0 && codeCounts?.phoneNo > 0
                    ? Object.keys(phone)
                    : [];
                const address = keyBy(renter?.addressList || [], addressKey);
                const addressBasicKey =
                  (factors as FactorsType[])?.filter(({ factorCode }) =>
                    addressConstant.includes(factorCode)
                  )?.length > 0 && codeCounts?.[addressTypeDictKey] > 0
                    ? Object.keys(address)
                    : [];
                const personBasicKey =
                  size(renter?.contactPersonList) > 0
                    ? ['contactPersonName']
                    : [];

                const basicBody = [
                  ...effective,
                  // -投保要素没配但是数据有返回的话 不展示
                  ...phoneBasicKey,
                  ...addressBasicKey,
                  ...personBasicKey,
                ];
                const basicColumn = isCompany
                  ? [
                      'organizationName',
                      'organizationIdType',
                      'organizationIdNo',
                    ]
                  : ['fullName', 'gender', 'birthday', 'certiNo'];
                const renterData = {
                  id: 3,
                  title: t('RENTER'),
                  column: basicColumn.map(key => {
                    const factor = find(factors, ['factorCode', key]);
                    if (factor) {
                      return handleFactorDisplayName(factor as FactorsType);
                    }
                    return key;
                  }),
                  data: [
                    {
                      id: '2-1',
                      head: basicColumn.map(key => {
                        if (['organizationIdType', 'gender'].includes(key)) {
                          return (
                            find(enumsMap?.[key], [
                              'enumItemName',
                              renter?.[key],
                            ])?.dictValueName ||
                            renter?.[key] ||
                            t('--')
                          );
                        }
                        return renter?.[key];
                      }),
                      body: basicBody.map(key => {
                        const factor = find(factors, ['factorCode', key]);
                        if (address?.[key]) {
                          return {
                            label:
                              find(enumsMap?.[addressTypeDictKey], [
                                'enumItemName',
                                key,
                              ])?.dictValueName || key,
                            value: address?.[key],
                          };
                        }
                        if (phone?.[key]) {
                          const { countryCode = '', phoneNo = '' } = phone[key];
                          return {
                            label:
                              find(enumsMap?.phoneType, ['enumItemName', key])
                                ?.dictValueName || key,
                            value: `${countryCode} ${phoneNo}`,
                          };
                        }
                        // -BA说person join(',') 这么展示
                        if (key === 'contactPersonName') {
                          return {
                            label: factor
                              ? handleFactorDisplayName(factor as FactorsType)
                              : key,
                            value: renter?.contactPersonList
                              ?.map(
                                ({ contactPersonName }) => contactPersonName
                              )
                              .join(','),
                          };
                        }
                        if (enumsMap?.[key]) {
                          return {
                            label: factor
                              ? handleFactorDisplayName(factor as FactorsType)
                              : key,
                            value:
                              renter[key] &&
                              enumsMap?.[key]?.find(
                                item =>
                                  // - follow https://gitlab.zatech.online/genesis/base-web/-/commit/d5d22b00a2be6150d56781b7b6c7a503775e75e7
                                  item.enumItemName === renter[key] ||
                                  item.dictValue === renter[key]
                              )?.dictValueName,
                          };
                        }
                        return {
                          label: factor
                            ? handleFactorDisplayName(factor as FactorsType)
                            : key,
                          value: renter[key],
                        };
                      }),
                    },
                  ],
                };
                carOwerDriverData.push(renterData);
              }

              // 补充column空位，防止值键错位
              const columnSupplement =
                carOwerDriverData[1].column.length < dataHeadLength
                  ? Array(dataHeadLength - carOwerDriverData[1].column.length)
                  : [];
              carOwerDriverData[1].column = [
                ...carOwerDriverData[1].column,
                ...columnSupplement,
              ];

              return <CarOwerDriver dataSource={carOwerDriverData} />;
            }
            if (isVehicleLoan(object)) {
              const { loan } = item as { loan: LoanType };
              return (
                <VehicleLoan
                  vehicleData={[loan]}
                  objectInfo={objectInfos[+object]}
                  enumsMap={enumsMap}
                  policyZoneId={policyZoneId}
                />
              );
            }
            if (isAdditionalEquipment(object)) {
              const {
                policyAdditionalEquipmentList,
                issuanceAdditionalEquipmentList,
              } = item as {
                policyAdditionalEquipmentList: AdditionalEquipmentType[];
                issuanceAdditionalEquipmentList: AdditionalEquipmentType[];
              };
              return (
                <AdditionalEquipment
                  additionalEquipmentList={
                    policyAdditionalEquipmentList ||
                    issuanceAdditionalEquipmentList
                  }
                  objectInfo={objectInfos[+object]}
                  hiddenAdd
                  enumsMap={enumsMap}
                  currency={currency}
                />
              );
            }
            // https://jira.zaouter.com/browse/GIS-38843 判断下后端是否返回值，如若全部未返回，则返回undefined
            let objectItemValues = 0;

            // https://jira.zaouter.com/browse/GIS-35932 支持多张标的图片的落库，保单落下相应数据后。（以pet为例）
            // eslint-disable-next-line no-param-reassign
            objectInfos[+object] = objectInfos[+object].filter(
              info => !delObjectInfosList?.includes(info?.factorCode)
            );
            const objectInfosData = [] as string[];
            if (attachmentList(item)) {
              attachmentList(item)?.forEach(attach => {
                if (attach?.documentType) {
                  objectInfosData?.push(attach?.documentType);
                }
              });
            }
            const newData: FactorsType[] = [];

            attachmentDocumentEnums
              ?.filter(itemData =>
                objectInfosData?.includes(itemData?.dictValue)
              )
              .map(enumItem =>
                newData.push({
                  factorCode: enumItem.dictValue || '',
                  factorName: enumItem.dictValueName || '',
                  bizDictKey: enumItem.dictValue || '',
                  dataType: DataTypeEnum.FILE,
                  isExtension: 2,
                  isRequired: '2',
                  factorDisplayName: '',
                  configured: true,
                  objectCategory: 6,
                  objectSubCategory: 6,
                })
              );

            let objectInfosList = [...objectInfos?.[+object], ...newData];

            objectInfosList = objectInfosList?.filter(objectInfo =>
              plateFilter?.plateNos &&
              needFilterFileds.includes(objectInfo?.factorCode as string)
                ? !plateFilter?.plateNos?.includes(
                    objectInfo?.factorCode as string
                  )
                : true
            );

            const dataSourceItem: Promise<DesTableType>[] =
              objectInfosList?.map(async next => {
                if (
                  (item as Record<string, string>)?.[next.factorCode] ||
                  (item as Record<string, Record<string, string>>)
                    ?.extensions?.[next.factorCode]
                ) {
                  objectItemValues += 1;
                }
                const originValue =
                  typeof (item as Record<string, string>)?.[next.factorCode] !==
                  'undefined'
                    ? (item as Record<string, string>)?.[next.factorCode]
                    : (item as Record<string, Record<string, string>>)
                        ?.extensions?.[next.factorCode];
                return {
                  label: handleFactorDisplayName(next),
                  rowKey: next.factorCode,
                  originValue,
                  value: await handleObjectValue(
                    originValue,
                    next.factorCode,
                    next.dataType,
                    attachmentList(item),
                    next,
                    next.isCurrencyAmount
                  ),
                };
              });

            if (objectItemValues === 0) {
              return undefined;
            }
            return (
              <VehicleInfo
                dataSource={dataSourceItem}
                objectInfo={item as TripType}
                id={object}
                policyZoneId={policyZoneId}
              />
            );
          }),
        }));
    }
    return [];
  }, [
    objectInfos,
    isCarOwnerOrDrvier,
    keyType,
    t,
    objectSubCategory,
    getTabData,
    isVehicleLoan,
    isAdditionalEquipment,
    attachmentList,
    attachmentDocumentEnums,
    policyZoneId,
    policyInsuredObject,
    enumsMap,
    l10n.dateFormat,
    relationshipEnum,
    handleObjectValue,
    currency,
  ]);
  return [title, dataSource];
};
