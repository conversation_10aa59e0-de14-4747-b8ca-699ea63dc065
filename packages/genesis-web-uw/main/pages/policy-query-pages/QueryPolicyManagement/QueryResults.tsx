import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { Divider, MenuProps, TableProps } from 'antd';
import { SorterResult, TableCurrentDataSource } from 'antd/es/table/interface';

import { isEmpty } from 'lodash-es';

import {
  OperationContainer,
  Pagination,
  QueryOperationSelect,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
  Table,
} from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import {
  NewBusinessModuleEnum,
  PageCodeEnum,
  PolicyBaseObjectType,
  RenewService,
  SectionCodeEnum,
  YesOrNo,
} from 'genesis-web-service';
import {
  useCardFooterFields,
  useConfigColumns,
} from 'genesis-web-shared/lib/hook/global-template/index';
import { GlobalPaginationConfig } from 'genesis-web-shared/lib/pagination';

import { CardRenew } from '@uw/assets/new-icons/index';
import { useDict } from '@uw/biz-dict/hooks';
import { CardInfo, CardList } from '@uw/components';
import { QueryBitMap, QueryStatus } from '@uw/components/QueryBiMap';
import { usePermission } from '@uw/hook/permission';
import { useNavigateToProposalEntry } from '@uw/hook/useNavigateToProposalEntry';
import {
  QueryModuleEnum,
  RenewModalTypeEnum,
  SortDirectionEnum,
  SortUpperCase,
} from '@uw/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';

import { handleFilterNullObj } from '.';
import { RenewModal } from '../components/RenewModal';
import { getColumns } from './getColumns';

const CARD_SORT_SPLIT_SYMBOL = '$$$';

const getJoinedSortRule = (
  fieldName: string,
  sortType: SortUpperCase,
  label: string
) => {
  return `${fieldName}${CARD_SORT_SPLIT_SYMBOL}${sortType}${CARD_SORT_SPLIT_SYMBOL}${label}`;
};

const getParsedSortRule = (value?: string) => {
  const [fieldName, sortType, label] =
    value?.split(CARD_SORT_SPLIT_SYMBOL) ?? [];
  return {
    fieldName,
    sortType: sortType as SortUpperCase,
    label,
  };
};

interface Props {
  QueryFormRef: React.RefObject<Record<string, unknown>>;
  searchResultData: PolicyBaseObjectType[];
  allState: Record<string, unknown>;
  loading: boolean;
  pageTotal: number;
  onSearch: () => void;
}

export const QueryResults = forwardRef(
  (
    { QueryFormRef, searchResultData, loading, pageTotal, onSearch }: Props,
    ref
  ) => {
    const { t } = useTranslation(['uw', 'common']);
    const hasRenewalAuth = !!usePermission('query.policy.renewal');
    const navigate = useNavigate();
    const location = useLocation();
    const [state, setState] = useRouterState<Record<string, unknown>>();
    const [queryStatus, setQueryStatus] =
      useState<QueryStatus>('ChooseCondition');

    const [showModal, setShowModal] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [operatePolicyNo, setOperatePolicyNo] = useState<string>();
    const [modalType, setModalType] = useState<RenewModalTypeEnum>();
    const [validateErrorMessage, setValidateErrorMessage] = useState<string[]>(
      []
    );
    const { setIssuanceNo } = useNavigateToProposalEntry();
    const [policyStatusMap, organizationIdTypeMap, certiTypeMap] = useDict([
      'policyStatus',
      'organizationIdType',
      'certiType',
    ]);
    useImperativeHandle(ref, () => ({
      setQueryStatus,
    }));

    const sortMenuItems = useMemo<MenuProps['items']>(() => {
      const insureDateDescLabel = `${t('Application Date')} (${t('Msg_query_Descending')})`;
      const insureDateAscLabel = `${t('Application Date')} (${t('Msg_query_Ascending')})`;
      const effectiveDateDesc = `${t('Msg_query_Policy_Effective_Date')} (${t('Msg_query_Descending')})`;
      const effectiveDateAsc = `${t('Msg_query_Policy_Effective_Date')} (${t('Msg_query_Ascending')})`;
      return [
        {
          value: getJoinedSortRule(
            'insureDate',
            SortUpperCase.DESC,
            insureDateDescLabel
          ),
          label: insureDateDescLabel,
        },
        {
          value: getJoinedSortRule(
            'insureDate',
            SortUpperCase.ASC,
            insureDateAscLabel
          ),
          label: insureDateAscLabel,
        },
        {
          value: getJoinedSortRule(
            'effectiveDate',
            SortUpperCase.DESC,
            effectiveDateDesc
          ),
          label: effectiveDateDesc,
        },
        {
          value: getJoinedSortRule(
            'effectiveDate',
            SortUpperCase.ASC,
            effectiveDateAsc
          ),
          label: effectiveDateAsc,
        },
      ];
    }, [t]);

    const handleCardSortTypeChange = (value: string) => {
      const { fieldName, sortType, label } = getParsedSortRule(value);
      setState({
        ...state,
        sortRule: {
          label,
          fieldName,
          sortType: sortType as SortUpperCase,
        },
      });
      onSearch();
    };

    const handleCardTableSwitch = useCallback<(value: RenderMode) => void>(
      value => {
        const isCard = value === RenderMode.Card;
        setState({
          ...state,
          switchToCard: isCard,
          pageIndex: 1,
          pageSize: isCard
            ? GlobalPaginationConfig.card.pageSize
            : GlobalPaginationConfig.table.pageSize,
        });
        const { getFieldsValue } = QueryFormRef?.current ?? {};
        const haveSearchParams = !isEmpty(
          handleFilterNullObj(getFieldsValue?.())
        );
        if (haveSearchParams) {
          onSearch();
        }
      },
      [state]
    );
    // 预览
    const handleActionView = (record: { policyNo: string }) => {
      navigate(`/uw/query-policy/detail?policyNo=${record.policyNo}`, {
        state: location?.state,
      });
    };

    const handleCloseModal = () => {
      setShowModal(false);
    };

    // 处理表格变化事件
    const handleTableChange: TableProps['onChange'] = (
      { pageSize, current },
      _,
      sorter
    ) => {
      const sorterResult = sorter as SorterResult<PolicyBaseObjectType>;
      let sortRule = state.sortRule;
      if (sorterResult?.field) {
        const sortType =
          sorterResult?.order === SortDirectionEnum.ASCEND
            ? SortUpperCase?.ASC
            : SortUpperCase?.DESC;
        const label = sortMenuItems?.find(menuItem =>
          menuItem.key?.includes(
            `${sorterResult?.field}${CARD_SORT_SPLIT_SYMBOL}${sortType}`
          )
        )?.label;
        sortRule = {
          sortType:
            sorterResult?.order === SortDirectionEnum.ASCEND
              ? SortUpperCase?.ASC
              : SortUpperCase?.DESC,
          fieldName: sorterResult?.field as string,
          label,
        };
      }
      setState({
        ...state,
        pageSize,
        pageIndex: current,
        sortRule,
      });
      onSearch();
    };

    const handleManualRenewal = (policyNo?: string) => {
      RenewService.manualRenewal({
        policyNo: (operatePolicyNo || policyNo) as string,
        needRenewalQuote: YesOrNo.NO,
      })
        .then(res => {
          setIssuanceNo(res?.issuanceNo);
        })
        .catch(e => {
          messagePopup((e as Error).message, 'error');
        })
        .finally(() => {
          setModalLoading(false);
        });
    };

    const handleValidatePeriod: (policyNo: string) => void = policyNo => {
      RenewService.validatePeriod({
        policyNo: policyNo as string,
      })
        .then(res => {
          // isWithdrawPeriod | isValidateMasterPolicy 如果是null 或者 true就验证通过
          if (
            res.isWithdrawPeriod !== false &&
            res.isValidateMasterPolicy !== false
          )
            handleManualRenewal(policyNo);
          else {
            const errMsg = [];
            if (res.isWithdrawPeriod === false) {
              errMsg.push(
                t(
                  'The policy is not within renewal extraction period, please confirm whether to raise renewal.'
                )
              );
            }
            if (res.isValidateMasterPolicy === false) {
              errMsg.push(
                t(
                  'No valid master policy is available for this normal policy renewal, please check.'
                )
              );
            }
            setValidateErrorMessage(errMsg);
            setModalType(RenewModalTypeEnum.ALERT);
            setShowModal(true);
          }
        })
        .catch(err => {
          messagePopup(err.message, 'warn');
        })
        .finally(() => {
          setModalLoading(false);
        });
    };

    const handleActionRenewal = (record: PolicyBaseObjectType) => {
      setOperatePolicyNo(record.policyNo);
      handleValidatePeriod(record.policyNo);
    };

    const columns = getColumns({
      handleActionView,
      hasRenewalAuth,
      handleActionRenewal,
      organizationIdTypeMap,
      certiTypeMap,
    });
    const fetchParams = {
      pageCode: PageCodeEnum.PolicyQueryPageList,
      section: SectionCodeEnum.PolicyQueryPageListIndividual,
      businessModule: NewBusinessModuleEnum.QUERY,
    };
    const { columns: tenantPolicyQueryColumns } = useConfigColumns({
      fetchParams,
      columns,
    });
    const cardFooterFields = useCardFooterFields(fetchParams);

    const contentSection = useMemo(
      () =>
        state.switchToCard ? (
          <>
            <CardList
              cardKey={'policyNo'}
              loading={loading}
              queryModule={QueryModuleEnum.PolicyQuery}
              statusEnumMap={policyStatusMap}
              policyList={
                searchResultData?.map(data => ({
                  ...data,
                  policyEffectiveDate: data.effectiveDate,
                  policyExpiryDate: data.expiryDate,
                  proposalNo: data.policyNo,
                  proposalStatus: data.policyStatusEnum,
                  zoneId: data.zoneId,
                  goodsCategoryId: data.goodsCategory,
                  policyHolderName: data.fullName,
                  goodsName: data.goodsName,
                  temporary: undefined,
                })) as unknown as CardInfo[]
              }
              handleActionView={policyNo => handleActionView({ policyNo })}
              extraActions={[
                ...(hasRenewalAuth
                  ? [
                      {
                        show: () => true,
                        title: t('Renewal'),
                        onClick: handleActionRenewal,
                        icon: CardRenew,
                      },
                    ]
                  : []),
              ]}
              cardFooterFields={cardFooterFields}
            />
            {!isEmpty(searchResultData) && (
              <Pagination
                current={state.pageIndex}
                pageSize={state.pageSize}
                total={pageTotal}
                showSizeChanger
                showQuickJumper
                pageSizeOptions="singlePageTwelve"
                onChange={(curPage, curPageSize) =>
                  handleTableChange(
                    {
                      current: curPage,
                      pageSize: curPageSize,
                    },
                    {},
                    {},
                    {} as TableCurrentDataSource<PolicyBaseObjectType>
                  )
                }
                onShowSizeChange={(curPage, curPageSize) =>
                  handleTableChange(
                    {
                      current: curPage,
                      pageSize: curPageSize,
                    },
                    {},
                    {},
                    {} as TableCurrentDataSource<PolicyBaseObjectType>
                  )
                }
              />
            )}
          </>
        ) : (
          <Table
            loading={loading}
            columns={tenantPolicyQueryColumns}
            dataSource={searchResultData?.map(item => ({
              ...item,
              certiNo: item.certiNo ?? item.organizationIdNo,
              certiType: item.certiType ?? item.organizationIdType,
              key: Math.random(),
            }))}
            scroll={{ x: 2550 }}
            onChange={handleTableChange}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              total: pageTotal,
              current: state.pageIndex,
              pageSize: state.pageSize,
            }}
          />
        ),
      [
        loading,
        policyStatusMap,
        searchResultData,
        hasRenewalAuth,
        cardFooterFields,
        state.switchToCard,
        state.pageIndex,
        state.pageSize,
        pageTotal,
        tenantPolicyQueryColumns,
      ]
    );

    const sortRuleValue = useMemo(() => {
      return state?.sortRule
        ? getJoinedSortRule(
            state.sortRule?.fieldName,
            state.sortRule?.sortType,
            state.sortRule?.label
          )
        : undefined;
    }, [state?.sortRule]);

    return (
      <>
        <QueryResultContainer>
          <OperationContainer>
            <OperationContainer.Right>
              {state.switchToCard && (
                <>
                  <QueryOperationSelect
                    options={sortMenuItems}
                    onChange={handleCardSortTypeChange}
                    value={sortRuleValue}
                    // QueryOperationSelect 设置的defaultValue为options的第一项，会导致从空切换为第一项时无法工作，因为此处将defaultValue设置为与value一致
                    defaultValue={sortRuleValue}
                  />
                  <Divider type="vertical" />
                </>
              )}
              <RenderModeSwitch onChange={handleCardTableSwitch} />
            </OperationContainer.Right>
          </OperationContainer>
          {!loading && isEmpty(searchResultData) ? (
            <QueryBitMap queryStatus={queryStatus} />
          ) : (
            contentSection
          )}
        </QueryResultContainer>
        <RenewModal
          showModal={showModal}
          content={validateErrorMessage}
          type={modalType}
          loading={modalLoading}
          handleCloseModal={handleCloseModal}
          handleManualRenewal={handleManualRenewal}
        />
      </>
    );
  }
);
