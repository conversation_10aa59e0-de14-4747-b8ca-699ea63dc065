/**
 * <AUTHOR>
 * @since 2024-12-16 14:47
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  type ChangeEvent,
  type FC,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { type FormInstance, message } from 'antd';

import { cloneDeep, isEmpty, omit, pickBy, uniqWith } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment/moment';

import { GlobalSkeleton, QueryForm } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import {
  type BizDictItem,
  MetadataService,
  NewBusinessModuleEnum,
  PageCodeEnum,
  ProposalService,
  QueryService,
  SalesAgreementsItem,
  SchemaDefType,
  SectionCodeEnum,
  YesOrNo,
} from 'genesis-web-service';
import { PolicyBaseObjectType } from 'genesis-web-service/lib/query/query.interface';
import { Types } from 'genesis-web-shared';
import { useBizDict, useFunction } from 'genesis-web-shared/lib/hook';
import { useMapAntdSearchFields } from 'genesis-web-shared/lib/hook/global-template';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { GlobalPaginationConfig } from 'genesis-web-shared/lib/pagination';

import {
  BizDictConfigListEnum,
  PolicyTypeNameEnum,
} from '@uw/interface/enum.interface';
import type { AgreementListItem } from '@uw/pages/proposal-entry/hooks/request';
import {
  selectDefaultZoneInfo,
  selectPolicyQueryCondition,
} from '@uw/redux/selector';
import { searchedResult } from '@uw/util/searchedResult';

import { QueryResults } from './QueryResults';
import { type IFieldRef, useQueryFields } from './useQueryFields';

export interface IState extends Record<string, any> {
  policyStatusDropDownListData: BizDictItem[];
  policyTypeDropDownListData: BizDictItem[];
  selectedChannelCode?: string;
  pageTotal: number;
}

export const handleFilterNullObj = (obj: Record<string, unknown>) => {
  const copiedValue = { ...(obj ?? {}) };
  for (const key in copiedValue) {
    const value = copiedValue[key];
    if (value instanceof Object) {
      // policyHolder/Insured是对象，内部有值，都是null
      const filteredValue = pickBy(value);
      copiedValue[key] = isEmpty(filteredValue) ? undefined : filteredValue;
    } else {
      copiedValue[key] = value;
    }
  }
  return pickBy(copiedValue);
};

const QueryManagement: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const { l10n } = useL10n();
  const defaultZoneInfo = useSelector(selectDefaultZoneInfo);
  const queryPolicySession = useSelector(selectPolicyQueryCondition);

  const [state, setState] = useRouterState({
    pageTotal: 0,
    pageIndex: 1,
    pageSize: GlobalPaginationConfig.card.pageSize,
    switchToCard: true,
    plateTypeDropDownList: [],
    policyStatusDropDownListData: [],
    policyTypeDropDownListData: [],
    switchChecked: true,
    condition: {},
    sequenceNoDisabled: true,
    relationSortOrder: '',
    operatePolicyNo: '',
    isShowFullName2: false,
    selectedChannelCode: undefined,
    salesChannelType: undefined,
    nonStandardTariffData: {},
    goodsId: '',
  });

  const [agreementList, setAgreementList] = useState<SalesAgreementsItem[]>([]);
  const [searchedAgreementList, setSearchedAgreementList] = useState<
    SalesAgreementsItem[]
  >([]);

  const [searchResultData, setSearchResultData] = useState<
    PolicyBaseObjectType[]
  >([]);

  const [tableLoading, setTableLoading] = useState<boolean>(false);

  const policyScenarioTypeList: BizDictItem[] =
    useBizDict('policyScenarioType') || [];

  const queryFormRef = useRef<FormInstance & { form: FormInstance }>();
  const queryResultsRef = useRef<{
    getSortsAndPagination: () => Record<string, unknown>;
    setQueryStatus: (queryStatus: string) => void;
    onClear: () => void;
  }>();
  const fieldRef = useRef<IFieldRef>();

  useEffect(() => {
    let defaultSearch = null;
    if (!Types.isEmptyObject(queryPolicySession)) {
      defaultSearch = setTimeout(() => queryFormRef?.current?.submit(), 1500);
    }
    return () => {
      defaultSearch && clearTimeout(defaultSearch);
    };
  }, []);

  const getPolicySchemaDefFieldsData = useFunction(async () => {
    const policySchemaDefFieldsData = await MetadataService.querySchemaDef({
      schemaDefType: SchemaDefType.POLICY,
    });
    const nonStandardTariffData =
      policySchemaDefFieldsData?.policySchemaDefFields[0]?.fields?.find(
        item => item.code === 'nonStandardTariff'
      );
    setState({ nonStandardTariffData });
  }, []);

  // 获取PolicyStatus下拉数据
  const getPolicyStatusDropDownListData = useFunction(async () => {
    let policyStatusDropDownListData: BizDictItem[] = [];
    const res = await MetadataService.queryBizDict({
      dictKeys: ['policyStatus'],
    });
    if (Array.isArray(res)) {
      policyStatusDropDownListData = res;
    }
    setState({ policyStatusDropDownListData });
  }, []);

  const getShowFullName2 = useFunction(async () => {
    const customerNameGroup = await MetadataService.queryBizDict({
      dictKeys: ['customerNameGroup'],
    });

    const isOptional = customerNameGroup?.find(
      item => item?.dictValue === BizDictConfigListEnum.CustomerNameGroup
    )?.isOptional;

    setState({ isShowFullName2: isOptional === YesOrNo.YES });
  }, []);

  const getPlateTypeDropDownListData = useFunction(async () => {
    let plateTypeDropDownList: BizDictItem[] = [];
    const res = await MetadataService.queryBizDict({
      dictKeys: ['plateType'],
    });
    if (Array.isArray(res)) {
      plateTypeDropDownList = res;
    }
    setState({ plateTypeDropDownList });
  }, []);

  useEffect(() => {
    getPolicySchemaDefFieldsData();
    getPolicyStatusDropDownListData();
    getShowFullName2();
    getPlateTypeDropDownListData();
  }, []);

  useEffect(() => {
    if (policyScenarioTypeList) {
      setState({
        policyTypeDropDownListData: policyScenarioTypeList?.filter(policy => {
          return [
            PolicyTypeNameEnum.GROUP_POLICY,
            PolicyTypeNameEnum.NORMAL,
          ].includes(policy?.enumItemName);
        }),
      });
    }
  }, [policyScenarioTypeList]);

  const formatDateTime = useFunction((time: Moment, hhmmss: string) => {
    return `${l10n.dateFormat.getDateString(time)} ${hhmmss}`;
  }, []);

  const onSearchSalesAgreement = useFunction((searchContent: string) => {
    searchedResult(
      searchContent,
      agreementList,
      data => setSearchedAgreementList(data as AgreementListItem[]),
      ['agreementName', 'agreementCode']
    );
  }, []);

  const getAgreementList = async (selectedChannelCode?: string) => {
    if (!selectedChannelCode) {
      setSearchedAgreementList([]);
      setAgreementList([]);
      return;
    }
    const agreementListData = await ProposalService.queryChannelSalesAgreements(
      {
        pageSize: 1000,
        sort: 'ASC',
        salesCode: selectedChannelCode,
      }
    );
    if (!agreementListData || !agreementListData?.content) {
      setAgreementList([]);
    } else {
      const uniqAgreementList = uniqWith(
        agreementListData?.content,
        (value, other) => {
          return value.agreementCode === other.agreementCode;
        }
      );
      setSearchedAgreementList(uniqAgreementList);
      setAgreementList(uniqAgreementList);
    }
  };

  const onChangeAgent = useFunction(
    (option?: { value: string; data: Record<string, string> }) => {
      setState({
        selectedChannelCode: option?.value,
        salesChannelType: option?.data?.salesChannelType,
      });
      getAgreementList(option?.value);
      queryFormRef?.current?.resetFields(['agreementCodeList']);
    },
    []
  );

  const handleSubmit = useFunction(
    async (_values: Record<string, any>, isClear: boolean = true) => {
      const __values = cloneDeep(_values);

      for (const i in __values) {
        if (
          Array.isArray(__values[i]) &&
          __values[i].every(item => moment.isMoment(item))
        ) {
          __values[i] = __values[i].map(item => item.valueOf());
        }
      }

      setState({ condition: __values });

      const values = cloneDeep(_values) as Record<string, any>;
      const { pageIndex, pageSize: limit, sortRule } = state || {};

      const sortRules = sortRule ? [omit(sortRule, 'label')] : null;

      for (const labelKey in values) {
        const zoneid = Object.keys(defaultZoneInfo)?.[0] || 'Asia/Shanghai';

        if (
          labelKey === 'policyEffectiveDate' &&
          Array.isArray(values[labelKey]) &&
          values[labelKey].length
        ) {
          values.effectiveStartDate = l10n.dateFormat.formatTz(
            formatDateTime(values[labelKey][0], '00:00:00'),
            zoneid
          );
          values.effectiveEndDate = l10n.dateFormat.formatTz(
            formatDateTime(values[labelKey][1], '23:59:59'),
            zoneid
          );
        }

        if (
          labelKey === 'insureDate' &&
          Array.isArray(values[labelKey]) &&
          values[labelKey].length
        ) {
          values.insureStartDate = l10n.dateFormat.formatTz(
            formatDateTime(values[labelKey][0], '00:00:00'),
            zoneid
          );
          values.insureEndDate = l10n.dateFormat.formatTz(
            formatDateTime(values[labelKey][1], '23:59:59'),
            zoneid
          );
        }

        if (
          !values[labelKey] ||
          labelKey === 'policyEffectiveDate' ||
          labelKey === 'insureDate'
        ) {
          delete values[labelKey];
        }
        if (
          ['agreementCodeList', 'policyInsurantList'].includes(labelKey) &&
          values[labelKey] &&
          !Array.isArray(values[labelKey])
        ) {
          values[labelKey] = [values[labelKey]];
        }
      }

      if (Types.isEmptyObject(pickBy(values.policyHolder ?? {}))) {
        delete values.policyHolder;
      }
      if (Types.isEmptyObject(pickBy(values.policyInsurantList?.[0] ?? {}))) {
        delete values.policyInsurantList;
      }

      if (Types.isEmptyObject(values)) {
        message.error(`${t('at_least_one_input')}!`);
        return false;
      }

      try {
        setTableLoading(true);
        values.sortMap = sortRules ?? [];
        if (state.salesChannelType) {
          values.salesChannelType = state.salesChannelType;
        }

        if (values.policyHolder && values.policyHolder.certiNo) {
          values.policyHolder.certiNo =
            values.policyHolder.certiNo.toUpperCase();
        }

        const params = {
          condition: values,
          pageIndex: isClear ? 1 : pageIndex,
          limit,
        };

        let pageTotal = 0;
        let searchResultData: PolicyBaseObjectType[] = [];
        const res = await QueryService.queryPolicyListByCondition(params);

        if (
          res &&
          res.success &&
          res.value &&
          Array.isArray(res.value.results)
        ) {
          if ((res.value.total || 0) > 10000) {
            queryResultsRef?.current?.setQueryStatus?.('NoContent');
            message.error(t('query.Msg_query_more10000'));
          } else {
            searchResultData = res.value.results;
            pageTotal = res.value.total || 0;
            if (isEmpty(searchResultData)) {
              queryResultsRef?.current?.setQueryStatus?.('NoContent');
            }
          }
        }
        setSearchResultData(searchResultData);

        setState({ pageTotal });
      } finally {
        setTableLoading(false);
      }
    },
    []
  );

  const onSubmit = useFunction(
    (_values: Record<string, any>, isClear?: boolean) => {
      setTimeout(() => handleSubmit(_values, isClear), 200);
    },
    []
  );

  // 明细单号需求
  const hostRelationNoChange = useFunction(
    (e: ChangeEvent<HTMLSelectElement>) => {
      if (e.target.value) {
        setState({ sequenceNoDisabled: false });
      } else {
        queryFormRef?.current?.form?.setFieldsValue?.({
          hostTransactionNo: '',
        });
        setState({ sequenceNoDisabled: true });
      }
    },
    []
  );

  const convert = useFunction((params?: Record<string, any>) => {
    if (params?.insureDate) {
      const [first, last] = params.insureDate;
      params.insureDate = [moment(first), moment(last)];
    }
    if (params?.policyEffectiveDate) {
      const [first, last] = params.policyEffectiveDate;
      params.policyEffectiveDate = [moment(first), moment(last)];
    }
    return params;
  }, []);

  useEffect(() => {
    if (!isEmpty(handleFilterNullObj(state?.condition))) {
      const condition = convert(state.condition);
      queryFormRef?.current?.setFieldsValue(condition);
      handleSubmit(state.condition, false);
    }
  }, []);

  const localFields = useQueryFields({
    ...state,
    fieldRef,
    queryFormRef,
    goodsId: state.goodsId,
    searchedAgreementList,
    queryPolicySession,
    setState,
    hostRelationNoChange,
    onChangeAgent,
    onSearchSalesAgreement,
  });

  const { fields: bottomFields } = useMapAntdSearchFields({
    fields: localFields,
    fetchParams: {
      businessModule: NewBusinessModuleEnum.QUERY,
      pageCode: PageCodeEnum.PolicyQueryPageList,
      section: SectionCodeEnum.PolicyQueryPageListIndividual,
    },
  });

  const handleSearch = useFunction(() => {
    const values = queryFormRef.current?.getFieldsValue();
    onSubmit(values, false);
  }, []);

  return (
    <div id="query-QueryManagement">
      <GlobalSkeleton autoClose rows={15}>
        <QueryForm
          queryFields={bottomFields}
          formProps={{
            ref: queryFormRef,
          }}
          title={t('policy_query')}
          onSearch={values => onSubmit(values)}
          onClear={() => {
            fieldRef.current?.onReset();
            queryFormRef.current?.resetFields();
            setState({
              pageIndex: 1,
              sequenceNoDisabled: true,
            });
          }}
          loading={tableLoading}
        />

        <QueryResults
          ref={queryResultsRef}
          onSearch={handleSearch}
          QueryFormRef={queryFormRef}
          searchResultData={searchResultData}
          loading={tableLoading}
          pageTotal={state.pageTotal}
        />
      </GlobalSkeleton>
    </div>
  );
};

export default QueryManagement;
