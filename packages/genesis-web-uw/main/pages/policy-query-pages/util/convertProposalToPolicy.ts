import {
  AssociatedPolicyDetail,
  PolicyDetailSubsidiaryType,
  ProposalDetailResponse,
} from 'genesis-web-service';

import {
  PolicyStatusEnum,
  ProposalStatusEnum,
} from '@uw/interface/enum.interface';

export const convertProposalToPolicyData = (param: {
  policyInfo?: PolicyDetailSubsidiaryType | ProposalDetailResponse;
  isPolicyQuery?: boolean;
  coveragePeriodTypeEnum?: Record<string, string>;
}) => {
  const { policyInfo, isPolicyQuery, coveragePeriodTypeEnum } = param;
  if (!policyInfo || isPolicyQuery) {
    return policyInfo as PolicyDetailSubsidiaryType;
  }
  const originalProposalInfo = { ...policyInfo } as ProposalDetailResponse;

  const {
    proposalStatus,
    coveragePeriodType,
    associatedProposalList,
    proposalInsuredObject,
    packageDefId,
    premiumFrequencyTypeI18n,
    issuanceSpecialAgreementList,
    proposalPayerResponseList,
    proposalPayeeResponseList,
    proposalProductList,
    paymentPlanList,
    proposalProductLiabilityList,
    combinedAttachment,
    holderCustomerId,
    issuanceId,
    issuanceInsurantList,
    issuanceInsuredObjectList,
  } = originalProposalInfo;

  const issuanceInsuredAuto = issuanceInsuredObjectList?.[0]
    ?.issuanceInsuredAuto as Record<string, string>;

  const policyBaseObject = {
    ...originalProposalInfo,
    policyStatus:
      proposalStatus === ProposalStatusEnum.EFFECTIVE
        ? PolicyStatusEnum.POLICYEFFECTIVE
        : proposalStatus,
    coveragePeriodTypeI18n: coveragePeriodTypeEnum?.[coveragePeriodType ?? ''],
    associatedPolicyList: (associatedProposalList ??
      []) as AssociatedPolicyDetail[],
    packageId: packageDefId,
    paymentFrequencyI18n: premiumFrequencyTypeI18n,
  };

  const holderKey = isPolicyQuery
    ? 'policyHolderRelatedAttachment'
    : 'issuanceHolderRelatedAttachment';
  const insurantKey = isPolicyQuery
    ? 'policyInsurantRelatedAttachment'
    : 'issuanceInsurantRelatedAttachment';
  const objectKey = isPolicyQuery
    ? 'policyObjectRelatedAttachment'
    : 'issuanceObjectRelatedAttachment';

  const ifInsureObject = isPolicyQuery
    ? undefined
    : [
        issuanceInsuredAuto?.vinNo,
        issuanceInsuredAuto?.plateNo,
        issuanceInsuredAuto?.engineNo,
      ].some(Boolean);

  const convertedPolicyInfo = {
    policyBaseObject,
    policyInsuredObject: proposalInsuredObject,
    policySpecialAgreementList: issuanceSpecialAgreementList,
    policyPayerList: proposalPayerResponseList,
    policyPayeeList: proposalPayeeResponseList,
    policyProductList: proposalProductList,
    paymentPlanList,
    policyProductLiabilityList: proposalProductLiabilityList,
    combinedAttachment: {
      ...combinedAttachment,
      holderRelatedAttachment: combinedAttachment?.[holderKey],
      insurantRelatedAttachment: combinedAttachment?.[insurantKey],
      objectRelatedAttachment: combinedAttachment?.[objectKey],
      holderCustomerId,
      issuanceId,
      insurantCustomerId: issuanceInsurantList?.[0]?.customerId,
      insuredObjectId: issuanceInsuredAuto?.issuanceInsuredAutoId,
    },
    ifInsureObject,
  } as unknown as Partial<PolicyDetailSubsidiaryType>;

  return convertedPolicyInfo;
};
