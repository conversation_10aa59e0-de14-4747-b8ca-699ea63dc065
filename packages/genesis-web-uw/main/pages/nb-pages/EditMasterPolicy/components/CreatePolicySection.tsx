import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  Form,
  FormInstance,
  Menu,
  Popconfirm,
  Radio,
  RadioChangeEvent,
  Row,
  Select,
  Switch,
  Upload,
  message,
} from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface.d';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { DatePickerSupport24 } from 'genesis-web-component/lib/components';
import {
  DocumentTypeEnum,
  PlaceholderEnum,
  PolicyService,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { getFileExtension } from 'genesis-web-shared/lib/util/fileType';

import ErrorFileUpload from '@uw/components/ErrorFileUpload';
import {
  BizDict,
  Mode,
  UploadStatus,
  UploadSubCategoryTypeEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { CreatePolictType, FileType } from '@uw/interface/field.interface';
import { selectEnums } from '@uw/redux/selector';
import { getUploadExtraParams } from '@uw/util/getUploadExtraParamsConfig';
import { messagePopup } from '@uw/util/messagePopup';
import { pickChannelCode } from '@uw/util/pickChannelCode';

import styles from '../EditMasterPolicy.module.scss';

const { Option } = Select;

interface Props {
  partnerCodeListObj: Record<string, BizDict[]>;
  dataSource?: CreatePolictType;
  form: FormInstance<any>;
  busiNo: string;
  mode: Mode;
  defaultList: UploadFile[];
  goodsId: number | undefined;
  goodsPlanIds?: number[];
  oldMasterPolicyNo?: number;
  onDownload?: (fileUniqueCode: string) => void;
  uploadExtraParams?: {
    type: UploadSubCategoryTypeEnum;
    id: string;
  };
}
interface TimeRange {
  startTime: string;
  endTime: string;
}
const layout = {
  labelCol: { span: 16 },
  wrapperCol: { span: 16 },
};
const CreatePolicySection: FC<Props> = ({
  partnerCodeListObj,
  dataSource,
  form,
  mode,
  defaultList,
  goodsId,
  goodsPlanIds,
  oldMasterPolicyNo,
  busiNo,
  onDownload,
  uploadExtraParams,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const enums = useSelector(selectEnums);
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const [fileList, setFileList] = useState<Array<UploadFile<any>>>();
  const [isCollectionRequired, setIsCollectionRequired] = useState(true);
  const [confirmLoading] = useState<boolean>(false);
  const L10nUtil = useL10n();
  const [delIds, setDelIds] = useState<number[]>([]);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const hasNoErrorUpload = useMemo(
    () =>
      (fileList || []).length > 0 &&
      fileList?.[0]?.status === UploadStatus.Done,
    [fileList]
  );

  const disabled = useMemo(
    () => mode === Mode.View || mode === Mode.Edit,
    [mode]
  );
  const [effectiveTimes, setEffectiveTimes] = useState<Array<TimeRange>>([]);

  const handleIfCreate = useCallback<(arg: boolean) => void>(value => {
    setIsCreate(value);
  }, []);

  const effectiveTimesValue = Form.useWatch('effectiveTimes', form);

  useEffect(() => {
    const valuesTemp = form.getFieldValue('effectiveTimes');
    if (valuesTemp?.filter(Boolean).length !== 0) {
      const timeStr = (valuesTemp as [moment.Moment, moment.Moment][])?.map(
        (timerange: [moment.Moment, moment.Moment]) => ({
          startTime: L10nUtil.l10n.dateFormat.formatTz(
            timerange?.[0],
            L10nUtil.l10n.dateFormat.defaultTimeZone
          ) as string,
          endTime: L10nUtil.l10n.dateFormat.formatTz(
            timerange?.[1],
            L10nUtil.l10n.dateFormat.defaultTimeZone
          ) as string,
        })
      );
      setEffectiveTimes(timeStr);
    }
  }, [effectiveTimesValue]);

  useEffect(() => {
    if (dataSource) {
      form.setFieldsValue({
        paymentMethod: dataSource?.paymentMethod,
        premiumCollection: dataSource?.premiumCollection,
        premiumCollectionTime: dataSource?.premiumCollectionTime,
        policyFiles: dataSource?.policyFiles,
      });
      setFileList(dataSource?.policyFiles);
      if (dataSource?.premiumCollection === YesOrNo.YES) {
        setIsCollectionRequired(true);
      } else {
        setIsCollectionRequired(false);
      }
    }
  }, [dataSource]);

  const calcGoodsPlanId = useMemo(
    () => goodsPlanIds?.join(','),
    [goodsPlanIds]
  );

  const handleDownloadTemplate = useCallback(
    (oldMasterPolicyNoParam?: number) => {
      setDownloadLoading(true);
      PolicyService[
        oldMasterPolicyNoParam ? 'downloadTemplate' : 'downloadTemplateNew'
      ](
        goodsId,
        calcGoodsPlanId,
        oldMasterPolicyNoParam
        // eslint-disable-next-line consistent-return
      )
        .then(response => {
          let fileName = window.decodeURI(
            response.headers['content-disposition'] &&
              response.headers['content-disposition'].split('=')[1]
          );
          if (
            fileName === 'undefined' ||
            fileName === null ||
            fileName === ''
          ) {
            if (response.headers.msg) {
              message.error(response.headers.msg);
            }
            return false;
          }
          fileName = fileName.replace(' ', '_');
          if (fileName.startsWith('"') && fileName.endsWith('"')) {
            fileName = fileName.slice(1, -1);
          }
          const link = document.createElement('a');
          const mimeTypeMapping: Record<string, string> = {
            xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
            xls: 'application/vnd.ms-excel;charset=UTF-8',
            docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8',
            doc: 'application/msword;charset=UTF-8',
            pdf: 'application/pdf;charset=UTF-8',
          };
          const fileType = getFileExtension(fileName);
          const url = window.URL.createObjectURL(
            new Blob([response.data], {
              type: mimeTypeMapping[fileType],
            })
          );
          link.href = url;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        })
        .finally(() => {
          setDownloadLoading(false);
        });
    },
    [goodsId, goodsPlanIds, calcGoodsPlanId]
  );

  const handleChangeCollection = useCallback<(e: RadioChangeEvent) => void>(
    e => {
      if (e.target.value === YesOrNo.YES) {
        setIsCollectionRequired(true);
      } else {
        setIsCollectionRequired(false);
      }
    },
    []
  );

  const handleUploadFiles = (formData: FormData) => {
    PolicyService.uploadFileNew(formData)
      .then(res => {
        const index = res.fileUrl.lastIndexOf('/');
        if (!res.hasError) {
          messagePopup(t('Upload Successfully'), 'success');
          setFileList([
            {
              name: res.fileUrl.substring(index + 1, res.fileUrl.length),
              status: UploadStatus.Done,
              url: res.fileUrl,
              taskNo: res.taskNo,
              fileUniqueCode: res.fileUniqueCode,
            },
          ]);
          form.setFieldsValue({
            policyFiles: [
              {
                documentName: res.fileUrl.substring(
                  index + 1,
                  res.fileUrl.length
                ),
                taskNo: res.taskNo,
                fileUrl: res.fileUrl,
                fileUniqueCode: res.fileUniqueCode,
                documentType: DocumentTypeEnum.APPLICATION_FILE,
              },
            ],
          });
        } else {
          setFileList([
            {
              name: res.fileUrl.substring(index + 1, res.fileUrl.length),
              status: UploadStatus.Error,
              url: res.fileUrl,
              taskNo: res.taskNo,
              fileUniqueCode: res.fileUniqueCode,
            },
          ]);
        }
      })
      .catch(() => {
        if (fileList?.length) {
          setFileList([
            {
              ...(fileList?.[0] ?? {}),
              status: UploadStatus.Error,
            },
          ]);
        }

        messagePopup(t('Upload Failed'), 'error');
      });
  };

  const handleConfirmToUpload = useCallback(() => {
    (
      document.querySelector(
        '#createPolicySection .antd-uw-upload > .antd-uw-btn'
      ) as HTMLElement
    ).click();
  }, []);

  const uploadProps = useMemo<UploadProps>(
    () => ({
      name: 'file',
      accept: '.xls, .xlsx',
      multiple: false,
      fileList,
      showUploadList: { showRemoveIcon: true, showDownloadIcon: true },
      beforeUpload: cb => {
        setFileList([
          {
            name: cb.name,
            status: UploadStatus.Uploading,
            url: '',
            uid: cb.uid,
          },
        ]);
      },
      disabled:
        mode === Mode.View || fileList?.[0]?.status === UploadStatus.Uploading,
      customRequest: (cb: any) => {
        const {
          masterPolicyNo,
          channelType,
          channelCode,
          partnerType,
          partnerCode,
        } = form.getFieldsValue();
        let channelCodeStr: string | undefined;

        if (partnerType === PlaceholderEnum.SALE_CHANNEL && partnerCode) {
          channelCodeStr = pickChannelCode(
            partnerCodeListObj[partnerType],
            partnerCode
          );
        } else if (channelType && channelCode) {
          channelCodeStr = pickChannelCode(
            partnerCodeListObj[channelType],
            channelCode
          );
        } else {
          channelCodeStr = '';
        }
        const formData = new FormData();
        formData.append('file', cb?.file);
        formData.append('goodsId', goodsId?.toString() || '');
        formData.append('effectiveTimesJson', JSON.stringify(effectiveTimes));
        formData.append('masterPolicyNo', masterPolicyNo);
        formData.append('businessNo', busiNo);
        formData.append('channelCode', channelCodeStr);
        formData.append('goodsPlanIds', calcGoodsPlanId ?? '');
        getUploadExtraParams(formData, uploadExtraParams);
        handleUploadFiles(formData);
      },
    }),
    [
      fileList,
      defaultList,
      effectiveTimes,
      form,
      calcGoodsPlanId,
      uploadExtraParams,
    ]
  );

  const handleSetFileList = useCallback(files => {
    setFileList(files);
    form.setFieldsValue({
      fileList: files,
    });
  }, []);
  const handleDelAttachment = useCallback(
    (file: FileType) => {
      const files = fileList?.filter(curFile => curFile.uid !== file.uid);
      handleSetFileList(files);
      setDelIds([...(delIds || []), file.id as number]);
    },
    [delIds, fileList]
  );

  const handleDownloadFile = useCallback(() => {
    window.open(fileList?.[0].url);
  }, [fileList]);

  return (
    <section
      className={styles['create-policy-section']}
      id="createPolicySection"
    >
      <div className={styles['section-sub-title']} style={{ border: 0 }}>
        {t('Create Policy under Master Agreement')}
        <Switch
          style={{ marginLeft: '10px' }}
          checked={isCreate}
          onChange={handleIfCreate}
        />
      </div>
      {isCreate ? (
        <>
          <Row style={{ marginBottom: '20px' }}>
            {oldMasterPolicyNo ? (
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item key="DownloadTemplate">
                      <a
                        href="#"
                        style={{ display: 'block' }}
                        onClick={() => {
                          handleDownloadTemplate();
                        }}
                      >
                        {t('Download Template')}
                      </a>
                    </Menu.Item>
                    <Menu.Item key="DownloadOldApplicationForm">
                      <a
                        href="#"
                        style={{ display: 'block' }}
                        onClick={() => {
                          handleDownloadTemplate(oldMasterPolicyNo);
                        }}
                      >
                        {t('Download Old Application Form')}
                      </a>
                    </Menu.Item>
                  </Menu>
                }
              >
                <Button
                  icon={<DownloadOutlined />}
                  disabled={
                    !goodsId || !goodsPlanIds || goodsPlanIds?.length === 0
                  }
                >
                  {t('Download Template')}
                </Button>
              </Dropdown>
            ) : (
              <Button
                icon={<DownloadOutlined />}
                disabled={
                  !goodsId || !goodsPlanIds || goodsPlanIds?.length === 0
                }
                onClick={() => {
                  handleDownloadTemplate();
                }}
                loading={downloadLoading}
              >
                {t('Download Template')}
              </Button>
            )}
          </Row>
          <Row>
            <Col span="24" key="uploadform">
              <Form.Item
                label={t('Upload Application Form')}
                name={'policyFiles'}
                rules={[
                  {
                    required: true,
                    message: t('Please Upload File'),
                  },
                ]}
              >
                <span className={styles['input-file-box']}>
                  {fileList &&
                  fileList.length > 0 &&
                  fileList[0].status === UploadStatus.Done
                    ? decodeURIComponent(fileList[0]?.name)
                    : ''}{' '}
                </span>
                {hasNoErrorUpload && (
                  <>
                    <Popconfirm
                      disabled={disabled}
                      placement="topLeft"
                      title={t('Are you sure to delete this task?')}
                      onConfirm={handleConfirmToUpload}
                      okButtonProps={{ loading: confirmLoading }}
                      okText={t('Yes')}
                      cancelText={t('No')}
                    >
                      <Button
                        disabled={
                          disabled ||
                          fileList?.[0]?.status === UploadStatus.Uploading
                        }
                        icon={<UploadOutlined />}
                        loading={
                          fileList?.[0]?.status === UploadStatus.Uploading
                        }
                      >
                        {t('Upload')}
                      </Button>
                    </Popconfirm>
                    <Button
                      style={{ marginLeft: styles.gapXs }}
                      onClick={() => {
                        if (onDownload) {
                          onDownload(fileList?.[0]?.fileUniqueCode);
                        } else {
                          handleDownloadFile();
                        }
                      }}
                      icon={<DownloadOutlined />}
                    >
                      {t('Download')}
                    </Button>
                  </>
                )}
                <Upload
                  disabled={
                    disabled || fileList?.[0]?.status === UploadStatus.Uploading
                  }
                  className={
                    hasNoErrorUpload ? styles['upload-transparent'] : ''
                  }
                  {...uploadProps}
                  // antd 原生 无法实现定制 download 以及 delete 功能，所以下方进行定制化
                  fileList={[]}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={fileList?.[0]?.status === UploadStatus.Uploading}
                  >
                    {t('Upload')}
                  </Button>
                </Upload>
                <div className={styles['upload-desc-box']}>
                  {t('You can only upload XLS')}
                </div>
                <Row>
                  <Col span="24" key="erroruploadform">
                    {fileList?.map(file => (
                      <UploadFileItem
                        key={file.uid}
                        style={{
                          margin: `${styles.gapMd} 0`,
                        }}
                        needPreview={false}
                        fileName={
                          file.name?.endsWith('.xls') ||
                          file.name?.endsWith('.xlsx')
                            ? file.name
                            : `${file.name}.xls`
                        }
                        showIconDirectly={false}
                        hoverInfoList={[
                          {
                            icon: <Icon type="download" />,
                            onClick: () => {
                              onDownload?.(
                                file?.fileUniqueCode || (file?.url as string)
                              );
                            },
                          },
                          {
                            icon: <Icon type="delete" />,
                            onClick: () => {
                              handleDelAttachment(file);
                            },
                          },
                        ]}
                      />
                    ))}
                  </Col>
                </Row>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span="24" key="erroruploadform">
              {fileList
                ?.filter(file => file.status === UploadStatus.Error)
                ?.map(file => (
                  <ErrorFileUpload
                    hoverHtml={
                      <>
                        <DownloadOutlined
                          onClick={() => {
                            if (file.url) {
                              PolicyService.downloadFile(file.url)
                                .then(response => {
                                  downloadFile(response).then(() => {
                                    message.success(t('Download successfully'));
                                  });
                                })
                                .catch((error: Error) => {
                                  message.error(
                                    error.message || t('Download failed')
                                  );
                                });
                            }
                          }}
                        />

                        <DeleteOutlined
                          onClick={() => {
                            handleDelAttachment(file);
                          }}
                        />
                      </>
                    }
                  ></ErrorFileUpload>
                ))}
            </Col>
          </Row>
          <Row>
            <Col span="24" key="premiumCollection">
              <Form.Item
                label={t('Premium Collection')}
                name={'premiumCollection'}
                initialValue={YesOrNo.YES}
              >
                <Radio.Group onChange={handleChangeCollection}>
                  {enums.yesNo?.map(dict => (
                    <Radio disabled={disabled} value={dict.enumItemName}>
                      {dict.itemName}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col
              span="24"
              key="needCreateRelationalPolicy"
              className={styles['copy-relation-policy']}
            >
              <Form.Item
                label={t('Copy master agreement  to Relational Policy')}
                name={'needCreateRelationalPolicy'}
                initialValue={YesOrNo.YES}
              >
                <Radio.Group>
                  {enums.yesNo?.map(dict => (
                    <Radio disabled={disabled} value={dict.enumItemName}>
                      {dict.itemName}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span="8" key="premiumCollection">
              <Form.Item
                label={t('Payment Method')}
                name={'paymentMethod'}
                rules={
                  isCollectionRequired
                    ? [
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]
                    : undefined
                }
                {...layout}
              >
                <Select
                  placeholder={t('Please select')}
                  allowClear={true}
                  disabled={!isCollectionRequired}
                >
                  {enums.payMethod?.map(option => (
                    <Option value={option.enumItemName}>
                      {option.itemName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span="8" key="premiumCollection">
              <Form.Item
                label={t('Premium Collection Time')}
                name={'premiumCollectionTime'}
                rules={
                  isCollectionRequired
                    ? [
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]
                    : undefined
                }
                {...layout}
              >
                <DatePickerSupport24
                  disabled={!isCollectionRequired}
                  showTime={true}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ) : null}
    </section>
  );
};

export default CreatePolicySection;
