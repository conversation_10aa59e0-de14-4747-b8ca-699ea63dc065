import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, Col, Form, Row } from 'antd';
import { FormInstance } from 'antd/es/form/Form';

import { cloneDeep } from 'lodash-es';
import moment, { Moment } from 'moment';
import type { RangeInfo } from 'rc-picker/lib/RangePicker';
import type { EventValue, RangeValue } from 'rc-picker/lib/interface';

import { RangePickerSupport24 } from 'genesis-web-component/lib/components';
import {
  GoodsInfoDataType,
  MasterPolicyHolderType,
  PlaceholderEnum,
  PolicyService,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { BizDict, Mode, PartnerTypeList } from '@uw/interface/enum.interface';
import {
  useGetGoodsProduct,
  useGetPlanVersion,
} from '@uw/pages/nb-pages/EditMasterPolicy/request';
import { selectEnums } from '@uw/redux/selector';
import { getFields } from '@uw/util/getFields';
import { messagePopup } from '@uw/util/messagePopup';

import styles from '../EditMasterPolicy.module.scss';
import { settingsFields } from '../pageConfig';
import { Relations } from './Relations';

const layout = {
  labelCol: { span: 16 },
  wrapperCol: { span: 16 },
};

const rangeYears = [1, 2, 5, 10];
const defaultRange = (
  year: number
): [EventValue<Moment>, EventValue<Moment>] => [
  moment().startOf('day'),
  moment().add(year, 'y').endOf('day'),
];
interface Props {
  partnerCodeListObj: Record<string, BizDict[]>;
  partnerCodeList: BizDict[];
  form: FormInstance;
  setOrganizationMode: React.Dispatch<React.SetStateAction<Mode>>;
  setPartnerCodeList: React.Dispatch<React.SetStateAction<BizDict[]>>;
  setOrganizationData: React.Dispatch<
    React.SetStateAction<MasterPolicyHolderType | undefined>
  >;
  goodsInfo: (BizDict & GoodsInfoDataType)[];
  setSelectGoodsId: React.Dispatch<React.SetStateAction<number | undefined>>;
  setGoodsCategory: React.Dispatch<React.SetStateAction<number | null>>;
  customizedData: MasterPolicyHolderType | undefined;
  selectedGoodsId: number | undefined;
  setShowEditBtn: React.Dispatch<React.SetStateAction<boolean>>;
  showAddNew: boolean;
  setShowAddNew: React.Dispatch<React.SetStateAction<boolean>>;
  setShowDrawer: React.Dispatch<React.SetStateAction<boolean>>;
  setPeriodStartDate: React.Dispatch<React.SetStateAction<number | undefined>>;
  hideMasterPolicyNo: boolean;
  advancedPaymentId?: number;
  complianceError: string;
  isShowOld?: boolean;
}
const MarketGoodsSettings: FC<Props> = ({
  partnerCodeListObj,
  partnerCodeList,
  form,
  setOrganizationMode,
  setPartnerCodeList,
  setOrganizationData,
  goodsInfo,
  setSelectGoodsId,
  setGoodsCategory,
  customizedData,
  selectedGoodsId,
  setShowEditBtn,
  showAddNew,
  setShowAddNew,
  setShowDrawer,
  setPeriodStartDate,
  hideMasterPolicyNo,
  advancedPaymentId,
  complianceError,
  isShowOld,
}) => {
  const { mode } = useParams();
  const [t] = useTranslation(['uw', 'common']);
  const enums = useSelector(selectEnums);
  const L10nUtil = useL10n();
  const [showViewDetail, setShowViewDetail] = useState(false);

  const disabled = useMemo(() => mode === Mode.View, [mode]);
  const { planVersion } = useGetPlanVersion(selectedGoodsId);
  const [isShowChannel, setShowChannel] = useState<boolean>(false);
  const [salesChannelList, setSalesChannelList] = useState<BizDict[]>([]);
  const [editInit, setEditInit] = useState<boolean>(mode !== Mode.Add);
  // 根据goodsId 获取goodsProduct列表
  const { salesChannels } = useGetGoodsProduct(selectedGoodsId as number);
  const [ranges, setRanges] =
    useState<[EventValue<Moment>, EventValue<Moment>][]>();
  const partnerTypeValue = Form.useWatch('partnerType', form);
  useEffect(() => {
    setShowViewDetail(partnerTypeValue === PlaceholderEnum.ORGANIZATION);
  }, [partnerTypeValue]);
  const [disableAdvancePayment, setDisableAdvancePayment] =
    useState<boolean>(true);

  useEffect(() => {
    setDisableAdvancePayment(!!advancedPaymentId);
  }, [advancedPaymentId]);

  useEffect(() => {
    // 感觉这个effect可以去掉。。。
    const { channelCode, partnerType } = form.getFieldsValue([
      'channelCode',
      'partnerType',
    ]);

    if (
      channelCode &&
      Object.keys(partnerCodeListObj)?.length &&
      editInit &&
      partnerType === PlaceholderEnum.ORGANIZATION
    ) {
      setEditInit(false);
      const partnerCodeListObjData = cloneDeep(partnerCodeListObj);
      Object.keys(partnerCodeListObjData)
        .filter(item => item !== PlaceholderEnum.ORGANIZATION)
        .map(current => {
          const data = partnerCodeListObjData[current]?.find(
            item => item.dictValue === channelCode || item.code === channelCode
          );
          if (data) {
            form.setFieldsValue({
              channelType: current,
              channelCode: data?.enumItemName,
            });
            setSalesChannelList(partnerCodeListObjData[current]);
          }
          return current;
        });

      setShowChannel(true);
    }
  }, [
    form.getFieldValue('channelCode'),
    form.getFieldValue('partnerType'),
    partnerCodeListObj,
    editInit,
  ]);

  const callbackChannelTypeFn = useCallback(
    (channelType: PlaceholderEnum, keepForm?: boolean) => {
      const partnerCodeListObjData = cloneDeep(partnerCodeListObj);
      if (
        salesChannels?.length &&
        channelType === PlaceholderEnum.SALE_CHANNEL &&
        salesChannels.every(
          item => item.partnerType === PlaceholderEnum.SALE_CHANNEL
        )
      ) {
        const channelList = partnerCodeListObjData[
          PlaceholderEnum.SALE_CHANNEL
        ]?.reduce((result, current) => {
          const channel = salesChannels.find(
            item => item.channelCode === current.code
          );
          if (channel) {
            result.push(current);
          }
          return result;
        }, []);
        partnerCodeListObjData[channelType] = channelList?.length
          ? channelList
          : partnerCodeListObjData[channelType];
      }
      setSalesChannelList(partnerCodeListObjData![channelType]);
      if (!keepForm) {
        form.resetFields(['channel', 'channelCode']);
      }
    },
    [partnerCodeListObj, salesChannels, form]
  );

  useEffect(() => {
    if (form.getFieldValue('channelType')) {
      callbackChannelTypeFn(form.getFieldValue('channelType'), true);
    }
  }, [salesChannels]);

  const settingFields = useMemo(
    () =>
      settingsFields(
        isShowOld,
        disabled,
        PartnerTypeList,
        partnerCodeList,
        setShowDrawer,
        showAddNew,
        setOrganizationMode,
        showViewDetail,
        hideMasterPolicyNo,
        disableAdvancePayment,
        partnerType => {
          setPartnerCodeList(partnerCodeListObj![partnerType]);
          form.resetFields([
            'partnerCode',
            'companyId',
            'channel',
            'channelCode',
          ]);
          if (partnerType !== PlaceholderEnum.ORGANIZATION) {
            setShowAddNew(false);
            setShowViewDetail(false);
            setShowChannel(false);
          } else {
            setShowAddNew(true);
            setShowViewDetail(true);
            setShowChannel(true);
            if (customizedData?.companyName) {
              setShowAddNew(false);
            }
          }
        },
        () => {
          // 如果是organization 并且是当前新增的，可以编辑
          if (
            form.getFieldValue('partnerType') ===
              PlaceholderEnum.ORGANIZATION &&
            form.getFieldValue('companyId')
          ) {
            if (form.getFieldValue('companyId') !== 'NewCompany') {
              PolicyService.queryOrganizationDetail(
                form.getFieldValue('companyId')
              ).then(res => {
                setOrganizationData(res);
              });
            }
            if (form.getFieldValue('companyId') === 'NewCompany') {
              setShowEditBtn(true);
              setOrganizationData(customizedData);
            }
            setShowDrawer(true);
            setOrganizationMode(Mode.View);
          }
        },
        callbackChannelTypeFn,
        isShowChannel,
        salesChannelList
      ),
    [
      goodsInfo,
      enums,
      planVersion,
      setSelectGoodsId,
      setGoodsCategory,
      partnerCodeList,
      form,
      customizedData,
      showAddNew,
      setOrganizationData,
      partnerCodeListObj,
      showViewDetail,
      salesChannelList,
      hideMasterPolicyNo,
    ]
  );

  const showTime = useMemo(
    () => ({
      hideDisabledOptions: true,
      defaultValue: [
        moment('00:00:00', L10nUtil.l10n.dateFormat.timeFormatByZeus),
        moment('23:59:59', L10nUtil.l10n.dateFormat.timeFormatByZeus),
      ],
    }),
    []
  );

  const pickerFormat = useMemo(
    () => L10nUtil?.l10n?.dateFormat?.getDatePickerFormat(showTime),
    [showTime]
  );

  const onOpenChange = useCallback(
    (open: boolean, field: any) => {
      const effectiveTimesData = form.getFieldValue('effectiveTimes');
      const curStartTime = effectiveTimesData?.[field.fieldKey]?.[0];
      if (open) {
        const rangesCur: [EventValue<Moment>, EventValue<Moment>][] = [];
        rangeYears.forEach(item => {
          rangesCur.push([
            curStartTime ? moment(curStartTime) : moment().startOf('day'),
            moment(curStartTime)
              .subtract(1, 'days')
              .add(item, 'y')
              .endOf('day'),
          ]);
        });
        setRanges(rangesCur);
      } else {
        PolicyService.effectiveTimeTask({
          effectiveTime: L10nUtil.l10n.dateFormat.formatTz(
            curStartTime,
            L10nUtil.l10n.dateFormat.defaultTimeZone
          ) as string,
        })
          .then(() => {
            setPeriodStartDate(new Date(curStartTime).getTime());
          })
          .catch(res => {
            messagePopup(res?.message, 'error');
            effectiveTimesData[field.fieldKey] = [undefined, undefined];
            form.setFieldsValue({
              effectiveTimes: effectiveTimesData,
            });
          });
      }
    },
    [form]
  );

  const handleOnChange = useCallback<
    (
      values: RangeValue<Moment>,
      formatString: [string, string],
      info: RangeInfo
    ) => void
  >(
    (values, formatString, info) => {
      if (info.range === 'start') {
        const rangesCur: [EventValue<Moment>, EventValue<Moment>][] = [];
        rangeYears.forEach(item => {
          rangesCur.push([
            moment(formatString[0], pickerFormat),
            moment(formatString[0], pickerFormat)
              .subtract(1, 'days')
              .add(item, 'y')
              .endOf('day'),
          ]);
        });
        setRanges(rangesCur);
      }
    },
    [pickerFormat]
  );

  return (
    <>
      <div className={styles['section-title']}>
        {t('Marketing Goods Settings')}
      </div>
      <Row
        style={{
          marginTop: '30px',
        }}
      >
        {settingFields.map(
          field =>
            field.isShowOld !== false && (
              <Col span={field.col as number} key={field.key}>
                <Form.Item
                  label={field.label}
                  name={field.key}
                  rules={field.rules}
                  required={field.required}
                  {...layout}
                >
                  {getFields({ ...field })}
                </Form.Item>
                {field?.isMasterPolicyholder && complianceError && (
                  <div className="flex translate-y-[-25px]">
                    <InfoCircleOutlined className="text-lg mr-xs items-start text-warning" />
                    <div className="text-sm">{complianceError}</div>
                  </div>
                )}
              </Col>
            )
        )}
      </Row>
      <Relations disabled={disabled} />
      <Form.List name="effectiveTimes" initialValue={[undefined]}>
        {(effetiveFields, { add, remove }) => (
          <>
            {effetiveFields.map((field, index) => (
              <Form.Item
                {...field}
                label={index === 0 ? t('Effective Period') : ''}
                key={field.key}
                required
                rules={[
                  () => ({
                    validator(_, value) {
                      if (value?.[0] && value?.[1]) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('Please select')));
                    },
                  }),
                ]}
                className="&_.antd-form-item-control-input-content:flex"
                style={{ display: 'flex' }}
              >
                <Form.Item {...field} noStyle>
                  <RangePickerSupport24
                    onCalendarChange={handleOnChange}
                    presets={[
                      {
                        label: `1 ${t('Year')}`,
                        value: ranges?.[0] || defaultRange(1),
                      },
                      {
                        label: `2 ${t('Years')}`,
                        value: ranges?.[1] || defaultRange(2),
                      },
                      {
                        label: `5 ${t('Years')}`,
                        value: ranges?.[2] || defaultRange(5),
                      },
                      {
                        label: `10 ${t('Years')}`,
                        value: ranges?.[3] || defaultRange(10),
                      },
                    ]}
                    className={'long-input-wrapper'}
                    onOpenChange={open => {
                      onOpenChange(open, field);
                    }}
                    disabled={disabled}
                    placeholder={[t('Start Time'), t('End Time')]}
                    style={index === 0 ? {} : { marginTop: styles.gapXs }}
                    showTime={showTime}
                  />
                </Form.Item>
                {effetiveFields.length > 1 ? (
                  <DeleteOutlined
                    style={{ marginLeft: '10px' }}
                    onClick={() => remove(field.name)}
                  />
                ) : null}
              </Form.Item>
            ))}
            <Form.Item>
              <Button
                type="link"
                onClick={() => add()}
                style={{
                  padding: 0,
                }}
                disabled={disabled}
              >
                {t('+ Add')}
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </>
  );
};
export default MarketGoodsSettings;
