import React, { Dispatch, SetStateAction } from 'react';

import { PlusOutlined, ProfileOutlined } from '@ant-design/icons';
import { Divider, Tooltip } from 'antd';
import { FormInstance } from 'antd/es/form';
import { NamePath } from 'antd/es/form/interface';

import {
  BizDict,
  FieldType,
  Mode,
} from 'genesis-web-component/lib/interface/enum.interface';
import { PlaceholderEnum } from 'genesis-web-service';

import I18nInstance from '@uw/i18n';
import {
  MasterPolicyRelationTypeEnum,
  MinPremiumDurationTypeList,
} from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';

interface Relationship {
  type: string;
  relationNo: string;
}

const ns = { ns: ['uw', 'common'] };

export const fields = (
  disableGoods: boolean,
  form: FormInstance<any>,
  enums: Record<string, BizDict[]>,
  goodsInfo: BizDict[],
  planVersions: BizDict[],
  goodsProduct: BizDict[],
  categoryCallbackFn: (val: number) => void,
  callbackFn: (val: number) => void,
  planCallback: (val: number[]) => void,
  format: string,
  disabledPlans: boolean
): FieldDataType[] => [
  {
    label: I18nInstance.t('Goods Category', ns),
    key: 'goodsCategory',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    options: enums.goodsCategory,
    col: 8,
    allowClear: true,
    disabled: disableGoods,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
    onChange: (value: string) => {
      form.setFieldsValue({
        goodsId: undefined,
        goodsPlanIds: undefined,
        salesTime: [undefined, undefined],
      });
      if (typeof categoryCallbackFn === 'function') {
        categoryCallbackFn(+value);
      }
    },
  },
  {
    label: I18nInstance.t('Goods Version', ns),
    key: 'goodsId',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    options: goodsInfo,
    col: 8,
    allowClear: true,
    disabled: disableGoods,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
    onChange: (value: string) => {
      form.setFieldsValue({
        goodsPlanIds: undefined,
        salesTime: [undefined, undefined],
      });
      if (typeof callbackFn === 'function') {
        callbackFn(+value);
      }
    },
  },
  {
    label: I18nInstance.t('Plan', ns),
    key: 'goodsPlanIds',
    options: planVersions,
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    allowClear: true,
    col: 8,
    disabled: disabledPlans,
    mode: 'multiple',
    onChange: (value: number[]) => {
      if (typeof planCallback === 'function') {
        planCallback(value);
      }
    },
  },
  {
    label: I18nInstance.t('Product in Goods', ns),
    key: 'productIds',
    options: goodsProduct,
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    allowClear: true,
    col: 8,
    disabled: disableGoods,
    mode: 'multiple',
  },
  {
    label: I18nInstance.t('Sales Time', ns),
    key: 'salesTime',
    placeholder: [
      I18nInstance.t('Start Date', ns),
      I18nInstance.t('End Date', ns),
    ],
    type: FieldType.DateRange,
    format,
    allowClear: true,
    disabled: true,
    col: 12,
  },
];

export const settingsFields = (
  isShowOld: boolean,
  disabled: boolean,
  placeHolderOptions: BizDict[],
  partnerCodeList: BizDict[],
  setShowDrawer: Dispatch<SetStateAction<boolean>>,
  showAddNew: boolean,
  setOrganizationMode: Dispatch<SetStateAction<Mode>>,
  showViewDetail: boolean,
  hideMasterPolicyNo: boolean,
  isEdit: boolean, // 当advancedPaymentId存在时,MasterPolicyholder和channel不能编辑
  callbackTypeFn: (val: PlaceholderEnum) => void,
  checkOrganizationDetailFn: () => void,
  callbackChannelTypeFn: (val: PlaceholderEnum) => void,
  isShowChannel: boolean,
  salesChannelList: BizDict[]
) =>
  [
    ...(hideMasterPolicyNo
      ? []
      : [
          {
            label: I18nInstance.t('Master Agreement No.', ns),
            key: 'masterPolicyNo',
            placeholder: I18nInstance.t('Please input', ns),
            type: FieldType.Input,
            col: 8,
            disabled,
            required: true,
            rules: [
              {
                required: true,
                message: I18nInstance.t('Please input', ns),
              },
              {
                validator(_: any, value: string) {
                  if (/^[0-9a-zA-Z]*$/.test(value)) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(
                      I18nInstance.t('Field can only be digital or letter', ns)
                    )
                  );
                },
              },
            ],
          },
        ]),
    {
      label: I18nInstance.t('Original Master Agreement No.', ns),
      key: 'oldMasterPolicyNo',
      placeholder: I18nInstance.t('Please input', ns),
      type: FieldType.Input,
      col: 8,
      isShowOld,
      disabled: true,
      required: true,
      rules: [
        {
          required: true,
          message: I18nInstance.t('Please input', ns),
        },
        {
          validator(_: any, value: string) {
            if (/^[0-9a-zA-Z]*$/.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(
              new Error(
                I18nInstance.t('Field can only be digital or letter', ns)
              )
            );
          },
        },
      ],
    },
    {
      label: I18nInstance.t('Master Policyholder', ns),
      key: '',
      placeholder: I18nInstance.t('Please input', ns),
      type: FieldType.InputGroup,
      col: 24,
      disabled,
      required: true,
      // 用于区分policyholder，它没key
      isMasterPolicyholder: true,
      children: [
        {
          label: '',
          key: 'partnerType',
          options: placeHolderOptions,
          placeholder: I18nInstance.t('Please select', ns),
          type: FieldType.Select,
          col: 8,
          disabled: disabled || isEdit,
          hideSearchIcon: true,
          rules: [
            {
              required: true,
              message: I18nInstance.t('Please select', ns),
            },
          ],
          onChange: (value: PlaceholderEnum) => {
            callbackTypeFn(value);
          },
        },
        {
          label: '',
          key: showViewDetail ? 'companyId' : 'partnerCode',
          options: partnerCodeList,
          placeholder: I18nInstance.t('Please select', ns),
          type: FieldType.Select,
          col: 8,
          disabled: disabled || isEdit,
          suffixIcon: showViewDetail ? (
            <Tooltip placement="top" title={I18nInstance.t('View Detail', ns)}>
              <ProfileOutlined onClick={checkOrganizationDetailFn} />
            </Tooltip>
          ) : null,
          rules: [
            {
              required: true,
              message: I18nInstance.t('Please select', ns),
            },
          ],
          dropdownRender: (menu: string) =>
            showAddNew ? (
              <div>
                {menu}
                <Divider style={{ margin: '4px 0' }} />
                <div
                  style={{
                    display: 'flex',
                    flexWrap: 'nowrap',
                    padding: 8,
                    justifyContent: 'center',
                  }}
                >
                  <a
                    style={{
                      flex: 'none',
                      display: 'block',
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      setOrganizationMode(Mode.Add);
                      setShowDrawer(true);
                    }}
                  >
                    <PlusOutlined /> {I18nInstance.t('Add New', ns)}
                  </a>
                </div>
              </div>
            ) : (
              <div>{menu}</div>
            ),
        },
      ],
    },
    isShowChannel && {
      label: I18nInstance.t('Channel', ns),
      key: 'channel',
      placeholder: I18nInstance.t('Please select', ns),
      type: FieldType.InputGroup,
      col: 24,
      disabled: disabled || isEdit,
      required: true,
      children: [
        {
          label: I18nInstance.t('Channel', ns),
          key: 'channelType',
          options: placeHolderOptions.filter(
            item => item.enumItemName !== PlaceholderEnum.ORGANIZATION
          ),
          placeholder: I18nInstance.t('Please select', ns),
          type: FieldType.Select,
          col: 8,
          disabled,
          hideSearchIcon: true,
          rules: [
            {
              required: true,
              message: I18nInstance.t('Please select', ns),
            },
          ],
          onChange: (value: PlaceholderEnum) => {
            callbackChannelTypeFn(value);
          },
        },
        {
          label: '',
          key: 'channelCode',
          options: salesChannelList?.map(item => ({
            ...item,
            enumItemName: item.code || item.enumItemName,
          })),
          placeholder: I18nInstance.t('Please select', ns),
          type: FieldType.Select,
          col: 8,
          disabled: disabled || isEdit,
          rules: [
            {
              required: true,
              message: I18nInstance.t('Please select', ns),
            },
          ],
        },
      ],
    },
  ].filter(Boolean);

export const getAgreementSettlementRuleFields = (
  paymentEnums: BizDict[] = [],
  disabled: boolean
) => [
  {
    label: I18nInstance.t('Min-Premium Type / Min-Premium', ns),
    key: 'premiumType',
    placeholder: I18nInstance.t('Please input', ns),
    type: FieldType.InputGroupNew,
    col: 24,
    disabled,
    required: false,
    rules: [
      ({ getFieldValue }: { getFieldValue: (name: NamePath) => any }) => ({
        validator() {
          const minPremiumDurationType = getFieldValue(
            'minPremiumDurationType'
          );
          const minPremium = getFieldValue('minPremium');
          if (
            (!minPremiumDurationType && minPremium) ||
            (minPremiumDurationType && !minPremium)
          ) {
            return Promise.reject(
              new Error(
                I18nInstance.t(
                  'Min-Premium Type / Min-Premium should be completed or empty',
                  ns
                )
              )
            );
          }
          if (
            minPremium &&
            !new RegExp(/^[0-9]+([.]{1}[0-9]+){0,1}$/).test(minPremium)
          ) {
            return Promise.reject(
              new Error(I18nInstance.t('Please input a number', ns))
            );
          }
          return Promise.resolve();
        },
      }),
    ],
    children: [
      {
        label: '',
        key: 'minPremiumDurationType',
        options: MinPremiumDurationTypeList,
        placeholder: I18nInstance.t('Please select', ns),
        allowClear: true,
        type: FieldType.Select,
        col: 8,
        disabled,
        hideSearchIcon: true,
      },
      {
        label: '',
        key: 'minPremium',
        placeholder: I18nInstance.t('Please input', ns),
        type: FieldType.Input,
        col: 8,
        maxLength: 50,
        disabled,
        hideSearchIcon: true,
      },
    ],
  },
  {
    label: I18nInstance.t('Settlement Date', ns),
    key: 'settleBatchDate',
    placeholder: I18nInstance.t('Please input', ns),
    type: FieldType.DayPicker,
    col: 8,
    disabled,
    required: false,
    format: 'D',
  },
  {
    label: I18nInstance.t('Settlement Start Date', ns),
    key: 'settleExtractDate',
    placeholder: I18nInstance.t('Please input', ns),
    type: FieldType.DayPicker,
    col: 8,
    disabled,
    required: false,
    format: 'D',
  },
  {
    label: I18nInstance.t('Settlement Frequency', ns),
    key: 'settleFrequency',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    allowClear: true,
    col: 8,
    disabled,
    required: false,
    options: paymentEnums,
  },
];

export const getRelationshipFields = (
  disabled: boolean,
  list: BizDict[],
  changeRelationshipType: (val: MasterPolicyRelationTypeEnum) => void
) => ({
  label: I18nInstance.t('Relationship Type / Relatinship No.', ns),
  key: 'relationshipTypeNo',
  placeholder: I18nInstance.t('Please input', ns),
  type: FieldType.InputGroupNew,
  col: 24,
  disabled,
  required: false,
  rules: [
    ({ getFieldValue }: { getFieldValue: (name: NamePath) => any }) => ({
      validator() {
        const relationDTOs = getFieldValue('relationDTOs');
        const fail = relationDTOs?.reduce(
          (prev: boolean, cur: Relationship) =>
            prev ||
            (cur?.type && !cur?.relationNo) ||
            (!cur?.type && cur?.relationNo),
          false
        );
        if (fail) {
          return Promise.reject(
            new Error(
              I18nInstance.t(
                'Relationship Type / Relatinship No. should be completed or empty',
                ns
              )
            )
          );
        }
        return Promise.resolve();
      },
    }),
  ],
  children: [
    {
      label: '',
      key: 'type',
      options: list,
      placeholder: I18nInstance.t('Please select', ns),
      type: FieldType.Select,
      col: 8,
      disabled,
      hideSearchIcon: true,
      allowClear: true,
      onChange: (val: MasterPolicyRelationTypeEnum) => {
        changeRelationshipType(val);
      },
    },
    {
      label: '',
      key: 'relationNo',
      placeholder: I18nInstance.t('Please input', ns),
      type: FieldType.Input,
      col: 8,
      disabled,
      required: false,
      hideSearchIcon: true,
    },
    {
      label: '',
      key: 'id',
      placeholder: I18nInstance.t('Please input', ns),
      type: FieldType.Input,
      col: 0,
      disabled,
      required: false,
    },
  ],
});

export const taxFields = (
  enums: Record<string, BizDict[]>
): FieldDataType[] => [
  {
    label: I18nInstance.t('Country'),
    key: 'countryCode',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    col: 8,
    allowClear: true,
    options: enums.country,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Tax Type'),
    key: 'taxTypeEnum',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    col: 8,
    allowClear: true,
    options: enums.taxType,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Rate Type'),
    key: 'taxRateTypeEnum',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    col: 8,
    allowClear: true,
    options: enums.taxRateType,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Currency'),
    key: 'currencyEnum',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    col: 8,
    allowClear: true,
    options: enums.currencys,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('PayMethod'),
    key: 'payMethod',
    placeholder: I18nInstance.t('Please select', ns),
    type: FieldType.Select,
    col: 8,
    allowClear: true,
    options: enums.payMethod,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Tax Rate/Value'),
    key: 'taxRateValue',
    placeholder: I18nInstance.t('Please input', ns),
    type: FieldType.Input,
    col: 8,
    allowClear: true,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please input', ns),
      },
    ],
  },
];
export const extraFields = (): FieldDataType[] => [
  {
    label: I18nInstance.t('Code'),
    key: 'code',
    type: FieldType.Input,
    col: 8,
    allowClear: true,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Name'),
    key: 'name',
    type: FieldType.Input,
    col: 8,
    allowClear: true,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Value'),
    key: 'value',
    type: FieldType.Input,
    col: 8,
    allowClear: true,
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
];
