import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';

import { Form, Layout, Modal, Row, message } from 'antd';
import { UploadFile } from 'antd/lib/upload/interface.d';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';

import {
  CreatePaymentType,
  DocumentType,
  DocumentTypeEnum,
  ExtraSettingTableType,
  GoodsInfoDataType,
  GoodsStatusEnum,
  MasterPolicyDetailDataType,
  MasterPolicyHolderType,
  PlaceholderEnum,
  PolicyService,
  Relation,
  SystemService,
  TaxSettingTableType,
} from 'genesis-web-service';
import { amountFormatInstance, useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import {
  BizDict,
  GoodsCategoryId,
  MasterAgreementStatus,
  Mode,
  PartnerTypeList,
  RenewalSourceEnums,
  SaveMode,
  UploadSubCategoryTypeEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { CreatePolictType } from '@uw/interface/field.interface';
import {
  useGetGoodsProduct,
  useGetPlanVersion,
  useIfHideMasterPolicyNo,
  usePartnerCode,
} from '@uw/pages/nb-pages/EditMasterPolicy/request';
import { ComplianceCheckStatus } from '@uw/pages/nb-pages/MasterAgreementDetail/interface';
import { selectEnums } from '@uw/redux/selector';
import { getFields } from '@uw/util/getFields';
import { messagePopup } from '@uw/util/messagePopup';
import { pickChannelCode } from '@uw/util/pickChannelCode';

import styles from './EditMasterPolicy.module.scss';
import { AgreementSettlementRule } from './components/AgreementSettlementRule';
import CreatePaymentSection from './components/CreatePaymentSection';
import CreatePolicySection from './components/CreatePolicySection';
import ExtraSettingTable from './components/ExtraSettingTable';
import MarketGoodsSettings from './components/MarketGoodsSettings';
import MasterPolicyHeader from './components/MasterPolicyHeader';
import OrganizationDrawer from './components/OrganizationDrawer';
import { SubmitModal } from './components/SubmitModal';
import TaxSettingTable from './components/TaxSettingTable';
import UploadDocument from './components/UploadDocument';
import { fields } from './pageConfig';

const { Content } = Layout;

const partnerTypeMap = PartnerTypeList.reduce<Record<string, string>>(
  (cur, next) => {
    const curObj = { ...cur };
    curObj[next.enumItemName] = next.itemName;
    return curObj;
  },
  {}
);

const layout = {
  labelCol: { span: 16 },
  wrapperCol: { span: 16 },
};

interface MasterPolicySettlements {
  [index: string]: string;
  minPremiumDurationType: string;
  minPremium: string;
  settleFrequency: string;
  settleExtractDate: string;
  settleBatchDate: string;
}

const successCheckStatus = [
  ComplianceCheckStatus.COMPLIANCE_COMPLETED_NO_RULE,
  ComplianceCheckStatus.COMPLIANCE_COMPLETED_AUTO,
  ComplianceCheckStatus.COMPLIANCE_COMPLETED_MANUAL,
];

const EditMasterPolicy: FC = () => {
  const enums = useSelector(selectEnums);
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const { mode, id } = useParams();
  const L10nUtil = useL10n();
  const navigate = useNavigate();
  // const editOrView = mode === Mode.Edit || mode === Mode.View;
  const [agreementDetail, setAgreementDetal] =
    useState<MasterPolicyDetailDataType>({});
  const [goodsCategory, setGoodsCategory] = useState<number | null>(null);
  const [goodsInfo, setGoodsInfo] = useState<(BizDict & GoodsInfoDataType)[]>(
    []
  );
  const [goodsMap, setGoodsMap] = useState<Record<number, string>>();
  const [selectedPlans, setSelectedPlans] = useState<number[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [submitParams, setSubmitParams] =
    useState<MasterPolicyDetailDataType>();
  const disableGoods = useMemo(() => mode !== Mode.Add, [mode]);
  const masterPolicyId = useRef(id);
  const { partnerCodeListObj, setPartnerCodeListObj, partnerCodeMap } =
    usePartnerCode();
  // taxSetting相关state
  const taxSettingRef = useRef<{
    getTaxSettingData: () => Promise<TaxSettingTableType[]>;
  }>();
  const [taxSettingData, setTaxSettingData] = useState<
    (TaxSettingTableType & { key: string })[]
  >([]);
  const partnerType = Form.useWatch('partnerType', form);
  const partnerCode = Form.useWatch('partnerCode', form);
  const channelCodeId = Form.useWatch('channelCode', form);

  // organization 相关state
  const [organizationData, setOrganizationData] =
    useState<MasterPolicyHolderType>();
  const [customizedData, setCustomizedData] =
    useState<MasterPolicyHolderType>(); // 自定义的organization暂存的地方
  const [organizationMode, setOrganizationMode] = useState<Mode>(Mode.View);

  const [showDrawer, setShowDrawer] = useState(false);
  const [showAddNew, setShowAddNew] = useState(true);
  const [showEditBtn, setShowEditBtn] = useState(false);

  const [saveButtonLoading, setSaveButtonLoading] = useState(false);

  const [advancedPaymentId, setAdvancedPaymentId] = useState<number>();
  // extraSetting相关state
  const extraSettingRef = useRef<{
    getExtraSettingData: () => Promise<ExtraSettingTableType[]>;
  }>();
  const [extraSettingData, setExtraSettingData] = useState<
    (ExtraSettingTableType & { key: string })[]
  >([]);
  const [savedMaterPolicyNo, setSavedMaterPolicyNo] = useState('');

  // document相关state
  const documentRef = useRef<{
    getDocumentData: () => Promise<DocumentType[]>;
  }>();
  const [documents, setDocuments] = useState<UploadFile[]>();

  // create policy相关state
  const [createPolicyData, setCreatePolicyData] = useState<CreatePolictType>();

  // create payment相关state
  const [createPaymentData, setCreatePaymentData] =
    useState<CreatePaymentType>();

  // relationDTOs相关state
  const [relationDTO, setRelationDTO] = useState<Relation[]>([]);

  const isShowOld = useMemo(
    () =>
      relationDTO?.some(item => item.type === 'RENEWAL') || mode === Mode.Renew,
    [relationDTO, mode]
  );

  // 存储queryform里的goodsId, 用来联动
  const [selectedGoodsId, setSelectGoodsId] = useState<number>();
  // 根据partnerType获取partnerCode信息
  const { planVersion } = useGetPlanVersion(selectedGoodsId);

  // 根据goodsId 获取goodsProduct列表
  const { goodsProduct, salesAttributes, salesChannels, packageBasicList } =
    useGetGoodsProduct(selectedGoodsId as number);

  // Master Agreement生效前允许增加税
  const [periodStartDate, setPeriodStartDate] = useState<number | undefined>();
  const [addTax, setAddTax] = useState(false);
  const [systemTime, setSystemTime] = useState<number | undefined>();

  // submit modal
  const [successModal, setSuccessModal] = useState(false);

  const [complianceDisabled, setComplianceDisabled] = useState<boolean>();

  useEffect(() => {
    setComplianceDisabled(!agreementDetail?.masterPolicyId || !partnerType);
  }, [agreementDetail?.masterPolicyId, partnerType]);

  // plan 选择之后需要从 packageList 中获取 packageCode
  const packageCodeList = useMemo(() => {
    if (
      !selectedPlans?.length ||
      !packageBasicList?.length ||
      !planVersion?.length
    ) {
      return [];
    }

    const packageIds = selectedPlans
      .map(
        key => planVersion.find(item => item.enumItemName === key)?.itemExtend1
      )
      .filter(Boolean);

    return packageIds
      ?.map(
        pId =>
          packageBasicList.find(item => item.packageId! === +pId!)?.packageCode
      )
      .filter(Boolean) as string[] | undefined;
  }, [packageBasicList, selectedPlans, planVersion]);

  const channelCodeParam = useMemo(() => {
    if (
      [PlaceholderEnum.SALE_CHANNEL, PlaceholderEnum.AGENCY].includes(
        partnerType
      )
    ) {
      return pickChannelCode(partnerCodeListObj[partnerType], partnerCode);
    }

    return pickChannelCode(
      partnerCodeListObj[form.getFieldValue('channelType')],
      channelCodeId
    );
  }, [form, partnerCodeListObj, partnerType, partnerCode, channelCodeId]);

  const [hideMasterPolicyNo] = useIfHideMasterPolicyNo(mode, {
    goodsCode: selectedGoodsId ? goodsMap?.[selectedGoodsId] : undefined,
    packageCode: packageCodeList?.[0],
    channelCode: channelCodeParam,
  });

  const [complianceError, setComplianceError] = useState('');

  const [status, setStatus] = useState('');

  // https://jira.zaouter.com/browse/GIS-37389 进到页面先检测是否有生成master policy no的rule
  // useEffect(() => {
  //   if (
  //     masterPolicyNo &&
  //     (mode === Mode.Add || mode === Mode.Copy || mode === Mode.Renew)
  //   ) {
  //     form.setFieldsValue({ masterPolicyNo });
  //   }
  // }, [masterPolicyNo, mode]);

  useEffect(() => {
    const startTime = salesAttributes?.saleStartTime;
    const endTime = salesAttributes?.saleEndTime;
    form.setFieldsValue({
      salesTime: [
        startTime
          ? L10nUtil.l10n.dateFormat.l10nMoment(startTime as string)
          : undefined,
        endTime
          ? L10nUtil.l10n.dateFormat.l10nMoment(endTime as string)
          : endTime,
      ],
    });
  }, [salesAttributes]);

  useEffect(() => {
    if (mode === Mode.Edit) {
      SystemService.getTenantZoneInfo().then(res => {
        setSystemTime(res.defaultTimeStamp);
      });
    }
  }, [mode]);

  // If system date>= effective date, 那么Add按钮置灰，不允许添加
  // GIS-36483 生效过的含税的 master policy  改了生效期间后不可以添加修改税
  // 生效的master policy 改了生效期间后   也是只能添加
  useEffect(() => {
    if (
      periodStartDate &&
      systemTime &&
      systemTime < periodStartDate &&
      !agreementDetail?.taxSettings
    ) {
      setAddTax(true);
    } else {
      setAddTax(false);
    }
  }, [systemTime, periodStartDate, agreementDetail]);

  // @see https://jira.zaouter.com/browse/GIS-119846 Normal master Policy类型effective状态允许编辑plan
  // 跟后端沟通，当前页面只有group eb与Normal master Policy，用group eb的goodsCategory=33，排除掉33即 Normal master Policy
  const disabledPlans = useMemo(
    () =>
      !(
        mode === Mode.Add ||
        (mode === Mode.Edit &&
          status === 'POLICY_EFFECT' &&
          goodsCategory?.toString() !== GoodsCategoryId.GROUP_EMPLOYEE_BENEFIT)
      ),
    [mode, status, goodsCategory]
  );

  // Master Goods Selection fields cofig
  const queryFields = useMemo(
    () =>
      fields(
        disableGoods,
        form,
        enums,
        goodsInfo,
        planVersion,
        goodsProduct,
        goodCategory => {
          setGoodsCategory(goodCategory);
        },
        goodId => {
          setSelectGoodsId(goodId);
          setSelectedPlans([]);
        },
        goodsPlans => {
          setSelectedPlans(goodsPlans);
        },
        L10nUtil?.dateFormat?.dateFormat || 'YYYY-MM-DD',
        disabledPlans
      ),
    [
      goodsInfo,
      planVersion,
      setSelectGoodsId,
      setGoodsCategory,
      enums,
      goodsProduct,
      disabledPlans,
    ]
  );

  const [partnerCodeList, setPartnerCodeList] = useState<BizDict[]>([]);

  // goodsId列表
  useEffect(() => {
    if (!goodsCategory) return;
    PolicyService.queryGoodsRelating({
      categoryList: [goodsCategory as number],
      querySalesAttributes: true,
    })
      .then(res => {
        const mapSelect = res
          ?.map(good => ({
            enumItemName: good?.goodsId.toString(),
            itemName: `${
              good?.goodsName || good?.goodsId.toString()
            } - ${good?.goodsVersion}`,
            ...(good ?? {}),
          }))
          .filter(good => good.goodsStatus === GoodsStatusEnum.TAKE_EFFECT);
        const goodsCodeMap = mapSelect?.reduce(
          (cur, next) => ({
            ...cur,
            [next.goodsId]: next.code,
          }),
          {}
        );
        setGoodsInfo(mapSelect);
        setGoodsMap(goodsCodeMap);
      })
      .catch(() => {
        // error
      });
  }, [goodsCategory]);

  const documentsTransfer = useCallback<
    (documents: DocumentType[]) => UploadFile[]
  >(
    documentParams =>
      documentParams.map(document => ({
        name: document.documentName as string,
        uid: document.fileUniqueCode as string,
        url: (document.fileUrl as string) || document.fileUniqueCode || '',
        documentType: document.documentType,
      })),
    []
  );

  // 获取master policy详情
  useEffect(() => {
    if (
      mode === Mode.Edit ||
      mode === Mode.Copy ||
      mode === Mode.View ||
      mode === Mode.Renew
    ) {
      PolicyService.queryMasterPolicyDetail(
        id,
        L10nUtil.l10n.dateFormat.defaultTimeZone
      ).then(resp => {
        let res = { ...resp };
        if (mode === Mode.Renew) {
          delete res.masterPolicyNo;
          delete res.groupPolicyNo;
          res = {
            ...res,
            oldMasterPolicyNo: resp.masterPolicyNo,
          };
        }
        setAgreementDetal(res);
        if (res?.status) {
          setStatus(res?.status);
        }
      });
    }
  }, [mode, id]);

  // set form fields
  useEffect(() => {
    if (
      mode === Mode.Edit ||
      mode === Mode.Copy ||
      mode === Mode.View ||
      mode === Mode.Renew
    ) {
      setPeriodStartDate(
        new Date(
          agreementDetail?.effectiveTimes?.[0]?.startTime as string
        ).getTime()
      );
      const plans = agreementDetail.goodsPlanIds;
      setGoodsCategory(agreementDetail.goodsCategory as number);
      setSelectGoodsId(agreementDetail.goodsId as number);
      setSelectedPlans(plans || []);
      const dtos = agreementDetail?.relationDTOs?.map(dto => ({
        id: dto.id,
        isDeleted: dto.isDeleted,
        relationNo:
          // 刚renew取外层的关系
          mode !== Mode.Renew
            ? dto.relationNo
            : agreementDetail?.masterPolicyNo,
        type: dto.type,
      })) || [{}];
      let fieldsValue = {};
      if (mode === Mode.Copy) {
        fieldsValue = {
          masterPolicyId: undefined,
          effectiveTimes: [undefined],
          partnerType: undefined,
          partnerCode: undefined,
        };
      }
      form.setFieldsValue({
        ...agreementDetail,
        goodsCategory: agreementDetail.goodsCategory?.toString(),
        productIds: agreementDetail.productIds,
        ...(isShowOld
          ? {
              oldMasterPolicyNo:
                mode !== Mode.Renew
                  ? dtos?.find(dto => dto?.type === 'RENEWAL')?.relationNo
                  : agreementDetail?.oldMasterPolicyNo,
            }
          : {}),
        goodsId: agreementDetail.goodsId?.toString(),
        goodsPlanIds: plans,
        effectiveTimes:
          mode === Mode.Renew
            ? agreementDetail.effectiveTimes?.map(time => [
                L10nUtil.l10n.dateFormat
                  .l10nMoment(time.endTime)
                  .add(1, 'seconds'),
                null,
              ])
            : agreementDetail.effectiveTimes?.map(time => [
                L10nUtil.l10n.dateFormat.l10nMoment(time.startTime),
                L10nUtil.l10n.dateFormat.l10nMoment(time.endTime),
              ]),
        minPremium: agreementDetail?.masterPolicySettlements?.[0]?.minPremium,
        minPremiumDurationType:
          agreementDetail?.masterPolicySettlements?.[0]?.minPremiumDurationType,
        settleFrequency:
          agreementDetail?.masterPolicySettlements?.[0]?.settleFrequency,
        settleBatchDate:
          agreementDetail?.masterPolicySettlements?.[0]?.settleBatchDate &&
          moment().date(
            +agreementDetail?.masterPolicySettlements?.[0]?.settleBatchDate
          ),
        settleExtractDate:
          agreementDetail?.masterPolicySettlements?.[0]?.settleExtractDate &&
          moment().date(
            +agreementDetail?.masterPolicySettlements?.[0]?.settleExtractDate
          ),
        ...fieldsValue,
        paymentMethod: undefined,
        premiumCollection: undefined,
        paymentCollection: undefined,
        premiumCollectionTime: undefined,
        policyFiles: undefined,
        relationDTOs: dtos,
      });
      if (agreementDetail.taxSettings) {
        setTaxSettingData(
          agreementDetail.taxSettings.map(setting => ({
            ...setting,
            key: id.toString(),
          }))
        );
      }
      if (agreementDetail.extraSettings) {
        setExtraSettingData(
          agreementDetail.extraSettings.map(setting => ({
            ...setting,
            key: id.toString(),
          }))
        );
      }
      let policyFiles: UploadFile<any>[] = [];
      if (agreementDetail.documents) {
        const documentFiles = documentsTransfer(agreementDetail.documents);
        setDocuments(documentFiles);
      }
      if (agreementDetail.applicationFile) {
        policyFiles = documentsTransfer([agreementDetail.applicationFile]);
        setCreatePolicyData({
          paymentMethod: agreementDetail.paymentMethod,
          premiumCollection: agreementDetail.paymentMethod
            ? YesOrNo.YES
            : YesOrNo.NO,
          premiumCollectionTime: agreementDetail.premiumCollectionTime
            ? L10nUtil.l10n.dateFormat.l10nMoment(
                agreementDetail.premiumCollectionTime
              )
            : undefined,
          policyFiles,
        });
      }

      if (agreementDetail?.masterPolicyAdvancedPayments?.[0]) {
        const advancedPayments =
          agreementDetail.masterPolicyAdvancedPayments[0];

        setAdvancedPaymentId(advancedPayments.masterPolicyAdvancedPaymentId);
        setCreatePaymentData({
          advancedPaymentAmount: advancedPayments.advancedPaymentAmount,
          advancedPaymentCurrency: advancedPayments.advancedPaymentCurrency,
          advancedPaymentPayMethod: advancedPayments.advancedPaymentPayMethod,
          paymentCollection: advancedPayments.advancedPaymentPayMethod
            ? YesOrNo.YES
            : YesOrNo.NO,
          masterPolicyAdvancedPaymentId:
            advancedPayments.masterPolicyAdvancedPaymentId,
          remainingAmount: advancedPayments.remainingAmount,
          balanceAmountHandleMethod: advancedPayments.balanceAmountHandleMethod,
          calculateTax: advancedPayments.calculateTax,
          taxType: advancedPayments.taxType,
          formulaCategory: advancedPayments?.formulaCategory?.toString(),
          formulaCode: advancedPayments.formulaCode,
        });
      }

      if (agreementDetail?.relationDTOs) {
        setRelationDTO(dtos);
      }
    }
  }, [mode, id, agreementDetail, enums, goodsInfo, PartnerTypeList, isShowOld]);

  useEffect(() => {
    if (
      !successCheckStatus?.includes(agreementDetail?.complianceStatus) &&
      !!agreementDetail?.statusMessage
    ) {
      setComplianceError(agreementDetail?.statusMessage);
    }
  }, [agreementDetail]);

  useEffect(() => {
    const partnerCodeListObjData = cloneDeep(partnerCodeListObj);
    const fieldValue = form.getFieldValue('partnerType');
    if (
      salesChannels?.length &&
      fieldValue === PlaceholderEnum.SALE_CHANNEL &&
      salesChannels.every(
        item => item.partnerType === PlaceholderEnum.SALE_CHANNEL
      )
    ) {
      const channelList = partnerCodeListObjData[
        PlaceholderEnum.SALE_CHANNEL
      ]?.reduce((result, current) => {
        const channel = salesChannels.find(
          item => item.channelCode === current.code
        );
        if (channel) {
          result.push(current);
        }
        return result;
      }, [] as BizDict[]);
      partnerCodeListObjData[fieldValue] = channelList?.length
        ? channelList
        : partnerCodeListObjData[fieldValue];
    }
    setPartnerCodeList(
      partnerCodeListObjData[
        form.getFieldValue('partnerType') as PlaceholderEnum
      ]
    );
  }, [salesChannels, agreementDetail, JSON.stringify(partnerCodeListObj), partnerType]);
  // submit organization 回传
  const onSubmitOrganization = useCallback<
    (args: MasterPolicyHolderType) => void
  >(
    values => {
      const partnerCodeObj = cloneDeep(partnerCodeListObj) ?? {};
      partnerCodeObj[PlaceholderEnum.ORGANIZATION] =
        partnerCodeObj[PlaceholderEnum.ORGANIZATION] ?? [];
      const curCompanyObj = partnerCodeObj[PlaceholderEnum.ORGANIZATION].find(
        code => code.enumItemName === 'NewCompany'
      );
      if (curCompanyObj) {
        curCompanyObj.itemName = values.companyName || '';
      } else {
        partnerCodeObj[PlaceholderEnum.ORGANIZATION].push({
          enumItemName: 'NewCompany',
          itemName: values.companyName || '',
        });
      }
      setPartnerCodeListObj(partnerCodeObj);
      setCustomizedData(values);
      setShowAddNew(false);
      form.setFieldsValue({
        companyId: 'NewCompany',
      });
      setShowDrawer(false);
    },
    [partnerCodeListObj, organizationData, form]
  );

  const handleRemoveKey = useCallback<
    (value: Array<{ key?: string }>) => typeof value
  >(
    value =>
      value?.map(setting => {
        const settingClone = cloneDeep(setting);
        delete settingClone.key;
        return settingClone;
      }),
    []
  );

  const handleSaveOrSubmit = useCallback<
    (
      params?: MasterPolicyDetailDataType,
      saveMode?: SaveMode,
      // 用于complancecheck
      callback?: (masterPolicyNo: string) => void
    ) => void
  >(
    (params, saveMode, callback) => {
      // 兜这两种穿参方式
      const submitParamsCopy = cloneDeep(submitParams);
      const paramsCopy = cloneDeep(params);

      if (
        submitParamsCopy &&
        submitParamsCopy?.relationDTOs &&
        JSON.stringify(submitParamsCopy.relationDTOs) === '[{}]'
      ) {
        submitParamsCopy.relationDTOs = [];
      }

      if (
        paramsCopy &&
        paramsCopy?.relationDTOs &&
        JSON.stringify(paramsCopy.relationDTOs) === '[{}]'
      ) {
        paramsCopy.relationDTOs = [];
      }

      const saveParam = {
        ...(paramsCopy ?? (submitParamsCopy as MasterPolicyDetailDataType)),
        // 存在save过产生的savedMaterPolicyNo时
        ...(savedMaterPolicyNo
          ? {
              masterPolicyId: savedMaterPolicyNo,
              masterPolicyNo: savedMaterPolicyNo,
            }
          : {}),
        // status 不传给后端
        status: undefined,
      };
      if (saveMode === SaveMode.SAVE) {
        setSaveButtonLoading(true);

        if (saveParam?.goodsId) {
          saveParam.goodsCode = goodsMap?.[saveParam.goodsId];
        }
        PolicyService.saveMasterPolicy(saveParam)
          .then(res => {
            setSavedMaterPolicyNo(res);
            setComplianceDisabled(false);
            if (callback) {
              callback?.(res as string);
              return;
            }
            messagePopup(t('Save Successfully'), 'success');
            navigate(`/uw/master-policy/edit/${res}`);
          })
          .catch(err => {
            messagePopup(err?.message, 'error');
          })
          .finally(() => {
            setSaveButtonLoading(false);
          });
        return;
      }
      setConfirmLoading(true);

      PolicyService.submitMasterPolicy(saveParam)
        .then(res => {
          Modal.info({
            title: t('Submit Successfully'),
            content: (
              <div>
                <p>
                  {t('Master Agreement No.: {{masterPolicyNo}}', {
                    masterPolicyNo: res,
                  })}
                </p>
              </div>
            ),
            onOk() {
              navigate('/uw/master-policy');
            },
          });
        })
        .catch(err => {
          messagePopup(err?.message, 'error');
        })
        .finally(() => {
          setShowModal(false);
          setConfirmLoading(false);
        });
    },
    [submitParams, savedMaterPolicyNo, form, goodsMap]
  );

  const relationHandler = useCallback(
    (dtos: Relation[]) => {
      if (mode === Mode.Edit) {
        let origin = relationDTO ? cloneDeep(relationDTO) : [];
        // 结果中id仍然存在的为更新/不变，把origin数组中的id与dtos里面的id重复的去掉
        const ids = dtos.map((dto: Relation) => dto.id);
        origin = origin.filter((o: Relation) => !ids.includes(o.id));
        // 剩下的为删除的
        origin = origin.map((item: Relation) => ({
          id: item.id,
          relationNo: item.relationNo,
          type: item.type,
          isDeleted: 'Y',
        }));
        // 拼上结果
        return [...dtos, ...origin].map(item => {
          if (!item?.relationNo && !item?.type && item?.id) {
            return { ...item, isDeleted: 'Y' };
          }
          return item;
        });
      }
      if (mode === Mode.Renew) {
        // 清id
        return dtos
          .map((item: Relation) => ({
            relationNo: item.relationNo,
            type: item.type,
          }))
          .filter(item => item?.type && item?.relationNo);
      }
      return dtos.filter(item => item?.type && item?.relationNo);
    },
    [mode, relationDTO]
  );

  const handleFormatTzTime = useCallback<(time?: string) => undefined | string>(
    time => (time ? L10nUtil.l10n.dateFormat.formatTz(time) : undefined),
    []
  );

  const addMasterPolicySettlements = useCallback(
    (
      values: MasterPolicyDetailDataType & {
        effectiveTimes: [moment.Moment, moment.Moment][];
      }
    ) => {
      const minPremiumDurationType: string = values?.minPremiumDurationType;
      const minPremium: string = values?.minPremium;
      const settleFrequency: string = values?.settleFrequency;
      const settleExtractDate: string = values?.settleExtractDate?.format('D');
      const settleBatchDate: string = values?.settleBatchDate?.format('D');
      const masterPolicySettlements: MasterPolicySettlements = {
        minPremiumDurationType,
        minPremium,
        settleFrequency,
        settleExtractDate,
        settleBatchDate,
        id: values?.masterPolicySettlements?.[0]?.id,
      };

      if (
        Object.keys(masterPolicySettlements).filter(
          key => !!masterPolicySettlements[key]
        ).length === 0
      ) {
        return null;
      }
      return { masterPolicySettlements: [masterPolicySettlements] };
    },
    []
  );

  const handleAction = async (
    values: MasterPolicyDetailDataType & {
      effectiveTimes: [moment.Moment, moment.Moment][];
    },
    saveMode?: SaveMode,
    // 用于complancecheck
    callback?: (masterPolicyNo: string) => void
  ) => {
    try {
      const valuesTemp: MasterPolicyDetailDataType & {
        oldMasterPolicyNo?: number;
      } = { ...values };
      // 处理effective times
      valuesTemp.effectiveTimes = (
        values.effectiveTimes as [moment.Moment, moment.Moment][]
      )?.map((timerange: [moment.Moment, moment.Moment]) => ({
        startTime: L10nUtil.l10n.dateFormat.formatTz(
          timerange[0],
          L10nUtil.l10n.dateFormat.defaultTimeZone
        ) as string,
        endTime: L10nUtil.l10n.dateFormat.formatTz(
          timerange[1],
          L10nUtil.l10n.dateFormat.defaultTimeZone
        ) as string,
      }));
      const taxSettingSubmitData =
        await taxSettingRef.current?.getTaxSettingData();
      const extraSettingSubmitData =
        await extraSettingRef.current?.getExtraSettingData();
      const documentData = await documentRef.current?.getDocumentData();
      delete valuesTemp.salesTime;
      delete valuesTemp.premiumCollection;
      // delete valuesTemp.channelType;
      const policyFileArray = (
        (valuesTemp.policyFiles as (DocumentType | UploadFile<any>)[]) || []
      ).map<DocumentType>(file => ({
        documentName:
          (file as DocumentType).documentName || (file as UploadFile<any>).name,
        fileUniqueCode:
          (file as DocumentType).fileUniqueCode ||
          (file as UploadFile<any>).uid,
        fileUrl:
          (file as DocumentType).fileUrl || (file as UploadFile<any>).url || '',
        documentType: DocumentTypeEnum.APPLICATION_FILE,
        taskNo:
          (file as DocumentType).taskNo || (file as UploadFile<any>).taskNo,
      }));
      const taskNo =
        policyFileArray && policyFileArray[0]?.taskNo
          ? policyFileArray[0].taskNo
          : undefined;
      delete valuesTemp.policyFiles;

      let channelCode: string | undefined = values?.channelCode;

      if (
        valuesTemp.partnerType === PlaceholderEnum.SALE_CHANNEL ||
        valuesTemp.partnerType === PlaceholderEnum.AGENCY
      ) {
        channelCode = pickChannelCode(
          partnerCodeListObj[valuesTemp.partnerType],
          valuesTemp.partnerCode
        );
      }

      //  如果有advancedPaymentPayMethod,说明AdvancedPayments被选中了
      const masterPolicyAdvancedPayments = valuesTemp.advancedPaymentPayMethod
        ? [
            {
              advancedPaymentPayMethod: valuesTemp.advancedPaymentPayMethod,
              balanceAmountHandleMethod: valuesTemp.balanceAmountHandleMethod,
              calculateTax: valuesTemp.calculateTax,
              taxType: valuesTemp.taxType,
              formulaCategory: Number(valuesTemp.formulaCategory),
              formulaCode: valuesTemp.formulaCode,
              channelCode,
              masterPolicyAdvancedPaymentId: advancedPaymentId,
              ...valuesTemp?.masterPolicyAdvancedPayments,
            },
          ]
        : [];

      delete valuesTemp?.advancedPaymentPayMethod;
      delete valuesTemp?.balanceAmountHandleMethod;
      delete valuesTemp?.calculateTax;
      delete valuesTemp?.taxType;

      const submiteffectiveParams = {
        goodsId: valuesTemp.goodsId,
        goodsPlanIds: valuesTemp.goodsPlanIds,
        partnerCode: valuesTemp.partnerCode,
        partnerType: valuesTemp.partnerType,
        companyId:
          valuesTemp.companyId !== 'NewCompany'
            ? valuesTemp.companyId
            : undefined,
        effectiveTimes: valuesTemp.effectiveTimes,
        masterPolicyNo: valuesTemp.masterPolicyNo,
        masterPolicyId: masterPolicyId.current,
        taskNo,
        channelCode,
      };

      // master policy 规则配置在product上时不需校验goods是否重复
      const goodsCode = valuesTemp.masterPolicyId
        ? goodsMap?.[+valuesTemp.masterPolicyId]
        : undefined;
      const params = {
        goodsCode,
        ...valuesTemp,
        ...(valuesTemp.oldMasterPolicyNo
          ? {
              oldMasterPolicyNo: undefined,
            }
          : {}),
        companyId:
          valuesTemp.companyId !== 'NewCompany'
            ? valuesTemp.companyId
            : undefined,
        taskNo,
        // // 当时编辑模式时，premiumCollectionTime不传值
        // premiumCollectionTime: editOrView
        //   ? undefined
        //   : handleFormatTzTime(valuesTemp.premiumCollectionTime),
        masterPolicyHolder: !customizedData
          ? undefined
          : {
              ...customizedData,
              registrationDate: handleFormatTzTime(
                customizedData?.registrationDate
              ),
            },
        // 当时system date > effective date时，taxSetting
        taxSettings:
          (mode === Mode.Edit && addTax) ||
          mode === Mode.Add ||
          mode === Mode.Copy
            ? (handleRemoveKey(
                taxSettingSubmitData as (TaxSettingTableType & {
                  key?: string;
                })[]
              ) as (TaxSettingTableType & { key: string })[])
            : undefined,
        extraSettings: handleRemoveKey(
          extraSettingSubmitData as (ExtraSettingTableType & {
            key?: string;
          })[]
        ) as (ExtraSettingTableType & { key: string })[],
        documents: [...documentData!, ...policyFileArray],
        masterPolicyAdvancedPayments,
      };
      const mps = addMasterPolicySettlements(values);
      if (mps) {
        params.masterPolicySettlements = mps.masterPolicySettlements;
      }

      if (mode === Mode.Edit) {
        params.masterPolicyId = masterPolicyId.current;
      }

      if (isShowOld) {
        params.renewalSource = RenewalSourceEnums.MANUAL_RENEWAL;
      } else {
        params.renewalSource = RenewalSourceEnums.NONE_RENEWAL;
      }
      params.needCreateRelationalPolicy = YesOrNo.YES;

      if (mode === Mode.Renew) {
        params.relationDTOs = [];
      } else {
        params.relationDTOs = relationHandler(params.relationDTOs || []);
      }

      if (mode === Mode.Renew) {
        params.relationDTOs.push({
          type: 'RENEWAL',
          relationNo: valuesTemp.oldMasterPolicyNo,
        });
      }
      params.channelCode = channelCode;
      params.packageCodeList = packageCodeList;

      if (saveMode === SaveMode.SAVE) {
        setSubmitParams(cloneDeep(params));
        handleSaveOrSubmit(params, saveMode, callback);
        return;
      }

      // master policy 规则配置在product上时不需校验goods是否重复
      if (!valuesTemp?.productIds) {
        PolicyService.submitCheckTask(submiteffectiveParams).then(res => {
          const { masterPolicyExistsInSelectedPeriod } = res;
          const newParams = cloneDeep(params);
          newParams.duplicateMasterPolicyNos = res?.duplicateMasterPolicyNos;
          setSubmitParams(newParams);
          if (masterPolicyExistsInSelectedPeriod) {
            setShowModal(true);
          } else {
            setSuccessModal(true);
          }
        });
      } else {
        setSubmitParams(params);
        setSuccessModal(true);
      }
    } catch (err) {
      messagePopup(t('Please fill in the Setting Table'), 'error');
    }
  };

  const submitTopsInfo = useMemo(() => {
    let code: string | undefined = '';
    if (submitParams?.partnerCode || submitParams?.companyId) {
      code =
        partnerCodeMap &&
        submitParams?.partnerType &&
        (submitParams?.partnerCode || submitParams?.companyId)
          ? partnerCodeMap[submitParams?.partnerType][
              submitParams?.partnerCode || submitParams?.companyId
            ]
          : undefined;
    } else {
      code = submitParams?.masterPolicyHolder?.companyName;
    }

    const advancedPayments = submitParams?.masterPolicyAdvancedPayments?.[0];

    const advancedPaymentAmount = advancedPayments?.advancedPaymentAmount;
    const advancedPaymentCurrency = advancedPayments?.advancedPaymentCurrency;
    const duplicateMasterPolicyNos = submitParams?.duplicateMasterPolicyNos;

    return (
      <>
        {submitParams?.masterPolicyNo && (
          <div className={styles['submit-text-wrapper']}>
            <div>{t('Master Policy No.')}</div>: {submitParams?.masterPolicyNo}
          </div>
        )}
        <div className={styles['submit-text-wrapper']}>
          <div>{t('Master Policyholder')}</div>:{' '}
          {`${
            partnerTypeMap && submitParams?.partnerType
              ? partnerTypeMap[submitParams?.partnerType]
              : ''
          } - ${code}`}
        </div>
        <div className={styles['submit-text-wrapper']}>
          <div>{t('Effective Time')}</div>:{' '}
          <div>
            {submitParams?.effectiveTimes?.map(time => (
              <div>
                {L10nUtil.l10n.dateFormat.getDateTimeString(
                  time.startTime,
                  L10nUtil.l10n.dateFormat.defaultTimeZone
                )}{' '}
                ~{' '}
                {L10nUtil.l10n.dateFormat.getDateTimeString(
                  time.endTime,
                  L10nUtil.l10n.dateFormat.defaultTimeZone
                )}
              </div>
            ))}
          </div>
        </div>
        {advancedPaymentAmount && advancedPaymentCurrency && (
          <div className={styles['submit-text-wrapper']}>
            <div>
              {amountFormatInstance.getAmountCurrencyString(
                advancedPaymentAmount,
                advancedPaymentCurrency
              )}
            </div>
          </div>
        )}

        {duplicateMasterPolicyNos && (
          <div className={styles['submit-text-wrapper']}>
            <div>{t('Duplicate Master Agreement No.')}</div>:{' '}
            <div style={{ wordBreak: 'break-all' }}>
              {duplicateMasterPolicyNos?.reduce(
                (cur, next) => `${cur}${cur.length > 0 ? ';' : ''}${next}`,
                ''
              )}
            </div>
          </div>
        )}
      </>
    );
  }, [submitParams, partnerTypeMap, partnerCodeMap]);

  const onDownload = useCallback((fileUniqueCode: string) => {
    PolicyService.downloadFile(fileUniqueCode)
      .then(response => {
        downloadFile(response).then(() => {
          message.success(t('Download successfully'));
        });
      })
      .catch((error: Error) => {
        message.error(error.message || t('Download failed'));
      });
  }, []);

  const onSaveOrSubmit = useCallback(
    (mode: SaveMode) => {
      form.validateFields().then(() => {
        const saveData = { ...form.getFieldsValue(true) };
        delete saveData?.masterPolicyId;
        handleAction(saveData, mode);
      });
    },
    [form, handleAction]
  );

  const onComplianceCheck = useCallback(() => {
    handleAction(form.getFieldsValue(true), SaveMode.SAVE, masterPolicyNo => {
      PolicyService.complianceCheckMasterPolicy(masterPolicyNo)
        .then(res => {
          if (
            !successCheckStatus?.includes(
              res?.holderComplianceCheck
                ?.complianceStatus as ComplianceCheckStatus
            ) &&
            !!res?.holderComplianceCheck?.statusMessage
          ) {
            setComplianceError(res?.holderComplianceCheck?.statusMessage);
            return;
          }
          // 设置状态
          form.setFieldValue('status', res?.policyStatus);
          setStatus(res?.policyStatus);
          setComplianceError('');
        })
        .catch(err => {
          messagePopup((err as Error)?.message?.toString(), 'error');
        });
    });
  }, [form, handleAction]);

  const submitDisabled = useMemo(() => {
    const isWaitingForIssuance =
      status === MasterAgreementStatus.WAITING_FOR_ISSUANCE;

    const isEditOrAddMode = mode === Mode.Edit || mode === Mode.Add;
    const isRenewMode = mode === Mode.Renew;

    return !(
      (isWaitingForIssuance && isEditOrAddMode) ||
      isRenewMode ||
      (agreementDetail?.renewalTimes > 0 && mode === Mode.Edit)
    );
  }, [mode, status]);

  return (
    <Layout>
      <Form layout="vertical" form={form} name="selection-form">
        <MasterPolicyHeader
          mode={mode as Mode}
          busiNo={id}
          onComplianceCheck={onComplianceCheck}
          onSaveOrSubmit={onSaveOrSubmit}
          complianceDisabled={complianceDisabled}
          status={status}
          submitDisabled={submitDisabled}
          saveButtonLoading={saveButtonLoading}
        />
        <Layout>
          <Content>
            <section style={{ display: 'flex' }}>
              <div style={{ width: 'calc(100% - 420px)' }}>
                <Layout className={styles['section-box']}>
                  <div className={styles['section-title']}>
                    {t('Marketing Goods Selection')}
                  </div>
                  <Row
                    style={{
                      marginTop: '30px',
                      maxWidth: '100%',
                    }}
                  >
                    {queryFields.map(field => (
                      <Form.Item
                        key={field.key}
                        style={{ marginRight: '100px' }}
                      >
                        <Form.Item
                          label={field.label}
                          name={field.key}
                          rules={field.rules}
                          {...layout}
                        >
                          {getFields({ ...field })}
                        </Form.Item>
                      </Form.Item>
                    ))}
                  </Row>
                </Layout>
                <Layout
                  className={styles['section-box']}
                  style={{ paddingBottom: '60px' }}
                >
                  <MarketGoodsSettings
                    form={form}
                    partnerCodeListObj={partnerCodeListObj}
                    partnerCodeList={partnerCodeList}
                    setOrganizationMode={setOrganizationMode}
                    setPartnerCodeList={setPartnerCodeList}
                    setOrganizationData={setOrganizationData}
                    goodsInfo={goodsInfo}
                    setSelectGoodsId={setSelectGoodsId}
                    setGoodsCategory={setGoodsCategory}
                    customizedData={customizedData}
                    selectedGoodsId={selectedGoodsId}
                    setShowEditBtn={setShowEditBtn}
                    showAddNew={showAddNew}
                    setShowAddNew={setShowAddNew}
                    setShowDrawer={setShowDrawer}
                    setPeriodStartDate={setPeriodStartDate}
                    hideMasterPolicyNo={hideMasterPolicyNo}
                    complianceError={complianceError}
                    isShowOld={isShowOld}
                  />
                  <AgreementSettlementRule mode={mode as Mode} />
                  <TaxSettingTable
                    ref={taxSettingRef}
                    mode={mode as Mode}
                    dataSource={taxSettingData}
                    addTax={addTax}
                  />
                  <ExtraSettingTable
                    ref={extraSettingRef}
                    mode={mode as Mode}
                    dataSource={extraSettingData}
                  />
                  <CreatePaymentSection
                    dataSource={createPaymentData}
                    form={form}
                    mode={mode as Mode}
                  />
                  <CreatePolicySection
                    partnerCodeListObj={partnerCodeListObj}
                    oldMasterPolicyNo={form.getFieldValue('oldMasterPolicyNo')}
                    dataSource={createPolicyData}
                    form={form}
                    busiNo={id || new Date().getTime().toString()}
                    mode={mode as Mode}
                    goodsId={selectedGoodsId}
                    goodsPlanIds={selectedPlans}
                    defaultList={[]}
                    onDownload={onDownload}
                    uploadExtraParams={{
                      type: UploadSubCategoryTypeEnum.POLICY_UPLOAD_FILE,
                      id: '',
                    }}
                  />
                </Layout>
              </div>
              <UploadDocument
                ref={documentRef}
                defaultList={documents}
                busiNo={id || new Date().getTime().toString()}
                mode={mode as Mode}
                uploadExtraParams={{
                  type: UploadSubCategoryTypeEnum.POLICY_UPLOAD_FILE,
                  id: '',
                }}
              />
            </section>
          </Content>
        </Layout>
        <OrganizationDrawer
          showDrawer={showDrawer}
          setShowDrawer={setShowDrawer}
          organizationData={organizationData}
          mode={organizationMode}
          setMode={setOrganizationMode}
          showEditBtn={showEditBtn}
          onSubmit={onSubmitOrganization}
        />
      </Form>
      <Modal
        title={t('Issue Tips')}
        open={showModal}
        okText={t('Confirm')}
        confirmLoading={confirmLoading}
        onOk={() => {
          setSuccessModal(true);
        }}
        onCancel={() => {
          setShowModal(false);
        }}
      >
        <p>
          {t('Duplicate master policy has existed, do you want to continue?')}
        </p>
        <div style={{ fontWeight: 700 }}>{submitTopsInfo}</div>
      </Modal>
      <SubmitModal
        setShowModal={setSuccessModal}
        confirmLoading={confirmLoading}
        showModal={successModal}
        submitTopsInfo={submitTopsInfo}
        handleSuccess={handleSaveOrSubmit}
        submitParams={submitParams}
      />
    </Layout>
  );
};

export default EditMasterPolicy;
