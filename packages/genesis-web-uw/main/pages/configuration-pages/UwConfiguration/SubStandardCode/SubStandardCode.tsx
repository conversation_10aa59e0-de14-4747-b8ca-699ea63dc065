import React, { FC, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EditableTable } from '@zhongan/nagrand-ui';

import { StandardCodeService, SubstandardCodeList } from 'genesis-web-service';

import { ErrorType } from '@uw/interface/common.interface';
import { messagePopup } from '@uw/util/messagePopup';

import SubStandardHeadAction from './SubStandardHeadAction';
import {
  handleDeleteStandardCode,
  useGetStandardCodeList,
} from './hook/request';
import { standardColumns } from './tablePageConfig';

export const SubStandardCode: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const {
    standardCodeList,
    setStandardCodeList,
    queryStandardCodeList,
    onChange,
    current,
    total,
  } = useGetStandardCodeList();

  const [, setIsEditing] = useState(false);

  const handleSave = useCallback<(row?: SubstandardCodeList) => void>(
    async row => {
      if (!row) return;
      try {
        const saveResult = await StandardCodeService.updateStandardCode(row);
        if (!saveResult) {
          queryStandardCodeList(1, 10);
          return true;
        }
      } catch (error) {
        messagePopup((error as ErrorType).message, 'error');
        return false;
      }
    },
    [queryStandardCodeList]
  );

  const handleDelete = useCallback<(idx: number) => void>(
    async idx => {
      const delFlag = await handleDeleteStandardCode(
        idx,
        standardCodeList as (SubstandardCodeList & {
          key: string;
        })[]
      );
      if (!delFlag) {
        queryStandardCodeList(1, 10);
      }
    },
    [standardCodeList]
  );

  return (
    <>
      <div>
        <section className="py-md px-lg border-0 border-b border-solid border-[#DAE8F9] leading-4 text=[#333] font-bold">
          {t('Sub-standard Code')}
        </section>
        <div className="py-big px-lg">
          <EditableTable
            title={
              <span className="font-bold">{t('Sub-standard Code List')}</span>
            }
            columns={standardColumns}
            dataSource={
              standardCodeList as (SubstandardCodeList & {
                key: string;
              })[]
            }
            scroll={{ x: 'max-content' }}
            setDataSource={setStandardCodeList}
            setIsEditing={setIsEditing}
            handleConfirm={(row?: SubstandardCodeList) => handleSave(row)}
            // handleCancel={(row?: SubstandardCodeList) => handleDelete(row)}
            deleteBtnProps={{
              handleDelete: (idx: number) => handleDelete(idx),
            }}
            addBtnLeftSection={
              <SubStandardHeadAction
                queryStandardCodeList={queryStandardCodeList}
              />
            }
            needReserveEditing={true}
            pagination={{
              current,
              total,
              onChange,
            }}
          />
        </div>
      </div>
    </>
  );
};
