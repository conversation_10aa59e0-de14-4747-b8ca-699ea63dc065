import React, { FC, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';

import type { UploadRequestOption } from 'rc-upload/lib/interface';

import { StandardCodeService } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { ErrorType } from '@uw/interface/common.interface';
import { UploadStatus } from '@uw/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';

interface Props {
  queryStandardCodeList: (pageNum: number, pageSize: number) => void;
}

const SubStandardHeadAction: FC<Props> = ({ queryStandardCodeList }) => {
  const [t] = useTranslation(['uw', 'common']);
  const [fileList, setFileList] = useState<Array<UploadFile>>([]);
  const handleUploadFiles = useCallback<
    (formData: FormData, filename: string) => void
  >(
    (formData, filename) => {
      StandardCodeService.uploadStandardCode(formData)
        .then(res => {
          queryStandardCodeList(1, 10);
          messagePopup(t('Upload Successfully'), 'success');
          setFileList([
            {
              uid: res.fileUniqueCode,
              name: filename,
              status: UploadStatus.Done,
              url: res.fileUrl,
            },
          ]);
        })
        .catch((error: unknown) => {
          messagePopup((error as ErrorType).message, 'error');
        });
    },
    [queryStandardCodeList]
  );
  const uploadProps = useMemo<UploadProps>(
    () => ({
      name: 'file',
      accept: '.xls, .xlsx',
      multiple: false,
      fileList,
      customRequest: (cb: UploadRequestOption) => {
        const formData = new FormData();
        formData.append('file', cb.file);
        handleUploadFiles(formData, (cb.file as File).name);
      },
    }),
    [fileList]
  );
  const handleDownload = useCallback<
    (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void
  >(() => {
    StandardCodeService.downloadStandardCode()
      .then(response => {
        downloadFile(response).then(() => {
          messagePopup(t('Download successfully'), 'success');
        });
      })
      .catch((error: unknown) => {
        messagePopup(
          (error as ErrorType).message || t('Download failed'),
          'error'
        );
      });
  }, []);
  return (
    <div className="leading-8">
      <div>
        <Upload {...uploadProps} fileList={[]}>
          <Button icon={<UploadOutlined />} className="mr-xs">
            Upload
          </Button>
        </Upload>
        <Button
          icon={<DownloadOutlined />}
          onClick={handleDownload}
          className="ml-xs"
        >
          {t('Download')}
        </Button>
      </div>
    </div>
  );
};
export default SubStandardHeadAction;
