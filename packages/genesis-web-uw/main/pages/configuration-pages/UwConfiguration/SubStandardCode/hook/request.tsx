import { ErrorType } from '@uw/interface/common.interface';
import { messagePopup } from '@uw/util/messagePopup';
import { StandardCodeService, SubstandardCodeList } from 'genesis-web-service';
import { useCallback, useEffect, useState } from 'react';

export const useGetStandardCodeList = (): {
  standardCodeList: SubstandardCodeList[];
  queryStandardCodeList: (pageNum: number, pageSize: number) => void;
  setStandardCodeList: React.Dispatch<
    React.SetStateAction<SubstandardCodeList[]>
  >;
  onChange: (pageNum: number, pageSize: number) => void;
  current: number;
  total: number;
} => {
  const [standardCodeList, setStandardCodeList] = useState<
    SubstandardCodeList[]
  >([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const queryStandardCodeList = useCallback(
    (pageNum: number, pageSize: number) => {
      StandardCodeService.getStandardCodePage({ pageNum, pageSize }).then(
        res => {
          setStandardCodeList(
            res.results?.map((results, idx) => ({
              ...results,
              key: `${results.id}${idx}`,
            }))
          );
          setTotal(res.total);
        }
      );
    },
    []
  );

  useEffect(() => {
    queryStandardCodeList(1, 10);
  }, [queryStandardCodeList]);
  const onChange = useCallback<(page: number, pageSize: number) => void>(
    (page, pageSize) => {
      setCurrent(page);
      queryStandardCodeList(page, pageSize);
    },
    [queryStandardCodeList]
  );
  return {
    standardCodeList,
    queryStandardCodeList,
    setStandardCodeList,
    onChange,
    current,
    total,
  };
};

export const handleDeleteStandardCode = async (
  index: number,
  exclusionItem: (SubstandardCodeList & {
    key: string;
  })[]
) => {
  try {
    const deleteItem = exclusionItem[index];
    const deleteRuleResults = await StandardCodeService.deleteStandardCode(
      deleteItem.id
    );
    return deleteRuleResults;
  } catch (error) {
    messagePopup((error as ErrorType).message, 'error');
  }
};
