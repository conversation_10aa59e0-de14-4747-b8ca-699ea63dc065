import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';

import { i18nFn } from '@uw/util/i18nFn';

export const standardColumns = [
  {
    title: i18nFn('Category'),
    dataIndex: 'category',
    key: 'category',
    editable: true,
    fieldProps: {
      placeholder: i18nFn('Please input'),
      type: FieldType.Input,
    },
  },
  {
    title: i18nFn('Sub-standard Code'),
    dataIndex: 'code',
    key: 'code',
    editable: true,
    fieldProps: {
      placeholder: i18nFn('Please input'),
      type: FieldType.Input,
    },
  },
  {
    title: i18nFn('Description'),
    dataIndex: 'description',
    key: 'description',
    editable: true,
    fieldProps: {
      placeholder: i18nFn('Please input'),
      type: FieldType.Input,
    },
  },
];
