import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Form, Row, Select } from 'antd';
import FormItem from 'antd/es/form/FormItem';

import { RuleEngine, UnderWritingCriteriaService } from 'genesis-web-service';

import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

import {
  useQueryCriteriaInfo,
  useQueryRuleUnderCategory,
} from '../hooks/request';

const ruleCategorys = [
  {
    label: i18nFn('Underwriting Criteria'),
    value: RuleEngine.RuleCategory.UW_Criteria,
  },
];

export const Criteria = () => {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const ruleCodes = useQueryRuleUnderCategory([
    RuleEngine.RuleCategory.UW_Criteria,
  ]);
  const criteriaInfo = useQueryCriteriaInfo();

  const submitHandler = useCallback(async () => {
    const values = await form.validateFields();
    UnderWritingCriteriaService.saveCriteriaInfo(values)
      .then(() => {
        messagePopup(t('Submitted successfully'), 'success');
      })
      .catch(error => messagePopup(error?.message, 'error'));
  }, [form]);

  useEffect(() => {
    form.setFieldsValue({
      ruleCategory: RuleEngine.RuleCategory.UW_Criteria,
      ruleCode: criteriaInfo?.ruleCode,
    });
  }, [criteriaInfo]);

  return (
    <div className="flex flex-col h-full">
      <section className="py-md px-lg border-0 border-b border-solid border-[#DAE8F9] leading-md font-bold">
        {t('Underwriting Criteria')}
      </section>
      <section className="font-blod leading-md p-lg">
        {t('UW Critieria Standard')}
      </section>
      <Form form={form} layout="vertical" className="flex-1">
        <Row className="px-lg">
          <Col span={6}>
            <FormItem
              label={t('Rule/Rule Set Category')}
              name="ruleCategory"
              required
            >
              <Select options={ruleCategorys} disabled />
            </FormItem>
          </Col>
          <Col offset={6} span={6}>
            <FormItem label={t('Rule/Rule Set Code')} name="ruleCode">
              <Select
                allowClear
                options={ruleCodes}
                placeholder={t('Please select')}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
      <section className="px-lg py-md text-right">
        <Button onClick={submitHandler} type="primary" size="large">
          {t('Submit')}
        </Button>
      </section>
    </div>
  );
};
