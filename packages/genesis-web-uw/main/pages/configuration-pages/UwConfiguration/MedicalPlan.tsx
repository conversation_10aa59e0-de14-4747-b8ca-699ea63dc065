import React, { FC, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { Pagination } from '@zhongan/nagrand-ui';

import {
  ExamItemType,
  ExaminationItemType,
  MedicalPlanType,
  SaveMedicalPlanType,
  UnderwritingService,
} from 'genesis-web-service';

import {
  FormatMedicalPlanType,
  useExaminationItems,
  useMedicalPlans,
} from '@uw/hook/request';

import FieldEditTable from '../../../components/EditDrawer/FieldEditTable';
import { ExaminationItemTable } from './ExaminationItemTable';
import MedicalPlanDocument from './MedicalPlanDocument';
import styles from './config.module.scss';
import { medicalConfigs, medicalPlanColumns } from './medical.config';

const MedicalPlan: FC = () => {
  const [t] = useTranslation(['uw', 'common']);

  const [isEditing, setIsEditing] = useState(false);
  const { medicalPlans, queryMedicalPlans, onChange, current, total } =
    useMedicalPlans();
  const { examinationItems } = useExaminationItems();
  const [selectedExamItems, setSelectedExamItems] = useState<ExamItemType[]>(
    []
  );

  const handleCancel = useCallback(() => {
    setSelectedExamItems([]);
    setIsEditing(false);
  }, []);

  const handleSaveData = useCallback<
    (
      values: MedicalPlanType & {
        key?: React.Key;
      },
      uid?: string
    ) => void
  >(
    (values, uid) => {
      const valTemp = { ...values };
      delete valTemp.key;
      const params: SaveMedicalPlanType = {
        ...valTemp,
        examinationItemRelationList: selectedExamItems.map(item => ({
          itemCode: item.itemCode,
          itemName: item.itemName,
        })),
      };
      const request = uid
        ? UnderwritingService.UpdateMedicalPlan
        : UnderwritingService.saveMedicalPlan;
      request(params, uid as string)
        .then(() => {
          queryMedicalPlans(1, 10);
          message.success({
            content: t('Save Successfully'),
            prefixCls: 'antd-uw-message',
          });
        })
        .catch(e => {
          message.error({
            content: e.message,
            prefixCls: 'antd-uw-message',
          });
        })
        .finally(() => {
          handleCancel();
        });
    },
    [selectedExamItems]
  );

  const handleDel = useCallback<(val: number) => void>(
    async val => {
      const { id } = medicalPlans[val];
      await UnderwritingService.deleteMedicalPlan(id);
      queryMedicalPlans(1, 10);
    },
    [medicalPlans]
  );

  const handleGetSelections = useCallback<
    (selectedRows: ExaminationItemType[]) => void
  >(selectedRows => {
    setSelectedExamItems(selectedRows);
  }, []);

  const handleEdit = useCallback<(row: FormatMedicalPlanType) => void>(row => {
    setSelectedExamItems(row.examinationItemRelationList);
  }, []);

  return (
    <>
      <div className={styles['rule-wrapper']}>
        <section className="py-md px-lg border-0 border-b border-solid border-[#DAE8F9] leading-4 text=[#333] font-bold">
          {t('Medical Plan')}
        </section>
        <div className="py-big px-lg">
          <section className={styles['upload-wrapper']}>
            <MedicalPlanDocument />
          </section>
          <section className={styles['rule-title']}>
            {t('Medical Plan Value')}
          </section>
          <FieldEditTable<FormatMedicalPlanType>
            uKey={'id'}
            tableColumns={medicalPlanColumns()}
            data={medicalPlans}
            setData={() => {}}
            setIsEditing={setIsEditing}
            tableFields={medicalConfigs()}
            handleSaveData={handleSaveData}
            handleDel={handleDel}
            handleEdit={handleEdit}
            handleCancel={handleCancel}
            showPage={false}
            selectTable={
              <ExaminationItemTable
                reload={!isEditing}
                selectedInitialRows={selectedExamItems}
                examinationItems={examinationItems}
                handleGetSelections={handleGetSelections}
              />
            }
          />
          <Pagination
            className="mt-[18px]"
            current={current}
            total={total}
            onChange={onChange}
          />
        </div>
      </div>
      {/* hide */}
      {/* <FootBar onSubmit={onSubmit} /> */}
    </>
  );
};
export default MedicalPlan;
