import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';

import I18nInstance from '@uw/i18n';
import styles from '@uw/pages/configuration-pages/UwConfiguration/config.module.scss';

const ns = {
  ns: ['uw', 'common'],
};
const getExclusionColumnText = (text: string, exclusionColumnText: BizDict[]) =>
  exclusionColumnText?.find(item => item.dictValue === text)?.dictValueName;

export const exclusionColumns = (
  exclusionStatus: BizDict[],
  exclusionReasons: BizDict[]
) => [
  {
    title: I18nInstance.t('Exclusion Reason', ns),
    dataIndex: 'exclusionReason',
    key: 'exclusionReason',
    editable: true,
    width: 200,
    fieldProps: {
      placeholder: I18nInstance.t('Please select'),
      type: FieldType.Select,
      extraProps: {
        options: exclusionReasons?.map(reasonItem => ({
          value: reasonItem.dictValue,
          label: reasonItem.dictValueName || reasonItem.itemName,
        })),
      },
    },
    render: (text: string) => getExclusionColumnText(text, exclusionReasons),
  },
  {
    title: I18nInstance.t('Exclusion Category', ns),
    dataIndex: 'exclusionCategory',
    key: 'exclusionCategory',
    editable: true,
    width: 200,
    fieldProps: {
      placeholder: I18nInstance.t('Please input'),
      type: FieldType.Input,
    },
  },
  {
    title: I18nInstance.t('Exclusion Code', ns),
    dataIndex: 'exclusionCode',
    key: 'exclusionCode',
    editable: true,
    width: 200,
    fieldProps: {
      placeholder: I18nInstance.t('Please input'),
      type: FieldType.Input,
      options: [],
    },
  },
  {
    title: I18nInstance.t('Exclusion Content', ns),
    dataIndex: 'exclusionContent',
    key: 'exclusionContent',
    editable: true,
    width: 180,
    height: '200px',
    fieldProps: {
      placeholder: I18nInstance.t('text'),
      type: FieldType.InputTextArea,
    },
    render: (text: string) => (
      <div
        className={styles.exclusionContent}
        dangerouslySetInnerHTML={{ __html: text?.replaceAll('\n', '<br/>') }}
      ></div>
    ),
  },
  {
    title: I18nInstance.t('Status', ns),
    dataIndex: 'status',
    key: 'status',
    type: 'select',
    mode: 'multiple',
    editable: true,
    width: 180,
    fieldProps: {
      placeholder: I18nInstance.t('Please select'),
      type: FieldType.Select,
      extraProps: {
        options: exclusionStatus?.map(reasonItem => ({
          value: reasonItem.dictValue,
          label: reasonItem.dictValueName || reasonItem.itemName,
        })),
      },
    },
    render: (text: string) => getExclusionColumnText(text, exclusionStatus),
  },
];
