@import '@uw/styles/variables.scss';
@import '@uw/styles/common.scss';

.rule-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  .upload-wrapper {
    padding-bottom: $gap-lg;
  }
}
.examination-item-table {
  width: 100%;
  :global {
    .antd-uw-table-filter-column {
      align-items: center;
      justify-content: flex-start;
      .antd-uw-table-column-title {
        display: block;
        flex-grow: 0;
        flex-shrink: 0;
        min-width: 80px;
      }
    }
  }
}
.rule-content {
  width: calc(100% - 238px);
  background: var(--white);
}
.title {
  height: 52px;
  color: var(--text-color);
  font-weight: bold;
  font-size: $font-size-lg;
  line-height: 52px;
  border-bottom: 1px solid var(--border-default);
}
.rule-title {
  padding-top: var(--gap-xs);
  font-size: $font-size-lg;
  color: var(--text-color);
  font-weight: bold;
  line-height: 20px;
}
.rule-sub-title {
  margin: 14px 0;
  font-size: $font-size-root;
  color: var(--text-color);
  font-weight: bold;
  line-height: 20px;
}

.ellipse {
  max-width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.exclusion-content {
  max-width: 560px;
}

.medical-item-title {
  margin-bottom: var(--gap-xs);
  color: var(--primary-color);
}

.section-box {
  margin: $gap-md $gap-md 0 $gap-md;
  padding: 0 $gap-big $gap-big;
  background-color: var(--white);

  .section-title,
  .section-sub-title,
  .section-title-no-dash {
    padding: $gap-md 0;
    color: var(--text-color);
    font-weight: bold;
    font-size: $font-size-lg; /* todo  shard 暂无 */
    line-height: 1.3em;
    border-bottom: 1px dashed var(--border-default);
  }
  .section-sub-title {
    padding: $gap-big 0;
    font-size: $font-size-root; /* todo  shard 暂无 */
  }
  .section-title-no-dash {
    border-bottom: 0;
  }
  .section-content {
    border-bottom: 1px dashed var(--border-default);
  }
}
.file-items {
  margin-top: $gap-md; /* todo  shard 暂无 */
  margin-bottom: 0px;
  padding: 0;
}

:export {
  gapMd: $gap-md;
  gapXs: var(--gap-xs);
  primaryColor: var(--primary-color);
  labelColor: var(--label-color);
  fontSizeRoot: $font-size-root;
}
