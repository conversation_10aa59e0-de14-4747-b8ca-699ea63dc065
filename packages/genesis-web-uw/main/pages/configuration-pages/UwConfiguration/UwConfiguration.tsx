import React, { FC, Suspense } from 'react';
import { Spin } from 'antd';
import { ExtendSecondMenu } from 'genesis-web-component/lib/components/SecondMenu';

import { useTranslation } from 'react-i18next';
import { Route, Routes } from 'react-router-dom';
import { uwConfigrationRoutes } from '@uw/router';

import { useSecondMenuMap } from '@uw/hook/useMenuData';

import styles from './config.module.scss';
import { uwConfigurationMenu } from './menu';

export const UwConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const { defaultKey, defaultMenuMap, handleMenuClick } =
    useSecondMenuMap(uwConfigurationMenu);
  return (
    <div style={{ display: 'flex', height: '100%' }}>
      <ExtendSecondMenu
        title={t('Underwriting Configuration')}
        handleMenuClick={handleMenuClick}
        defaultSelected={defaultKey ?? 'exclusion'}
        showLine={false}
        menuMap={defaultMenuMap}
        showMenuSearch={false}
      />
      <section className={styles['rule-content']}>
        <Routes>
          {uwConfigrationRoutes[0].children.map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <Suspense
                  fallback={
                    <div
                      style={{
                        width: '100%',
                        minHeight: '500px',
                        textAlign: 'center',
                        lineHeight: '500px',
                      }}
                    >
                      <Spin size="large" />
                    </div>
                  }
                >
                  {route.component && <route.component />}
                </Suspense>
              }
            ></Route>
          ))}
        </Routes>
      </section>
    </div>
  );
};
