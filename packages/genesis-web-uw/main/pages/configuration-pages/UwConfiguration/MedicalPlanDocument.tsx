import React, { useMemo, forwardRef, useCallback } from 'react';
import { Layout, Upload, Button } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { FileItem } from 'genesis-web-component/lib/components';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import { useTranslation } from 'react-i18next';
import {
  InboxOutlined,
  DownloadOutlined,
  DeleteOutlined,
} from '@ant-design/icons';

import styles from './config.module.scss';
import {
  useDeleteFile,
  useExport,
  useUploadFile,
  useGetFileList,
  useExportFile,
} from './hooks/request';

const { Dragger } = Upload;

const MedicalPlanDocument = forwardRef(() => {
  const [t] = useTranslation(['uw', 'common']);
  const [fileList, , getFileList] = useGetFileList();
  const [deleteFile] = useDeleteFile(getFileList);
  const [handleExport] = useExport();
  const [handleUploadFiles] = useUploadFile(getFileList);
  const [exportFile] = useExportFile(getFileList);

  const uploadProps = useMemo<UploadProps>(
    () => ({
      name: 'file',
      accept: '.xls, .xlsx',
      multiple: false,
      disabled: false,
      showUploadList: false,
      customRequest: (cb: UploadRequestOption) => {
        const formData = new FormData();
        formData.append('file', cb.file);
        handleUploadFiles(formData);
      },
    }),
    [fileList]
  );

  const handleDeleteFile = useCallback(
    id => {
      deleteFile(id);
    },
    [fileList]
  );

  const handleDownload = useCallback(
    fileUniqueCode => {
      exportFile(fileUniqueCode);
    },
    [fileList]
  );

  return (
    <Layout
      className={styles.sectionBox}
      style={{ width: '420px', flexShrink: 0, marginLeft: 0, padding: '0px' }}
    >
      <div className={styles.medicalItemTitle}>
        {t('Medical Examination Item ')}
      </div>
      <Button
        icon={<DownloadOutlined />}
        onClick={handleExport}
        type="primary"
        ghost
        style={{ width: '180px' }}
      >
        {t('Download')}
      </Button>

      {fileList.length >= 1 ? (
        <div className={styles.fileItems}>
          {fileList?.map(item => (
            <FileItem
              key={item?.fileId}
              fileName={item.attachmentName || ''}
              fileUrl={item.fileUrl}
              style={{
                marginRight: 16,
              }}
              hoverInfoList={[
                {
                  icon: (
                    <DownloadOutlined
                      style={{ color: styles.labelColor, marginRight: 16 }}
                    />
                  ),
                  onClick: () => handleDownload(item?.fileUniqueCode),
                },
                {
                  icon: <DeleteOutlined />,
                  onClick: () => handleDeleteFile(item?.fileId),
                },
              ]}
            />
          ))}
        </div>
      ) : (
        <div>
          <Dragger
            {...uploadProps}
            style={{
              padding: '30px 0',
              marginTop: styles.gapMd,
              height: '184px',
              width: 'calc(100% - 64px)',
            }}
          >
            <p className={styles.antUploadDragIcon}>
              <InboxOutlined
                style={{ color: styles.primaryColor, fontSize: '46px' }}
              />
            </p>
            <p className={styles.uploadText}>
              {t('Click or drag file here area to upload')}
            </p>
            <p className={styles.uploadHint}>
              {t('You can only upload PDF/DOC/XLS/PNG/JPG')}
            </p>
          </Dragger>
        </div>
      )}
    </Layout>
  );
});

export default MedicalPlanDocument;
