import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';

import { Icon, ModalConfirm, StatusTag } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import {
  ConfigurationLevelRecord,
  ConfigurationLevelStatus,
} from 'genesis-web-service';

import { usePermission } from '@uw/hook/permission';
import { useDict } from '@uw/hook/useDict';
import { getStatusTagType } from '@uw/util/getStatusTagType';

import styles from '../index.module.scss';

type UseColumns = (
  length: number,
  deleteLevel: (record: ConfigurationLevelRecord) => void,
  openLevelDrawer: (mode: Mode, record: ConfigurationLevelRecord) => void,
  isSorting: boolean
) => {
  columns: ColumnsType<ConfigurationLevelRecord>;
  hasEditAuth: boolean;
};

export const useColumns: UseColumns = (
  length,
  deleteLevel,
  openLevelDrawer,
  isSorting
) => {
  const { t } = useTranslation(['uw', 'common']);

  const [uwConfigurationLevelStatus] = useDict('uwConfigurationLevelStatus');

  const hasViewAuth = !!usePermission('uw.configuration.view');
  const hasEditAuth = !!usePermission('uw.configuration.edit');

  const columns: ColumnsType<ConfigurationLevelRecord> = useMemo(
    () => [
      {
        title: t('Level Order'),
        dataIndex: 'priority',
        key: 'priority',
        render: (text: number, record, index: number) => (
          <Space>
            {text}
            {index + 1 === length && (
              <StatusTag type={'error'} statusI18n={t('High Level')} />
            )}
          </Space>
        ),
      },
      {
        title: t('Level Name'),
        dataIndex: 'levelName',
        key: 'levelName',
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        key: 'status',
        render: (text: ConfigurationLevelStatus) => (
          <StatusTag
            needDot
            type={getStatusTagType(text)}
            statusI18n={
              uwConfigurationLevelStatus?.[text as string] ?? (text as string)
            }
          />
        ),
      },
      {
        title: t('Actions'),
        align: 'right',
        fixed: 'right',
        key: 'sort',
        render: (text: unknown, record) => {
          if (isSorting) {
            return null;
          }
          const deleteContext =
            record?.status === ConfigurationLevelStatus.DRAFT
              ? t('Are you sure to delete the level?')
              : t(
                  'Are you sure to delete the level? Current authority level is in force.'
                );
          return (
            <Space className={styles.actionSpace}>
              <Button
                type="text"
                disabled={!hasViewAuth}
                onClick={() => openLevelDrawer(Mode.View, record)}
              >
                <Icon type="view" />
              </Button>
              <Button
                type="text"
                disabled={!hasEditAuth}
                onClick={() => openLevelDrawer(Mode.Edit, record)}
              >
                <Icon type="edit" />
              </Button>
              <ModalConfirm
                onOk={() => deleteLevel(record)}
                className={styles.modalConfirm}
                title={t('Delete')}
                okText={t('yes')}
                cancelText={t('no')}
                content={deleteContext}
              >
                <Button type="text" disabled={!hasEditAuth}>
                  <Icon type="delete" />
                </Button>
              </ModalConfirm>
            </Space>
          );
        },
      },
    ],
    [
      deleteLevel,
      hasEditAuth,
      hasViewAuth,
      length,
      openLevelDrawer,
      t,
      uwConfigurationLevelStatus,
      isSorting,
    ]
  );

  return {
    columns,
    hasEditAuth,
  };
};
