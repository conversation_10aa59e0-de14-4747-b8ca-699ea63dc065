import { Dispatch, useCallback, useEffect, useState } from 'react';

import { Form, FormInstance } from 'antd';

import { isEmpty, orderBy } from 'lodash-es';
import { v4 as uuidV4 } from 'uuid';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import {
  ConfigurationLevelRecord,
  ConfigurationLevelStatus,
  RuleEngine,
  SaveOrSubmit,
  UnderWritingAuthorityService,
} from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { messagePopup } from '@uw/util/messagePopup';

import { Action, SET_INIT_VALUE, State, UPDATE_MODE } from '../provider';
import { judgeLevelNameExist } from './useJudgeLevelNameExist';

// hook type
type UseAuthorityType = (
  state: State,
  dispatch: Dispatch<Action>
) => {
  tableData: ConfigurationLevelRecord[];
  sortData: ConfigurationLevelRecord[];
  deleteLevel: (record: ConfigurationLevelRecord) => void;
  openLevelDrawer: (openMode: Mode, record?: ConfigurationLevelRecord) => void;
  isSorting: boolean;
  setIsSorting: SetState<boolean>;
  cancelSort: () => void;
  handleSortSave: () => void;
  setSortData: SetState<ConfigurationLevelRecord[]>;
  startSort: () => void;
  form: FormInstance;
  drawerOpen: boolean;
  mode: Mode;
  closeLevelDrawer: () => void;
  handleFinishDrawer: (finishMode: SaveOrSubmit) => void;
};

export const useAuthority: UseAuthorityType = (state, dispatch) => {
  const [tableData, setTableData] = useState<ConfigurationLevelRecord[]>([]);
  // 排序时使用的 数据 与表格原有数据互不影响
  const [sortData, setSortData] = useState<ConfigurationLevelRecord[]>([]);
  const [isSorting, setIsSorting] = useState(false);
  const [editInfo, setEditInfo] = useState<ConfigurationLevelRecord | null>(
    null
  );
  const [form] = Form.useForm();
  // drawer open status
  const [drawerOpen, setDrawerOpen] = useState(false);
  // drawer open mode
  const [mode, setMode] = useState<Mode>(Mode.Add);

  const getLevels = useCallback(async () => {
    const levels =
      (await UnderWritingAuthorityService.getConfigurationLevels()) as unknown as ConfigurationLevelRecord[];

    if (levels) {
      // 不相信后端返回的顺序，可能中间有缺失的顺序，那么前端就手动重新排序
      const orderLevels = orderBy(levels, 'priority')?.map((level, index) => ({
        ...level,
        priority: index + 1,
      }));
      setTableData(orderLevels);
      setSortData(orderLevels);
    }
  }, []);

  const getEmptyInitFormlua = useCallback(() => {
    const hardCodeString = uuidV4();
    return {
      ruleType: RuleEngine.RuleType.General,
      ruleNature: RuleEngine.RuleNature.Single,
      ruleSource: RuleEngine.RuleSource.UW_LEVEL,
      ruleComponent: null,
      category: RuleEngine.RuleCategory.Underwriting,
      ruleConfigurationType: RuleEngine.RuleConfiguration.ConditionalRule,
      // name code description 使用 uuid 生成
      name: hardCodeString,
      code: hardCodeString,
      description: hardCodeString,
      parameterLevel: RuleEngine.ParameterLevel.TenantLevel,
      ruleTags: {
        tag: null,
        subTag: null,
      },
      revision: {
        config: {
          pageType: RuleEngine.PageType.Standard,
          filters: [],
          // Middle Factor Definition 公式内容 暂不支持
          interFactors: [],
          cases: [],
          // 规则引擎中规定 else then 区域， UW 不需要 所以直接写死
          lastCase: {
            actions: [
              {
                actionType: RuleEngine.ActionCategory.GeneralDecision,
                decisionType: RuleEngine.UwDecision.Decline,
              },
            ],
          },
          decisionTable: {
            dataSource: [],
            drawerRows: [],
            drawerCols: [],
          },
        },
      },
    };
  }, []);

  const deleteLevel = useCallback(
    async (record: ConfigurationLevelRecord) => {
      if (!record?.iamLevelId) return;
      // 删除
      await UnderWritingAuthorityService.deleteLevel(record?.iamLevelId);
      // 前端手动调用排序接口
      const sortedData = tableData
        // 过滤掉删除的数据
        ?.filter(level => level?.iamLevelId !== record?.iamLevelId)
        // 更改排序字段
        ?.map((level, index) => ({
          iamLevelId: level?.iamLevelId,
          levelName: level?.levelName,
          priority: index + 1,
        }));
      // save sort
      await UnderWritingAuthorityService.sortLevels(sortedData);
      // get level list
      await getLevels();
    },
    [getLevels, tableData]
  );

  const openLevelDrawer = useCallback(
    async (openMode: Mode, record?: ConfigurationLevelRecord) => {
      try {
        // view 或者 edit
        if (openMode !== Mode.Add) {
          setEditInfo(record as ConfigurationLevelRecord);
          // update Context
          if (dispatch) {
            dispatch({
              type: SET_INIT_VALUE,
              rules: getEmptyInitFormlua(),
            });
          }
        }
        // 新增
        else {
          // init Context
          dispatch({
            type: SET_INIT_VALUE,
            rules: getEmptyInitFormlua(),
          });
        }

        if (dispatch) {
          dispatch({
            type: UPDATE_MODE,
            mode: openMode,
          });
        }
        // set current form field
        form.setFieldsValue({
          levelName: record?.levelName,
          ruleCode: record?.ruleCode,
        });
        // open drawer
        setDrawerOpen(true);
        // set current mode
        setMode(openMode);
        // open drawer
        setDrawerOpen(true);
      } catch (error) {
        messagePopup(
          (error as { message: unknown })?.message?.toString?.() as string,
          'error'
        );
      }
    },
    [dispatch, form, getEmptyInitFormlua]
  );

  const closeLevelDrawer = useCallback(() => {
    setMode(Mode.Add);
    dispatch({
      type: UPDATE_MODE,
      mode: Mode.Add,
    });
    form.resetFields();
    setDrawerOpen(false);
    setEditInfo(null);
  }, [dispatch, form]);

  const submitData = useCallback(
    async status => {
      form.validateFields().then(async values => {
        try {
          const levelName = (values?.levelName as string)?.trim?.();
          let levelList = tableData;
          // 非 add 判断除自己本身的所有 levelName
          if (state?.mode !== Mode.Add) {
            levelList = levelList?.filter(
              item => item?.iamLevelId !== editInfo?.iamLevelId
            );
          }
          // 判断 levelName 是否已存在
          judgeLevelNameExist(levelName, levelList);
          if (values && !isEmpty(values)) {
            const { ruleCode } = values;
            // 判断 finish mode: save or submit
            const saveValues = {
              iamLevelId: editInfo?.iamLevelId ?? undefined,
              ruleCode,
              status,
              levelName,
              // 优先取自己本身的 排序
              priority: editInfo?.priority ?? tableData?.length + 1,
            };
            // save UW
            await UnderWritingAuthorityService.saveConfigurationLevels(
              saveValues
            );
            // close the drawer
            closeLevelDrawer();
            // get new levels
            getLevels();
          }
        } catch (error) {
          // only catch errors
          messagePopup(
            (error as { message: string })?.message?.toString?.(),
            'error'
          );
        }
      });
    },
    [
      closeLevelDrawer,
      editInfo?.iamLevelId,
      form,
      getLevels,
      state?.mode,
      editInfo?.priority,
      tableData,
    ]
  );

  const handleFinishDrawer = useCallback(
    (finishMode: SaveOrSubmit) => {
      submitData(
        finishMode === SaveOrSubmit.Save
          ? ConfigurationLevelStatus.DRAFT
          : ConfigurationLevelStatus.EFFECTIVE
      );
    },
    [submitData]
  );

  const startSort = useCallback(() => {
    setIsSorting(true);
    setSortData(tableData);
  }, [tableData]);

  const cancelSort = useCallback(() => {
    setIsSorting(false);
    setSortData(tableData);
  }, [tableData]);

  const handleSortSave = useCallback(async () => {
    // save sort
    await UnderWritingAuthorityService.sortLevels(
      sortData?.map(level => ({
        iamLevelId: level?.iamLevelId,
        priority: level?.priority,
        levelName: level?.levelName,
      }))
    );
    // get new levels
    await getLevels();
    setIsSorting(false);
  }, [getLevels, sortData]);

  useEffect(() => {
    getLevels();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    tableData,
    sortData,
    deleteLevel,
    openLevelDrawer,
    isSorting,
    setIsSorting,
    cancelSort,
    handleSortSave,
    setSortData,
    startSort,
    form,
    drawerOpen,
    mode,
    closeLevelDrawer,
    handleFinishDrawer,
  };
};
