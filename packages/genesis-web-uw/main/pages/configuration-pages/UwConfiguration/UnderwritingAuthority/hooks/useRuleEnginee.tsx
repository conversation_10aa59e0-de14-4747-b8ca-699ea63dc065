/* eslint-disable no-param-reassign */
import {
  RuleEngine,
  UnderWritingAuthorityService,
  ruleService,
} from 'genesis-web-service';
import { useCallback } from 'react';

import { explicitCustom } from '../components/utils';

/**
 * @implements { Sider tree components usage }
 */
const schemaProperties = (layer: RuleEngine.SiderChildrenAttr) => ({
  name: layer.name,
  value: layer.value,
  former: layer.former,
  display: layer.display,
  enumKey: layer.enumKey,
  category: layer.category,
  dataType: layer.dataType,
  tokenType: layer.tokenType,
  extension: layer.extension,
  externalId: layer.externalId,
  description: layer.description,
  qualifiedName: layer.qualifiedName,
  externalCategory: layer.externalCategory,
});

export const useRuleEnginee = () => {
  const getOptions = useCallback(async () => {
    const res = await UnderWritingAuthorityService.getOptions();

    const displayRoots = res?.filter(root => root?.display);

    /** @implements { process backend data to tree component data  } */
    return displayRoots?.map(firstLayer => ({
      ...schemaProperties(
        firstLayer as unknown as RuleEngine.SiderChildrenAttr
      ),
      passedName: firstLayer?.qualifiedName as string | undefined,
      enumItems: (
        firstLayer as unknown as { enumItems: RuleEngine.SchemaUnit[] }
      )?.enumItems,
      qualifiedName: explicitCustom(
        firstLayer.category,
        firstLayer.qualifiedName
      ),
    }));
  }, []);

  // accounting to params get enums list
  const getFactorEnums = useCallback(async (enumKey, tokenType) => {
    const res = await ruleService.searchRuleEngineFactorEnums(
      enumKey,
      tokenType
    );
    return res;
  }, []);

  // add new rule
  const addNewRule = useCallback(async (values: RuleEngine.Detail) => {
    const res = await ruleService.newRuleDetail(values);
    return res?.code;
  }, []);

  // update rule
  const updateRuleDetail = useCallback(async (values: RuleEngine.Detail) => {
    await ruleService.updateRuleDetail(values);
    await ruleService.putRuleDetail(
      values?.code as string,
      values?.revision?.revision as number,
      values?.revision
    );
  }, []);

  // delete rule
  const deleteRule = useCallback(async (ruleCode: string) => {
    await ruleService.deleteRuleDetail(ruleCode);
  }, []);

  return {
    getOptions,
    getFactorEnums,
    addNewRule,
    updateRuleDetail,
    deleteRule,
  };
};
