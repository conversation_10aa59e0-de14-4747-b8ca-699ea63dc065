import { some } from 'lodash-es';
import { ConfigurationLevelRecord } from 'genesis-web-service';
import { i18nFn } from '@uw/util/i18nFn';

export const judgeLevelNameExist = (
  levelName: string,
  existLevels: ConfigurationLevelRecord[]
) => {
  if (some(existLevels, ['levelName', levelName])) {
    throw new Error(
      i18nFn('{{levelname}} already exists', ['uw', 'common'], {
        levelname: levelName,
      })
    );
  }
};
