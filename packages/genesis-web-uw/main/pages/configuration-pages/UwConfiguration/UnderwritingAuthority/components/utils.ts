import { message } from 'antd';
import { RuleEngine } from 'genesis-web-service';
import { cloneDeep } from 'lodash-es';
import { i18nFn } from '@uw/util/i18nFn';

export const splitString = '\u001d';

export const getPopupContainer = () =>
  document.getElementById('DraggableIfWrapper') as HTMLElement;

export const transferNull = (value: unknown) => {
  if (typeof value === 'string' && value === '*') {
    return undefined;
  }
  if (value === null) {
    return undefined;
  }
  return value;
};

export const handleBelongOperation = (
  units: RuleEngine.SchemaUnit[],
  unitIndex: number,
  value: string
) => {
  const newUnits = cloneDeep(units);
  newUnits[unitIndex].name = RuleEngine.BelongSymbol.name;
  newUnits[unitIndex].value = RuleEngine.BelongSymbol.value;
  newUnits[unitIndex].qualifiedName = RuleEngine.BelongSymbol.qualifiedName;
  const [left, right] = value.split(':')[1].split(',');
  const leftSpecialSymbol = RuleEngine.SpecialSymbols.find(
    symbol => symbol.qualifiedName === left.trim()
  );
  const rightSpecialSymbol = RuleEngine.SpecialSymbols.find(
    symbol => symbol.qualifiedName === right.trim()
  );
  const leftUnit: RuleEngine.SchemaUnit = {
    ...RuleEngine.DefaultOperateUnit,
    ...leftSpecialSymbol,
  };
  const rightUnit: RuleEngine.SchemaUnit = {
    ...RuleEngine.DefaultOperateUnit,
    ...rightSpecialSymbol,
  };
  const commaUnit: RuleEngine.SchemaUnit = {
    ...RuleEngine.DefaultOperateUnit,
    qualifiedName: ',',
    name: ',',
    value: 'COMMA',
  };
  /** @desc factor ∈ ( unit1 , unit2 )
   * ∈ : unitIndex
   * ( : unitIndex + 1
   * , : unitIndex + 3
   * unit2 : unitIndex + 4
   * ) : unitIndex + 5
   * */
  if (newUnits[unitIndex + 5]) {
    newUnits[unitIndex + 1] = leftUnit;
    newUnits[unitIndex + 5] = rightUnit;
  } else {
    newUnits.splice(unitIndex + 1, 0, leftUnit);
    newUnits.splice(unitIndex + 3, 0, commaUnit);
    newUnits.splice(unitIndex + 4, 0, RuleEngine.DefaultSchemaUnit);
    newUnits.splice(unitIndex + 5, 0, rightUnit);
  }
  return newUnits;
};

export const handleGeneralOperation = (
  units: RuleEngine.SchemaUnit[],
  unitIndex: number,
  value: string
) => {
  const newUnits = cloneDeep(units);

  const matchedSymbol = RuleEngine.GeneralSymbols.find(
    symbol => symbol.value === value
  );
  newUnits[unitIndex].value = value;
  if (matchedSymbol) {
    newUnits[unitIndex].qualifiedName = matchedSymbol.qualifiedName;
    newUnits[unitIndex].name = matchedSymbol.qualifiedName;
  } else {
    message
      .warning(i18nFn('Symbol not matched, please check GeneralSymbols.'))
      .then();
  }
  return newUnits;
};

export const handleResetFormula = (
  formulas: { units: RuleEngine.SchemaUnit[] }[],
  formulaIndex: number,
  units: RuleEngine.SchemaUnit[]
) => {
  formulas.splice(formulaIndex, 1);
  formulas.splice(formulaIndex, 0, {
    units,
  });
  return formulas;
};

export const replaceFormulaUnit = (
  units: RuleEngine.SchemaUnit[],
  unitIndex: number,
  unit: RuleEngine.SchemaUnit
) => {
  units.splice(unitIndex, 1);
  units.splice(unitIndex, 0, unit);
  return units;
};

export const explicitCustom = (
  category: string | null,
  qualifiedName: string | null
): string | null => {
  let showName = qualifiedName;
  if (category && qualifiedName === null) {
    message
      .error(
        i18nFn(
          'Please check schema correction. Some schemas have category without name.'
        )
      )
      .then();
  }
  if (category && qualifiedName) {
    showName =
      category === 'custom' ? qualifiedName : `${category}_${qualifiedName}`;
  }
  return showName;
};

export const schemaUnitProperties = (data: RuleEngine.SchemaUnit) => {
  const result = {
    ...data,
    qualifiedName:
      data.passedName ?? (data.qualifiedName as string | undefined),
  };
  if (result.passedName) {
    delete result.passedName;
  }
  return result;
};

export const transferSplit = (
  value: string | string[] | null
): string[] | undefined => {
  if (value === null) {
    return undefined;
  }
  if (typeof value === 'string') {
    return value.split(splitString);
  }
  return value;
};

export const isOperateSymbol = (unitName: string | null) =>
  RuleEngine.OperateSymbols?.map(symbol => symbol?.qualifiedName).includes(
    unitName
  );

export const isCompareSymbol = (unitName: string) =>
  RuleEngine.GeneralSymbols.map(symbol => symbol?.qualifiedName).includes(
    unitName
  );

export const isSpecialSymbol = (unitName: string) =>
  RuleEngine.SpecialSymbols.map(symbol => symbol?.qualifiedName).includes(
    unitName
  );
