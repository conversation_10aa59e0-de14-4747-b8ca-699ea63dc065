import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { Icon, cssVars, ModalConfirm } from '@zhongan/nagrand-ui';
import { Button, Col, Drawer, Form, FormInstance, Row } from 'antd';
import { RuleEngine, SaveOrSubmit, ruleService } from 'genesis-web-service';
import { FC, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { capitalize } from 'lodash-es';

import { useAsyncEffect } from 'ahooks';

import styles from './index.module.scss';
import { fields } from '../../config';

interface Props {
  form: FormInstance;
  mode: Mode;
  open: boolean;
  closeLevelDrawer: () => void;
  handleFinishDrawer: (finishMode: SaveOrSubmit) => void;
  hasEditAuth: boolean;
}

const getRuleList = (rule: RuleEngine.RuleListItem[]) =>
  rule?.map(item => ({
    enumItemName: item?.code,
    itemName: item?.code,
  }));

export const LevelDrawer: FC<Props> = ({
  form,
  mode,
  open,
  closeLevelDrawer,
  handleFinishDrawer,
  hasEditAuth,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const [rule, setRule] =
    useState<{ enumItemName: string; itemName: string }[]>();

  const footer = useMemo(() => {
    const footerActions = [
      <Button key="cancel" onClick={closeLevelDrawer}>
        {t('Cancel')}
      </Button>,
      <Button
        key="save"
        disabled={!hasEditAuth}
        className={styles.save}
        onClick={() => handleFinishDrawer(SaveOrSubmit.Save)}
      >
        {t('Save')}
      </Button>,
      <ModalConfirm
        className={styles.modalConfirm}
        onOk={() => handleFinishDrawer(SaveOrSubmit.Submit)}
        cancelText={t('Cancel')}
        okText={t('Submit')}
        title={t('Submit')}
        key="submit"
        content={t(
          'Are you sure to submit current authority configuration? After submitting, the status of level will become effective.'
        )}
      >
        <Button type="primary" disabled={!hasEditAuth}>
          {t('Submit')}
        </Button>
      </ModalConfirm>,
    ];
    if (mode === Mode.View) {
      return footerActions?.slice(0, 1);
    }
    return footerActions;
  }, [closeLevelDrawer, handleFinishDrawer, hasEditAuth, mode, t]);

  useAsyncEffect(async () => {
    const allRules = await ruleService.queryRulesUnderCategory(
      ['UW_AUTHORITY'],
      true
    );
    setRule(getRuleList(allRules));
  }, []);

  return (
    <Drawer
      open={open}
      title={t('{{action}} UW Authority Configuration', {
        action: capitalize(mode),
      })}
      width={408}
      closable={false}
      onClose={closeLevelDrawer}
      destroyOnClose
      extra={
        <Icon
          type="close"
          className={styles.closeIcon}
          onClick={closeLevelDrawer}
        />
      }
      footer={footer}
      footerStyle={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-end',
        padding: `${cssVars.gapMd} ${cssVars.gapLg}`,
      }}
    >
      <Form
        form={form}
        disabled={!hasEditAuth || mode === Mode.View}
        layout="vertical"
      >
        <Row gutter={40} className={styles.basicInfo}>
          {fields?.map(field => (
            <Col span={field?.span} key={field?.name}>
              <Form.Item
                name={field?.name}
                label={field?.label}
                required={field?.required}
                rules={field?.rule}
              >
                {getFields({ ...field, options: rule })}
              </Form.Item>
            </Col>
          ))}
        </Row>
      </Form>
    </Drawer>
  );
};
