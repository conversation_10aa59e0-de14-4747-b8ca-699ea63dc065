import React, { FC, useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Tooltip } from 'antd';

import { Icon, ModalConfirm, Table, TextBody } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { LevelDrawer } from './components';
import { useAuthority } from './hooks/useAuthority';
import { useColumns } from './hooks/useColumns';
import styles from './index.module.scss';
import {
  Action,
  Context,
  Reducer,
  State,
  initialContextValue,
} from './provider';

export const UnderwritingAuthority: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const [state, dispatch] = useReducer<React.Reducer<State, Action>>(
    Reducer,
    initialContextValue
  );

  const {
    tableData,
    openLevelDrawer,
    deleteLevel,
    isSorting,
    startSort,
    cancelSort,
    handleSortSave,
    sortData,
    setSortData,
    drawerOpen,
    form,
    mode,
    closeLevelDrawer,
    handleFinishDrawer,
  } = useAuthority(state, dispatch);

  const { columns, hasEditAuth } = useColumns(
    tableData?.length,
    deleteLevel,
    openLevelDrawer,
    isSorting
  );

  const actionArea = useMemo(() => {
    if (isSorting) {
      return (
        <>
          <ModalConfirm
            onOk={handleSortSave}
            className={styles.modalConfirm}
            title={t('Save')}
            okText={t('yes')}
            cancelText={t('no')}
            content={t('Are you sure you want to save the current sort?')}
          >
            <Button type="primary">{t('Save')}</Button>
          </ModalConfirm>
          <Button className={styles.addNew} onClick={cancelSort}>
            {t('Cancel')}
          </Button>
        </>
      );
    }
    return (
      <>
        <Button
          icon={<Icon type="filter-line" />}
          disabled={!hasEditAuth}
          onClick={startSort}
        >
          {t('Sort')}
        </Button>
        <Button
          className={styles.addNew}
          disabled={!hasEditAuth}
          icon={<Icon type="add" />}
          onClick={() => openLevelDrawer(Mode.Add)}
        >
          {t('Add New Level')}
        </Button>
      </>
    );
  }, [
    cancelSort,
    handleSortSave,
    hasEditAuth,
    isSorting,
    openLevelDrawer,
    startSort,
    t,
  ]);

  return (
    <Context.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <div className={styles.ruleWrapper}>
        <section className="py-md px-lg border-0 border-b border-solid border-[#DAE8F9] leading-4 text=[#333] font-bold">
          {t('Underwriting Authority')}
        </section>
        <div className="py-big px-lg">
          <section className={styles.subTitle}>
            <TextBody weight={700}>
              <span>{t('Authority Configuration')}</span>
              <Tooltip
                placement="top"
                title={t(
                  'After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.'
                )}
              >
                <Icon type="info-circle" className={styles.infoIcon} />
              </Tooltip>
            </TextBody>
            <section className={styles.actionArea}>{actionArea}</section>
          </section>
          <Table
            columns={columns}
            rowKey="iamLevelId"
            dataSource={sortData}
            scroll={{ x: 'max-content' }}
            pagination={false}
            draggable={isSorting}
            setDataSource={setSortData}
          />
        </div>
      </div>
      {drawerOpen && (
        <LevelDrawer
          hasEditAuth={hasEditAuth}
          form={form}
          mode={mode}
          open={drawerOpen}
          closeLevelDrawer={closeLevelDrawer}
          handleFinishDrawer={handleFinishDrawer}
        />
      )}
    </Context.Provider>
  );
};
