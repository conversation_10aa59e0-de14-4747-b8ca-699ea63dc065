import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';

import { i18nFn } from '@uw/util/i18nFn';

export const fields = [
  {
    type: FieldType.Input,
    placeholder: i18nFn('Please input'),
    required: true,
    name: 'levelName',
    label: i18nFn('Level Name'),
    span: 20,
    rule: [{ required: true }],
  },
  {
    type: FieldType.Select,
    placeholder: i18nFn('please select'),
    required: true,
    name: 'ruleCode',
    label: i18nFn('Rule Code/Rule Set'),
    span: 20,
    rule: [{ required: true }],
  },
];
