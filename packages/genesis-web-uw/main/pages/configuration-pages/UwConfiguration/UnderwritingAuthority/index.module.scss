.rule-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  .rule-title {
    padding-top: var(--gap-xs);
    font-size: $font-size-lg;
    color: var(--text-color);
    font-weight: bold;
    line-height: 20px;
  }

  .divider-line {
    margin: $gap-md 0 $gap-lg 0;
  }

  .sub-title {
    margin-bottom: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info-icon {
      margin-left: var(--gap-xss);
    }
  }

  .action-area {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .add-new {
      margin-left: $gap-md;
    }
  }
}

.action-space {
  // important 覆盖 行内 style
  gap: 0 !important;
  :global {
    .#{$ant-prefix}-space-item {
      .#{$ant-prefix}-btn {
        padding: var(--gap-xss) var(--gap-xs);
      }
      .#{$ant-prefix}-btn-text:hover,
      .#{$ant-prefix}-btn-text:focus {
        background: none;
      }
    }
  }
}

.modal-confirm {
  :global {
    .#{$ant-prefix}-modal-confirm-title {
      display: contents;
    }
  }
}
