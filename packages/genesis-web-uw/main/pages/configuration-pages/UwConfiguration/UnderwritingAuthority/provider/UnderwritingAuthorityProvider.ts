/* eslint-disable @typescript-eslint/no-redeclare */
/* eslint-disable no-param-reassign */
/* eslint-disable no-case-declarations */
import React, { Dispatch } from 'react';
import { RuleEngine } from 'genesis-web-service';
import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import {
  handleBelongOperation,
  handleGeneralOperation,
  handleResetFormula,
  replaceFormulaUnit,
  splitString,
} from '../components/utils';

// reducer
export const SET_INIT_VALUE = 'SET_INIT_VALUE';

export const UPDATE_MODE = 'UPDATE_MODE';

export const GET_INIT_OPTIONS = 'GET_INIT_OPTIONS';

export const UPDATE_FACTOR_ENUMS = 'UPDATE_FACTOR_ENUMS';
export const UPDATE_FACTOR_ENUM = 'UPDATE_FACTOR_ENUM';

export const CHANGE_CUSTOMER_UNIT = 'CHANGE_CUSTOMER_UNIT';
export const CHANGE_GENERAL_OPERATEUNIT = 'CHANGE_GENERAL_OPERATEUNIT';
export const CHANGE_FORMULA_LIST_ENUM_UNIT = 'CHANGE_FORMULA_LIST_ENUM_UNIT';
export const RESET_FORMULA = 'RESET_FORMULA';
export const CHANGE_FORMULA_UNIT = 'CHANGE_FORMULA_UNIT';
export const REMOVE_FORMULA = 'REMOVE_FORMULA';
export const CHANGE_BELOING_OPREATE_UNIT = 'CHANGE_BELOING_OPREATE_UNIT';
export const ADD_FORMILA = 'ADD_FORMILA';
export const TOGGLE_RELATION = 'TOGGLE_RELATION';
export const TOGGLE_LIST_CASE_TYPE = 'TOGGLE_LIST_CASE_TYPE';
export const DELETE_CASE = 'DELETE_CASE';
export const ADD_CASE = 'ADD_CASE';
export const TOGGLE_CASE_TYPE = 'TOGGLE_CASE_TYPE';

type Value = { value: string };
type UnitIndex = { unitIndex: number };
type FormulaIndex = { formulaIndex: number };
type CaseIndex = { caseIndex: number };
type Index = { index: number };
type Unit = { unit: RuleEngine.SchemaUnit };

export interface Rules {
  rules: RuleEngine.Detail;
}

export interface ModeType {
  mode: Mode;
}

export type ChangeCustomerUnit = Value & FormulaIndex & UnitIndex & CaseIndex;

export type ChangeGeneralOperateUnit = Omit<ChangeCustomerUnit, 'unitIndex'> &
  Index;

export type ChangeFormulaListEnumUnit = Omit<
  ChangeCustomerUnit & ChangeGeneralOperateUnit,
  'value'
> & { value: string | null | string[]; qualifiedName: string };

export type ResetFormla = Omit<ChangeCustomerUnit, 'value'>;

export type ChangeFormulaUnit = ResetFormla & Unit;

export type RemoveFormula = FormulaIndex & CaseIndex;

export type ChangeBelongOperateUnit = ChangeCustomerUnit;

export type AddFormula = CaseIndex;

export type AndOr = CaseIndex;

export type ToggleListCaseType = Index;

export type DeleteCase = Index;

export type ToggleCaseType = CaseIndex;

export interface NameListGeneralObjectSchema {
  generalObjectAndLeafSchema: RuleEngine.SchemaUnit[];
}

export interface FactorEnums {
  factorEnums: Record<string, RuleEngine.SchemaUnit[]>;
}

export interface FactorEnum {
  factorEnum: RuleEngine.SchemaUnit[];
  enumKey: string;
}

export type State = Rules &
  ModeType &
  NameListGeneralObjectSchema &
  FactorEnums;

export type ReducerType =
  | Rules
  | ChangeCustomerUnit
  | ChangeGeneralOperateUnit
  | ChangeFormulaListEnumUnit
  | ResetFormla
  | ChangeFormulaUnit
  | RemoveFormula
  | ChangeBelongOperateUnit
  | AddFormula
  | AndOr
  | ToggleListCaseType
  | DeleteCase
  | ToggleCaseType
  | ModeType
  | NameListGeneralObjectSchema
  | FactorEnums
  | FactorEnum;

export type Action = {
  readonly type: string;
} & ReducerType;

export const Reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case UPDATE_MODE:
      return {
        ...state,
        mode: (action as ModeType).mode,
      };

    case SET_INIT_VALUE:
      return {
        ...state,
        rules: (action as Rules).rules,
      };

    case GET_INIT_OPTIONS:
      return {
        ...state,
        generalObjectAndLeafSchema: (action as NameListGeneralObjectSchema)
          ?.generalObjectAndLeafSchema,
      };

    case UPDATE_FACTOR_ENUMS:
      return {
        ...state,
        factorEnums: (action as FactorEnums)?.factorEnums,
      };

    case UPDATE_FACTOR_ENUM:
      return {
        ...state,
        factorEnums: {
          ...state?.factorEnums,
          [(action as FactorEnum)?.enumKey]: (action as FactorEnum)?.factorEnum,
        },
      };

    case CHANGE_CUSTOMER_UNIT:
      const { caseIndex, formulaIndex, unitIndex, value } =
        action as ChangeCustomerUnit;

      state.rules.revision.config.cases[caseIndex as number].formulas[
        formulaIndex as number
      ].units[unitIndex as number] = {
        ...RuleEngine.DefaultArgumentUnit,
        qualifiedName: value,
        name: value,
        value,
      };
      return {
        ...state,
        rules: state.rules,
      };

    case CHANGE_GENERAL_OPERATEUNIT:
      const { units } =
        state?.rules.revision.config.cases[
          (action as ChangeGeneralOperateUnit)?.caseIndex
        ].formulas[(action as ChangeGeneralOperateUnit)?.formulaIndex];
      state.rules.revision.config.cases[
        (action as ChangeGeneralOperateUnit)?.caseIndex
      ].formulas[(action as ChangeGeneralOperateUnit)?.formulaIndex].units =
        handleGeneralOperation(
          units,
          (action as ChangeGeneralOperateUnit)?.index,
          (action as ChangeGeneralOperateUnit)?.value
        );
      return {
        ...state,
        rules: state.rules,
      };

    case CHANGE_FORMULA_LIST_ENUM_UNIT:
      const unitValue =
        (action as ChangeFormulaListEnumUnit)?.value === null
          ? null
          : ((action as ChangeFormulaListEnumUnit)?.value as string[])?.join(
              splitString
            );
      state.rules.revision.config.cases[
        (action as ChangeFormulaListEnumUnit)?.caseIndex
      ].formulas[(action as ChangeFormulaListEnumUnit)?.formulaIndex].units[
        (action as ChangeFormulaListEnumUnit)?.unitIndex
      ] = {
        ...RuleEngine.DefaultArgumentUnit,
        dataType: null,
        value: unitValue,
        name: (action as ChangeFormulaListEnumUnit)?.qualifiedName,
        qualifiedName: (action as ChangeFormulaListEnumUnit)?.qualifiedName,
      };
      return {
        ...state,
        rules: state.rules,
      };

    case RESET_FORMULA:
      const { formulas } =
        state.rules.revision.config.cases[(action as ResetFormla)?.caseIndex];
      const list = formulas[(action as ResetFormla)?.formulaIndex]?.units;
      list.splice((action as ResetFormla)?.unitIndex + 2);
      list[(action as ResetFormla)?.unitIndex] = RuleEngine.DefaultEqualUnit;
      list[(action as ResetFormla)?.unitIndex + 1] =
        RuleEngine.DefaultSchemaUnit;
      state.rules.revision.config.cases[
        (action as ResetFormla)?.caseIndex
      ].formulas = handleResetFormula(
        formulas,
        (action as ResetFormla)?.formulaIndex,
        list
      );
      return {
        ...state,
        rules: state.rules,
      };

    case CHANGE_FORMULA_UNIT:
      const unit =
        state.rules.revision.config.cases[
          (action as ChangeFormulaUnit)?.caseIndex
        ].formulas[(action as ChangeFormulaUnit)?.formulaIndex]?.units;
      state.rules.revision.config.cases[
        (action as ChangeFormulaUnit)?.caseIndex
      ].formulas[(action as ChangeFormulaUnit)?.formulaIndex].units =
        replaceFormulaUnit(
          unit,
          (action as ChangeFormulaUnit)?.unitIndex,
          (action as ChangeFormulaUnit)?.unit
        );
      return {
        ...state,
        rules: state.rules,
      };

    case REMOVE_FORMULA:
      state.rules.revision.config.cases[
        (action as RemoveFormula)?.caseIndex
      ].formulas.splice((action as RemoveFormula)?.formulaIndex, 1);
      // 只有配置2条以上的公式时，公式之间才会存在 AND OR 关系，无公式或只有一条公式则公式之间的关系为 NO
      if (
        state.rules.revision.config.cases[(action as RemoveFormula)?.caseIndex]
          .formulas.length < 2
      ) {
        state.rules.revision.config.cases[
          (action as RemoveFormula)?.caseIndex
        ].relation = RuleEngine.Relation.No;
      }
      return {
        ...state,
        rules: state.rules,
      };

    case CHANGE_BELOING_OPREATE_UNIT:
      const unitList =
        state.rules.revision.config.cases[
          (action as ChangeBelongOperateUnit)?.caseIndex
        ].formulas[(action as ChangeBelongOperateUnit)?.formulaIndex]?.units;
      state.rules.revision.config.cases[
        (action as ChangeBelongOperateUnit)?.caseIndex
      ].formulas[(action as ChangeBelongOperateUnit)?.formulaIndex].units =
        handleBelongOperation(
          unitList,
          (action as ChangeBelongOperateUnit)?.unitIndex,
          (action as ChangeBelongOperateUnit)?.value
        );

      return {
        ...state,
        rules: state.rules,
      };

    case ADD_FORMILA:
      // add new if

      switch (state.rules?.ruleType) {
        case RuleEngine.RuleType.General:
        case RuleEngine.RuleType.RuleSet:
          state.rules?.revision?.config?.cases?.[
            (action as AddFormula)?.caseIndex
          ].formulas?.push?.({
            units: [
              RuleEngine.DefaultSchemaUnit,
              RuleEngine.DefaultEqualUnit,
              RuleEngine.DefaultSchemaUnit,
            ],
          });
          break;
        case RuleEngine.RuleType.NameList:
          // 选取 ListCompareSymbols 中的第一个为新增公式的默认值
          const { qualifiedName, value: valueString } =
            RuleEngine.ListCompareSymbols[0];
          const LEFT_PARENTHESIS: RuleEngine.SchemaUnit = {
            ...RuleEngine.DefaultOperateUnit,
            qualifiedName: '(',
            name: '(',
            value: 'LEFT_PARENTHESIS',
          };
          const RIGHT_PARENTHESIS: RuleEngine.SchemaUnit = {
            ...RuleEngine.DefaultOperateUnit,
            qualifiedName: ')',
            name: ')',
            value: 'RIGHT_PARENTHESIS',
          };
          state.rules?.revision?.config?.cases?.[
            (action as AddFormula)?.caseIndex
          ].formulas?.push?.({
            units: [
              RuleEngine.DefaultSchemaUnit,
              LEFT_PARENTHESIS,
              RuleEngine.DefaultSchemaUnit,
              RuleEngine.DefaultSchemaUnit,
              RIGHT_PARENTHESIS,
              {
                ...RuleEngine.DefaultOperateUnit,
                qualifiedName,
                name: qualifiedName,
                value: valueString,
              },
              RuleEngine.DefaultSchemaUnit,
              LEFT_PARENTHESIS,
              RuleEngine.DefaultSchemaUnit,
              RuleEngine.DefaultSchemaUnit,
              RIGHT_PARENTHESIS,
            ],
          });
          break;
        default:
          break;
      }
      if (
        state.rules?.revision?.config?.cases?.[
          (action as AddFormula)?.caseIndex
        ].relation === RuleEngine.Relation.No &&
        state.rules?.revision?.config?.cases?.[
          (action as AddFormula)?.caseIndex
        ].formulas?.length > 1
      ) {
        state.rules.revision.config.cases[
          (action as AddFormula)?.caseIndex
        ].relation = RuleEngine.Relation.And;
      }
      return {
        ...state,
        rules: state.rules,
      };

    case TOGGLE_RELATION:
      const { relation } =
        state?.rules.revision.config.cases[(action as AndOr)?.caseIndex];
      state.rules.revision.config.cases[(action as AndOr)?.caseIndex].relation =
        relation === RuleEngine.Relation.And
          ? RuleEngine.Relation.Or
          : RuleEngine.Relation.And;

      return {
        ...state,
        rules: state.rules,
      };

    case TOGGLE_LIST_CASE_TYPE:
      const { cases } = state?.rules.revision.config;
      state.rules.revision.config.cases[
        (action as ToggleListCaseType)?.index
      ].caseType =
        cases[(action as ToggleListCaseType)?.index].caseType ===
        RuleEngine.CaseTypes.ListAll
          ? RuleEngine.CaseTypes.ListAny
          : RuleEngine.CaseTypes.ListAll;

      return {
        ...state,
        rules: state.rules,
      };

    case DELETE_CASE:
      state.rules.revision.config.cases.splice(
        (action as DeleteCase)?.index,
        1
      );

      return {
        ...state,
        rules: state.rules,
      };

    // 新增 if 没有 then
    case ADD_CASE:
      state.rules?.revision?.config?.cases?.push({
        relation: RuleEngine.Relation.No,
        formulas: [],
        // 页面中没有操作 case 中 then 的区域，所以 then 这一部分写死
        actions: [
          {
            actionType: RuleEngine.ActionCategory.GeneralDecision,
            decisionType: RuleEngine.UwDecision.Accept,
          },
        ],
        caseType: RuleEngine.CaseTypes.Regular,
      });
      return {
        ...state,
        rules: state.rules,
      };

    case TOGGLE_CASE_TYPE:
      if (
        state.rules.revision?.config.cases[(action as ToggleCaseType).caseIndex]
          ?.caseType === RuleEngine.CaseTypes.Regular
      ) {
        state.rules.revision.config.cases[
          (action as ToggleCaseType).caseIndex
        ].caseType = RuleEngine.CaseTypes.ListAll;
      } else {
        state.rules.revision.config.cases[
          (action as ToggleCaseType).caseIndex
        ].caseType = RuleEngine.CaseTypes.Regular;
      }

      return {
        ...state,
        rules: state.rules,
      };

    default:
      return state;
  }
};

export const Context = React.createContext<{
  state: State;
  dispatch: Dispatch<Action>;
}>(
  {} as {
    state: State;
    dispatch: Dispatch<Action>;
  }
);

export const initialContextValue = {
  rules: null as unknown as RuleEngine.Detail,
  mode: Mode.View,
  generalObjectAndLeafSchema: [],
  factorEnums: {},
};
