import { useCallback, useState, useEffect } from 'react';
import {
  UnderwritingService,
  ConfigFileHistoryType,
  RuleEngine,
  ruleService,
  UnderWritingCriteriaService,
} from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { useTranslation } from 'react-i18next';
import { messagePopup } from '@uw/util/messagePopup';
import { useRequest } from 'ahooks';

export const useGetFileList = (): [
  fileList: ConfigFileHistoryType[],
  setFileList: React.Dispatch<React.SetStateAction<ConfigFileHistoryType[]>>,
  getFileList: () => void
] => {
  const [fileList, setFileList] = useState<ConfigFileHistoryType[]>([]);
  const getFileList = useCallback(() => {
    UnderwritingService.getConfigFileHistory('MEDICAL_EXAMINATION_ITEM').then(
      res => {
        setFileList(res || []);
      }
    );
  }, []);
  useEffect(() => {
    getFileList();
  }, []);
  return [fileList, setFileList, getFileList];
};

export const useDeleteFile = (
  getFileList: () => void
): [(id: number) => void] => {
  const [t] = useTranslation(['uw', 'common']);
  const deleteFile = useCallback((id: number) => {
    UnderwritingService.deleteFileHistory('MEDICAL_EXAMINATION_ITEM', id).then(
      () => {
        messagePopup(t('Delete successfully'), 'success');
        getFileList();
      }
    );
  }, []);

  return [deleteFile];
};

export const useExport = (): [
  handleExport: (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void
] => {
  const [t] = useTranslation(['uw', 'common']);
  const handleExport = useCallback<
    (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void
  >(() => {
    UnderwritingService.downloadMedical('MEDICAL_EXAMINATION_ITEM')
      .then(response => {
        downloadFile(response).then(() => {
          messagePopup(t('Download successfully'), 'success');
        });
      })
      .catch((error: Error) => {
        messagePopup(error.message || t('Download failed'), 'error');
      });
  }, []);
  return [handleExport];
};

export const useUploadFile = (
  getFileList: () => void
): [handleUploadFiles: (formData: FormData) => void] => {
  const [t] = useTranslation(['uw', 'common']);
  const handleUploadFiles = (formData: FormData) => {
    UnderwritingService.uploadMedicalPlanFile(formData)
      .then(() => {
        messagePopup(t('Upload Successfully'), 'success');
        getFileList();
      })
      .catch(() => {
        messagePopup('Upload Failed', 'error');
      });
  };
  return [handleUploadFiles];
};

export const useExportFile = (
  getFileList: () => void
): [(id: string) => void] => {
  const [t] = useTranslation(['uw', 'common']);
  const exportFile = useCallback((fileUniqueCode: string) => {
    UnderwritingService.exportFileHistory(fileUniqueCode)
      .then(response => {
        downloadFile(response).then(() => {
          messagePopup(t('Download successfully'), 'success');
        });
        getFileList();
      })
      .catch((error: Error) => {
        messagePopup(error.message || t('Download failed'), 'error');
      });
  }, []);

  return [exportFile];
};

export const useQueryRuleUnderCategory = (
  categorys: RuleEngine.RuleCategory[]
) => {
  const { data: ruleList } = useRequest(() =>
    ruleService.queryRulesUnderCategory(categorys, true)
  );
  return (ruleList || []).map(rule => ({
    value: rule.code,
    label: rule.name,
  }));
};

export const useQueryCriteriaInfo = () => {
  const { data: criteriaInfo } = useRequest(() =>
    UnderWritingCriteriaService.getCriteriaInfo()
  );
  return criteriaInfo;
};
