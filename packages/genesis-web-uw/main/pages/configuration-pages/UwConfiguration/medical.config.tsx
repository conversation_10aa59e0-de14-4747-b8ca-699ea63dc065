import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';

import { FormatMedicalPlanType } from '@uw/hook/request';
import I18nInstance from '@uw/i18n';

import styles from './config.module.scss';

const ns = {
  ns: ['uw', 'common'],
};
export const medicalConfigs = () => [
  {
    label: I18nInstance.t('Medical Plan Name', ns),
    key: 'medicalPlanName',
    col: 8,
    type: FieldType.Input,
    placeholder: I18nInstance.t('Please input'),
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please input'),
      },
    ],
  },
  {
    label: I18nInstance.t('Medical Plan Code', ns),
    key: 'medicalPlanCode',
    col: 8,
    type: FieldType.Input,
    placeholder: I18nInstance.t('Please input'),
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please input'),
      },
    ],
  },
];

export const medicalPlanColumns = () => [
  {
    title: I18nInstance.t('Medical Plan Name', ns),
    dataIndex: 'medicalPlanName',
    key: 'medicalPlanName',
    editable: true,
    width: 180,
    render: (text: string) => <div className={styles.ellipse}>{text}</div>,
  },
  {
    title: I18nInstance.t('Medical Plan Code', ns),
    dataIndex: 'medicalPlanCode',
    key: 'medicalPlanCode',
    editable: true,
    width: 180,
    render: (text: string) => <div className={styles.ellipse}>{text}</div>,
  },
  {
    title: I18nInstance.t('Medical Examination Item', ns),
    dataIndex: 'examinationItemRelationList',
    key: 'examinationItemRelationList',
    editable: true,
    render: (value: string, record: FormatMedicalPlanType) =>
      record.examinationItemRelationList
        ? record.examinationItemRelationList?.reduce(
            (cur, next) =>
              `${cur}${cur.length > 0 ? ', ' : ''}${next.itemName}`,
            ''
          )
        : I18nInstance.t('--', ns),
  },
];
