// 添加exclusion
import { StatusTypeEnum } from '@uw/util/getStatusTagType';
import { message } from 'antd';
import { QueryExclusionType, UnderwritingService } from 'genesis-web-service';
import { cloneDeep } from 'lodash-es';

export const handleSaveExclusion = async (
  data: QueryExclusionType & { key?: string }
) => {
  try {
    const params = { ...data };
    delete params.key;
    let saveRuleResults;
    const delId = params.id;
    if (delId) {
      const editParams = cloneDeep(params);
      delete editParams.id;
      saveRuleResults = await UnderwritingService.updateExclusion(
        {
          ...editParams,
        },
        delId
      );
    } else {
      saveRuleResults = await UnderwritingService.saveExclusion({
        ...params,
      });
    }
    return saveRuleResults;
  } catch (e: any) {
    message.error({
      content: e.message,
      prefixCls: 'antd-uw-message',
    });
    return StatusTypeEnum.Error;
  }
};

export const handleDeleteExclusion = async (
  index: number,
  exclusionItem: (QueryExclusionType & {
    key: string;
  })[]
) => {
  try {
    const deleteItem = exclusionItem[index];
    const deleteRuleResults = await UnderwritingService.deleteExclusion(
      deleteItem.id
    );
    return deleteRuleResults;
  } catch (e: any) {
    message.error({
      content: e.message,
      prefixCls: 'antd-uw-message',
    });
  }
};
