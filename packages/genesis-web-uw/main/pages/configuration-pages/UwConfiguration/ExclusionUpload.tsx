import React, { FC, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Upload, message } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';

import type { UploadRequestOption } from 'rc-upload/lib/interface';

import { UnderwritingService } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useGetExclusion } from '@uw/hook/request';
import { UploadStatus } from '@uw/interface/enum.interface';

const ExclusionUpload: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const { queryExclusion } = useGetExclusion();
  const [fileList, setFileList] = useState<Array<UploadFile>>([]);
  const handleUploadFiles = useCallback<
    (formData: FormData, filename: string) => void
  >((formData, filename) => {
    UnderwritingService.importExclusion(formData)
      .then(res => {
        queryExclusion();
        message.success({
          content: t('Upload Successfully'),
          prefixCls: 'antd-uw-message',
        });
        setFileList([
          {
            uid: res.fileUniqueCode,
            name: filename,
            status: UploadStatus.Done,
            url: res.fileUrl,
          },
        ]);
      })
      .catch(e => {
        message.error({
          content: e.message,
          prefixCls: 'antd-uw-message',
        });
      });
  }, []);
  const uploadProps = useMemo<UploadProps>(
    () => ({
      name: 'file',
      accept: '.xls, .xlsx',
      multiple: false,
      fileList,
      customRequest: (cb: UploadRequestOption) => {
        const formData = new FormData();
        formData.append('file', cb.file);
        handleUploadFiles(formData, (cb.file as File).name);
      },
    }),
    [fileList]
  );
  const handleExport = useCallback<
    (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void
  >(() => {
    UnderwritingService.exportExclusionConfiguration()
      .then(response => {
        downloadFile(response).then(() => {
          message.success(t('Download successfully'));
        });
      })
      .catch((error: Error) => {
        message.error(error.message || t('Download failed'));
      });
  }, []);
  return (
    <div className="flex gap-md">
      <Button icon={<DownloadOutlined />} onClick={handleExport}>
        {t('Download')}
      </Button>
      <Upload {...uploadProps} fileList={[]}>
        <Button icon={<UploadOutlined />}>Upload</Button>
      </Upload>
    </div>
  );
};
export default ExclusionUpload;
