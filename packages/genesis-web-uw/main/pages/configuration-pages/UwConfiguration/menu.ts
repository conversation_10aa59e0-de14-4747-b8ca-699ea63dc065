import { i18nFn } from '@uw/util/i18nFn';

export const menuData = [
  {
    key: 'medical-plan',
    label: i18nFn('Medical Plan'),
    path: 'medical-plan',
  },
  {
    key: 'exclusion',
    label: i18nFn('Exclusion'),
    path: 'exclusion',
  },
  {
    key: 'authority',
    label: i18nFn('Underwriting Authority'),
    path: 'Authority',
  },
  {
    key: 'substahdard-code',
    label: i18nFn('Sub-standard Code'),
    path: 'substahdard-code',
  },
  {
    key: 'criteria',
    label: i18nFn('Underwriting Criteria'),
    path: 'Criteria',
  },
];

export const uwConfigurationMenu = [
  {
    title: i18nFn('Underwriting Configuration'),
    key: 'uwConfiguration',
    parentKey: null,
    level: 1,
  },
  {
    title: i18nFn('Medical Plan'),
    key: 'medical-plan',
    parentKey: 'uwConfiguration',
    level: 2,
    path: 'medical-plan',
  },
  {
    title: i18nFn('Exclusion'),
    key: 'exclusion',
    parentKey: 'uwConfiguration',
    level: 2,
    path: 'exclusion',
  },
  {
    title: i18nFn('Underwriting Authority'),
    key: 'authority',
    parentKey: 'uwConfiguration',
    level: 2,
    path: 'Authority',
  },
  {
    title: i18nFn('Sub-standard Code'),
    key: 'substahdard-code',
    parentKey: 'uwConfiguration',
    level: 2,
    path: 'substahdard-code',
  },
  {
    title: i18nFn('Underwriting Criteria'),
    key: 'criteria',
    parentKey: 'uwConfiguration',
    level: 2,
    path: 'Criteria',
  },
];
