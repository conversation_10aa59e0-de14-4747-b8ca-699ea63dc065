import { Icon, TextBody } from '@zhongan/nagrand-ui';
import { Button } from 'antd';
import { capitalize } from 'lodash-es';
import { FC, useEffect, useMemo } from 'react';
import { useBizModule } from '@uw/hook/useBizModule';
import { GenerationRuleEnum } from '@uw/interface/enum.interface';
import { Divider } from 'genesis-web-component/lib/components';
import { Mode } from "genesis-web-component/lib/interface";

import { useBizDict } from '@uw/hook/useBizDict';
import { useTranslation } from 'react-i18next';

import { RuleCard } from './components/RuleCard';
import { CopyRuleDrawer } from './components/CopyRuleDrawer';
import { RuleEditDrawer } from './components/RuleEditDrawer';
import { useQueryBusinessNoRules } from './hooks/request';
import { usePathToSelectedKey } from './hooks/usePathToSelectedKey';
import { useDrawerOpt } from './hooks/useDrawerOpt';

import styles from './BusinessNoGenerationRule.module.scss';

const NewBusiness: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const biznoRuleTypeEnums = useBizDict('biznoRuleType');
  const { hasEditAuth } = useBizModule(GenerationRuleEnum.Policy);
  const [selectedKey] = usePathToSelectedKey();
  const {
    ruleTypeListMap,
    queryBusinessNoRules,
    ruleTypeListMapV2,
    queryBusinessNoRulesV2,
  } = useQueryBusinessNoRules();
  const {
    mode,
    showRuleDrawer,
    showCopyDrawer,
    ruleNoType,
    handleTriggerCard,
    handleClose,
    groupIndex,
  } = useDrawerOpt();

  useEffect(() => {
    queryBusinessNoRulesV2(selectedKey);
    queryBusinessNoRules(selectedKey);
  }, [selectedKey]);

  const drawerDetail = useMemo(() => {
    if (mode === Mode.Add) {
      return [];
    }
    if (String(ruleNoType) === '507' || String(ruleNoType) === '508') {
      const currentGroup = ruleTypeListMapV2?.[Number(ruleNoType)]?.group?.[groupIndex];
      if (currentGroup?.ruleDetailList?.length) {
        return currentGroup.ruleDetailList.map(item => ({
          ...item,
          ruleGroupCode: currentGroup.ruleGroupCode,
          isDefault: currentGroup.isDefault ?? false,
          businessNoList: currentGroup.businessNoList ?? [],
        }))
      }
      return [];
    }
    return ruleTypeListMap?.[+(ruleNoType as string)]?.ruleDetailList ?? [];
  }, [mode, ruleNoType, ruleTypeListMap, ruleTypeListMapV2, groupIndex]);

  const curRuleTypes = useMemo(
    () => biznoRuleTypeEnums?.filter(item => item.itemExtend1 === selectedKey),
    [biznoRuleTypeEnums, selectedKey]
  );
  const handleQueryDetail = () => {
    queryBusinessNoRules(selectedKey);
    queryBusinessNoRulesV2(selectedKey);
  };

  return (
    <>
      <TextBody
        type="h5"
        weight={600}
        style={{ padding: styles.gapBig, paddingBottom: styles.gapLg }}
      >
        {t('New Business')}
      </TextBody>
      <div className={styles.ruleBody}>
        {curRuleTypes?.map(({ enumItemName, dictValueName }) => {
          if (enumItemName === '507' && ruleTypeListMapV2?.[507]) {
            const { group } = ruleTypeListMapV2?.[507];
            return (
              <div>
                {group.map((groupItem, index) => (
                  <RuleCard
                    title={{
                      title: dictValueName as string,
                      enumItemName: enumItemName as string,
                    }}
                    cardDetail={groupItem}
                    handleTriggerCard={handleTriggerCard}
                    hasEditAuth={hasEditAuth}
                    isGroup
                    groupIndex={index}
                    handleQueryDetail={handleQueryDetail}
                    groupData={ruleTypeListMapV2?.[507]}
                  />
                ))}
                <Button
                  icon={<Icon type="add" />}
                  onClick={() => handleTriggerCard(Mode.Add, enumItemName, group?.length > 0 ? 0 : -1)}
                >
                  {t('Add')}
                </Button>
                <Divider category="body" />
              </div>
            )
          }
          if (enumItemName === '508' && ruleTypeListMapV2?.[508]) {
            const { group } = ruleTypeListMapV2?.[508];
            return (
              <div>
                {group.map((groupItem, index) => (
                  <RuleCard
                    title={{
                      title: dictValueName as string,
                      enumItemName: enumItemName as string,
                    }}
                    cardDetail={groupItem}
                    handleTriggerCard={handleTriggerCard}
                    hasEditAuth={hasEditAuth}
                    isGroup
                    groupIndex={index}
                    handleQueryDetail={handleQueryDetail}
                    groupData={ruleTypeListMapV2?.[508]}
                  />
                ))}
                <Button
                  icon={<Icon type="add" />}
                  onClick={() => handleTriggerCard(Mode.Add, enumItemName, group?.length > 0 ? 0 : -1)}
                >
                  {t('Add')}
                </Button>
                <Divider category="body" />
              </div>
            )
          }
          return (
            <RuleCard
              title={{
                title: dictValueName as string,
                enumItemName: enumItemName as string,
              }}
              cardDetail={ruleTypeListMap?.[+enumItemName]}
              handleTriggerCard={handleTriggerCard}
              hasEditAuth={hasEditAuth}
            />
          )
        })}
      </div>
      <RuleEditDrawer
        mode={mode}
        disabled={mode === Mode.View}
        title={t(capitalize(mode))}
        detail={drawerDetail}
        handleClose={handleClose}
        showDrawer={showRuleDrawer}
        ruleType={ruleNoType ?? ''}
        handleQueryDetail={handleQueryDetail}
        groupIndex={groupIndex}
        groupData={ruleNoType ? ruleTypeListMapV2?.[ruleNoType] : {}}
      />
      <CopyRuleDrawer
        mode={mode}
        handleClose={handleClose}
        showDrawer={showCopyDrawer}
        ruleType={ruleNoType!}
        handleQueryDetail={handleQueryDetail}
      />
    </>
  );
};

export default NewBusiness;
