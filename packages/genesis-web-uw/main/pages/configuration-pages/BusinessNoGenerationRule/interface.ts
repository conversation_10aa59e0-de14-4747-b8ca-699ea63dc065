export enum RuleStrategy {
  FIXED_VALUE = 'FIXED_VALUE',
  TIME_STAMP = 'TIME_STAMP',
  SEQUENCE = 'SEQUENCE',
  MATRIX_WITH_BUSINESS_ELEMENTS = 'MATRIX_WITH_BUSINESS_ELEMENTS',
  BUSINESS_ELEMENTS = 'BUSINESS_ELEMENTS',
  CHECK_DIGIT = 'CHECK_DIGIT',
  COMBINATION_VALUES = 'COMBINATION_VALUES',
  COMBINATION_SEQUENCE = 'COMBINATION_SEQUENCE',
  PREVIOUS_POLICY = 'PREVIOUS_POLICY',
}

export enum RuleAccumulation {
  RE_ACCUMULATE_BY_TIME_STAMP = 'RE_ACCUMULATE_BY_TIME_STAMP',
}
