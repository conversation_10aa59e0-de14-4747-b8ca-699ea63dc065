.rule-content {
  width: calc(100% - 238px - 32px);
  margin: $gap-md;
  background: var(--white);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .rule-body {
    padding: $gap-big;
    padding-top: 0;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.divider-comma {
  color: var(--text-color-quaternary);
  font-weight: 700;
  margin-top: 4px;
  margin-left: 8px;
}

.rule-card-container {
  .rule-card-wrapper {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: $gap-md;
    overflow: hidden;
    .rule-card-opts {
      position: absolute;
      padding-top: 22px;
      top: 0;
      right: $gap-md;
      width: 152px;
      height: 100%;
      text-align: right;
      background: linear-gradient(
        268.15deg,
        var(--white) 60%,
        rgba(255, 255, 255, 0) 98.63%
      );
      & > * {
        margin-left: $gap-md;
      }
    }
    &:hover {
      background-color: var(--table-body-hover-bg);
      border-radius: var(--border-radius-base);
      .rule-card-opts {
        background: linear-gradient(
          268.15deg,
          var(--table-body-hover-bg) 60%,
          rgba(255, 255, 255, 0) 98.63%
        );
      }
    }
    .rule-card-content {
      display: flex;
      overflow-x: auto;
      padding-right: 152px;
      .rule-item-wrapper {
        width: 160px;
        text-align: center;
        margin-left: var(--gap-xs);
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-shrink: 0;
        overflow: hidden;
        .rule-value {
          box-sizing: border-box;
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid var(--border-line-color-secondary);
          border-radius: var(--border-radius-base);
          background-color: var(--table-body-hover-bg);
          color: var(--text-color-quaternary);
        }
        .connect-line {
          height: 10px;
          width: 1px;
          background-color: var(--border-line-color-secondary);
        }
      }
    }
  }
}

.rule-edit-drawer-container {
  :global {
    .#{$ant-prefix}-table-thead > tr > th {
      color: var(--text-color-quaternary);
      font-weight: 700;
    }
    .#{$ant-prefix}-table-thead > tr > th,
    .#{$ant-prefix}-table-tbody > tr > td {
      background-color: transparent !important;
      border-bottom: 0 !important;
    }
    .#{$ant-prefix}-table-thead
      > tr
      > th:not(:last-child):not(.#{$ant-prefix}-table-selection-column):not(
        .#{$ant-prefix}-table-row-expand-icon-cell
      ):not([colspan])::before {
      display: none;
    }
    .#{$ant-prefix}-table-tbody > tr > td {
      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(4),
      &:nth-child(5) {
        padding-top: 17px !important;
      }
      .#{$ant-prefix}-form-item {
        margin-bottom: 0;
      }
    }
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small
      .#{$ant-prefix}-table-title,
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small
      .#{$ant-prefix}-table-footer,
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small
      .#{$ant-prefix}-table-thead
      > tr
      > th,
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small
      .#{$ant-prefix}-table-tbody
      > tr
      > td,
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small tfoot > tr > th,
    .#{$ant-prefix}-table.#{$ant-prefix}-table-small tfoot > tr > td {
      padding: $gap-sm var(--gap-xs) !important;
    }
  }
}

.no-data-wrapper {
  background-color: var(--form-bg);
  border-radius: var(--border-radius-base);
}

.rule-copy-drawer-container {
  :global {
    .#{$ant-prefix}-checkbox-wrapper {
      display: flex;
      margin-bottom: $gap-md;
    }
    .#{$ant-prefix}-checkbox-group-item {
      display: inline-flex;
      width: 280px;
      margin-right: $gap-lg;
    }
  }
}
.matrix-card-content {
  display: flex;
  align-items: center;
  > span:nth-child(2) {
    display: inline-block;
    width: calc(100% - 14px);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.input-range-left {
  :global {
    .antd-uw-input-number-input {
      text-align: right;
    }
    .antd-uw-input-number-outlined {
      border-right: 0;
    }
  }
}

.input-range-right {
  :global {
    .antd-uw-input-number-group-addon {
      background-color: white;
      border-left: 0;
    }
    .antd-uw-input-number-outlined {
      border-left: 0;
    }
  }
}

:export {
  gapLg: $gap-lg;
  gapBig: $gap-big;
  gapMd: $gap-md;
  gapXss: var(--gap-xss);
  gapXs: var(--gap-xs);
  fontSizeBig: $font-size-big;
  disabledColor: var(--primary-light);
  primaryColor: var(--primary-color);
}
