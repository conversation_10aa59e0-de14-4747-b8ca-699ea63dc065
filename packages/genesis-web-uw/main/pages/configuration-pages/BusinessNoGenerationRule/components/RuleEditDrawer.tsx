import { FC, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Dropdown, Form, MenuProps, message } from 'antd';

import { Select, Table, TextBody } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import {
  BiznoDynamicRuleElementStrategyEnum,
  BiznoRuleTypeForFinanceEnum,
  BusinessNumberConfiguration,
  MarketService,
} from 'genesis-web-service';

import { CommonDrawer } from '@uw/components/CommonDrawer';
import { useBizDict } from '@uw/hook/useBizDict';
import { useDict } from '@uw/hook/useDict';

import styles from '../BusinessNoGenerationRule.module.scss';
import { useRuleColumns } from '../hooks/config';
import { handleSubmitRule } from '../hooks/request';
import { RuleStrategy } from '../interface';

export const isCombinationStrategy = (elementStrategy: RuleStrategy): boolean =>
  [RuleStrategy.COMBINATION_VALUES, RuleStrategy.COMBINATION_SEQUENCE].includes(
    elementStrategy
  );

interface Props {
  mode: Mode;
  disabled: boolean;
  title: string;
  detail?: BusinessNumberConfiguration.RuleDetailListType[];
  showDrawer: boolean;
  ruleType: string;
  groupIndex?: number;
  groupData?: BusinessNumberConfiguration.DynamicRuleListResponseV2;
  handleClose: (opt: Mode) => void;
  handleQueryDetail: () => void;
  subTitle?: string;
}

const handleRuleCoeValueMap = (
  value: BusinessNumberConfiguration.RuleDetailListType
) => {
  const { accumulationRule, reaccumulatePeriod } =
    value as BusinessNumberConfiguration.SequenceRuleValue;
  if (value.elementStrategy === RuleStrategy.SEQUENCE) {
    return {
      ...value,
      accumulationRule: reaccumulatePeriod
        ? [accumulationRule, reaccumulatePeriod]
        : [accumulationRule],
    };
  }
  if (value.elementStrategy === RuleStrategy.MATRIX_WITH_BUSINESS_ELEMENTS) {
    const matrixValue =
      value as BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue;
    return {
      ...value,
      fileUpload: {
        fileName: matrixValue.fileName,
        fileUniqueCode: matrixValue.fileUniqueCode,
        fileType: matrixValue.fileType,
      },
    };
  }
  return value;
};

export const RuleEditDrawer: FC<Props> = ({
  mode,
  disabled,
  title,
  detail = [],
  showDrawer,
  ruleType,
  groupIndex,
  groupData,
  handleClose,
  handleQueryDetail,
  subTitle,
}) => {
  const [data, setData] = useState<
    BusinessNumberConfiguration.RuleDetailListType[]
  >([]);
  const [t] = useTranslation(['uw', 'common']);
  const [visible, setVisible] = useState(false);
  const [tableSort, setTableSort] = useState(true);
  const [goodsList, setGoodsList] = useState([]);
  const [loading] = useState(false);
  const [matrixFileMap, setMatrixFileMap] =
    useState<
      Record<
        string,
        Omit<
          BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
          'factorList'
        >
      >
    >();
  const biznoDynamicRuleElementStrategyEnums = useBizDict(
    'biznoDynamicRuleElementStrategy'
  );
  const checkDigitStrategy = useBizDict('checkDigitStrategy');
  const [biznoRuleTypeMap] = useDict('biznoRuleType');
  const [form] = Form.useForm();

  const handleDelete = (index: number, filterCombination?: boolean) => {
    if (filterCombination) {
      const result = data.filter(
        ({ elementStrategy }) =>
          !isCombinationStrategy(elementStrategy as RuleStrategy)
      );
      setData(result);
      setTableSort(true);
      return;
    }
    const copyData = [...data];
    copyData.splice(index, 1);
    setData(copyData);
    setTableSort(true);
  };

  const { columns } = useRuleColumns(
    disabled,
    handleDelete,
    matrixFileMap,
    ruleType,
    form,
    checkDigitStrategy,
    data
  );

  useEffect(() => {
    if (String(ruleType) === '507' || String(ruleType) === '508') {
      MarketService.fetchGoods({
        statuses: ['TAKE_EFFECT', 'ON_SHELVES'] as unknown as string,
      }).then((res: any) => {
        setGoodsList(
          res?.length > 0
            ? res.map((item: { code: string; name: string }) => ({
                value: item.code,
                label: item.name,
              }))
            : []
        );
      });
    }
  }, [ruleType]);

  useEffect(() => {
    setData(detail);
    if (visible) {
      const detailMap = detail.reduce(
        (cur, next) => ({
          ...cur,
          [`${next.elementStrategy}-${next.sortNumber}`]:
            handleRuleCoeValueMap(next),
        }),
        {}
      );
      const firstDetailItem =
        detail?.[0] as BusinessNumberConfiguration.ProposalNoRule;
      if (['507', '508', '601'].includes(String(ruleType))) {
        form.setFieldValue('isDefault', firstDetailItem?.isDefault ?? false);
        if (firstDetailItem?.ruleGroupCode) {
          form.setFieldValue('businessNoList', firstDetailItem.businessNoList);
          form.setFieldValue('ruleGroupCode', firstDetailItem.ruleGroupCode);
        }
        if (mode === Mode.Add && groupIndex === -1) {
          form.setFieldValue('isDefault', true);
        }
      }
      form.setFieldsValue(detailMap);
      const fileMap: Record<
        string,
        Omit<
          BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
          'factorList'
        >
      > = {};
      Object.entries(detailMap).forEach(([formKey, value]) => {
        const matrixValue = value as Record<string, Record<string, unknown>>;
        if (matrixValue.fileUpload) {
          fileMap[formKey] = matrixValue.fileUpload as Omit<
            BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
            'factorList'
          >;
        }
      });
      setMatrixFileMap(fileMap);
    }
  }, [detail, form, visible, ruleType, mode, groupIndex]);

  const ruleStrategyList = useMemo(
    () =>
      biznoDynamicRuleElementStrategyEnums
        ?.filter(({ enumItemName }) =>
          // finance 类型 Invoice Credit Note No 可配置类型比其他的少
          ruleType === BiznoRuleTypeForFinanceEnum.InvoiceCreditNoteNo
            ? enumItemName !==
                BiznoDynamicRuleElementStrategyEnum.BusinessElements &&
              enumItemName !==
                BiznoDynamicRuleElementStrategyEnum.MatrixWithBusinessElements
            : true
        )
        .filter(({ enumItemName }) =>
          enumItemName === BiznoDynamicRuleElementStrategyEnum.PreviousPolicy
            ? ruleType === BiznoRuleTypeForFinanceEnum.Renewal
            : ruleType !== BiznoRuleTypeForFinanceEnum.Renewal ||
              enumItemName === BiznoDynamicRuleElementStrategyEnum.FixedValue ||
              enumItemName ===
                BiznoDynamicRuleElementStrategyEnum.BusinessElements
        )
        .filter(
          ({ enumItemName }) =>
            enumItemName !== RuleStrategy.COMBINATION_SEQUENCE
        )
        ?.map(({ enumItemName, dictValueName }) => ({
          key: enumItemName,
          label: dictValueName,
          disabled:
            enumItemName === RuleStrategy.COMBINATION_VALUES &&
            !!data.find(
              item => item.elementStrategy === RuleStrategy.COMBINATION_VALUES
            ),
        })),
    [biznoDynamicRuleElementStrategyEnums, ruleType, data]
  );

  const handleCloseDrawer = () => {
    handleClose(mode as Mode);
    setTableSort(true);
    form.resetFields();
  };

  const handleMenuSelect: MenuProps['onClick'] = ({ key }) => {
    const sortNumberList = data.map(item => item.sortNumber);
    const sortNumber = !sortNumberList?.[0]
      ? 1
      : Math.max(...sortNumberList) + 1;
    form.resetFields([`${key}-${sortNumber}`]);
    const newData = {
      sortNumber,
      elementStrategy: key,
    } as BusinessNumberConfiguration.RuleDetailListType;

    if (key === RuleStrategy.COMBINATION_VALUES) {
      form.setFieldValue(
        [`${RuleStrategy.COMBINATION_SEQUENCE}-${sortNumber + 1}`],
        {
          targetRuleNo: data.length + 1,
          combinationRule: 'RE_ACCUMULATE_AND_ACCUMULATE_TARGET_SEQUENCE',
        }
      );
      setData([
        ...data,
        newData,
        {
          sortNumber: sortNumber + 1,
          elementStrategy: RuleStrategy.COMBINATION_SEQUENCE,
        } as BusinessNumberConfiguration.RuleDetailListType,
      ]);
    } else {
      setData([...data, newData]);
    }

    if (key === RuleStrategy.CHECK_DIGIT) {
      setTableSort(false);
    }
  };

  useEffect(() => {
    setVisible(showDrawer);
  }, [showDrawer]);

  return (
    <CommonDrawer
      title={title}
      visible={visible}
      handleCloseDrawer={handleCloseDrawer}
      width="1096px"
      action={
        <>
          <Button onClick={handleCloseDrawer}>{t('Cancel')}</Button>
          <Button
            type={'primary'}
            disabled={disabled}
            onClick={async () => {
              if (['507', '508', '601'].includes(String(ruleType))) {
                const isDefault = form.getFieldValue('isDefault');
                if (
                  isDefault &&
                  data?.length === 0 &&
                  groupData?.group &&
                  groupData.group.length > 1
                ) {
                  return message.warning(
                    'This is the default. Please remove others first.'
                  );
                }
              }
              await form.validateFields();
              await handleSubmitRule(+ruleType, data, form);
              handleQueryDetail();
              handleCloseDrawer();
            }}
            style={{ marginLeft: styles.gapMd }}
            htmlType="submit"
            loading={loading}
          >
            {t('Submit')}
          </Button>
        </>
      }
    >
      <div className={styles.ruleEditDrawerContainer}>
        <TextBody weight={700} style={{ marginBottom: 'var(--gap-xs)' }}>
          {subTitle ??
            t('{{ruleTitle}} Generation Rule', {
              ruleTitle: biznoRuleTypeMap?.[ruleType as string],
            })}
        </TextBody>
        <Form form={form} name="ruleForm" disabled={disabled}>
          <Form.Item name="isDefault" noStyle></Form.Item>
          <Form.Item name="ruleGroupCode" noStyle></Form.Item>
          {['507', '508'].includes(String(ruleType)) &&
            ((mode === Mode.Add && groupIndex !== -1) ||
              (mode !== Mode.Add &&
                form.getFieldValue('isDefault') !== true)) && (
              <Form.Item
                colon={false}
                name="businessNoList"
                label={t('Specified Applicable Goods')}
                style={{ marginTop: 'var(--gap-md)' }}
                rules={[
                  {
                    required: true,
                    message: t(
                      'A general rule without a specified Goods already exists. Please select a Goods.'
                    ),
                  },
                ]}
              >
                <Select
                  allowClear
                  mode="multiple"
                  style={{ width: 340 }}
                  options={goodsList}
                />
              </Form.Item>
            )}
          {String(ruleType) === '601' &&
            ((mode === Mode.Add && groupIndex !== -1) ||
              (mode !== Mode.Add &&
                form.getFieldValue('isDefault') !== true)) && (
              <Form.Item
                colon={false}
                name="businessNoList"
                label={t('Business Scenario')}
                style={{ marginTop: 'var(--gap-md)' }}
                rules={[{ required: true, message: t('Please select') }]}
              >
                <Select
                  allowClear
                  style={{ width: 340 }}
                  options={[
                    { value: 'pos', label: t('Policy Change') },
                    { value: 'claim', label: t('CLAIM') },
                    { value: 'bcp', label: t('BCP') },
                  ]}
                />
              </Form.Item>
            )}
          <Table
            size="small"
            bordered={false}
            dataSource={data}
            columns={columns}
            rowKey={'sortNumber'}
            pagination={false}
            draggable={true}
            setDataSource={setData}
          />
        </Form>
        <Dropdown
          placement="bottomLeft"
          disabled={!tableSort || disabled}
          menu={{ items: ruleStrategyList ?? [], onClick: handleMenuSelect }}
        >
          <Button style={{ marginTop: 'var(--gap-sm)' }}>{t('+ Add')}</Button>
        </Dropdown>
      </div>
    </CommonDrawer>
  );
};
