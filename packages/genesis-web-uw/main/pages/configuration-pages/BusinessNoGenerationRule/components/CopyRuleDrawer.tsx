import { CommonDrawer } from '@uw/components/CommonDrawer';
import { Button, Checkbox, CheckboxOptionType } from 'antd';
import { Modal } from '@zhongan/nagrand-ui';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useBizDict } from '@uw/hook/useBizDict';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { useDict } from '@uw/hook/useDict';

import { Divider } from 'genesis-web-component/lib/components';

import { handleSubmitCopy, handleSubmitCopyV2 } from '../hooks/request';
import styles from '../BusinessNoGenerationRule.module.scss';

const CheckboxGroup = Checkbox.Group;

interface Props {
  mode: Mode;
  showDrawer: boolean;
  ruleType: string;
  handleClose: (opt: Mode) => void;
  handleQueryDetail: () => void;
}

export const CopyRuleDrawer: FC<Props> = ({
  mode,
  showDrawer,
  ruleType,
  handleClose,
  handleQueryDetail,
}) => {
  const [biznoRuleModuleEnumMap] = useDict('biznoRuleModule');
  const [visible, setVisible] = useState(false);
  const biznoRuleTypeEnums = useBizDict('biznoRuleType');
  const [t] = useTranslation(['uw', 'common']);
  const [checkedListMap, setCheckedListMap] = useState<
    Record<string, CheckboxValueType[]>
  >({});
  const [indeterminate, setIndeterminate] = useState<Record<string, boolean>>();
  const [checkAll, setCheckAll] = useState<Record<string, boolean>>();

  useEffect(() => {
    setVisible(showDrawer);
  }, [showDrawer]);

  const ruleTypeMap = useMemo(
    () =>
      biznoRuleTypeEnums?.reduce(
        (cur, next) => ({
          ...cur,
          [next.itemExtend1 as string]: [
            ...(cur[next.itemExtend1 as string] ?? []),
            {
              value: next.enumItemName,
              label: next.itemName,
            },
          ],
        }),
        {} as Record<string, CheckboxOptionType[]>
      ),
    [biznoRuleTypeEnums]
  );

  const onChange = useCallback(
    (
      list: CheckboxValueType[],
      ruleModule: string,
      ruleTypes: CheckboxOptionType[]
    ) => {
      setCheckedListMap({
        ...checkedListMap,
        [ruleModule]: list,
      });
      setIndeterminate({
        ...indeterminate,
        [ruleModule]: !!list.length && list.length < ruleTypes.length,
      });
      setCheckAll({
        ...checkAll,
        [ruleModule]: list.length === ruleTypes.length,
      });
    },
    [checkAll, checkedListMap, indeterminate]
  );

  const onCheckAllChange = useCallback(
    (
      e: CheckboxChangeEvent,
      ruleModule: string,
      ruleTypes: CheckboxOptionType[]
    ) => {
      const values = ruleTypes.map(rule => rule.value);
      setCheckedListMap(
        e.target.checked
          ? {
              ...checkedListMap,
              [ruleModule]: values,
            }
          : {
              ...checkedListMap,
              [ruleModule]: [],
            }
      );
      setIndeterminate({
        ...indeterminate,
        [ruleModule]: false,
      });
      setCheckAll({
        ...checkAll,
        [ruleModule]: e.target.checked,
      });
    },
    [checkAll, checkedListMap, indeterminate]
  );

  const handleClear = useCallback(() => {
    setIndeterminate(undefined);
    setCheckAll(undefined);
    setCheckedListMap({});
  }, []);

  const handleCloseDrawer = useCallback(() => {
    handleClear();
    handleClose(mode as Mode);
  }, [handleClear, handleClose, mode]);

  const targetRuleList = useMemo<number[]>(
    () =>
      Object.values(checkedListMap).reduce<number[]>(
        (cur, next) => [
          ...cur,
          ...(next as number[]).map(ruleCode => +ruleCode),
        ],
        []
      ),
    [checkedListMap]
  );

  return (
    <CommonDrawer
      title={t('Copy To')}
      visible={visible}
      handleCloseDrawer={handleCloseDrawer}
      width="1096px"
      action={
        <>
          <Button onClick={handleCloseDrawer}>{t('Cancel')}</Button>
          <Button type="primary" ghost onClick={handleClear} style={{ marginLeft: styles.gapMd }}>
            {t('Clear')}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginLeft: styles.gapMd }}
            onClick={() => {
              Modal.warning({
                okCancel: true,
                okText: t('Confirm'),
                cancelText: t('Cancel'),
                content: t('Changes made after submission will not be revoked.'),
                onOk: async () => {
                  if (['507', '508', '601'].includes(String(ruleType))) {
                    await handleSubmitCopyV2(+ruleType, targetRuleList);
                  } else {
                    await handleSubmitCopy(+ruleType, targetRuleList);
                  }
                  handleQueryDetail();
                  handleCloseDrawer();
                },
              })
            }}
          >
            {t('Submit')}
          </Button>
        </>
      }
    >
      <div className={styles.ruleCopyDrawerContainer}>
        {Object.entries(ruleTypeMap ?? {})?.map(([ruleModule, ruleTypes]) => (
          <>
            <Checkbox
              indeterminate={indeterminate?.[ruleModule]}
              onChange={e => {
                onCheckAllChange(e, ruleModule, ruleTypes);
              }}
              checked={checkAll?.[ruleModule]}
            >
              {biznoRuleModuleEnumMap?.[ruleModule]}
            </Checkbox>
            <CheckboxGroup
              options={ruleTypes}
              value={checkedListMap?.[ruleModule] ?? []}
              onChange={list => {
                onChange(list, ruleModule, ruleTypes);
              }}
            />
            <Divider category="body" />
          </>
        ))}
      </div>
    </CommonDrawer>
  );
};
