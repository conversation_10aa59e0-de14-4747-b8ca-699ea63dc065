import { Icon, TextBody, TextEllipsisDetect, DeleteAction } from '@zhongan/nagrand-ui';

import { FC, useMemo } from 'react';
import { Tooltip, message } from 'antd';
import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';

import { Divider } from 'genesis-web-component/lib/components';

import { BusinessNumberConfiguration, BusinessNumberConfigurationService } from 'genesis-web-service';
import { useDict } from '@uw/hook/useDict';

import { NoData } from '@uw/components/NoData';

import { sortBy } from 'lodash-es';

import { NoDataGray } from '@uw/assets/new-icons';

import { Trans } from 'react-i18next';

import { TextLink } from 'genesis-web-component/lib/components/TextLink';

import styles from '../BusinessNoGenerationRule.module.scss';
import { useRuleFields } from '../hooks/config';

interface Props {
  title: {
    title: string;
    enumItemName: string;
  };
  cardDetail?: BusinessNumberConfiguration.DynamicRuleListResponse & {
    isDefault?: boolean;
    businessNoList?: string | string[];
    ruleGroupCode?: string;
  };
  handleTriggerCard: (mode: Mode, ruleNoType: string, groupIndex?: number) => void;
  hasEditAuth: boolean;
  hasCopyAuth?: boolean;
  isGroup?: boolean;
  groupIndex?: number;
  handleQueryDetail?: () => void;
  groupData?: BusinessNumberConfiguration.DynamicRuleListResponseV2;
}

const style = {
  style: {
    fontSize: styles.fontSizeBig,
    cursor: 'pointer',
  },
};

export const RuleCard: FC<Props> = props => {
  const {
    title,
    cardDetail,
    handleTriggerCard,
    hasEditAuth,
    hasCopyAuth = true,
    isGroup,
    groupIndex,
    handleQueryDetail,
    groupData,
  } = props;
  const [biznoDynamicRuleElementStrategyEnumMap] = useDict(
    'biznoDynamicRuleElementStrategy'
  );
  const { getRuleType } = useRuleFields(false, false, undefined);

  const ordedRuleDetails = useMemo(
    () => sortBy(cardDetail?.ruleDetailList, ['sortNumber']),
    [cardDetail?.ruleDetailList]
  );

  const iconStyle = useMemo(
    () => ({
      color: !hasEditAuth && styles.disabledColor,
      cursor: hasEditAuth ? 'pointer' : 'not-allowed',
    }),
    [hasEditAuth]
  );

  return (
    <div className={styles.ruleCardContainer} style={{ marginBottom: isGroup ? 'var(--gap-md)' : 'var(--gap-lg)' }}>
      {(!isGroup || groupIndex === 0) && (
        <TextBody weight={700} style={{ marginBottom: styles.gapXs }}>
          {title.title}
        </TextBody>
      )}
      {cardDetail ? (
        <div className={styles.ruleCardWrapper}>
          <section className={styles.ruleCardContent}>
            {cardDetail.businessNoList && cardDetail.businessNoList.length > 0 && (
              <>
                <div className={styles.ruleItemWrapper}>
                  <div className={styles.ruleValue}>
                    <TextEllipsisDetect
                      maxWidth={700}
                      text={
                        String(cardDetail.ruleTypeCode) === '601'
                          ? (cardDetail.businessNoList as string[]).join(', ').toUpperCase()
                          : (cardDetail.businessNoList as string[]).join(', ')
                      }
                    />
                  </div>
                  <div className={styles.connectLine}></div>
                  <TextBody
                    type={'caption'}
                    weight={500}
                    style={{ marginTop: styles.gapXs }}
                  >
                    {String(cardDetail.ruleTypeCode) === '601' ? 'Business Scenario' : 'Specified Applicable Goods'}
                  </TextBody>
                </div>
                <div className={styles.dividerComma}>
                  :
                </div>
              </>
            )}
            {ordedRuleDetails?.map(values => (
              <div
                className={styles.ruleItemWrapper}
                key={`${values.elementStrategy}${values.sortNumber}`}
              >
                <div className={styles.ruleValue}>
                  <TextBody
                    weight={700}
                    style={{
                      width: '100%',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {getRuleType(
                      values as BusinessNumberConfiguration.RuleDetailListType
                    )}
                  </TextBody>
                </div>
                <div className={styles.connectLine}></div>
                <TextBody
                  type={'caption'}
                  weight={500}
                  style={{ marginTop: styles.gapXs }}
                >
                  {
                    biznoDynamicRuleElementStrategyEnumMap[
                      values.elementStrategy
                    ]
                  }
                </TextBody>
              </div>
            ))}
          </section>
          <section className={styles.ruleCardOpts}>
            <Tooltip title={i18nFn('View')}>
              <Icon
                type="view"
                {...style}
                onClick={() => {
                  handleTriggerCard(Mode.View, title.enumItemName, groupIndex);
                }}
              />
            </Tooltip>
            <Tooltip title={i18nFn('Edit')}>
              <Icon
                type="edit"
                style={{
                  ...style.style,
                  ...iconStyle,
                }}
                onClick={() => {
                  if (!hasEditAuth) return;
                  if (groupIndex || groupIndex === 0) {
                    handleTriggerCard(Mode.Edit, title.enumItemName, groupIndex);
                    return;
                  }
                  handleTriggerCard(Mode.Edit, title.enumItemName);
                }}
              />
            </Tooltip>
            {hasCopyAuth && (!isGroup || cardDetail.isDefault) && (
              <Tooltip title={i18nFn('Copy')}>
                <Icon
                  type="copy"
                  style={{
                    ...style.style,
                    ...iconStyle,
                  }}
                  onClick={() => {
                    if (!hasEditAuth) return;
                    if (groupIndex || groupIndex === 0) {
                      handleTriggerCard(Mode.Copy, title.enumItemName, groupIndex);
                      return;
                    }
                    handleTriggerCard(Mode.Copy, title.enumItemName);
                  }}
                />
              </Tooltip>
            )}
            {isGroup && (
              <DeleteAction
                tooltipTitle="Popconfim"
                doubleConfirmType="popconfirm"
                onClick={() => {
                  if (cardDetail.isDefault && groupData?.group && groupData.group.length > 1) {
                    return message.warning('This is the default. Please remove others first.');
                  }
                  BusinessNumberConfigurationService.submitGenerateRuleV2({
                    ...cardDetail,
                    ruleDetailList: [],
                  } as any).then(() => {
                    handleQueryDetail?.();
                  })
                }}
              />
            )}
          </section>
        </div>
      ) : (
        <div className={styles.noDataWrapper}>
          <NoData
            icon={NoDataGray}
            imgSize={64}
            style={{
              minHeight: '160px',
            }}
            text={
              <Trans i18nKey="configKey">
                Please{' '}
                <TextLink
                  style={{
                    color: hasEditAuth
                      ? styles.primaryColor
                      : styles.disabledColor,
                  }}
                  onClick={() => {
                    if (!hasEditAuth) return;
                    handleTriggerCard(Mode.Add, title.enumItemName, -1);
                  }}
                >
                  config
                </TextLink>{' '}
                generation rule for corresponding business no.
              </Trans>
            }
          />
        </div>
      )}
      {!isGroup && (
        <Divider category="body" />
      )}
    </div>
  );
};
