import { Icon, TextBody } from '@zhongan/nagrand-ui';

import { FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Mode, GenerationRuleEnum } from '@uw/interface/enum.interface';

import { useBizDict } from '@uw/hook/useBizDict';

import { capitalize } from 'lodash-es';
import { useBizModule } from '@uw/hook/useBizModule';

import { BiznoRuleTypeForFinanceEnum } from 'genesis-web-service';
import { Button } from 'antd';
import { Divider } from 'genesis-web-component/lib/components';
import { RuleCard } from './components/RuleCard';
import { RuleEditDrawer } from './components/RuleEditDrawer';
import { useQueryBusinessNoRules } from './hooks/request';
import { usePathToSelectedKey } from './hooks/usePathToSelectedKey';
import { useDrawerOpt } from './hooks/useDrawerOpt';
import { CopyRuleDrawer } from './components/CopyRuleDrawer';
import styles from './BusinessNoGenerationRule.module.scss';

const Finance: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const biznoRuleTypeEnums = useBizDict('biznoRuleType');
  const { hasEditAuth } = useBizModule(GenerationRuleEnum.Finance);
  const [selectedKey] = usePathToSelectedKey();
  const {
    ruleTypeListMap,
    ruleTypeListMapV2,
    queryBusinessNoRules,
    queryBusinessNoRulesV2,
  } = useQueryBusinessNoRules();
  const {
    mode,
    showRuleDrawer,
    showCopyDrawer,
    ruleNoType,
    handleTriggerCard,
    handleClose,
    groupIndex,
  } = useDrawerOpt();

  useEffect(() => {
    queryBusinessNoRules(selectedKey);
    queryBusinessNoRulesV2(selectedKey);
  }, [selectedKey]);

  const drawerDetail = useMemo(() => {
    if (mode === Mode.Add) {
      return [];
    }
    if (String(ruleNoType) === '601') {
      const currentGroup = ruleTypeListMapV2?.[Number(ruleNoType)]?.group?.[groupIndex];
      if (currentGroup?.ruleDetailList?.length) {
        return currentGroup.ruleDetailList.map(item => ({
          ...item,
          ruleGroupCode: currentGroup.ruleGroupCode,
          isDefault: currentGroup.isDefault ?? false,
          businessNoList: currentGroup.businessNoList?.length > 0 ? currentGroup.businessNoList[0] : undefined,
        }))
      }
      return [];
    }
    return ruleTypeListMap?.[+(ruleNoType as string)]?.ruleDetailList ?? [];
  }, [mode, ruleNoType, ruleTypeListMap, ruleTypeListMapV2, groupIndex]);

  const curRuleTypes = useMemo(
    () =>
      biznoRuleTypeEnums
        ?.filter(item => item.itemExtend1 === selectedKey)
        ?.filter(
          // https://jira.zaouter.com/browse/GIS-91080 先隐藏Receipt No generation rule的配置
          item => item.enumItemName !== BiznoRuleTypeForFinanceEnum.ReceiptNo
        ),
    [biznoRuleTypeEnums, selectedKey]
  );
  const handleQueryDetail = () => {
    queryBusinessNoRules(selectedKey);
    queryBusinessNoRulesV2(selectedKey);
  };
  return (
    <>
      <TextBody
        type="h5"
        weight={600}
        style={{ padding: styles.gapBig, paddingBottom: styles.gapLg }}
      >
        {t('Finance')}
      </TextBody>
      <div className={styles.ruleBody}>
        {curRuleTypes?.map(({ enumItemName, dictValueName }) => {
          if (enumItemName === '601' && ruleTypeListMapV2?.[601]) {
            const { group } = ruleTypeListMapV2?.[601];
            return (
              <div>
                {group.map((groupItem, index) => (
                  <RuleCard
                    title={{
                      title: dictValueName as string,
                      enumItemName: enumItemName as string,
                    }}
                    cardDetail={groupItem}
                    handleTriggerCard={handleTriggerCard}
                    hasEditAuth={hasEditAuth}
                    isGroup
                    groupIndex={index}
                    handleQueryDetail={handleQueryDetail}
                    groupData={ruleTypeListMapV2?.[601]}
                  />
                ))}
                <Button
                  icon={<Icon type="add" />}
                  onClick={() => handleTriggerCard(Mode.Add, enumItemName, group?.length > 0 ? 0 : -1)}
                >
                  {t('Add')}
                </Button>
                <Divider category="body" />
              </div>
            )
          }
          return (
            <RuleCard
              title={{
                title: dictValueName as string,
                enumItemName: enumItemName as string,
              }}
              cardDetail={ruleTypeListMap?.[+enumItemName]}
              handleTriggerCard={handleTriggerCard}
              hasEditAuth={hasEditAuth}
              // finance > Invoice Credit Note No 不可以copy
              hasCopyAuth={
                enumItemName !== BiznoRuleTypeForFinanceEnum.InvoiceCreditNoteNo
              }
            />
          )
        })}
      </div>
      <RuleEditDrawer
        mode={mode}
        disabled={mode === Mode.View}
        title={t(capitalize(mode))}
        detail={drawerDetail}
        handleClose={handleClose}
        showDrawer={showRuleDrawer}
        ruleType={ruleNoType ?? ''}
        handleQueryDetail={handleQueryDetail}
        groupIndex={groupIndex}
        groupData={ruleTypeListMapV2?.[601]}
      />
      <CopyRuleDrawer
        mode={mode}
        handleClose={handleClose}
        showDrawer={showCopyDrawer}
        ruleType={ruleNoType!}
        handleQueryDetail={handleQueryDetail}
      />
    </>
  );
};

export default Finance;
