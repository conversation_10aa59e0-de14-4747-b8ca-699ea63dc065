import React, { FC, Suspense } from 'react';
import { Spin } from 'antd';
import { SecondMenu } from 'genesis-web-component/lib/components/SecondMenu';
import { useTranslation } from 'react-i18next';
import { Route, Routes, useNavigate } from 'react-router-dom';
import { useMenuList } from './hooks/useMenuList';

import styles from './BusinessNoGenerationRule.module.scss';
import { usePathToSelectedKey } from './hooks/usePathToSelectedKey';
import { useBizModule } from '@uw/hook/useBizModule';

export const BusinessNoGenerationRule: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const { modulePermissionData } = useBizModule();
  const { menuData, routes } = useMenuList(modulePermissionData);
  const [selectedKey] = usePathToSelectedKey();
  const handleMenuClick = ({ keyPath }: { keyPath: string[] }) => {
    navigate(menuData?.find(menu => menu.key === keyPath[0])?.path as string);
  };

  return (
    <div style={{ display: 'flex', height: '100%' }}>
      <SecondMenu
        title={t('Business No. \n Generation Rule')}
        selectedKey={[selectedKey]}
        menuData={menuData}
        handleMenuClick={handleMenuClick}
      />
      <section className={styles.ruleContent}>
        <Routes>
          {routes.map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <Suspense
                  fallback={
                    <div
                      style={{
                        width: '100%',
                        minHeight: '500px',
                        textAlign: 'center',
                        lineHeight: '500px',
                      }}
                    >
                      <Spin size="large" />
                    </div>
                  }
                >
                  {route.component && <route.component />}
                </Suspense>
              }
            ></Route>
          ))}
        </Routes>
      </section>
    </div>
  );
};
