import { lazy, useMemo } from 'react';
import { SecondMenuDataType } from 'genesis-web-component/lib/components/SecondMenu/SecondMenu';

import { useDict } from '@uw/hook/useDict';
import { camelCase, capitalize } from 'lodash-es';
import { AppRoute } from 'genesis-web-shared/lib/redux';
import { BizModuleType } from 'genesis-web-service';

export const useMenuList = (
  modulePermissionData: BizModuleType
): {
  menuData: SecondMenuDataType[];
  routes: AppRoute[];
} => {
  const [biznoRuleModuleEnumMap] = useDict('biznoRuleModule');
  const { menuData, routes } = useMemo(() => {
    const menus: SecondMenuDataType[] = [];
    const route: AppRoute[] = [];
    Object.entries(biznoRuleModuleEnumMap ?? {})?.forEach(
      ([dictValue, i18n]) => {
        const path = dictValue.toLocaleLowerCase().replaceAll('_', '-');
        const componentName = capitalize(
          camelCase(dictValue.replaceAll('_', ' '))
        );

        if (modulePermissionData.viewModuleList.includes(dictValue)) {
          menus.push({
            key: dictValue,
            label: i18n,
            path,
          });
          route.push({
            id: dictValue,
            pageCode: dictValue,
            path,
            component: lazy(() => import(`../${componentName}`)),
          });
        }
      }
    );
    return {
      menuData: menus,
      routes: route,
    };
  }, [biznoRuleModuleEnumMap, modulePermissionData]);
  return { menuData, routes };
};
