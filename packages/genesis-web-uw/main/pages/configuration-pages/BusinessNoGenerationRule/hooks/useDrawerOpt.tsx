import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { useCallback, useState } from 'react';

const ruleEditDrawerModes = [Mode.Edit, Mode.View, Mode.Add];

export const useDrawerOpt = () => {
  const [showRuleDrawer, setShowRuleDrawer] = useState(false);
  const [showCopyDrawer, setShowCopyDrawer] = useState(false);
  const [ruleNoType, setRuleNoType] = useState<string>();
  const [groupIndex, setGroupIndex] = useState(0);
  const [mode, setMode] = useState(Mode.View);
  const handleTriggerCard = useCallback<
    (opt: Mode, ruleNoType: string, index?: number) => void
  >((opt, noType, index) => {
    setMode(opt);
    setRuleNoType(noType);
    if (index || index === 0) {
      setGroupIndex(index);
    }
    if (ruleEditDrawerModes.includes(opt)) {
      setShowRuleDrawer(true);
    } else {
      setShowCopyDrawer(true);
    }
  }, []);
  const handleClose = useCallback<(opt: Mode) => void>(opt => {
    if (ruleEditDrawerModes.includes(opt)) {
      setShowRuleDrawer(false);
    } else {
      setShowCopyDrawer(false);
    }
  }, []);

  return {
    mode,
    showRuleDrawer,
    showCopyDrawer,
    ruleNoType,
    handleTriggerCard,
    handleClose,
    groupIndex,
  };
};
