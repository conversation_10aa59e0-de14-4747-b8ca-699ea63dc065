import { messagePopup } from '@uw/util/messagePopup';
import { FormInstance } from 'antd';
import {
  BusinessNumberConfigurationService,
  BusinessNumberConfiguration,
} from 'genesis-web-service';

import { useCallback, useState } from 'react';

import { RuleStrategy } from '../interface';

export const useQueryBusinessNoRules = () => {
  const [ruleTypeListMap, setRuleTypeListMap] =
    useState<
      Record<number, BusinessNumberConfiguration.DynamicRuleListResponse>
    >();
  const [ruleTypeListMapV2, setRuleTypeListMapV2] =
    useState<
      Record<number, BusinessNumberConfiguration.DynamicRuleListResponseV2>
    >();
  const queryBusinessNoRules = useCallback<(module: string) => void>(
    async module => {
      const ruleDetails =
        await BusinessNumberConfigurationService.queryNumberDynamicRule(module);
      setRuleTypeListMap(
        ruleDetails?.reduce(
          (cur, next) => ({
            ...cur,
            [next.ruleTypeCode]: next,
          }),
          {}
        )
      );
    },
    []
  );
  const queryBusinessNoRulesV2 = useCallback<(module: string) => void>(
    async module => {
      const ruleDetails =
        await BusinessNumberConfigurationService.queryNumberDynamicRuleV2(module);
      setRuleTypeListMapV2(
        ruleDetails?.reduce((cur, next) => {
          const defaultGroupItems = (next?.group ?? []).filter(groupItem => groupItem.isDefault);
          const otherGroupItems = (next?.group ?? []).filter(groupItem => !groupItem.isDefault);
          const result = [...defaultGroupItems, ...otherGroupItems];
          return {
            ...cur,
            [next.ruleTypeCode]: {
              ...next,
              group: result,
            },
          }
        }, {})
      );
    },
    []
  );

  return { ruleTypeListMap, queryBusinessNoRules, ruleTypeListMapV2, queryBusinessNoRulesV2 };
};

export const handleSubmitRule = async (
  ruleTypeNo: number,
  data: BusinessNumberConfiguration.RuleDetailListType[],
  form: FormInstance
) => {
  const originFormData: BusinessNumberConfiguration.RuleDetailListType =
    form.getFieldsValue();
  const formData = Object.entries(originFormData).reduce(
    (cur, [key, value]) => ({
      ...cur,
      [key]: (value as Record<string, unknown>)?.fileUpload
        ? {
            ...value,
            ...((value as Record<string, unknown>).fileUpload ?? {}),
          }
        : value,
    }),
    {} as Record<string, BusinessNumberConfiguration.RuleDetailListType>
  );
  const reqData = data.map((item, index) => ({
    ...item,
    sortNumber: index + 1,
    ...(item.elementStrategy === RuleStrategy.SEQUENCE
      ? ({
          ...formData[`${item.elementStrategy}-${item.sortNumber}`],
          accumulationRule: (
            formData[
              `${item.elementStrategy}-${item.sortNumber}`
            ] as BusinessNumberConfiguration.SequenceRule
          )?.accumulationRule?.[0],
          reaccumulatePeriod: (
            formData[
              `${item.elementStrategy}-${item.sortNumber}`
            ] as BusinessNumberConfiguration.SequenceRule
          )?.accumulationRule?.[1],
        } as BusinessNumberConfiguration.SequenceRuleValue)
      : formData[`${item.elementStrategy}-${item.sortNumber}`]),
  }));
  try {
    if (['507', '508', '601'].includes(String(ruleTypeNo))) {
      const resultRuleDetailList = reqData.map(item => ({
        ...item,
        ruleGroupCode: undefined,
        businessNoList: undefined,
        isDefault: undefined,
      }))
      const proposalNoFormData = originFormData as BusinessNumberConfiguration.ProposalNoRule;
      await BusinessNumberConfigurationService.submitGenerateRuleV2({
        ruleTypeCode: ruleTypeNo,
        ruleDetailList: resultRuleDetailList,
        isDefault: proposalNoFormData.isDefault ?? false,
        businessNoList: String(ruleTypeNo) === '601' ? [proposalNoFormData.businessNoList] : proposalNoFormData.businessNoList,
        ruleGroupCode: proposalNoFormData.ruleGroupCode,
      } as any);
    } else {
      await BusinessNumberConfigurationService.submitGenerateRule({
        ruleTypeCode: ruleTypeNo,
        ruleDetailList: reqData,
      } as any);
    }
  } catch (e) {
    messagePopup((e as Error).toString(), 'error');
  }
};

export const handleSubmitCopy = async (
  sourceRuleTypeCode: number,
  targetRuleTypeCodeList: number[]
) => {
  try {
    await BusinessNumberConfigurationService.copyRule({
      sourceRuleTypeCode,
      targetRuleTypeCodeList,
    });
  } catch (e) {
    messagePopup((e as Error).toString(), 'error');
  }
};

export const handleSubmitCopyV2 = async (
  sourceRuleTypeCode: number,
  targetRuleTypeCodeList: number[]
) => {
  try {
    await BusinessNumberConfigurationService.copyRuleV2({
      sourceRuleTypeCode,
      targetRuleTypeCodeList,
    });
  } catch (e) {
    messagePopup((e as Error).toString(), 'error');
  }
};
