import { TextBody } from '@zhongan/nagrand-ui';

import { FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Mode, GenerationRuleEnum } from '@uw/interface/enum.interface';

import { useBizDict } from '@uw/hook/useBizDict';

import { capitalize } from 'lodash-es';

import styles from './BusinessNoGenerationRule.module.scss';
import { RuleCard } from './components/RuleCard';
import { RuleEditDrawer } from './components/RuleEditDrawer';
import { useQueryBusinessNoRules } from './hooks/request';
import { usePathToSelectedKey } from './hooks/usePathToSelectedKey';
import { useDrawerOpt } from './hooks/useDrawerOpt';
import { CopyRuleDrawer } from './components/CopyRuleDrawer';
import { useBizModule } from '@uw/hook/useBizModule';

const Compliance: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const biznoRuleTypeEnums = useBizDict('biznoRuleType');
  const { hasEditAuth } = useBizModule(GenerationRuleEnum.Compliance);

  const [selectedKey] = usePathToSelectedKey();
  const { ruleTypeListMap, queryBusinessNoRules } = useQueryBusinessNoRules();
  const {
    mode,
    showRuleDrawer,
    showCopyDrawer,
    ruleNoType,
    handleTriggerCard,
    handleClose,
  } = useDrawerOpt();

  useEffect(() => {
    queryBusinessNoRules(selectedKey);
  }, [selectedKey]);
  const curRuleTypes = useMemo(
    () => biznoRuleTypeEnums?.filter(item => item.itemExtend1 === selectedKey),
    [biznoRuleTypeEnums, selectedKey]
  );
  const handleQueryDetail = () => {
    queryBusinessNoRules(selectedKey);
  };
  return (
    <>
      <TextBody
        type="h5"
        weight={600}
        style={{ padding: styles.gapBig, paddingBottom: styles.gapLg }}
      >
        {t('Compliance')}
      </TextBody>
      <div className={styles.ruleBody}>
        {curRuleTypes?.map(({ enumItemName, dictValueName }) => (
          <RuleCard
            title={{
              title: dictValueName as string,
              enumItemName: enumItemName as string,
            }}
            cardDetail={ruleTypeListMap?.[+enumItemName]}
            handleTriggerCard={handleTriggerCard}
            hasEditAuth={hasEditAuth}
          />
        ))}
      </div>
      <RuleEditDrawer
        mode={mode}
        disabled={mode === Mode.View}
        title={t(capitalize(mode))}
        detail={
          ruleTypeListMap?.[+(ruleNoType as string)]?.ruleDetailList ?? []
        }
        handleClose={handleClose}
        showDrawer={showRuleDrawer}
        ruleType={ruleNoType ?? ''}
        handleQueryDetail={handleQueryDetail}
      />
      <CopyRuleDrawer
        mode={mode}
        handleClose={handleClose}
        showDrawer={showCopyDrawer}
        ruleType={ruleNoType!}
        handleQueryDetail={handleQueryDetail}
      />
    </>
  );
};

export default Compliance;
