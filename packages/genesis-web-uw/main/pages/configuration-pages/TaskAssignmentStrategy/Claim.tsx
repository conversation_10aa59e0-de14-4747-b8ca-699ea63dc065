import React, { FC, useEffect, useReducer } from 'react';

import { usePermission } from '@uw/hook/permission';

import {
  initialContextValue,
  TeamAction,
  ClaimTeamContext,
  TeamReducer,
  TeamState,
  UPDATE_AUTHINFO,
} from './TeamProvider';
import { TeamSteps } from './components/TeamSteps';
import { ContextType } from './interface/common.interface';

const Claim: FC = () => {
  const [state, dispatch] = useReducer<React.Reducer<TeamState, TeamAction>>(
    TeamReducer,
    initialContextValue
  );
  const hasClaimEditAuth = !!usePermission(
    'claim.task-assign.configuration.edit'
  );
  useEffect(() => {
    if (dispatch) {
      dispatch({ type: UPDATE_AUTHINFO, hasEditAuth: hasClaimEditAuth });
    }
  }, [dispatch, hasClaimEditAuth]);

  return (
    <ClaimTeamContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <TeamSteps type={ContextType.Claim} />
    </ClaimTeamContext.Provider>
  );
};

export default Claim;
