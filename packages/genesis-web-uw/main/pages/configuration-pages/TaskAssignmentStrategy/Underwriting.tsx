import React, { FC, useEffect, useReducer } from 'react';

import { usePermission } from '@uw/hook/permission';

import {
  initialContextValue,
  TeamAction,
  UnderwritingTeamContext,
  TeamReducer,
  TeamState,
  UPDATE_AUTHINFO,
} from './TeamProvider';
import { TeamSteps } from './components/TeamSteps';
import { ContextType } from './interface/common.interface';

const Underwriting: FC = () => {
  const [state, dispatch] = useReducer<React.Reducer<TeamState, TeamAction>>(
    TeamReducer,
    initialContextValue
  );
  const hasUnderwritingEditAuth = !!usePermission(
    'uw.task-assign.configuration.edit'
  );
  useEffect(() => {
    if (dispatch) {
      dispatch({ type: UPDATE_AUTHINFO, hasEditAuth: hasUnderwritingEditAuth });
    }
  }, [dispatch, hasUnderwritingEditAuth]);

  return (
    <UnderwritingTeamContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <TeamSteps type={ContextType.Underwriting} />
    </UnderwritingTeamContext.Provider>
  );
};

export default Underwriting;
