import { FormInstance, message } from 'antd/es';
import { BizDict, TeamTypeEnum } from '@uw/interface/enum.interface';
import {
  QueryRuleCondition,
  TaskAssignmentRuleCondition,
  TaskAssignmentService,
  StrategiesUnionType,
  YesOrNo,
  Strategy,
  StrategyList,
  TeamSummaryInfo,
  StrategiesConditionType,
  TaskAssignmentStrategy,
  StrategyConditionCategoryEnum,
  ConditionParamsType,
} from 'genesis-web-service';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { capitalize, cloneDeep } from 'lodash-es';

import { messagePopup } from '@uw/util/messagePopup';

import { i18nFn } from '@uw/util/i18nFn';

import {
  UPDATE_RULETEAMMAP,
  UPDATE_STRATEGIES,
  UPDATE_STRATEGYLIST,
  UPDATE_STRATEGYTEAMORDER,
  UPDATE_TEAM_STRATEGY_MAP,
} from '@uw/pages/configuration-pages/TaskAssignmentStrategy/TeamProvider';

import { ErrorType, SetState } from '@uw/interface/common.interface';

import useS<PERSON> from 'swr';

import {
  ContextType,
  FillConditionDetail,
  GetDictType,
  StepKey,
} from '../interface/common.interface';

import { UPDATE_RULES, UPDATE_TEAMS } from '../TeamProvider';
import { getContext } from '../utils/getContext';
import {
  TeamInfo,
  ItemStageTeamStrategies,
} from '../components/TeamManagement/types';

export const useGetDict = (
  dictKeys: string[],
  type: string
): [Record<GetDictType, BizDict[]> | undefined] => {
  const [dictsMap, setDictsMap] = useState<Record<GetDictType, BizDict[]>>();
  const getDicts = useCallback(async () => {
    const params = dictKeys.reduce((cur, next) => `${cur}&dictKey=${next}`, '');
    const originDicts = await TaskAssignmentService.getTaskDicts(type, params);
    const dicts = originDicts?.reduce(
      (cur, next) => ({
        ...cur,
        [next.dictKey]: [
          ...(cur[next.dictKey] ?? []),
          ...(next.valueItemList?.map(valueItem => ({
            enumItemName: valueItem.dictValue,
            itemName: valueItem.dictValueI18n,
            ...valueItem,
          })) ?? []),
        ],
      }),
      {} as Record<GetDictType, BizDict[]>
    );
    setDictsMap(dicts);
  }, [dictKeys, type]);

  useEffect(() => {
    getDicts();
  }, []);

  return [dictsMap];
};

export const useGetRuleConditions = (
  module: string,
  operatorDicts: BizDict[] = []
) => {
  const [ruleConditions, setRuleConditions] = useState<QueryRuleCondition[]>();
  const getRuleConditions = useCallback(async () => {
    const originRuleConditions = await TaskAssignmentService.getRuleConditions(
      module
    );
    setRuleConditions(originRuleConditions);
  }, [module]);

  const ruleConditionsBizDicts = useMemo(
    () =>
      ruleConditions?.map(ruleCondition => ({
        enumItemName: ruleCondition.factorCode,
        itemName: ruleCondition.factorNameI18n,
        dictValue: ruleCondition.factorName,
        ...ruleCondition,
        valueItems: ruleCondition.valueItems?.map(value => ({
          enumItemName: value.factorValue,
          itemName: value.factorValueNameI18n,
          dictValue: value.factorValueName,
        })),
        operators: operatorDicts.filter(operator =>
          ruleCondition.operators?.includes(operator.enumItemName as string)
        ),
      })),
    [ruleConditions, operatorDicts]
  );

  useEffect(() => {
    getRuleConditions();
  }, []);

  return [ruleConditionsBizDicts];
};

export const useGetRuleDetail = (module: string, ruleId?: number) => {
  const [ruleDetail, setRuleDetail] = useState<TaskAssignmentRuleCondition>();
  const getRuleDetail = useCallback(async () => {
    try {
      const ruleDetails = await TaskAssignmentService.queryRuleDetail(
        module,
        ruleId as number
      );
      setRuleDetail(ruleDetails);
    } catch (e) {
      // mock, to remove
    }
  }, [ruleId, module]);

  useEffect(() => {
    if (ruleId) {
      getRuleDetail();
    }
  }, [ruleId]);

  return [ruleDetail];
};

export const useSubmitRule = (module: string, ruleId?: number) => {
  const handleSubmitRule = useCallback<
    (values: TaskAssignmentRuleCondition) => void
  >(
    async values => {
      await TaskAssignmentService.saveRulesInfo(module, {
        id: ruleId,
        ...values,
      });
    },
    [module, ruleId]
  );

  return [handleSubmitRule];
};

export const useGetRulesList = (module: string) => {
  const { dispatch } = useContext(
    getContext(capitalize(module) as ContextType)
  );
  const getRuleList = useCallback(async () => {
    const rules = await TaskAssignmentService.queryRules(module);
    dispatch({ type: UPDATE_RULES, rules });
  }, []);

  useEffect(() => {
    getRuleList();
  }, []);

  return [getRuleList];
};

export const deleteRule = async (module: string, ruleId: number) => {
  try {
    await TaskAssignmentService.deleteRule(module, ruleId);
    messagePopup(i18nFn('Delete success'), 'success');
  } catch (e) {
    messagePopup((e as Error).toString(), 'error');
  }
};

export const useStrategiesInfo = (module: string) => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const { dispatch } = useContext(
    getContext(capitalize(module) as ContextType)
  );
  const getStrategiesUnion = useCallback(
    (tabActiveKey?: string) => {
      setLoading(true);
      TaskAssignmentService.strategiesUnion(module, tabActiveKey ?? '')
        .then(res => {
          dispatch({
            type: UPDATE_STRATEGYTEAMORDER,
            strategyTeamOrderList: res?.teamOrders ?? [],
          });
          dispatch({
            type: UPDATE_STRATEGIES,
            strategies: (res?.strategies ?? []).map(item => ({
              ...item,
            })) as unknown as Strategy[],
          });
        })
        .finally(() => setLoading(false));
    },
    [dispatch, module]
  );
  const submitStrategies = useCallback(
    async (formInfo: StrategiesUnionType) => {
      setSubmitLoading(true);
      return new Promise((resolve, reject) => {
        TaskAssignmentService.submitStrategies(module, formInfo)
          .then(() => {
            messagePopup(i18nFn('Submit Successfully'), 'success');
            resolve(true);
          })
          .catch(() => {
            reject();
            messagePopup(i18nFn('Submit Failed'), 'error');
          })
          .finally(() => setSubmitLoading(false));
      });
    },
    [module]
  );

  return {
    loading,
    submitLoading,
    getStrategiesUnion,
    submitStrategies,
  };
};

const getTeamsStrategyMap = (strategyLists: StrategyList[]) => {
  const strategyMap: Record<string, Strategy[]> = {};
  strategyLists.forEach(strategy => {
    strategy.strategy.teams.forEach(team => {
      const teamMap: Strategy[] = strategyMap[team.id];
      if (teamMap) {
        const itemStage = teamMap.find(
          item => item.id === strategy.strategy.id
        );
        if (!itemStage) {
          teamMap.push(strategy.strategy);
        }
      } else {
        strategyMap[team.id] = [strategy.strategy];
      }
    });
  });
  return strategyMap;
};

export const useGetStrategiesListInfo = (module: string) => {
  const [strategiesList, setStrategiesList] = useState<StrategyList[]>([]);
  const { dispatch } = useContext(
    getContext(capitalize(module) as ContextType)
  );
  const getStrategyList = useCallback(() => {
    TaskAssignmentService.strategyList(module).then(res => {
      setStrategiesList(res ?? []);
      const teamStrategyMap = getTeamsStrategyMap(res);
      dispatch({
        type: UPDATE_STRATEGYLIST,
        strategyList: res,
      });
      dispatch({
        type: UPDATE_TEAM_STRATEGY_MAP,
        teamStrategyMap,
      });
    });
  }, [module, dispatch]);

  useEffect(() => {
    getStrategyList();
  }, []);

  return {
    getStrategyList,
    strategiesList,
  };
};

export const useGetTeamsList = (module: string) => {
  const { dispatch } = useContext(
    getContext(capitalize(module) as ContextType)
  );
  const getTeamList = useCallback(async () => {
    try {
      const teams = await TaskAssignmentService.listTeams(module);
      const ruleTeamMap = teams?.reduce((cur, next) => {
        const { ruleIds } = next;
        const ruleTeamsMap = cloneDeep(cur);
        ruleIds?.forEach(curRuleId => {
          ruleTeamsMap[curRuleId] = [...(ruleTeamsMap[curRuleId] ?? []), next];
        });
        return ruleTeamsMap;
      }, {} as Record<number, TeamSummaryInfo[]>);
      dispatch({ type: UPDATE_TEAMS, teams });
      dispatch({ type: UPDATE_RULETEAMMAP, ruleTeamMap });
    } catch (e) {
      message.error(e?.toString());
    }
  }, [dispatch, module]);

  useEffect(() => {
    getTeamList();
  }, [getTeamList]);

  return [getTeamList];
};

export const useSaveTeam = () => {
  const [loading, setLoading] = useState(false);
  const saveTeam = useCallback(async (module: string, teamInfo: TeamInfo) => {
    try {
      setLoading(true);
      const result = await TaskAssignmentService.saveTeams(module, teamInfo);
      setLoading(false);
      return result;
    } catch (e) {
      setLoading(false);
      message.error(e?.toString());
    }
  }, []);
  return { saveTeam, loading };
};

export const useEditTeam = (
  setEditId: (id: string) => void,
  setVisibleKey: (visibleKey: StepKey) => void,
  form: FormInstance,
  setIsAutoFill: SetState<boolean>,
  setContentOptions: SetState<BizDict[]>,
  fillConditionsMap?: Record<string, FillConditionDetail>,
  setStageTeamStrategies?: (item: ItemStageTeamStrategies[]) => void,
  handleGetMemberList?: (values: ConditionParamsType[]) => void
) => {
  const handleEditTeams = useCallback(
    async (id: string, type: string) => {
      try {
        const teamDetail = await TaskAssignmentService.getTeamsDetail(type, id);
        // AUTO时返回的users仅为manager信息，members需要前端主动获取
        const teamMembers =
          teamDetail.memberType === TeamTypeEnum.AutomatchedFill
            ? await handleGetMemberList?.(teamDetail.teamConditions)
            : teamDetail.users;
        const teamManager = teamDetail?.users?.find(
          item => item?.isTeamManager === YesOrNo.YES
        )?.userId;
        form.setFieldsValue({
          teamName: teamDetail.teamName,
          ruleIds: teamDetail.rules.map(item => item.id),
          teamMembers,
          teamManager,
          memberType: teamDetail.memberType,
          teamConditions: teamDetail.teamConditions?.[0],
        });

        if (
          teamManager &&
          !teamMembers?.some(item => item.userId === teamManager)
        ) {
          messagePopup(
            i18nFn('Manager has been changed. Please check.'),
            'error'
          );
        }

        const curFactorCode = teamDetail.teamConditions?.[0]?.factorCode;
        setContentOptions(fillConditionsMap?.[curFactorCode]?.valueItems ?? []);
        if (teamDetail.memberType === TeamTypeEnum.AutomatchedFill) {
          setIsAutoFill(true);
        } else {
          setIsAutoFill(false);
        }

        setStageTeamStrategies?.(
          (teamDetail?.stageTeamStrategies as ItemStageTeamStrategies[]) ?? []
        );
        setEditId(id);
        setVisibleKey(StepKey.TeamStep);
      } catch (e) {
        message.error(e?.toString());
      }
    },
    [
      form,
      setEditId,
      setStageTeamStrategies,
      setVisibleKey,
      fillConditionsMap,
      handleGetMemberList,
      setContentOptions,
      setIsAutoFill,
    ]
  );
  return { handleEditTeams };
};
export const useDeleteTeam = (getTeamList: () => void) => {
  const handleDeleteTeams = useCallback(
    async (id: string, type: string) => {
      try {
        await TaskAssignmentService.deleteItemTeams(type, id);
        getTeamList();
      } catch (e) {
        message.error(e?.toString());
      }
    },
    [getTeamList]
  );
  return { handleDeleteTeams };
};

export const useGetRoleList = (type: string) =>
  useSWR(`role/list/${type}`, () => TaskAssignmentService.getRoleList(type));

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useGetStrageryList = (module: string, stage = 'underwriting') => {
  const [priorityConditionList, setPriorityConditionList] = useState<
    StrategiesConditionType[]
  >([]);
  const [oridinaryConditionList, setOridinaryConditionList] = useState<
    StrategiesConditionType[]
  >([]);
  const [totalConditionList, setTotalConditionList] = useState<
    StrategiesConditionType[]
  >([]);

  useEffect(() => {
    const getData = async () => {
      try {
        const res: StrategiesConditionType[] =
          await TaskAssignmentService.getStrategyConditionList(
            module as TaskAssignmentStrategy,
            stage
          );
        // 下方的两个sort是由于后端期望下拉选择的数据展示顺序以orderNo进行排序
        // priority可选项
        setPriorityConditionList(
          res
            ?.filter(
              item =>
                item?.category ===
                StrategyConditionCategoryEnum?.STRATEGY_PRIORITY
            )
            ?.sort((prev, next) => prev?.orderNo - next?.orderNo)
        );
        // oridition可选项
        setOridinaryConditionList(
          res
            ?.filter(
              item =>
                item?.category ===
                StrategyConditionCategoryEnum?.STRATEGY_FILTER
            )
            ?.sort((prev, next) => prev?.orderNo - next?.orderNo)
        );
        // 未分组的数据 留着备用
        setTotalConditionList(res);
      } catch (e) {
        message.error(e?.toString());
      }
    };
    getData();
  }, [module, stage]);

  return {
    priorityConditionList,
    oridinaryConditionList,
    totalConditionList,
  };
};

export const useGetFillConditionInfo = (module: string) => {
  const [fillConditions, setFillConditions] = useState<QueryRuleCondition[]>();
  const getFillConditions = useCallback(async () => {
    const originFillConditions = await TaskAssignmentService.getTeamConditions(
      module
    );
    setFillConditions(originFillConditions);
  }, [module]);

  const fillConditionsBizDicts = useMemo(
    () =>
      fillConditions?.map(fillCondition => ({
        enumItemName: fillCondition.factorCode,
        itemName: fillCondition.factorNameI18n,
        dictValue: fillCondition.factorName,
        ...fillCondition,
        valueItems: fillCondition.valueItems?.map(value => ({
          enumItemName: value.factorValue,
          itemName: value.factorValueNameI18n,
          dictValue: value.factorValueName,
        })),
      })),
    [fillConditions]
  );

  useEffect(() => {
    getFillConditions();
  }, [getFillConditions]);

  return [fillConditionsBizDicts];
};

export const useGetMemberList = (module: string) => {
  const handleGetMemberList = useCallback<
    (values: ConditionParamsType[]) => void
  >(
    async values => {
      try {
        const result = await TaskAssignmentService.getMemberListByLevelInfo(
          module,
          values
        );
        return result;
      } catch (err) {
        messagePopup((err as ErrorType).message, 'error');
      }
    },
    [module]
  );

  return [handleGetMemberList];
};
