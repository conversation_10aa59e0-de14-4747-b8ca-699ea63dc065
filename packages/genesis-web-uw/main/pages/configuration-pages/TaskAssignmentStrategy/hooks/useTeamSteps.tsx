import { StepStatusEnum } from '@uw/interface/enum.interface';
import { useContext, useMemo } from 'react';

import { ContextType } from '../interface/common.interface';

import { Step, getSteps } from '../page.config';
import { getContext } from '../utils/getContext';

export const useTeamSteps = (type: ContextType): [Step[]] => {
  const { state } = useContext(getContext(type));
  const rules = state?.rules ?? [];
  const teams = state?.teams ?? [];
  const strategyList = state?.strategyList ?? [];
  const curSteps = useMemo(
    () =>
      getSteps(type).map(step => {
        if (step.key === 'team') {
          return {
            ...step,
            total: teams.length,
            status:
              rules.length > 0 ? StepStatusEnum.Finish : StepStatusEnum.Wait,
          };
        }
        if (step.key === 'strategy') {
          return {
            ...step,
            total: strategyList.length,
            status:
              teams.length > 0 ? StepStatusEnum.Finish : StepStatusEnum.Wait,
          };
        }
        return {
          ...step,
          total: rules.length,
        };
      }),
    [type, rules.length, teams.length, strategyList.length]
  );

  return [curSteps];
};
