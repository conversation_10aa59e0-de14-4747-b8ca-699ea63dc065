import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { useMemo, useState } from 'react';

import { FormInstance } from 'antd';

import { cloneDeep } from 'lodash-es';

import { useGetDict, useGetRuleConditions } from './request';
import {
  ruleConditionFactorFields,
  ruleConditionFactorValueFields,
  ruleConditionOperatorFields,
} from '../components/rule.config';
import {
  RuleConditionDetail,
  DataTypeFieldType,
} from '../interface/common.interface';
import { convertModuleType } from '../components/convertModuleType';

export const useRuleConditionFields = (form: FormInstance, type: string) => {
  const moduleType = convertModuleType(type);
  const [dictMap] = useGetDict(['strategy', 'operator'], moduleType);
  const [ruleConditions] = useGetRuleConditions(moduleType, dictMap?.operator);
  const [operatorOptions, setOperatorOptions] = useState<BizDict[]>();
  const [factorValuesOptions, setFactorValuesOptions] = useState<BizDict[]>();

  const ruleConditionsMap = useMemo<
    Record<string, RuleConditionDetail> | undefined
  >(
    () =>
      ruleConditions?.reduce(
        (cur, next) => ({
          ...cur,
          [next.enumItemName]: next,
        }),
        {} as Record<string, RuleConditionDetail>
      ),
    [ruleConditions]
  );

  const { ruleFactorsFields, ruleOperatorFields, ruleFactorValueFields } =
    useMemo(() => {
      const factorsFields = (formIdx: number) =>
        ruleConditionFactorFields(ruleConditions, factorCode => {
          setOperatorOptions(ruleConditionsMap?.[factorCode]?.operators ?? []);
          setFactorValuesOptions(
            ruleConditionsMap?.[factorCode]?.valueItems ?? []
          );
          const formField = cloneDeep(form.getFieldValue('and'));
          formField?.splice(formIdx, 1, {
            factorCode,
            operator: undefined,
            factorValue: undefined,
          });
          form.setFieldsValue({
            and: formField,
          });
        });
      const operatorsFields = (initialFactorValue: string) =>
        ruleConditionOperatorFields(
          ruleConditionsMap?.[initialFactorValue]?.operators ??
            operatorOptions ??
            []
        );

      const factorValuesFields = (initialFactorValue: string) =>
        ruleConditionFactorValueFields(
          ruleConditionsMap?.[initialFactorValue]?.valueItems ??
            factorValuesOptions ??
            [],
          DataTypeFieldType[ruleConditionsMap?.[initialFactorValue]?.dataType]
        );

      return {
        ruleFactorsFields: factorsFields,
        ruleOperatorFields: operatorsFields,
        ruleFactorValueFields: factorValuesFields,
      };
    }, [
      ruleConditionsMap,
      ruleConditions,
      operatorOptions,
      factorValuesOptions,
      form,
    ]);

  return {
    ruleFactorsFields,
    ruleOperatorFields,
    ruleFactorValueFields,
  };
};
