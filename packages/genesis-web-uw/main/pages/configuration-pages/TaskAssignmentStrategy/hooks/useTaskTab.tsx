import { useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { TextBody } from '@uw/components/Text';
import { usePermission } from '@uw/hook/permission';
import { i18nFn } from '@uw/util/i18nFn';

export const tabs = [
  {
    label: <TextBody weight={'bold'}>{i18nFn('Underwriting')}</TextBody>,
    key: 'underwriting',
    children: '',
  },
  {
    label: <TextBody weight={'bold'}>{i18nFn('Claim')}</TextBody>,
    key: 'claim',
    children: '',
  },
  {
    label: <TextBody weight={'bold'}>{i18nFn('Policy Change')}</TextBody>,
    key: 'pos',
    children: '',
  },
];

export const useTaskTab = (childRoute: string) => {
  const hasUnderwritingViewAuth = !!usePermission(
    'uw.task-assign.configuration.view'
  );
  const hasClaimViewAuth = !!usePermission(
    'claim.task-assign.configuration.view'
  );
  const hasPosViewAuth = !!usePermission('pos.task-assign.configuration.view');
  const navigate = useNavigate();

  const tabWithAuth = useMemo(
    () => [
      ...(hasUnderwritingViewAuth ? [tabs[0]] : []),
      ...(hasClaimViewAuth ? [tabs[1]] : []),
      ...(hasPosViewAuth ? [tabs[2]] : []),
    ],
    [hasUnderwritingViewAuth, hasClaimViewAuth, hasPosViewAuth]
  );

  useEffect(() => {
    if (childRoute) {
      return;
    }
    if (hasUnderwritingViewAuth) {
      navigate('underwriting');
    } else if (hasClaimViewAuth) {
      navigate('claim');
    } else if (hasPosViewAuth) {
      navigate('pos');
    }
  }, [childRoute, hasUnderwritingViewAuth, hasClaimViewAuth, hasPosViewAuth]);

  return [tabWithAuth];
};