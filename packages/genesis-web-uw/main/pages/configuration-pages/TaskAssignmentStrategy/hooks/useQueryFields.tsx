import { FieldType, FieldDataType } from '@zhongan/nagrand-ui';
import { useTranslation } from 'react-i18next';

import { useGetRoleList } from './request';

export const useQueryFields = (moduleType: string) => {
  const { t } = useTranslation(['uw', 'common']);
  const { data: roleList } = useGetRoleList(moduleType);

  return [
    {
      label: t('Role Name'),
      key: 'roleId',
      type: FieldType.Select,
      extraProps: {
        options: roleList,
        fieldNames: { label: 'roleName', value: 'roleId' },
        optionFilterProp: 'roleName',
      },
      col: 8,
    },
    {
      label: t('User Name'),
      key: 'username',
      type: FieldType.Input,
      col: 8,
    },
    {
      label: t('Full Name'),
      key: 'fullName',
      type: FieldType.Input,
      col: 8,
    },
    {
      label: t('Email'),
      key: 'email',
      type: FieldType.Input,
      col: 8,
    },
    {
      label: t('Mobile'),
      key: 'mobile',
      type: FieldType.Input,
      col: 8,
    },
  ] as FieldDataType[];
};
