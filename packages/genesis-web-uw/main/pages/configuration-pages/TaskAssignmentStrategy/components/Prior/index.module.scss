@import '@uw/styles/variables.scss';

.drag-team {
  background: var(--item-bg-selected);
  z-index: 1999;
}

.sort-icon {
  margin-right: $gap-md;
  margin-top: var(--gap-xs);
}

.assignment-prior-sort {
  cursor: pointer;
  color: var(--text-color);
  height: 40px;
  line-height: 24px;
  font-weight: 500;
}

.prior-form-row {
  flex-wrap: nowrap;
  align-items: flex-start;
  height: 40px;
  width: 100%;
}

.delete-icon {
  margin-left: var(--gap-xs);
  margin-bottom: $gap-lg;
  margin-top: var(--gap-xs);
}

:export {
  gapLg: $gap-lg;
  gapMd: $gap-md;
  gapXs: var(--gap-xs);
}
