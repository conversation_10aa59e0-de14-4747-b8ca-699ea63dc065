import React from 'react';
import { Row, Col } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { SortableContainer, SortableElement } from 'react-sortable-hoc';

import styles from './index.module.scss';

const SortableItem = SortableElement(
  ({ value }: { value: React.ReactNode }) => (
    <Row className={styles.assignmentPriorSort}>
      <Col span={1}>
        <MenuOutlined className={styles.sortIcon} />
      </Col>
      <Col span={23}>{value}</Col>
    </Row>
  )
);

interface Props {
  list: React.ReactNode[];
}

const Prior: React.FC<Props> = ({ list }) => (
  <div style={{ width: '100%' }}>
    {list.map((listItem: React.ReactNode, index: number) => (
      <SortableItem key={`item-${index}`} index={index} value={listItem} />
    ))}
  </div>
);

export const PriorSort = SortableContainer(Prior);
