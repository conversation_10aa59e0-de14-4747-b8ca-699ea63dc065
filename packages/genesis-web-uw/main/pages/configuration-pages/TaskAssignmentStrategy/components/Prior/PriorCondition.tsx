import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { Row, Button, Form } from 'antd';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash-es';

import {
  PriorConditionProps,
  FieldsProp,
  StrategiesConditionDisabledType,
} from './interface';
import { PriorSort } from './PriorSort';
import { PriorsForm } from './PriorsForm';
import styles from './index.module.scss';

export const PriorCondition: React.FC<PriorConditionProps> = ({
  priorityConditionList,
  fieldsProp,
  formRef,
  form,
  cardInfo,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const [optionsList, setOptionsList] = useState<
    StrategiesConditionDisabledType[]
  >(priorityConditionList);

  const selectedData = useMemo(
    () =>
      cardInfo
        ? form?.getFieldValue('priors')
        : formRef?.getFieldValue('dynamicStrategyLists')?.[
            fieldsProp?.name as number
          ]?.priors,
    [
      cardInfo,
      form?.getFieldValue('priors'),
      formRef?.getFieldValue('dynamicStrategyLists'),
      fieldsProp?.name,
    ]
  );

  useEffect(() => {
    // 根据已填写数据的内容 进行实时动态判断 已添加的可选项将会被disabled掉
    const options = cloneDeep(priorityConditionList)?.map(item => ({
      ...item,
      disabled: !!selectedData?.some(
        (data: { factorCode: string }) => data?.factorCode === item?.factorCode
      ),
    }));
    setOptionsList(options);
  }, [selectedData, priorityConditionList]);

  // 是否可以新增
  const canAdd = useMemo(
    () => selectedData?.length >= optionsList?.length,
    [optionsList, selectedData]
  );

  const sortForm = useCallback(
    (list, remove, move) => {
      if (list?.length <= 1 || cardInfo) {
        return list?.map((item: FieldsProp, index: number) => (
          <PriorsForm
            key={index}
            dragable={list?.length > 1}
            index={index}
            record={item}
            remove={remove}
            optionsList={optionsList}
            cardInfo={cardInfo}
            fieldsProp={fieldsProp as FieldsProp}
            currentData={selectedData?.[item?.name] ?? {}}
          />
        ));
      }
      const sortList = list?.map((item: FieldsProp, index: number) => (
        <PriorsForm
          key={index}
          dragable={list?.length > 1}
          index={index}
          remove={remove}
          record={item}
          optionsList={optionsList}
          cardInfo={cardInfo}
          fieldsProp={fieldsProp as FieldsProp}
          currentData={selectedData?.[item?.name] ?? {}}
        />
      ));
      return (
        <PriorSort
          lockAxis={'y'}
          // 触发sort的最小移动距离
          distance={10}
          helperClass={styles.dragTeam}
          onSortEnd={({ oldIndex, newIndex }) => {
            move(oldIndex, newIndex);
          }}
          list={sortList || []}
        />
      );
    },
    [cardInfo, optionsList, fieldsProp, selectedData]
  );

  return (
    <Row
      style={{ flexDirection: 'column', position: 'relative', width: '100%' }}
    >
      <div style={{ marginBottom: styles.gapXs }}>{t('Prior condition')}</div>
      <Form.List
        {...fieldsProp}
        name={
          cardInfo ? 'priors' : [fieldsProp?.name as string | number, 'priors']
        }
      >
        {(list, { add, remove, move }) => (
          <>
            {sortForm(list, remove, move)}
            {!cardInfo && (
              <Form.Item style={{ width: '100%' }}>
                <Button
                  onClick={() => add()}
                  style={{ marginTop: styles.gapMd }}
                  disabled={canAdd}
                >
                  {t('+ Add')}
                </Button>
              </Form.Item>
            )}
          </>
        )}
      </Form.List>
    </Row>
  );
};
