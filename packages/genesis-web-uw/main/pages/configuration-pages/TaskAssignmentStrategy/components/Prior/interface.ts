import { FormInstance } from 'antd';
import {
  Strategy,
  StrategiesConditionType,
  OrdinaryRecord,
  PriorRecord,
} from 'genesis-web-service';

import { Conditions } from '../Strategy/interface';

export interface PriorFormProps {
  dragable: boolean;
  index: number;
  record: FieldsProp;
  remove: (index: number) => void;
  optionsList: StrategiesConditionDisabledType[];
  cardInfo?: CardInfo;
  fieldsProp: FieldsProp;
  currentData: {
    factorCode: string;
    factorValue: string | string[];
  };
}

export type CardInfo = Partial<
  | Strategy
  | {
      conditions: Conditions;
      priors: PriorRecord[];
      ordinaries: OrdinaryRecord[];
    }
>;

export type FieldsProp = {
  name: number | string;
  key: number | string;
  fieldKey?: number | string;
};

export interface CommonProps {
  cardInfo?: CardInfo;
  fieldsProp?: FieldsProp;
  type: string;
  formRef?: FormInstance;
  form?: FormInstance;
}

export interface PriorConditionProps extends CommonProps {
  priorityConditionList: StrategiesConditionType[];
}

export type StrategiesConditionDisabledType = StrategiesConditionType & {
  disabled?: boolean;
};
