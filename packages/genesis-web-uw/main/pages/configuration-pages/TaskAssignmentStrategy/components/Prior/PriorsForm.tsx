import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Row, Select } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import { StrategyConditionDataTypeEnum } from 'genesis-web-service';

import { Delete } from '@uw/assets/new-icons/index';

import styles from './index.module.scss';
import { PriorFormProps } from './interface';

const { Option } = Select;

export const PriorsForm: React.FC<PriorFormProps> = props => {
  const {
    record,
    optionsList,
    remove,
    index,
    dragable,
    cardInfo,
    currentData,
  } = props;
  const [t] = useTranslation(['uw', 'common']);

  const currentOption = useMemo(
    () =>
      optionsList?.find(item => item?.factorCode === currentData?.factorCode),
    [currentData?.factorCode, optionsList]
  );

  const isCurrentDataTypeNONE = useMemo(
    () => currentOption?.dataType === StrategyConditionDataTypeEnum.NONE,
    [currentOption?.dataType]
  );

  const FormField = useMemo(() => {
    let arrowForm = null;
    // 查找不到当前的相对应选项 那么就说明此时未选中状态 不需要展示
    if (!currentOption) {
      arrowForm = null;
    }
    // NUMBER 类型为 InputNumber
    if (currentOption?.dataType === StrategyConditionDataTypeEnum.NUMBER) {
      arrowForm = (
        <InputNumber
          placeholder={t('Please input')}
          style={{ width: '100%' }}
          step={1}
          min={0}
          precision={0}
        />
      );
    }
    // STRING 为 Input
    if (currentOption?.dataType === StrategyConditionDataTypeEnum.STRING) {
      arrowForm = (
        <Input placeholder={t('Please input')} style={{ width: '100%' }} />
      );
    }
    return arrowForm;
  }, [currentOption, t]);

  const span = useMemo(() => {
    // team 中查看
    if (cardInfo) {
      return 11;
    }
    if (dragable) {
      // 如果 含有子 form
      if (!isCurrentDataTypeNONE && FormField) {
        return 15;
      }
      return 21;
      // eslint-disable-next-line no-else-return
    } else {
      // 如果 含有子 form
      if (!isCurrentDataTypeNONE && FormField) {
        return 14;
      }
      return 21;
    }
  }, [FormField, cardInfo, dragable, isCurrentDataTypeNONE]);

  const childForm = useMemo(() => {
    // 当前选中的数据 dataType 为 NONE 的时候  代表没有需要级联的表单
    if (currentOption?.dataType === StrategyConditionDataTypeEnum.NONE) {
      return null;
    }
    // 如果没有相匹配的 dataType 也不展示级联表单
    if (!FormField) {
      return null;
    }
    return (
      <Col span={5} style={{ marginLeft: '20px' }}>
        <Form.Item
          {...record}
          style={{ width: '100%' }}
          name={[record?.name as string | number, 'factorValue']}
        >
          {FormField}
        </Form.Item>
      </Col>
    );
  }, [FormField, currentOption?.dataType, record]);

  const deleteIcon = useMemo(() => {
    if (!dragable) {
      return null;
    }
    if (cardInfo) {
      return null;
    }
    return (
      <Col span={1} className={styles.deleteIcon}>
        <Delete
          onClick={() => {
            remove(index);
          }}
          style={{ cursor: 'pointer' }}
        />
      </Col>
    );
  }, [cardInfo, dragable, index, remove]);

  return (
    <Row className={styles.priorFormRow} key={index}>
      {/* 如果长度为1 显示出来占位符 在长度大于1的时候用来显示 */}
      {!dragable && <Col span={1} />}
      <Col span={span}>
        <Form.Item
          {...record}
          name={[record?.name as string | number, 'factorCode']}
          style={{ marginBottom: 8 }}
        >
          <Select placeholder={t('Please select')} allowClear>
            {optionsList?.map(item => (
              <Option
                value={item?.factorCode}
                key={item?.factorCode}
                disabled={item?.disabled}
              >
                {item?.factorNameI18n}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      {childForm}
      {deleteIcon}
    </Row>
  );
};
