import { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Divider, Form, Row } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { Delete } from '@uw/assets/new-icons/index';
import { CommonDrawer } from '@uw/components/CommonDrawer';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { messagePopup } from '@uw/util/messagePopup';

import styles from '../TaskAssignmentStrategy.module.scss';
import { useRuleConditionFields } from '../hooks/config';
import { useGetRuleDetail, useSubmitRule } from '../hooks/request';
import { convertModuleType } from './convertModuleType';
import { ruleFields } from './rule.config';

interface Props {
  visible: boolean;
  type: string;
  mode: Mode;
  ruleId?: number;
  handleClose: () => void;
  getRuleList: () => void;
}

const initialFormListValue = {
  and: [
    {
      factorCode: undefined,
      operator: undefined,
      factorValue: undefined,
    },
  ],
};

export const RuleEditDrawer: FC<Props> = ({
  visible,
  type,
  mode,
  ruleId,
  handleClose,
  getRuleList,
}) => {
  const moduleType = convertModuleType(type);
  const [t] = useTranslation(['uw', 'common']);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [ruleDetail] = useGetRuleDetail(moduleType, ruleId);
  const [handleSubmitRule] = useSubmitRule(moduleType, ruleId);
  const [form] = Form.useForm();
  const { ruleFactorValueFields, ruleFactorsFields, ruleOperatorFields } =
    useRuleConditionFields(form, moduleType);
  const handleCloseDrawer = useCallback(() => {
    form.resetFields();
    handleClose();
  }, []);
  const formAnd = Form.useWatch('and', form);

  useEffect(() => {
    if (!visible) return;
    if (mode === Mode.Edit) {
      form.setFieldsValue(ruleDetail);
    } else {
      form.setFieldsValue(initialFormListValue);
    }
  }, [ruleDetail, mode, visible]);

  const handleSaveRule = useCallback(async () => {
    const values = await form.validateFields();
    if (!Object.values(values.and[0])?.[0]) {
      values.and = [];
    }

    setSubmitLoading(true);
    try {
      await handleSubmitRule({
        id: ruleId,
        ...values,
      });
      await getRuleList();
      handleCloseDrawer();
    } catch (e) {
      messagePopup((e as Error).toString(), 'error');
    }
    setSubmitLoading(false);
  }, [form, ruleId]);

  return (
    <CommonDrawer
      title={t('Task Assignment Rule')}
      visible={visible}
      handleCloseDrawer={handleCloseDrawer}
      action={
        <>
          <Button onClick={handleCloseDrawer}>{t('Cancel')}</Button>
          <Button
            type={'primary'}
            onClick={handleSaveRule}
            style={{ marginLeft: styles.gapMd }}
            htmlType="submit"
            loading={submitLoading}
          >
            {t('Submit')}
          </Button>
        </>
      }
    >
      <Form
        className={styles.ruleConditionWrapper}
        name="complex-form"
        form={form}
        layout="vertical"
      >
        <Row>
          <Col span={12} key={'ruleName'}>
            <Form.Item
              label={t('Rule Name')}
              name="ruleName"
              required={false}
              rules={[
                { required: true, message: ruleFields.placeholder as string },
              ]}
            >
              {getFields(ruleFields)}
            </Form.Item>
          </Col>
        </Row>
        <Row style={{ flexDirection: 'column', position: 'relative' }}>
          <div style={{ marginBottom: styles.gapXs }}>
            {t('Rule Condition')}
          </div>
          <Divider
            plain
            type="vertical"
            style={{
              position: 'absolute',
              height: 'calc(100% - 110px)',
              top: '30px',
              left: '16px',
            }}
          />
          <Form.List name="and">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row
                    key={key}
                    style={{
                      flexWrap: 'nowrap',
                      alignItems: 'flex-start',
                      marginLeft: '56px',
                    }}
                  >
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, 'factorCode']}
                        rules={[
                          { required: false, message: t('Please select') },
                        ]}
                      >
                        {getFields(ruleFactorsFields(name))}
                      </Form.Item>
                    </Col>
                    <Col span={11} style={{ marginLeft: styles.gapXs }}>
                      <Form.Item>
                        <Input.Group
                          compact
                          style={{
                            display: 'flex',
                          }}
                        >
                          <Form.Item
                            {...restField}
                            label=""
                            name={[name, 'operator']}
                            rules={[
                              { required: false, message: t('Please select') },
                            ]}
                            noStyle
                          >
                            {getFields(
                              ruleOperatorFields(formAnd?.[name]?.factorCode)
                            )}
                          </Form.Item>
                          <Form.Item
                            name={[name, 'factorValue']}
                            rules={[
                              { required: false, message: t('Please select') },
                            ]}
                            noStyle
                            style={{ flexGrow: 1 }}
                          >
                            {getFields({
                              ...ruleFactorValueFields(
                                formAnd?.[name]?.factorCode
                              ),
                              style: { flexGrow: 1, overflow: 'hidden' },
                            })}
                          </Form.Item>
                        </Input.Group>
                      </Form.Item>
                    </Col>
                    <Col
                      span={1}
                      style={{
                        marginLeft: styles.gapXs,
                        marginBottom: styles.gapLg,
                        marginTop: '6px',
                      }}
                    >
                      {fields.length > 1 ? (
                        <Delete
                          onClick={() => {
                            remove(name);
                          }}
                          style={{ cursor: 'pointer' }}
                        />
                      ) : null}
                    </Col>
                  </Row>
                ))}
                <Row>
                  <Col>
                    <Form.Item>
                      <Button
                        onClick={() => add()}
                        style={{ marginTop: styles.gapMd }}
                      >
                        {t('+ Add Rule Condition')}
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </Form.List>
        </Row>
      </Form>
    </CommonDrawer>
  );
};
