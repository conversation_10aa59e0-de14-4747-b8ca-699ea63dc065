@import '@uw/styles/variables.scss';

.team-title {
  font-weight: 700;
  font-size: $font-size-hg;
  line-height: 28px;
  margin-bottom: $gap-sm;
}
.team-sub-title {
  font-weight: 500;
  font-size: $font-size-root;
  @include line-height-22;
  margin-bottom: $gap-lg;
}
.team-list {
  height: calc(100% - 60px);
  overflow: auto;
}
.add-btn {
  width: 100%;
}
.total {
  color: var(--text-color);
  margin-top: $gap-md;
  margin-bottom: $gap-md;
  @include line-height-22;
}

.item-team-wrapper {
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: flex-start;
  padding: $gap-md;
  isolation: isolate;
  height: 94px;
  width: calc(100% - 24px);
  margin: $gap-sm;
  background: var(--white);

  box-shadow: 0px 4px 24px rgba(16, 42, 67, 0.12);
  border-radius: 20px;
  margin-bottom: $gap-md;
}
.item-team-right {
  flex: 1;
}

.team-header {
  display: flex;
  justify-content: space-between;
}
.avator {
  width: 46px;
  height: 46px;
  background: var(--table-header-select-bg);
  border-radius: 23px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: $gap-md;
}
.team-name {
  width: 250px;
  font-weight: 500;
  font-size: $font-size-root;
  color: var(--text-color-tertiary);
  @include line-height-22;
  @include text-ellipse;
}
.team-desc {
  width: 270px;
  font-weight: 700;
  font-size: $font-size-lg;
  color: var(--text-color);
  @include line-height-24;
  @include text-ellipse;
}
.check-circle {
  color: var(--success-color);
  margin-right: $gap-sm;
}
.stage-title {
  font-weight: 700;
  font-size: $font-size-root;
  @include line-height-22;
}
.stage-sub-title {
  font-weight: 500;
  font-size: $font-size-root;
  @include line-height-22;
  margin-top: $gap-md;
  margin-bottom: $gap-md;
}
.stage-content {
  margin-top: $gap-md;
}
.strategy-details-label {
  margin-right: $gap-sm;
}

:export {
  textColorQuaternary: var(--text-color-quaternary);
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  gapLg: $gap-lg;
  gapBig: $gap-big;
  successColor: var(--success-color);
}
