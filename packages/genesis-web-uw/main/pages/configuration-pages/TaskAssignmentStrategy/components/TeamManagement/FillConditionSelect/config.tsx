import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';

import { FieldDataType } from '@uw/interface/field.interface';
import { i18nFn } from '@uw/util/i18nFn';

export const authorityLevelField = (
  levelOptions: BizDict[] = [],
  handleLevelChange: (value: string) => void
): FieldDataType => ({
  label: '',
  key: 'factorCode',
  options: levelOptions,
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 8,
  onChange: (value: string) => {
    handleLevelChange(value);
  },
  required: true,
});

export const conditionContentField = (
  contentOptions: BizDict[] = [],
  getMemberList: () => void
): FieldDataType => ({
  label: '',
  key: 'factorValue',
  options: contentOptions,
  placeholder: i18nFn('Please select'),
  type: FieldType.MultiSelect,
  allowClear: true,
  col: 8,
  required: true,
  onBlur: () => {
    getMemberList();
  },
});
