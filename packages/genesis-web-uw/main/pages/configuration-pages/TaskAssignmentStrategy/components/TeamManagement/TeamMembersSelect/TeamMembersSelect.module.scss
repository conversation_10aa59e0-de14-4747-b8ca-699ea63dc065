@import '@uw/styles/variables.scss';
.add-team-member-wrapper {
  width: 100%;
  height: 100%;
}
.btn {
  width: 100%;
  margin-bottom: $gap-sm;
}
.query-form {
  padding: 0px;
}
.query-user-wrapper {
  :global {
    form > .antd-uw-row {
      margin-top: 0px !important;
    }
    .antd-uw-col-24.antd-uw-col {
      padding-right: 95px;
    }
  }
}

:export {
  textColorQuaternary: var(--text-color-quaternary);
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  gapLg: $gap-lg;
  gapBig: $gap-big;
  successColor: var(--success-color);
}
