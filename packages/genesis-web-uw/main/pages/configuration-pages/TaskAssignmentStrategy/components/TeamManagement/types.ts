import { FormInstance } from 'antd';

import { ConditionParamsType } from 'genesis-web-service';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { SetState } from '@uw/interface/common.interface';

import { FillConditionDetail } from '../../interface/common.interface';

export interface ItemTeam {
  teamName: string;
  id: string;
}
export interface TeamItemInfo {
  memberType: string;
  ruleIds: string[];
  teamName: string;
  teamMembers: { userId: string }[];
  type: string;
  teamManager?: string;
  teamConditions?: ConditionParamsType;
}
export interface NewTeamDrawerProps {
  visible: boolean;
  form: FormInstance;
  type: string;
  editId: string;
  getTeamList: () => void;
  handleCloseDrawer: () => void;
  stageTeamStrategies: ItemStageTeamStrategies[];
  title: string;
  fillConditions?: FillConditionDetail[];
  fillConditionsMap?: Record<string, FillConditionDetail>;
  isAutoFill: boolean;
  contentOptions: BizDict[];
  setContentOptions: SetState<BizDict[]>;
  setIsAutoFill: SetState<boolean>;
  handleGetMemberList?: (values: ConditionParamsType[]) => void;
}

export interface UserListItem {
  userId?: string;
  username: string;
  fullName: string;
  email: string;
  mobile: string;
  roles: {
    roleId: string;
    roleName: string;
  }[];
}

export enum UserTableType {
  ADD = 'ADD',
  SHOW = 'SHOW',
}

export interface TeamInfo {
  ruleIds: string[];
  teamName: string;
  userIds: string[];
  id?: string;
}

export interface ItemStageTeamStrategies {
  id: string;
  stage: string;
  strategy: {
    conditions: any;
    checkStaffOnDuty: string;
    id: number;
    numOfTaskAssignWhenUserAsk: number;
    strategyName: string;
    taskPushStrategy: string;
    checkRelatives: string;
    checkMutualExclusion: string;
  };
}
