import {
  FieldType,
  BizDict,
} from 'genesis-web-component/lib/interface/enum.interface';

import { ColumnProps } from 'antd/lib/table';
import { i18nFn } from '@uw/util/i18nFn';
import { DeleteOutlined } from '@ant-design/icons';
import { FormInstance } from 'antd';

import { messagePopup } from '@uw/util/messagePopup';

import { UserListItem, UserTableType } from '../types';

export interface TeamMembersItem {
  username: string;
  fullName: string;
  email: string;
  mobile: string;
  roles: { roleId: string; roleName: string }[];
  userId: number;
}

export const getTeamColumns = (
  form: FormInstance,
  type: UserTableType,
  value?: UserListItem[]
) => {
  const deleteUser = (index: number, userId: number) => () => {
    const teamManagerId = form.getFieldValue('teamManager');
    if (teamManagerId === userId) {
      messagePopup(
        i18nFn(
          'The user who appointed as team manager is deleted, please reset team manager if needs.'
        ),
        'warn'
      );
      form.setFieldsValue({
        teamManager: undefined,
      });
    }
    value?.splice(index, 1);
    form.setFieldsValue({
      teamMembers: value,
    });
  };
  const teamMembersTableColumns: ColumnProps<TeamMembersItem>[] = [
    {
      title: i18nFn('User Name'),
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: i18nFn('Full Name'),
      dataIndex: 'fullName',
      key: 'fullName',
    },
    {
      title: i18nFn('Email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: i18nFn('Mobile'),
      dataIndex: 'mobile',
      key: 'mobile',
    },
    {
      title: i18nFn('Roles'),
      dataIndex: 'roles',
      key: 'roles',
      render: roles =>
        roles.map((role: { roleName: string }) => role.roleName)?.join(','),
    },
  ];
  if (type === UserTableType.SHOW) {
    teamMembersTableColumns.push({
      title: i18nFn('Actions'),
      dataIndex: 'userId',
      key: 'userId',
      render: (text, record, index) => (
        <span onClick={deleteUser(index, record.userId)}>
          <DeleteOutlined />
        </span>
      ),
    });
  }

  return teamMembersTableColumns;
};
