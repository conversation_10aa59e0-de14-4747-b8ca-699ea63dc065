import { FC, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, FormInstance } from 'antd';
import clsx from 'clsx';
import { unionWith } from 'lodash-es';

import { Drawer, QueryForm, Table } from '@zhongan/nagrand-ui';

import { TaskAssignmentService } from 'genesis-web-service';

import { TextBody } from '@uw/components/Text';
import { messagePopup } from '@uw/util/messagePopup';

import { useQueryFields } from '../../../hooks/useQueryFields';
import { convertModuleType } from '../../convertModuleType';
import { UserListItem, UserTableType } from '../types';
import styles from './TeamMembersSelect.module.scss';
import { getTeamColumns } from './config';

interface TeamMembersSelectProps {
  onChange?: (value: UserListItem[]) => void;
  value?: UserListItem[];
  type: string;
  form: FormInstance;
  isAutoFill: boolean;
}

const TeamMembersSelect: FC<TeamMembersSelectProps> = ({
  onChange,
  value,
  type,
  form,
  isAutoFill,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [visible, setVisible] = useState(false);
  const [selectMember, setSelectMember] = useState(value ?? []);
  const [userList, setUserList] = useState<UserListItem[]>([]);
  const [curForm] = Form.useForm();
  const teamMembersTableColumns = getTeamColumns(
    form,
    UserTableType.SHOW,
    value
  );
  const moduleType = convertModuleType(type);
  const queryFields = useQueryFields(moduleType);
  const teamMembersTableAddColumns = getTeamColumns(form, UserTableType.ADD);
  const addTeamMember = () => {
    setVisible(true);
  };
  const handleCancel = useCallback(() => {
    setVisible(false);
    setSelectMember([]);
    setUserList([]);
    curForm.resetFields();
  }, [curForm]);

  const handleAddMember = useCallback(() => {
    const selectAllMember = unionWith(
      selectMember,
      value,
      (member1, member2) => member1.userId === member2.userId
    );
    onChange?.(selectAllMember);
    handleCancel();
  }, [handleCancel, onChange, selectMember, value]);

  const transferQueryParams = useCallback(
    async values => {
      if (!values) {
        return;
      }
      try {
        const userLists = await TaskAssignmentService.queryUser(moduleType, {
          ...values,
          page: 0,
          size: 120,
        });
        setUserList(userLists?.content ?? []);
      } catch (e) {
        messagePopup((e as Error)?.toString(), 'error');
      }
    },
    [moduleType]
  );
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: UserListItem[]) => {
      setSelectMember(selectedRows);
    },
  };

  return (
    <div className={styles.addTeamMemberWrapper}>
      {!isAutoFill && (
        <Button className={styles.btn} onClick={addTeamMember}>
          {t('+ Add')}
        </Button>
      )}
      <div>
        <Table
          columns={teamMembersTableColumns}
          dataSource={value}
          pagination={false}
          scroll={{ x: 700 }}
        />
      </div>
      <Drawer
        title={t('Add New Members')}
        open={visible}
        sendText={t('Confirm')}
        size="default"
        rootClassName={clsx('nagrand-drawer', 'default')}
        onClose={handleCancel}
        onSubmit={handleAddMember}
      >
        <div className={styles.queryUserWrapper}>
          <QueryForm
            queryFields={queryFields}
            onSearch={transferQueryParams}
            use="drawer"
          />

          <TextBody weight="bold">{t('User List')}</TextBody>

          <Table
            rowKey={'userId'}
            columns={teamMembersTableAddColumns}
            dataSource={userList}
            pagination={false}
            scroll={{ x: 800 }}
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
          />
        </div>
      </Drawer>
    </div>
  );
};

export default TeamMembersSelect;
