import { CommonDrawer } from '@uw/components/CommonDrawer';
import { Button, Form, message, Switch } from 'antd';
import { capitalize, pick } from 'lodash-es';
import { CheckCircleFilled } from '@ant-design/icons';

import { useCallback, useContext, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { FC } from 'react';

import { renderFields } from '@uw/util/renderFields';

import { TeamTypeEnum } from '@uw/interface/enum.interface';

import { ConditionParamsType } from 'genesis-web-service';

import { messagePopup } from '@uw/util/messagePopup';

import { getAddTeamFields } from './page.config';

import { NewTeamDrawerProps, TeamItemInfo } from './types';
import { getContext } from '../../utils/getContext';

import styles from './teamManagement.module.scss';
import { ContextType } from '../../interface/common.interface';
import {
  useGetDict,
  useSaveTeam,
  useGetStrageryList,
} from '../../hooks/request';
import { StrategyMainCard } from '../Strategy/StrategyMainCard';
import { convertModuleType } from '../convertModuleType';

interface TeamInfo {
  ruleIds: string[];
  teamName: string;
  userIds?: string[];
  id?: string;
  teamManager?: number;
  teamConditions?: ConditionParamsType[];
}

const NewTeamDrawer: FC<NewTeamDrawerProps> = ({
  visible,
  handleCloseDrawer,
  form,
  type,
  getTeamList,
  editId,
  stageTeamStrategies = [],
  title,
  fillConditions,
  fillConditionsMap,
  isAutoFill,
  contentOptions,
  setContentOptions,
  setIsAutoFill,
  handleGetMemberList,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [showStrategy, setShowStrategy] = useState(true);
  const { state } = useContext(getContext(capitalize(type) as ContextType));
  const { saveTeam, loading } = useSaveTeam();
  const handleCancel = useCallback(async () => {
    await form.resetFields();
    handleCloseDrawer();
  }, [form, handleCloseDrawer]);
  const moduleType = convertModuleType(type);
  const [dictMap] = useGetDict(['taskPushStrategy'], moduleType);
  const [memberTypeDicts] = useGetDict(['memberType'], moduleType);
  const { priorityConditionList, oridinaryConditionList } = useGetStrageryList(
    moduleType,
    type
  );

  const handleSaveTeam = useCallback(async () => {
    await form.submit();
  }, [form]);

  const onFinish = useCallback(
    async (value: TeamItemInfo) => {
      const teamInfo: TeamInfo = {
        ...value,
        userIds:
          value?.memberType === TeamTypeEnum.ManualAllocation
            ? value?.teamMembers?.map(member => member.userId)
            : undefined,
        teamConditions:
          value?.memberType === TeamTypeEnum.AutomatchedFill
            ? [value?.teamConditions as ConditionParamsType]
            : undefined,
      };
      const isExistCurManager = value?.teamMembers?.some(
        item => item.userId === value.teamManager
      );
      if (value.teamManager && !isExistCurManager) {
        messagePopup(t('Manager has been changed. Please check.'), 'error');
        return;
      }
      if (editId) {
        teamInfo.id = editId;
      }
      try {
        const result = await saveTeam(moduleType, teamInfo);
        if (result) {
          getTeamList();
          handleCancel();
        }
      } catch (e) {
        message.error(e?.toString());
      }
    },
    [editId, getTeamList, handleCancel, saveTeam, moduleType]
  );
  const ruleOptions = useMemo(
    () =>
      state?.rules?.map(rule => ({
        itemName: rule.ruleName,
        enumItemName: rule.id,
      })),
    [state?.rules]
  );

  const teamTypeOptions = useMemo(
    () =>
      memberTypeDicts?.memberType?.map(item =>
        pick(item, ['itemName', 'enumItemName'])
      ),
    [memberTypeDicts]
  );

  const onChange = useCallback((value: boolean) => {
    setShowStrategy(value);
  }, []);

  const handleTeamTypeChange = (teamType: string) => {
    setIsAutoFill(teamType === TeamTypeEnum.AutomatchedFill);
    form?.setFieldsValue({
      teamConditions: { factorCode: undefined, factorValue: undefined },
      teamMembers: [],
    });
  };

  return (
    <CommonDrawer
      width={'1096px'}
      title={title}
      visible={visible}
      handleCloseDrawer={handleCancel}
      action={
        <>
          <Button onClick={handleCancel}>{t('Cancel')}</Button>
          <Button
            type={'primary'}
            onClick={handleSaveTeam}
            style={{ marginLeft: styles.gapMd }}
            loading={loading}
          >
            {t('Submit')}
          </Button>
        </>
      }
    >
      <Form
        name="basic"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        form={form}
        layout="vertical"
        autoComplete="off"
      >
        {renderFields(
          getAddTeamFields(
            teamTypeOptions ?? [],
            ruleOptions,
            type,
            form,
            handleTeamTypeChange,
            isAutoFill,
            contentOptions,
            setContentOptions,
            fillConditions,
            handleGetMemberList,
            fillConditionsMap
          )
        )}
      </Form>
      {stageTeamStrategies?.length > 0 ? (
        <div>
          <div className={styles.stageTitle}>
            <CheckCircleFilled className={styles.checkCircle} />
            {t('Task Push Strategy has been configured')}
          </div>
          <div className={styles.stageSubTitle}>
            <span className={styles.strategyDetailsLabel}>
              {t('Preview Strategy Details')}
            </span>
            <Switch checked={showStrategy} onChange={onChange}></Switch>
          </div>
          <div
            className={styles.stageContent}
            style={{ display: showStrategy ? 'block' : 'none' }}
          >
            <div className={styles.total}>
              {t('Total: {{total}} Strategies', {
                total: stageTeamStrategies?.length,
              })}
            </div>

            <div>
              {stageTeamStrategies?.map((stage, index) => (
                <div>
                  <StrategyMainCard
                    taskPushStrategyEnums={dictMap?.taskPushStrategy}
                    key={stage.id}
                    cardInfo={{
                      ...stage.strategy,
                      priors: stage.strategy?.conditions
                        ?.filter(
                          (record: { orderNo: number }) => record?.orderNo
                        )
                        ?.sort(
                          (
                            prev: { orderNo: number },
                            next: { orderNo: number }
                          ) => prev?.orderNo - next?.orderNo
                        ),
                      ordinaries: stage.strategy?.conditions?.filter(
                        record => !record?.orderNo
                      ),
                    }}
                    type={type}
                    formRef={form}
                    fieldKey={index}
                    priorityConditionList={priorityConditionList}
                    oridinaryConditionList={oridinaryConditionList}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : null}
    </CommonDrawer>
  );
};

export default NewTeamDrawer;
