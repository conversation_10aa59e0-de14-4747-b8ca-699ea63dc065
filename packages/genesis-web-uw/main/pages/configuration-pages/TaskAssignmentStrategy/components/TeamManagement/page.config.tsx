import { FormInstance } from 'antd/es';

import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import { ConditionParamsType } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { MultipleType } from '@uw/interface/field.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { ItemFields } from '@uw/util/renderFields';

import { FillConditionDetail } from '../../interface/common.interface';
import { FillConditionSelect } from './FillConditionSelect';
import TeamManageSelect from './TeamManageSelect';
import { TeamMembersSelect } from './TeamMembersSelect';

const getRules = (required: boolean) => (message: string) =>
  required
    ? [
        {
          required: true,
          message,
        },
      ]
    : [];

export const getAddTeamFields = (
  teamTypeOptions: BizDict[],
  rulesList: BizDict[],
  type: string,
  form: FormInstance,
  handleTeamTypeChange: (value: string) => void,
  isAutoFill: boolean,
  contentOptions: BizDict[],
  setContentOptions: SetState<BizDict[]>,
  fillConditions?: FillConditionDetail[],
  handleGetMemberList?: (params: ConditionParamsType[]) => void,
  fillConditionsMap?: Record<string, FillConditionDetail>
): ItemFields[] => {
  const getRequiredRules = getRules(true);
  const formFields = [
    {
      type: MultipleType.Multiple,
      gutter: 64,
      children: [
        {
          type: FieldType.Input,
          label: i18nFn('Team Name'),
          placeholder: i18nFn('Please input'),
          key: 'teamName',
          span: 8,
          rules: getRequiredRules(i18nFn('Please input your Team Name!')),
        },
        {
          type: FieldType.Select,
          label: i18nFn('Team Maintenance Type'),
          placeholder: i18nFn('Please select'),
          key: 'memberType',
          allowClear: true,
          span: 8,
          rules: getRequiredRules(i18nFn('Please select your Team Type!')),
          options: teamTypeOptions,
          onChange: (value: string) => {
            handleTeamTypeChange(value);
          },
        },
      ],
    },
    {
      type: FieldType.Render,
      label: i18nFn('Automated Fill Condition'),
      key: 'automatedFillCondition',
      span: 24,
      show: isAutoFill,
      render: () => (
        <FillConditionSelect
          form={form}
          fillConditions={fillConditions}
          fillConditionsMap={fillConditionsMap}
          handleGetMemberList={handleGetMemberList}
          contentOptions={contentOptions}
          setContentOptions={setContentOptions}
        />
      ),
    },
    {
      type: FieldType.Render,
      label: i18nFn('Team Members'),
      key: 'teamMembers',
      span: 24,
      rules: getRequiredRules(i18nFn('Please select your Team Members!')),
      render: () => (
        <TeamMembersSelect type={type} form={form} isAutoFill={isAutoFill} />
      ),
      wrapperCol: { span: 22 },
    },
    {
      type: FieldType.Render,
      key: 'teamManager',
      span: 16,
      placeholder: i18nFn('Please select'),
      render: () => <TeamManageSelect type={type} form={form} />,
      wrapperCol: { span: 22 },
    },
    {
      type: FieldType.MultiSelect,
      label: i18nFn('Bind Task Assignment Rule'),
      placeholder: i18nFn('Please input'),
      key: 'ruleIds',
      span: 12,
      rules: getRequiredRules(
        i18nFn('Please select your Bind Task Assignment Rule!')
      ),
      wrapperCol: { span: 22 },
      options: rulesList,
    },
  ];
  return formFields;
};
