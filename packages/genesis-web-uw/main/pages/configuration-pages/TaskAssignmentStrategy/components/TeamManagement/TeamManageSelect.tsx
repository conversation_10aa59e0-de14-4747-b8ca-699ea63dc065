import { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Form, FormInstance, Select } from 'antd';

import styles from './teamManagement.module.scss';
import { UserListItem } from './types';

interface TeamMembersSelectProps {
  onChange?: (value: number) => void;
  value?: number;
  type: string;
  form: FormInstance;
}
const { Option } = Select;

const TeamManageSelect: FC<TeamMembersSelectProps> = ({
  onChange,
  value,
  form,
}) => {
  const { getFieldValue } = form;
  const [t] = useTranslation(['uw', 'common']);
  const [checkboxDisabled, setCheckboxDisabled] = useState(true);
  const [checked, setChecked] = useState(false);
  const handleChange = useCallback(
    event => {
      setChecked(event.target.checked);
      // 取消勾选则清空manager
      if (!event.target.checked) {
        form?.setFieldValue('teamManager', undefined);
      }
    },
    [form]
  );

  useEffect(() => {
    if (value) {
      setChecked(true);
    }
  }, [value]);

  return (
    <>
      <Form.Item shouldUpdate noStyle>
        {() => {
          const memberData = getFieldValue('teamMembers');
          setCheckboxDisabled(!memberData?.[0]);
          if (!memberData?.[0]) {
            setChecked(false);
          }
          return (
            <>
              <Checkbox
                disabled={checkboxDisabled}
                checked={checked}
                onChange={handleChange}
              >
                <div>{t('Select a manager for the team')}</div>
              </Checkbox>
              <div style={{ marginTop: styles.gapXs }}>
                {checked && (
                  <Form.Item shouldUpdate noStyle>
                    {() =>
                      memberData?.[0] && (
                        <Select
                          value={value}
                          placeholder={t('Please select')}
                          allowClear={true}
                          disabled={false}
                          onChange={e => {
                            onChange?.(e);
                          }}
                        >
                          {memberData?.map((option: UserListItem) => (
                            <Option value={option.userId}>
                              {option.username}
                            </Option>
                          ))}
                        </Select>
                      )
                    }
                  </Form.Item>
                )}
              </div>
            </>
          );
        }}
      </Form.Item>
    </>
  );
};

export default TeamManageSelect;
