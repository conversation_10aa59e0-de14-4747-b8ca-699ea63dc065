import {
  FC,
  useContext,
  useState,
  useCallback,
  useEffect,
  useMemo,
} from 'react';
import { i18nFn } from '@uw/util/i18nFn';
import { Form } from 'antd';
import { useTranslation } from 'react-i18next';
import { BizDict, Mode, TeamTypeEnum } from '@uw/interface/enum.interface';

import { capitalize } from 'lodash-es';

import { messagePopup } from '@uw/util/messagePopup';

import {
  ContextType,
  FillConditionDetail,
  StepKey,
} from '../../interface/common.interface';

import { getContext } from '../../utils/getContext';

import NewTeamDrawer from './NewTeamDrawer';

import taskAssignmentsStyles from '../../TaskAssignmentStrategy.module.scss';
import { TaskCardWrapper } from '../TaskCardWrapper';
import {
  useGetTeamsList,
  useEditTeam,
  useDeleteTeam,
  useGetFillConditionInfo,
  useGetMemberList,
} from '../../hooks/request';
import { ItemStageTeamStrategies } from './types';
import { convertModuleType } from '../convertModuleType';

interface TeamManageProps {
  visibleKey: StepKey;
  setVisibleKey: (key: StepKey | undefined) => void;
  type: string;
}

export const TeamManagement: FC<TeamManageProps> = ({
  visibleKey,
  setVisibleKey,
  type,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const moduleType = convertModuleType(type);
  const [getTeamList] = useGetTeamsList(moduleType);
  const { state } = useContext(getContext(capitalize(type) as ContextType));
  const [editId, setEditId] = useState<string>('');
  const [visible, setVisible] = useState<boolean>(false);
  const [isAutoFill, setIsAutoFill] = useState(false);
  const [contentOptions, setContentOptions] = useState<BizDict[]>([]);
  const [fillConditions] = useGetFillConditionInfo(moduleType);
  const fillConditionsMap = useMemo<
    Record<string, FillConditionDetail> | undefined
  >(
    () =>
      fillConditions?.reduce(
        (cur, next) => ({
          ...cur,
          [next.enumItemName]: next,
        }),
        {} as Record<string, FillConditionDetail>
      ),
    [fillConditions]
  );
  const [stageTeamStrategies, setStageTeamStrategies] = useState<
    ItemStageTeamStrategies[]
  >([]);
  const [handleGetMemberList] = useGetMemberList(moduleType);
  const { handleEditTeams } = useEditTeam(
    setEditId,
    setVisibleKey,
    form,
    setIsAutoFill,
    setContentOptions,
    fillConditionsMap,
    setStageTeamStrategies,
    handleGetMemberList
  );
  const { handleDeleteTeams } = useDeleteTeam(getTeamList);

  useEffect(() => {
    if (visibleKey === StepKey.TeamStep) {
      setVisible(true);
      if (!editId) {
        form?.setFieldValue('memberType', TeamTypeEnum.ManualAllocation);
        setIsAutoFill(false);
      }
    }
  }, [visibleKey, editId, form]);
  const handleCloseDrawer = () => {
    setVisibleKey(undefined);
    setVisible(false);
    setEditId('');
    setStageTeamStrategies([]);
  };
  const actionList = useCallback(
    (id: string) => [
      {
        key: Mode.Edit,
        label: t('Edit'),
        onClick: () => {
          handleEditTeams(id, moduleType);
        },
      },
      {
        key: Mode.Delete,
        label: t('Delete'),
        onClick: async () => {
          const strategyList = state?.teamStrategyMap?.[id];
          if (strategyList) {
            const TipsMessage = i18nFn(
              'This team has been binded by strategy {{StrategyNames}}, please unbind from strategy first.',
              undefined,
              {
                StrategyNames: strategyList
                  ?.map(item => item.strategyName)
                  .join(','),
              }
            );
            messagePopup(TipsMessage, 'warn');
          } else {
            await handleDeleteTeams(id, moduleType);
          }
        },
      },
    ],
    [t, state?.teamStrategyMap, handleEditTeams, handleDeleteTeams, moduleType]
  );
  return (
    <div className={taskAssignmentsStyles.cardListWrapper}>
      {state?.teams?.map(team => (
        <TaskCardWrapper
          key={team.id}
          fieldValue={team.teamName}
          fieldName={t('Team Name')}
          actionList={actionList(team.id)}
          disabled={!state?.hasEditAuth}
        />
      ))}

      <NewTeamDrawer
        title={editId ? t('Edit Team') : t('Add New Team')}
        type={type}
        visible={visible}
        handleCloseDrawer={handleCloseDrawer}
        form={form}
        getTeamList={getTeamList}
        editId={editId}
        stageTeamStrategies={stageTeamStrategies}
        fillConditions={fillConditions}
        fillConditionsMap={fillConditionsMap}
        isAutoFill={isAutoFill}
        contentOptions={contentOptions}
        setContentOptions={setContentOptions}
        setIsAutoFill={setIsAutoFill}
        handleGetMemberList={handleGetMemberList}
      />
    </div>
  );
};
