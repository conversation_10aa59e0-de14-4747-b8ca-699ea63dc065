import { FC, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, FormInstance, Row } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { ConditionParamsType } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { handleFilterNoEmptyObj } from '@uw/util/handleFilterNoEmptyObj';

import { FillConditionDetail } from '../../../interface/common.interface';
import styles from './FillConditionSelect.module.scss';
import { authorityLevelField, conditionContentField } from './config';

interface Props {
  form: FormInstance;
  fillConditions?: FillConditionDetail[];
  fillConditionsMap?: Record<string, FillConditionDetail>;
  handleGetMemberList?: (params: ConditionParamsType[]) => void;
  contentOptions: BizDict[];
  setContentOptions: SetState<BizDict[]>;
}

export const FillConditionSelect: FC<Props> = ({
  form,
  fillConditions,
  fillConditionsMap,
  handleGetMemberList,
  contentOptions,
  setContentOptions,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const getMemberList = useCallback(async () => {
    const conditionData = form?.getFieldValue('teamConditions');
    if (handleFilterNoEmptyObj(conditionData)) {
      const members = await handleGetMemberList?.([conditionData]);
      form?.setFieldValue('teamMembers', members);
    }
  }, [form, handleGetMemberList]);

  const levelFields = useMemo(
    () =>
      authorityLevelField(fillConditions, level => {
        setContentOptions(fillConditionsMap?.[level]?.valueItems ?? []);
        form?.setFieldsValue({
          teamConditions: { factorValue: undefined },
        });
      }),
    [fillConditionsMap, fillConditions, form, setContentOptions]
  );

  const contentFields = useMemo(
    () => conditionContentField(contentOptions, getMemberList),
    [contentOptions, getMemberList]
  );

  return (
    <Row>
      <Col span={16}>
        <Input.Group compact className={styles.conditionInputGroup}>
          <Form.Item name={['teamConditions', 'factorCode']} noStyle>
            {getFields(levelFields)}
          </Form.Item>
          <Form.Item
            name={['teamConditions', 'factorValue']}
            noStyle
            rules={[{ required: true, message: t('Please select') }]}
          >
            {getFields(contentFields)}
          </Form.Item>
        </Input.Group>
      </Col>
    </Row>
  );
};
