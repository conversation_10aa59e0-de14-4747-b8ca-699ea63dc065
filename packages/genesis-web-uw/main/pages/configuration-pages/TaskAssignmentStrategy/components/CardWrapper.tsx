import React, { FC, useCallback, useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import clsx from 'clsx';

import { StepStatusEnum } from '@uw/interface/enum.interface';

import { NoData } from '@uw/components/NoData';

import { TextBody } from '@uw/components/Text';

import styles from '../TaskAssignmentStrategy.module.scss';
import { Step } from '../page.config';
import { ContextType, StepKey } from '../interface/common.interface';
import { getContext } from '../utils/getContext';

interface Props {
  type: ContextType;
  step: Step;
  total?: number;
  handleAction: (cardKey: StepKey) => void;
}

export const CardWrapper: FC<Props> = ({
  type,
  step,
  total = 0,
  children,
  handleAction,
}) => {
  const { state } = useContext(getContext(type));
  const [t] = useTranslation(['uw', 'common']);
  const active = step.status === StepStatusEnum.Finish;
  const handleClick = useCallback(() => {
    handleAction(step.key);
  }, [step]);
  const disabled = !state?.hasEditAuth;

  return (
    <section
      key={step.key}
      className={clsx(styles.stepCardWrapper, active ? styles.active : '')}
    >
      <>
        <div
          className={styles.cardWithData}
          style={{
            display: total > 0 ? 'flex' : 'none',
          }}
        >
          <div className={styles.actionWrapper}>
            <Button block onClick={handleClick} disabled={disabled}>
              {step.buttonText}
            </Button>
          </div>
          <div style={{ margin: `0 ${styles.gapLg}` }}>
            {t('Total: {{total}}', { total })}
          </div>
          <div className={styles.dataEntryContent}>{children}</div>
        </div>
        <div
          className={styles.emptySection}
          style={{ display: total > 0 ? 'none' : 'block' }}
        >
          <TextBody type="h4" weight={700}>
            {active ? step.sectionTitle : t('How to Use')}
          </TextBody>
          <div className={styles.desc}>{step.desc}</div>
          {active ? (
            <Button
              disabled={disabled}
              block
              style={{
                marginTop: styles.gapLg,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              icon={step.buttonIcon}
              onClick={handleClick}
            >
              {step.buttonText}
            </Button>
          ) : (
            <NoData text={t('Configure the Task Assignment Rule first')} />
          )}
        </div>
      </>
    </section>
  );
};
