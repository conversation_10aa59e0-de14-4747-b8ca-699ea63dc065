import React, { FC, useState } from 'react';
import { Steps } from 'antd';

import { CardWrapper } from './CardWrapper';
import { useTeamSteps } from '../hooks/useTeamSteps';
import styles from '../TaskAssignmentStrategy.module.scss';
import { ContextType, StepKey } from '../interface/common.interface';

interface Props {
  type: ContextType;
}

export const TeamSteps: FC<Props> = ({ type }) => {
  const [visibleKey, setVisibleKey] = useState<StepKey>();
  const [steps] = useTeamSteps(type);
  const handleAction = (cardKey: StepKey) => {
    setVisibleKey(cardKey);
  };

  return (
    <div className={styles.teamStepsWrapper}>
      <Steps type="navigation" current={0} items={steps} />
      <div className={styles.stepContent}>
        {steps?.map(step => (
          <CardWrapper
            type={type}
            step={step}
            total={step.total}
            handleAction={handleAction}
          >
            {step.Component ? (
              <step.Component
                visibleKey={visibleKey}
                setVisibleKey={setVisibleKey}
                type={type.toLocaleLowerCase()}
              />
            ) : (
              <></>
            )}
          </CardWrapper>
        ))}
      </div>
    </div>
  );
};