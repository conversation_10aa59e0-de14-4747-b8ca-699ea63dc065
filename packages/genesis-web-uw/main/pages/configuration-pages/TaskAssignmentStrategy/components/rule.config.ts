import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';

import { FieldDataType } from '@uw/interface/field.interface';
import { i18nFn } from '@uw/util/i18nFn';

export const ruleFields: FieldDataType = {
  label: i18nFn('Rule Name'),
  key: 'ruleName',
  placeholder: i18nFn('Please input'),
  type: FieldType.Input,
  allowClear: true,
  col: 16,
  required: true,
  diabled: true,
  rules: [
    {
      required: true,
      message: i18nFn('Please input'),
    },
  ],
};

export const ruleConditionFactorFields = (
  factorCodeEnums: BizDict[] = [],
  handleFactorChange: (value: string) => void
): FieldDataType => ({
  label: '',
  key: 'factorCode',
  options: factorCodeEnums,
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: i18nFn('Please select'),
    },
  ],
  onChange: (value: string) => {
    handleFactorChange(value);
  },
});

export const ruleConditionOperatorFields = (
  operators: BizDict[] = []
): FieldDataType => ({
  label: '',
  key: 'operator',
  options: operators,
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: i18nFn('Please select'),
    },
  ],
});

const handleMessage = (fieldType: FieldType) =>
  fieldType === FieldType.Select || fieldType === FieldType.MultiSelect
    ? i18nFn('Please select')
    : i18nFn('Please input');
export const ruleConditionFactorValueFields = (
  factorValues: BizDict[] = [],
  fieldType: FieldType = FieldType.Input
): FieldDataType => ({
  label: '',
  key: 'factorValue',
  options: factorValues,
  placeholder: handleMessage(fieldType),
  type: fieldType,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: handleMessage(fieldType),
    },
  ],
});
