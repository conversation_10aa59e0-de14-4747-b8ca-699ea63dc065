import React, { <PERSON> } from 'react';

import { Card } from 'antd';

import { MenuListType, MoreAction } from '@uw/components/MoreAction';
import { TextBody } from '@uw/components/Text';

import styles from '../TaskAssignmentStrategy.module.scss';

interface Props {
  disabled: boolean;
  fieldName: string;
  fieldValue: string;
  icon?: JSX.Element;
  actionList: MenuListType[];
}

export const TaskCardWrapper: FC<Props> = ({
  disabled,
  fieldName,
  fieldValue,
  icon,
  actionList,
  children,
}) => (
  <Card className={styles.TaskCardWrapper}>
    <section className={styles.cardHeader}>
      {icon}
      <div style={{ flexGrow: '1', maxWidth: '100%' }}>
        <div style={{ color: styles.textColorTertiary }}>{fieldName}</div>
        <TextBody
          weight={700}
          type="h5"
          style={{
            maxWidth: '80%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {fieldValue}
        </TextBody>
      </div>
      <MoreAction menuList={actionList} disabled={disabled} />
    </section>
    <section className={styles.cardContent}>{children}</section>
  </Card>
);
