import React, {
  FC,
  useCallback,
  useEffect,
  useState,
  Dispatch,
  SetStateAction,
  useContext,
} from 'react';

import { useTranslation } from 'react-i18next';
import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { capitalize } from 'lodash-es';

import { messagePopup } from '@uw/util/messagePopup';

import styles from '../TaskAssignmentStrategy.module.scss';
import { RuleEditDrawer } from './RuleEditDrawer';
import { ContextType, StepKey } from '../interface/common.interface';
import { deleteRule, useGetRulesList } from '../hooks/request';
import { getContext } from '../utils/getContext';
import { TaskCardWrapper } from './TaskCardWrapper';
import { convertModuleType } from './convertModuleType';

interface Props {
  visibleKey: StepKey;
  type: string;
  setVisibleKey: Dispatch<SetStateAction<string | undefined>>;
}

export const Rules: FC<Props> = ({ visibleKey, type, setVisibleKey }) => {
  const [t] = useTranslation(['uw', 'common']);
  const { state } = useContext(getContext(capitalize(type) as ContextType));
  const [visible, setVisible] = useState(false);
  const [mode, setMode] = useState(Mode.Add);
  const moduleType = convertModuleType(type);
  const [getRuleList] = useGetRulesList(moduleType);
  const [ruleId, setRuleId] = useState<number>();

  useEffect(() => {
    if (visibleKey === StepKey.RuleStep) {
      setVisible(true);
    }
  }, [visibleKey]);

  const handleClose = useCallback(() => {
    setVisibleKey(undefined);
    setVisible(false);
    setMode(Mode.Add);
    setRuleId(undefined);
  }, []);

  const actionList = useCallback(
    (id: number) => [
      {
        key: Mode.Edit,
        label: t('Edit'),
        onClick: () => {
          setVisibleKey(StepKey.RuleStep);
          setVisible(true);
          setMode(Mode.Edit);
          setRuleId(id);
        },
      },
      {
        key: Mode.Delete,
        label: t('Delete'),
        onClick: async () => {
          let teamNames = '';
          const ruleTeamInfos = state?.ruleTeamMap?.[id];
          if (ruleTeamInfos) {
            teamNames = ruleTeamInfos
              .map(teamInfo => teamInfo.teamName)
              .join(', ');
          }
          if (teamNames.length > 0) {
            messagePopup(
              t(
                'This rule has been binded by team {{teamNames}}, please unbind from team first.',
                { teamNames }
              ),
              'warn'
            );
            return;
          }
          await deleteRule(moduleType, id);
          await getRuleList();
        },
      },
    ],
    [type, state?.ruleTeamMap, moduleType]
  );

  return (
    <div className={styles.stepCardsContent}>
      <section className={styles.cardListWrapper}>
        {state?.rules?.map(rule => (
          <TaskCardWrapper
            key={rule.id}
            fieldValue={rule.ruleName}
            fieldName={t('Rule Name')}
            actionList={actionList(rule.id)}
            disabled={!state?.hasEditAuth}
          />
        ))}
      </section>
      <RuleEditDrawer
        visible={visible}
        type={type}
        handleClose={handleClose}
        getRuleList={getRuleList}
        mode={mode}
        ruleId={ruleId}
      />
    </div>
  );
};
