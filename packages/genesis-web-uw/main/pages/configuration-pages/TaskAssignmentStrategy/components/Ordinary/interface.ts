import { FormInstance } from 'antd';
import { Strategy, StrategiesConditionType } from 'genesis-web-service';

export interface CommonProps {
  cardInfo?: CardInfo;
  fieldsProp?: FieldsProp;
  type: string;
  formRef?: FormInstance;
  form?: FormInstance;
}

export type CardInfo = Partial<
  | Strategy
  | {
      checkStaffOnDuty: boolean;
      checkRelatives: boolean;
      checkMutualExclusion: boolean;
    }
>;

export type FieldsProp = {
  name: number | string;
  key: number | string;
  fieldKey?: number | string;
};

// OrdinaryCondition组件内子组件的props
export interface OrdinaryFormProps {
  optionsList: StrategiesConditionType[];
  factorCode: string;
  record: FieldsProp;
  cardInfo?: CardInfo;
}

export interface OrdinaryConditionProps extends CommonProps {
  oridinaryConditionList: StrategiesConditionType[];
}

export type StrategiesConditionDisabledType = StrategiesConditionType & {
  disabled?: boolean;
};
