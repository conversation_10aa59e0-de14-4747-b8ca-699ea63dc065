import React from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Select } from 'antd';

import clsx from 'clsx';

import { StrategyConditionDataTypeEnum } from 'genesis-web-service';

import styles from './index.module.scss';
import { OrdinaryFormProps } from './interface';

const { Option } = Select;

export const OrdinaryForm: React.FC<OrdinaryFormProps> = props => {
  const { factorCode, optionsList, record, cardInfo } = props;
  const [t] = useTranslation(['uw', 'common']);

  const currentOption = optionsList?.find(
    item => item?.factorCode === factorCode
  );

  if (!currentOption) {
    return null;
  }
  if (!currentOption?.valueItems) {
    return null;
  }

  return (
    <Col span={cardInfo ? 9 : 22} className={styles.ordinaryFormCol}>
      <div className={styles.arrow} />
      <Form.Item
        {...record}
        name={[record?.name as string | number, 'factorValue']}
        className={styles.childForm}
      >
        <Select
          allowClear
          className={clsx(styles.childForm, styles.childFormSelect)}
          mode={
            currentOption?.dataType === StrategyConditionDataTypeEnum?.LIST
              ? 'multiple'
              : undefined
          }
          placeholder={t('Please select')}
        >
          {currentOption?.valueItems?.map(
            (item: { factorValue: string; factorValueNameI18n: string }) => (
              <Option value={item?.factorValue}>
                {item?.factorValueNameI18n}
              </Option>
            )
          )}
        </Select>
      </Form.Item>
    </Col>
  );
};
