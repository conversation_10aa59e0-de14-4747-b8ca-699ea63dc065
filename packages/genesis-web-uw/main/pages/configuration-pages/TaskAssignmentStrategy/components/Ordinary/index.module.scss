@import '@uw/styles/variables.scss';

.arrow {
  width: $gap-lg;
  height: $gap-huge;
  margin-left: $gap-sm;
  // 空心三角
  &::before {
    content: '';
    display: inline-block;
    border-top: 1px solid;
    border-right: 1px solid;
    width: $gap-md;
    height: $gap-md;
    border-color: var(--disabled-color);
    transform: rotate(-180deg);
  }
}

.ordinary-form-col {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.child-form {
  width: 100%;
  margin-bottom: var(--gap-xs);
}

.child-form-select {
  display: inline-block;
}

.ordinary-form-row {
  flex-direction: column;
  position: relative;
  margin-bottom: $gap-lg;
  .divider-line {
    position: absolute;
    height: calc(100% - 50px);
    top: 30px;
    left: 16px;
    &::before {
      background-color: white;
      display: block;
      content: 'And';
      position: absolute;
      font-weight: bold;
      top: 50%;
      margin-top: -11px;
      left: -15px;
    }
  }
  .form-layout {
    display: flex;
    align-items: flex-start;
    flex-wrap: nowrap;
    margin-left: 56px;
  }
}

:export {
  gapLg: $gap-lg;
  gapMd: $gap-md;
  gapXs: var(--gap-xs);
}
