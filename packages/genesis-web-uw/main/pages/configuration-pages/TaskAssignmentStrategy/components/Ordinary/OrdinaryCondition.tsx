import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Divider, Form, Row, Select } from 'antd';

import { cloneDeep } from 'lodash-es';

import { Delete } from '@uw/assets/new-icons/index';

import { OrdinaryForm } from './OrdinaryForm';
import styles from './index.module.scss';
import {
  OrdinaryConditionProps,
  StrategiesConditionDisabledType,
} from './interface';

const { Option } = Select;

export const OrdinaryCondition: React.FC<OrdinaryConditionProps> = ({
  fieldsProp,
  oridinaryConditionList,
  formRef,
  form,
  cardInfo,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [optionsList, setOptionsList] = useState<
    StrategiesConditionDisabledType[]
  >(oridinaryConditionList);

  const selectedData = useMemo(
    () =>
      cardInfo
        ? form?.getFieldValue('ordinaries')
        : formRef?.getFieldValue('dynamicStrategyLists')?.[
            fieldsProp?.name as number
          ]?.ordinaries,
    [
      cardInfo,
      form?.getFieldValue('ordinaries'),
      formRef?.getFieldValue('dynamicStrategyLists'),
      fieldsProp?.name,
    ]
  );

  useEffect(() => {
    // 根据已填写数据的内容 进行实时动态判断 已添加的可选项将会被disabled掉
    const options: StrategiesConditionDisabledType[] = cloneDeep(
      oridinaryConditionList
    )?.map(item => ({
      ...item,
      disabled: !!selectedData?.some(
        (data: { factorCode: string }) => data?.factorCode === item?.factorCode
      ),
    }));
    setOptionsList(options);
  }, [selectedData, oridinaryConditionList]);

  // 是否可以新增
  const canAdd = useMemo(
    () => selectedData?.length >= optionsList?.length,
    [optionsList, selectedData]
  );

  return (
    <Row className={styles.ordinaryFormRow}>
      <div style={{ marginBottom: styles.gapXs }}>
        {t('Ordinary condition')}
      </div>
      <Form.List
        {...fieldsProp}
        name={
          cardInfo
            ? 'ordinaries'
            : [fieldsProp?.name as string | number, 'ordinaries']
        }
        initialValue={cardInfo ? selectedData : [{ factorCode: undefined }]}
      >
        {(list, { add, remove }) => (
          <>
            {list?.length > 0 && (
              <Divider plain type="vertical" className={styles.dividerLine} />
            )}
            {list?.map((item, index) => (
              <div
                key={index}
                className={styles.formLayout}
                style={{
                  flexWrap: optionsList?.find(
                    option =>
                      option?.factorCode === selectedData?.[index]?.factorCode
                  )
                    ? 'wrap'
                    : 'nowrap',
                }}
              >
                <Col span={cardInfo ? 9 : 22}>
                  <Form.Item
                    {...item}
                    name={[item?.name, 'factorCode']}
                    style={{ marginBottom: 8 }}
                  >
                    <Select
                      allowClear
                      style={{ marginBottom: 8 }}
                      placeholder={t('Please select')}
                    >
                      {optionsList?.map(
                        (option: StrategiesConditionDisabledType) => (
                          <Option
                            value={option?.factorCode}
                            disabled={option?.disabled}
                          >
                            {option?.factorNameI18n}
                          </Option>
                        )
                      )}
                    </Select>
                  </Form.Item>
                </Col>
                <Col
                  span={1}
                  style={{
                    marginLeft: styles.gapXs,
                    marginTop: '6px',
                  }}
                >
                  {list?.length > 1 && !cardInfo && (
                    <Delete
                      onClick={() => {
                        remove(index);
                      }}
                      style={{ cursor: 'pointer' }}
                    />
                  )}
                </Col>
                {optionsList?.find(
                  option =>
                    option?.factorCode === selectedData?.[index]?.factorCode
                ) && (
                  <OrdinaryForm
                    factorCode={selectedData?.[index]?.factorCode}
                    optionsList={optionsList}
                    record={item}
                    cardInfo={cardInfo}
                  />
                )}
              </div>
            ))}
            {!cardInfo && (
              <Col
                span={8}
                style={{ marginLeft: selectedData?.length > 0 ? '56px' : 0 }}
              >
                <Button onClick={() => add()} disabled={canAdd}>
                  {t('+ Add')}
                </Button>
              </Col>
            )}
          </>
        )}
      </Form.List>
    </Row>
  );
};
