import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { TextBody } from '@uw/components/Text';
import { StrategyList } from 'genesis-web-service';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { StatusTag } from '@uw/components/StatusTag';

import { TaskPushStrategyType } from '../../interface/common.interface';

import styles from './index.module.scss';

interface IProps {
  type: string;
  strategiesList: StrategyList[];
  taskPushStrategyEnums: BizDict[] | undefined;
  stageEnums: BizDict[] | undefined;
}

export const StrategyInfoCard: React.FC<IProps> = ({
  stageEnums,
  strategiesList,
  taskPushStrategyEnums,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const taskPushStrategyMap: Record<string, string> = useMemo(
    () =>
      taskPushStrategyEnums?.reduce(
        (pre, cur) => ({
          ...pre,
          [cur.enumItemName || (cur.dictValue as string)]:
            cur.dictValueI18n || cur.dictValueName,
        }),
        {}
      ) || {},
    [taskPushStrategyEnums]
  );

  return (
    <>
      {strategiesList?.map(strategyListItem => {
        const {
          strategyName,
          taskPushStrategy,
          numOfTaskAssignWhenUserAsk,
          teams,
        } = strategyListItem.strategy;
        return (
          <div className={styles.strategyInfoCardWrapper}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <TextBody
                type="h5"
                weight={700}
                style={{ color: styles.textColor }}
              >
                {strategyName}
              </TextBody>
              {stageEnums && strategyListItem.stage && (
                <StatusTag
                  statusI18n={
                    stageEnums.find(
                      stageItem =>
                        stageItem.enumItemName === strategyListItem.stage
                    )?.itemName ?? ''
                  }
                  type="success"
                />
              )}
            </div>

            {taskPushStrategy && (
              <div className={styles.strategyItem}>
                <TextBody
                  weight={500}
                  style={{ color: styles.textColorTertiary }}
                >
                  {t('Task Push Strategy')}
                </TextBody>
                <TextBody weight={500} style={{ color: styles.textColor }}>
                  {taskPushStrategyMap?.[taskPushStrategy]}
                </TextBody>
              </div>
            )}
            {numOfTaskAssignWhenUserAsk &&
              taskPushStrategy ===
                TaskPushStrategyType.TaskPushWhenUserAskFor && (
                <div className={styles.strategyItem}>
                  <TextBody
                    weight={500}
                    style={{ color: styles.textColorTertiary }}
                  >
                    {t('Number of task assign when user ask')}
                  </TextBody>
                  <TextBody weight={500} style={{ color: styles.textColor }}>
                    {numOfTaskAssignWhenUserAsk}
                  </TextBody>
                </div>
              )}
            {teams?.length && (
              <div className={styles.strategyItem}>
                <TextBody
                  weight={500}
                  style={{ color: styles.textColorTertiary }}
                >
                  {t('For Team')}
                </TextBody>
                <div>
                  {teams.map(teamItem => (
                    <span className={styles.teamLabel}>
                      {teamItem?.teamName}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </>
  );
};
