import React, {
  useCallback,
  useEffect,
  useState,
  Dispatch,
  SetStateAction,
} from 'react';
import {
  useGetDict,
  useGetStrategiesListInfo,
} from '@uw/pages/configuration-pages/TaskAssignmentStrategy/hooks/request';

import { StepKey } from '../../interface/common.interface';

import { StrategyInfoCard } from './StrategyInfoCard';
import { StrategyDrawer } from './StrategyDrawer';
import { convertModuleType } from '../convertModuleType';

interface Props {
  visibleKey: StepKey;
  type: string;
  setVisibleKey: Dispatch<SetStateAction<string | undefined>>;
}

export const Strategy: React.FC<Props> = ({
  visibleKey,
  type,
  setVisibleKey,
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (visibleKey === StepKey.StrategyStep) {
      setVisible(true);
    }
  }, [visibleKey]);

  const handleClose = useCallback(() => {
    setVisibleKey(undefined);
    setVisible(false);
  }, [setVisibleKey]);

  const moduleType = convertModuleType(type);
  const { strategiesList, getStrategyList } =
    useGetStrategiesListInfo(moduleType);
  const [dictMap] = useGetDict(['taskPushStrategy', 'stage'], moduleType);

  return (
    <>
      <StrategyInfoCard
        type={type}
        strategiesList={strategiesList || []}
        taskPushStrategyEnums={dictMap?.taskPushStrategy}
        stageEnums={dictMap?.stage}
      />
      <StrategyDrawer
        getStrategyList={getStrategyList}
        type={type}
        visible={visible}
        setVisible={handleClose}
        stageEnums={dictMap?.stage}
        taskPushStrategyEnums={dictMap?.taskPushStrategy}
      />
    </>
  );
};
