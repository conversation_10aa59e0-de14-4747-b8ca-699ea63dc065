import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useContext,
} from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep, capitalize } from 'lodash-es';
import { Form, Button, Popconfirm } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  TeamType,
  Strategy,
  StrategyField,
  StrategiesUnionType,
  TeamOrder,
  PriorRecord,
  OrdinaryRecord,
} from 'genesis-web-service';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { TextBody } from '@uw/components/Text';

import { ContextType } from '../../interface/common.interface';
import { getContext } from '../../utils/getContext';
import {
  UPDATE_TEAM_STRATEGY_MAP,
  UPDATE_STRATEGYTEAMORDER,
} from '../../TeamProvider';
import { StrategyMainCard } from './StrategyMainCard';
import { TeamSort } from './TeamSort';
import styles from './index.module.scss';
import { useGetStrageryList } from '../../hooks/request';
import { convertModuleType } from '../convertModuleType';

interface IProps {
  setVisible: () => void;
  type: string;
  getStrategyList: () => void;
  taskPushStrategyEnums: BizDict[] | undefined;
  tabActiveKey: string | undefined;
  submitLoading: boolean;
  submitStrategies: (formInfo: StrategiesUnionType) => Promise<unknown>;
  loading: boolean;
  getStrategiesUnion: (tabActiveKey?: string) => void;
}

export const StrategyForm: React.FC<IProps> = ({
  setVisible,
  type,
  getStrategyList,
  taskPushStrategyEnums,
  tabActiveKey,
  submitLoading,
  submitStrategies,
  loading,
  getStrategiesUnion,
}) => {
  const [formRef] = Form.useForm();
  const [t] = useTranslation(['uw', 'common']);
  const { state, dispatch } = useContext(
    getContext(capitalize(type) as ContextType)
  );
  const { strategyTeamOrderList, strategies, hasEditAuth, teams } = state;
  const moduleType = convertModuleType(type);
  const { priorityConditionList, oridinaryConditionList } = useGetStrageryList(
    moduleType,
    tabActiveKey as string
  );

  const notAssignedTeams = useMemo(() => {
    const notAssignedTeamsList = teams?.filter(totalTeamsListItem =>
      strategyTeamOrderList?.every(
        strategyTeamOrderListItem =>
          strategyTeamOrderListItem.teamId !== +totalTeamsListItem.id
      )
    );
    const names = notAssignedTeamsList?.reduce(
      (pre, cur) => `${pre} ${cur.teamName}`,
      ''
    );
    return names;
  }, [teams, strategyTeamOrderList]);

  useEffect(() => {
    formRef?.setFieldsValue({
      dynamicStrategyLists: strategies.map((item: Strategy) => {
        const conditions: [OrdinaryRecord, PriorRecord] = item?.conditions;
        let priors: PriorRecord[] = conditions?.filter(
          record => (record as PriorRecord)?.orderNo
        ) as PriorRecord[];

        priors = priors?.sort((prev, next) => prev?.orderNo - next?.orderNo);

        const ordinaries: OrdinaryRecord[] = conditions?.filter(
          record => !(record as PriorRecord)?.orderNo
        ) as OrdinaryRecord[];

        return {
          ...item,
          // 后端有可能会返回number类型 导致回显失败
          taskPushStrategy: String(item?.taskPushStrategy),
          priors,
          ordinaries,
          teams: item.teams.map(teamItem => teamItem?.id),
        };
      }),
    });
  }, [strategies, formRef]);

  // id[] => {id, teamName}[]
  const restoreTeam = useCallback(
    (ids: number[]): TeamType[] =>
      ids.map(id =>
        teams?.find(totalTeamsListItem => +totalTeamsListItem.id === id)
      ),
    [teams]
  );

  // 获取表单数据
  const getFieldsValues = useCallback((): StrategyField[] => {
    const fieldsValues = formRef.getFieldsValue();
    const tempData: StrategyField[] = [];
    Object.entries(fieldsValues).forEach(([, value]) => {
      if (Array.isArray(value)) {
        tempData.push(
          ...value.filter(
            item => !!item && Object.values(item).some(itemValue => !!itemValue)
          )
        );
      }
    });
    return tempData;
  }, [formRef]);

  const getOrderStrategyTeams = useCallback(
    (tempData: StrategyField[]): TeamOrder[] => {
      let tempTeamsEdit: number[] = [];
      tempData.forEach(item => {
        tempTeamsEdit.push(...item.teams);
      });
      tempTeamsEdit = Array.from(new Set(tempTeamsEdit)); // tempTeamsEdit：去重后选中的teams列表
      let orderTempTeams: {
        orderNo: number;
        teamName: string;
        teamId?: number;
        id?: number;
      }[] = [];
      strategyTeamOrderList.forEach(strategyTeamOrderListItem => {
        if (
          tempTeamsEdit.some(
            tempTeamsEditItem =>
              strategyTeamOrderListItem.teamId === tempTeamsEditItem
          )
        ) {
          orderTempTeams.push(strategyTeamOrderListItem);
        }
      });
      tempTeamsEdit.forEach(tempTeamsEditItem => {
        if (
          !strategyTeamOrderList.some(
            strategyTeamOrderListItem =>
              strategyTeamOrderListItem.teamId === tempTeamsEditItem
          )
        ) {
          orderTempTeams.push(
            teams?.find(item => +item.id === tempTeamsEditItem)
          );
        }
      });
      orderTempTeams = orderTempTeams.map((orderTempTeamItem, index) => ({
        ...orderTempTeamItem,
        teamId: orderTempTeamItem?.id || orderTempTeamItem?.teamId,
        orderNo: index + 1,
      }));
      return orderTempTeams as TeamOrder[];
    },
    [strategyTeamOrderList, teams]
  );

  // 更新store中的strategyTeamOrderList
  const updateStrategyTeamorder = (orderTempTeams: TeamOrder[]) => {
    dispatch({
      type: UPDATE_STRATEGYTEAMORDER,
      strategyTeamOrderList: orderTempTeams,
    });
  };

  // 更新store中的teamStrategyMap
  const updateTeamStrategyMap = (
    tempData: StrategyField[],
    orderTempTeams: TeamOrder[]
  ) => {
    const teamTempStrategyMap: Record<string, Strategy[]> = {};
    orderTempTeams.forEach(orderTempTeamItem => {
      tempData.forEach(tempDataItem => {
        if (tempDataItem.teams?.includes(orderTempTeamItem.teamId)) {
          if (teamTempStrategyMap[orderTempTeamItem.teamId]) {
            teamTempStrategyMap[orderTempTeamItem.teamId].push({
              ...tempDataItem,
              teams: restoreTeam(tempDataItem.teams),
            });
          } else {
            teamTempStrategyMap[orderTempTeamItem.teamId] = [
              { ...tempDataItem, teams: restoreTeam(tempDataItem.teams) },
            ];
          }
        }
      });
    });
    dispatch({
      type: UPDATE_TEAM_STRATEGY_MAP,
      teamStrategyMap: teamTempStrategyMap,
    });
  };

  const onValuesChange = () => {
    // 1、 获得表单数据 不可更新UPDATE_STRATEGIES
    const fieldsValues = getFieldsValues();
    // 2、 基于strategyTeamOrderList和表格fields获得更新的teamOrderList
    const orderStrategyiesTeams = getOrderStrategyTeams(fieldsValues);
    updateStrategyTeamorder(orderStrategyiesTeams);
    // 3、更新store中的teamStrategyMap
    updateTeamStrategyMap(fieldsValues, orderStrategyiesTeams);
  };

  const onTeamSortEnd = useCallback(
    ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }) => {
      let tempOrderTeams = cloneDeep(strategyTeamOrderList);
      const tempTeam = tempOrderTeams[oldIndex];
      tempOrderTeams[oldIndex] = tempOrderTeams[newIndex];
      tempOrderTeams[newIndex] = tempTeam;
      tempOrderTeams = tempOrderTeams.map((item, index) => ({
        ...item,
        orderNo: index + 1,
      }));
      dispatch({
        type: UPDATE_STRATEGYTEAMORDER,
        strategyTeamOrderList: tempOrderTeams,
      });
    },
    [strategyTeamOrderList, dispatch]
  );

  const [popShow, setPopShow] = useState(false);

  const submit = useCallback(async () => {
    const fieldsValues = getFieldsValues();
    // 只是为了获取长度
    const cloneStrategies = cloneDeep(fieldsValues) || [];

    const strategiesData = (cloneDeep(fieldsValues) || [])?.map(
      (strategyItem: StrategyField, index) => {
        const copyStrategyItem: Partial<StrategyField> =
          cloneDeep(strategyItem);
        let conditions: OrdinaryRecord[] | PriorRecord[] = [];
        // 如果填写的含有 prior 那就放进 conditions 中
        if (copyStrategyItem?.priors) {
          conditions = (conditions as PriorRecord[])?.concat(
            (copyStrategyItem?.priors as PriorRecord[])?.map?.(
              (item: PriorRecord, ind: number) => ({
                ...item,
                orderNo: ind + 1,
              })
            ) as PriorRecord[]
          );
        }
        // 如果填写的包含 ordinary 那就放进 conditions 中
        if (copyStrategyItem?.ordinaries) {
          conditions = (conditions as OrdinaryRecord[])?.concat(
            copyStrategyItem?.ordinaries as OrdinaryRecord[]
          );
        }
        // 过滤掉 factorCode 为空的 conditions
        conditions = (conditions as PriorRecord[])?.filter(
          (item: OrdinaryRecord | PriorRecord) => !!item?.factorCode
        );
        // 删除源数据中的prior 和 oridnary 防止提交到后端
        delete copyStrategyItem.priors;
        delete copyStrategyItem.ordinaries;

        return {
          ...copyStrategyItem,
          teamIds: copyStrategyItem.teams,
          strategyName: !(
            copyStrategyItem as StrategyField & { isAdd: boolean }
          ).isAdd
            ? copyStrategyItem.strategyName
            : t('Strategy', { name: String(cloneStrategies?.length + index) }),
          conditions,
        };
      }
    );
    const params = {
      stage: tabActiveKey,
      strategies: strategiesData,
      teamOrders: strategyTeamOrderList,
    };

    const isSuccess = await submitStrategies(
      params as unknown as StrategiesUnionType
    );
    setPopShow(false);
    if (isSuccess) {
      getStrategyList(); // 更新外部strateglist
      getStrategiesUnion(tabActiveKey);
    }
  }, [
    submitStrategies,
    strategyTeamOrderList,
    t,
    getStrategyList,
    tabActiveKey,
    getStrategiesUnion,
    getFieldsValues,
  ]);

  const handleValidate = useCallback(async () => {
    await formRef.validateFields();
  }, [formRef]);

  const onSubmit = useCallback(async () => {
    await handleValidate();
    if (notAssignedTeams) {
      setPopShow(true);
      return false;
    }
    submit();
  }, [notAssignedTeams, submit, handleValidate]);

  const onClose = useCallback(() => {
    setVisible();
    formRef.resetFields();
  }, [setVisible, formRef]);

  const formEditable = useMemo(
    () => !submitLoading && hasEditAuth && !loading,
    [hasEditAuth, submitLoading, loading]
  );

  return (
    <div>
      <Form
        name="strategyForm"
        form={formRef}
        layout="vertical"
        onValuesChange={onValuesChange}
        disabled={!formEditable}
      >
        <Form.List name="dynamicStrategyLists">
          {(fields, { add, remove }) => (
            <>
              <Button
                className={styles.addStrategyButton}
                icon={<PlusOutlined />}
                onClick={() =>
                  add(
                    {
                      strategyName: t('Strategy Name (auto generate)'),
                      isAdd: true,
                    },
                    0
                  )
                }
              >
                {t('Add New Strategy')}
              </Button>
              {fields.map((fieldsProp, index) => (
                <StrategyMainCard
                  formRef={formRef}
                  key={index}
                  showMoreAction={!!hasEditAuth}
                  removeItem={remove}
                  fieldsProp={fieldsProp}
                  fieldKey={index}
                  teamOptions={teams || []}
                  taskPushStrategyEnums={taskPushStrategyEnums}
                  type={type}
                  priorityConditionList={priorityConditionList}
                  oridinaryConditionList={oridinaryConditionList}
                />
              ))}
            </>
          )}
        </Form.List>
        <div>
          <TextBody
            weight={700}
            style={{ color: styles.textColor, marginBottom: styles.gapXs }}
          >
            {t('Order the Teams while the assignment rule are same')}
          </TextBody>
          {strategyTeamOrderList?.length ? (
            <TeamSort
              lockAxis={'y'}
              helperClass={styles.dragTeam}
              onSortEnd={onTeamSortEnd}
              list={strategyTeamOrderList}
              totalTeamsList={teams || []}
            />
          ) : (
            <div style={{ marginBottom: styles.gapXs }}>
              <TextBody
                weight={700}
                style={{ color: styles.textColorTertiary }}
              >
                {t('The team selected above will be brought in here')}
              </TextBody>
            </div>
          )}
        </div>
      </Form>
      <div className={styles.stragegyDrawerFooter}>
        <Button onClick={onClose}>{t('Cancel')}</Button>
        <Popconfirm
          placement="topRight"
          title={t(
            '{Team Name} is not assigned any strategy, Confirm to submit?',
            { teamNames: notAssignedTeams }
          )}
          open={popShow}
          okText={t('Yes')}
          cancelText={t('No')}
          onConfirm={submit}
          onCancel={() => setPopShow(false)}
        ></Popconfirm>
        <Button
          type="primary"
          loading={submitLoading}
          disabled={!formEditable}
          onClick={onSubmit}
          style={{ marginLeft: 16 }}
        >
          {t('Submit')}
        </Button>
      </div>
    </div>
  );
};
