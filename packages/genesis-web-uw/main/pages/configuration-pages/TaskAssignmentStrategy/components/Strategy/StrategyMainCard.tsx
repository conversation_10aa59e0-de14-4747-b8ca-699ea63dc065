import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Form } from 'antd';
import type { FormInstance } from 'antd/lib/form';

import { StrategyMainCardInfoProps } from './interface';
import { StrategyMainCardInfo } from './StrategyMainCardInfo';
import { TaskPushStrategyType } from '../../interface/common.interface';

interface IProps extends StrategyMainCardInfoProps {
  formRef: FormInstance;
  type: string;
}

export const StrategyMainCard: React.FC<IProps> = ({
  formRef,
  ...restProps
}) => {
  const [showNumberTask, setShowNumberTask] = useState(false);
  const [form] = Form.useForm();

  const curFormData = useMemo(
    () =>
      restProps.cardInfo
        ? form.getFieldsValue()
        : formRef.getFieldValue('dynamicStrategyLists')?.[
            restProps.fieldKey as number
          ],
    [
      form.getFieldsValue(),
      formRef.getFieldValue('dynamicStrategyLists'),
      restProps.cardInfo,
      restProps.fieldKey,
    ]
  );

  useEffect(() => {
    setShowNumberTask(
      curFormData?.taskPushStrategy ===
        TaskPushStrategyType.TaskPushWhenUserAskFor
    );
  }, [curFormData]);
  const handleChangeTaskPushStrategy = useCallback<(val: string) => void>(
    val => {
      setShowNumberTask(val === TaskPushStrategyType.TaskPushWhenUserAskFor);
      const newData = formRef.getFieldValue('dynamicStrategyLists');
      newData[restProps.fieldKey as number] = {
        ...newData[restProps.fieldKey as number],
        numOfTaskAssignWhenUserAsk: undefined,
      };
      formRef.setFieldsValue({
        dynamicStrategyLists: newData,
      });
    },
    [formRef.getFieldValue('dynamicStrategyLists'), restProps.fieldKey]
  );
  return restProps.cardInfo ? (
    <Form
      form={form}
      layout="vertical"
      disabled
      initialValues={restProps.cardInfo}
    >
      <StrategyMainCardInfo
        {...restProps}
        form={form}
        formRef={formRef}
        showNumberTask={showNumberTask}
        handleChangeTaskPushStrategy={handleChangeTaskPushStrategy}
      />
    </Form>
  ) : (
    <StrategyMainCardInfo
      {...restProps}
      form={form}
      formRef={formRef}
      showNumberTask={showNumberTask}
      handleChangeTaskPushStrategy={handleChangeTaskPushStrategy}
    />
  );
};
