import React from 'react';
import { useTranslation } from 'react-i18next';

import { InfoCircleOutlined } from '@ant-design/icons';
import { Col, Form, FormInstance, Row, Select, Tooltip } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { MoreAction } from '@uw/components/MoreAction';
import { TextBody } from '@uw/components/Text/TextBody';

import { OrdinaryCondition } from '../Ordinary/OrdinaryCondition';
import { PriorCondition } from '../Prior/PriorCondition';
import styles from './index.module.scss';
import { StrategyMainCardInfoProps } from './interface';

const { Option } = Select;

interface CardProps extends StrategyMainCardInfoProps {
  showNumberTask: boolean;
  handleChangeTaskPushStrategy: (val: string) => void;
  form: FormInstance;
  type: string;
}

export const StrategyMainCardInfo: React.FC<CardProps> = ({
  showMoreAction,
  cardInfo,
  teamOptions,
  removeItem,
  fieldKey,
  fieldsProp,
  taskPushStrategyEnums,
  showNumberTask,
  handleChangeTaskPushStrategy,
  type,
  form,
  formRef,
  priorityConditionList = [],
  oridinaryConditionList = [],
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { name = '', ...rest } = fieldsProp || {};

  return (
    <Form.Item>
      <div className={styles.cardContainer}>
        <div
          className={styles.cardHeader}
          style={{
            justifyContent: showMoreAction ? 'space-between' : 'flex-start',
          }}
        >
          <Col span="10">
            <Form.Item
              name={cardInfo ? 'strategyName' : [name, 'strategyName']}
              validateTrigger={['onBlur']}
            >
              <Input bordered={false} disabled />
            </Form.Item>
          </Col>
          {showMoreAction && (
            <MoreAction
              menuList={[
                {
                  key: 'delete',
                  label: 'Delete',
                  disabled: false,
                  onClick: () => {
                    removeItem?.(fieldKey);
                  },
                },
              ]}
            />
          )}
        </div>
        <div className={styles.cardBody}>
          <Row justify="space-between">
            <Col span="10">
              <Form.Item
                label={t('Task Push Strategy')}
                {...rest}
                validateTrigger={['onBlur']}
                rules={[{ required: true, message: t('Please select') }]}
                name={
                  cardInfo ? 'taskPushStrategy' : [name, 'taskPushStrategy']
                }
              >
                <Select
                  placeholder={t('Please select')}
                  allowClear
                  onChange={e => {
                    handleChangeTaskPushStrategy(e);
                  }}
                >
                  {taskPushStrategyEnums?.map((item: BizDict) => (
                    <Option value={item.enumItemName} key={item?.enumItemName}>
                      {item.dictValueName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {showNumberTask && (
              <Col span="10">
                <Form.Item
                  label={t('Number of task assign when user ask')}
                  {...rest}
                  validateTrigger={['onBlur']}
                  name={
                    cardInfo
                      ? 'numOfTaskAssignWhenUserAsk'
                      : [name, 'numOfTaskAssignWhenUserAsk']
                  }
                  rules={[
                    {
                      required: showNumberTask,
                      message: t('Please input'),
                    },
                  ]}
                >
                  <Input type="number" />
                </Form.Item>
              </Col>
            )}
          </Row>
          <TextBody weight={700}>
            {t('Task Push Supplementary Strategy')}
            <Tooltip
              title={t(
                'For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.'
              )}
            >
              <InfoCircleOutlined style={{ marginLeft: '6px' }} />
            </Tooltip>
          </TextBody>
          <PriorCondition
            cardInfo={cardInfo}
            fieldsProp={fieldsProp}
            formRef={formRef}
            priorityConditionList={priorityConditionList}
            type={type}
            form={form}
          />
          <OrdinaryCondition
            cardInfo={cardInfo}
            fieldsProp={fieldsProp}
            formRef={formRef}
            oridinaryConditionList={oridinaryConditionList}
            type={type}
            form={form}
          />
          {teamOptions?.[0] ? (
            <Row>
              <Col span="24">
                <Form.Item
                  label={t('Team(s) for the stragegy')}
                  name={[name, 'teams']}
                  rules={[{ required: true, message: t('Please select') }]}
                >
                  <Select mode="multiple" allowClear>
                    {teamOptions?.map(teamOptionItem => (
                      <Option
                        value={teamOptionItem.id || teamOptionItem.teamId}
                        key={teamOptionItem?.id ?? teamOptionItem?.teamId}
                      >
                        {teamOptionItem.teamName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          ) : null}
        </div>
      </div>
    </Form.Item>
  );
};
