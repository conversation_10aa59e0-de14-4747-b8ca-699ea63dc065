@import '@uw/styles/variables.scss';

.stragegy-drawer-container {
  :global {
    .#{$ant-prefix}-drawer-content {
      position: relative;
    }
    .#{$ant-prefix}-tabs-content {
      position: static;
    }
    .#{$ant-prefix}-drawer-body {
      padding-bottom: 66px;
    }
    .anticon-close,
    .#{$ant-prefix}-drawer-title {
      font-weight: 700;
      color: var(--text-color);
    }
  }
}
.stragegy-drawer {
  .wording-title {
    margin-bottom: var(--gap-xs);
  }
}
.stragegy-drawer-footer {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: $gap-md $gap-lg;
  border-top: 1px solid #f0f0f0;
  background: white;
}

.stragegyTabListMenu {
  :global {
    .#{$ant-prefix}-tabs-nav .#{$ant-prefix}-tabs-nav-list {
      .#{$ant-prefix}-tabs-tab {
        color: var(--text-color);
        font-weight: 500;
        padding: $gap-md $gap-lg;
        width: 240px;
        &.#{$ant-prefix}-tabs-tab-active {
          background: var(--primary-disabled-color);
          color: var(--primary-hover-color);
          font-weight: 700;
        }
        &.#{$ant-prefix}-tabs-tab-disabled {
          color: var(--disabled-color);
        }
      }
    }
  }
}
.page-content {
  background-color: white;
  padding-right: 106px;
  .add-strategy-button {
    font-weight: 500;
    margin-bottom: $gap-md;
  }
}
.card-container {
  width: 100%;
  border: 1px solid var(--border-line-color-secondary);
  border-radius: var(--border-radius-base);
  padding: $gap-lg;
  padding-bottom: 0px;
  font-size: $font-size-root;
  background-color: white;
  .card-header {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 700;
    border-bottom: 0.5px solid var(--border-gray);
    margin-bottom: $gap-md;
    :global {
      .#{$ant-prefix}-form-item {
        margin-bottom: var(--gap-xs);
        margin-left: -($gap-sm);
      }
      .#{$ant-prefix}-input[disabled] {
        cursor: auto;
        font-weight: 700;
        color: var(--text-color);
      }
    }
  }
  .card-body {
    :global {
      .#{$ant-prefix}-form-item-label > label {
        font-weight: 500;
        color: var(--text-color);
      }
    }
  }
}
.assignment-team-sort {
  cursor: pointer;
  color: var(--text-color);
  margin-bottom: var(--gap-xs);
  height: 22px;
  line-height: 22px;
  font-weight: 500;
}
.drag-team {
  background: var(--item-bg-selected);
  z-index: 1999;
}

.strategy-info-card-wrapper {
  background-color: white;
  padding: $gap-lg;
  margin-bottom: $gap-md;
  box-shadow: 0px 4px 24px rgba(16, 42, 67, 0.12);
  border-radius: 20px;
  .strategy-name {
    color: var(--text-color);
  }
  .strategy-item {
    margin-top: $gap-md;
    .team-label {
      display: inline-block;
      padding: 0 var(--gap-xs);
      background: var(--item-bg-selected);
      border-radius: var(--border-radius-medium);
      height: 24px;
      line-height: 24px;
      color: var(--primary-color);
      margin-right: var(--gap-xs);
      margin-bottom: var(--gap-xss);
    }
  }
}

:export {
  gapLg: $gap-lg;
  gapMd: $gap-md;
  gapXss: var(--gap-xss);
  gapXs: var(--gap-xs);
  textColorTertiary: var(--text-color-tertiary);
  textColor: var(--text-color);
}
