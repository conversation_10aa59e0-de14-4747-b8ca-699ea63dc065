import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Drawer, Tabs } from 'antd';
import { capitalize } from 'lodash-es';
import { TextBody } from '@uw/components/Text';
import { useStrategiesInfo } from '@uw/pages/configuration-pages/TaskAssignmentStrategy/hooks/request';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import styles from './index.module.scss';
import { StrategyForm } from './StrategyForm';
import { convertModuleType } from '../convertModuleType';

interface IProps {
  visible: boolean;
  setVisible: () => void;
  type: string;
  getStrategyList: () => void;
  stageEnums: BizDict[] | undefined;
  taskPushStrategyEnums: BizDict[] | undefined;
}

export const StrategyDrawer: React.FC<IProps> = ({
  visible,
  setVisible,
  type,
  getStrategyList,
  stageEnums,
  taskPushStrategyEnums,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [tabActiveKey, setTabActiveKey] = useState<string>();

  useEffect(() => {
    setTabActiveKey(stageEnums?.[0]?.enumItemName as string);
  }, [stageEnums?.[0]?.enumItemName]);

  const moduleType = convertModuleType(type);
  const { loading, submitLoading, submitStrategies, getStrategiesUnion } =
    useStrategiesInfo(moduleType);

  useEffect(() => {
    if (tabActiveKey) getStrategiesUnion(tabActiveKey);
  }, [tabActiveKey, getStrategiesUnion]);

  return (
    <div>
      <Drawer
        title={t('Edit Strategy')}
        headerStyle={{ fontWeight: 700 }}
        className={styles.stragegyDrawerContainer}
        width="70%"
        open={visible}
        maskClosable={false}
        onClose={() => setVisible()}
        destroyOnClose
      >
        <div className={styles.stragegyDrawer}>
          <TextBody
            weight={700}
            style={{ color: styles.textColor, marginBottom: styles.gapMd }}
          >
            {t('{{ stage }} Stage', { stage: capitalize(type) })}
          </TextBody>
          <Tabs
            tabPosition="left"
            className={styles.stragegyTabListMenu}
            activeKey={tabActiveKey}
            onChange={tabkey => {
              setTabActiveKey(tabkey);
            }}
          >
            {stageEnums?.map(stage => (
              <Tabs.TabPane
                tab={stage.itemName}
                key={stage.enumItemName}
                disabled={stage.enumItemName === 'escalate'}
                destroyInactiveTabPane
              >
                <div className={styles.pageContent}>
                  <StrategyForm
                    tabActiveKey={tabActiveKey}
                    setVisible={setVisible}
                    type={type}
                    getStrategyList={getStrategyList}
                    taskPushStrategyEnums={taskPushStrategyEnums}
                    submitLoading={submitLoading}
                    submitStrategies={submitStrategies}
                    loading={loading}
                    getStrategiesUnion={getStrategiesUnion}
                  />
                </div>
              </Tabs.TabPane>
            ))}
          </Tabs>
        </div>
      </Drawer>
    </div>
  );
};
