import React, { useMemo } from 'react';
import { MenuOutlined } from '@ant-design/icons';
import { SortableContainer, SortableElement } from 'react-sortable-hoc';
import { TeamOrder } from 'genesis-web-service';

import { TeamInfo } from '../../TeamProvider';

import styles from './index.module.scss';

const SortableItem = SortableElement(({ value }: { value: string }) => (
  <div style={{ cursor: 'pointer' }} className={styles.assignmentTeamSort}>
    <MenuOutlined style={{ marginRight: styles.gapMd }} />
    {value}
  </div>
));

interface IProps {
  list: TeamOrder[];
  totalTeamsList: TeamInfo[];
}

const Team: React.FC<IProps> = ({ list, totalTeamsList }: IProps) => {
  const nameMap = useMemo(
    (): Record<string, string> =>
      totalTeamsList?.reduce(
        (pre, cur) => ({ ...pre, [cur.id]: cur.teamName }),
        {}
      ) || {},
    [totalTeamsList]
  );
  return (
    <div>
      {list.map((listItem: TeamOrder, index: number) => (
        <SortableItem
          key={`item-${listItem.teamId}`}
          index={index}
          value={nameMap[listItem.teamId]}
        />
      ))}
    </div>
  );
};

export const TeamSort = SortableContainer(Team);
