import { FormInstance } from 'antd';
import {
  Strategy,
  StrategiesConditionType,
  OrdinaryRecord,
  PriorRecord,
} from 'genesis-web-service';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { TeamInfo } from '../../TeamProvider';

export type Conditions = [OrdinaryRecord, PriorRecord];

export interface CommonProps {
  cardInfo?: Partial<
    | Strategy
    | {
        conditions: Conditions;
      }
  >;
  fieldsProp?: {
    name: number | string;
    key: number | string;
    fieldKey?: number | string;
  };
  type: string;
  formRef?: FormInstance;
}

export interface StrategyMainCardInfoProps
  extends Exclude<CommonProps, 'type'> {
  removeItem?: (index: number) => void;
  showMoreAction?: boolean;
  teamOptions?: TeamInfo[];
  taskPushStrategyEnums: BizDict[] | undefined;
  fieldKey: number;
  priorityConditionList: StrategiesConditionType[];
  oridinaryConditionList: StrategiesConditionType[];
}
