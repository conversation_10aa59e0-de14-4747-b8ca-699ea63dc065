import React, { FC, useEffect, useReducer } from 'react';

import { usePermission } from '@uw/hook/permission';

import {
  initialContextValue,
  TeamAction,
  TeamReducer,
  TeamState,
  UPDATE_AUTHINFO,
  PosTeamContext,
} from './TeamProvider';
import { TeamSteps } from './components/TeamSteps';
import { ContextType } from './interface/common.interface';

const Pos: FC = () => {
  const [state, dispatch] = useReducer<React.Reducer<TeamState, TeamAction>>(
    TeamReducer,
    initialContextValue
  );
  const hasPosEditAuth = !!usePermission('pos.task-assign.configuration.edit');
  useEffect(() => {
    if (dispatch) {
      dispatch({ type: UPDATE_AUTHINFO, hasEditAuth: hasPosEditAuth });
    }
  }, [dispatch, hasPosEditAuth]);

  return (
    <PosTeamContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <TeamSteps type={ContextType.Pos} />
    </PosTeamContext.Provider>
  );
};

export default Pos;
