import {
  YesOrNo,
  QueryRules,
  Strategy,
  TeamOrder,
  StrategyList as StrategyType,
  TeamSummaryInfo,
} from 'genesis-web-service';
import React, { Dispatch } from 'react';

// reducer
export const UPDATE_AUTHINFO = 'UPDATE_AUTHINFO';
export const UPDATE_RULES = 'UPDATE_RULES';
export const UPDATE_TEAMS = 'UPDATE_TEAAMS';
export const UPDATE_STRATEGIES = 'UPDATE_STRATEGIES';
export const UPDATE_STRATEGYLIST = 'UPDATE_STRATEGYLIST';
export const UPDATE_TEAM_STRATEGY_MAP = 'UPDATE_TEAM_STRATEGY_MAP';
export const UPDATE_STRATEGYTEAMORDER = 'UPDATE_STRATEGYTEAMORDER';
export const UPDATE_RULETEAMMAP = 'UPDATE_RULETEAMMAP';

export interface AuthInfo {
  hasEditAuth: boolean;
}
export interface Rules {
  rules: QueryRules[];
}
export interface TeamInfo {
  teamName: string;
  teamId?: string;
  teamMembers?: string[];
  bindingRules?: string;
  id: string;
}
export interface Teams {
  teams: TeamInfo[];
}
export interface StrategyInfo {
  strategy: string;
  taskNumber: number;
  isOnduty: YesOrNo;
  teams: string[];
}
export interface Strategies {
  strategies: Strategy[];
}
export interface StrategyList {
  strategyList: StrategyType[];
}
export interface TeamStrategyMap {
  teamStrategyMap: Record<string, Strategy[]>;
}
export interface StrategyTeamOrderList {
  strategyTeamOrderList: TeamOrder[];
}
export interface RuleTeamMap {
  ruleTeamMap: Record<number, TeamSummaryInfo[]>;
}

export type TeamState = AuthInfo &
  Strategies &
  StrategyList &
  Rules &
  Teams &
  TeamStrategyMap &
  StrategyTeamOrderList &
  RuleTeamMap;

export type TeamReducerType =
  | AuthInfo
  | Strategies
  | StrategyList
  | Rules
  | Teams
  | TeamStrategyMap
  | StrategyTeamOrderList
  | RuleTeamMap;

export type TeamAction = {
  readonly type: string;
} & TeamReducerType;

export const TeamReducer = (
  state: TeamState,
  action: TeamAction
): TeamState => {
  switch (action.type) {
    case UPDATE_AUTHINFO:
      return {
        ...state,
        hasEditAuth: (action as AuthInfo).hasEditAuth,
      };
    case UPDATE_RULES:
      return {
        ...state,
        rules: (action as Rules).rules,
      };
    case UPDATE_TEAMS:
      return {
        ...state,
        teams: (action as Teams).teams,
      };
    case UPDATE_TEAM_STRATEGY_MAP:
      return {
        ...state,
        teamStrategyMap: (action as TeamStrategyMap).teamStrategyMap,
      };
    case UPDATE_STRATEGIES:
      return {
        ...state,
        strategies: (action as Strategies).strategies,
      };
    case UPDATE_STRATEGYLIST:
      return {
        ...state,
        strategyList: (action as StrategyList).strategyList,
      };
    case UPDATE_STRATEGYTEAMORDER:
      return {
        ...state,
        strategyTeamOrderList: (action as StrategyTeamOrderList)
          .strategyTeamOrderList,
      };
    case UPDATE_RULETEAMMAP:
      return {
        ...state,
        ruleTeamMap: (action as RuleTeamMap).ruleTeamMap,
      };
    default:
      return state;
  }
};

export const UnderwritingTeamContext = React.createContext<{
  state: TeamState;
  dispatch: Dispatch<TeamAction>;
}>(
  {} as {
    state: TeamState;
    dispatch: Dispatch<TeamAction>;
  }
);

export const ClaimTeamContext = React.createContext<{
  state: TeamState;
  dispatch: Dispatch<TeamAction>;
}>(
  {} as {
    state: TeamState;
    dispatch: Dispatch<TeamAction>;
  }
);

export const PosTeamContext = React.createContext<{
  state: TeamState;
  dispatch: Dispatch<TeamAction>;
}>(
  {} as {
    state: TeamState;
    dispatch: Dispatch<TeamAction>;
  }
);

export const initialContextValue = {
  rules: [],
  teams: [],
  strategies: [],
  strategyList: [],
  strategyTeamOrderList: [],
  teamStrategyMap: {},
  hasEditAuth: false,
  ruleTeamMap: {},
};
