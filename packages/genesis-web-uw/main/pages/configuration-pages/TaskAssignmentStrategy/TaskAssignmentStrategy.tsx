import React, { FC, Suspense } from 'react';
import { Spin, Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import { Route, Routes, useNavigate, useParams } from 'react-router-dom';
import { taskAssignmentStrategyRoutes } from '@uw/router';

import { TextBody } from '@uw/components/Text';

import { useTaskTab } from './hooks/useTaskTab';
import styles from './TaskAssignmentStrategy.module.scss';

export const TaskAssignmentStrategy: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const params = useParams();
  const childRoute = params['*'];
  const [tabs] = useTaskTab(childRoute);

  const handleClickTab = (activeKey: string) => {
    navigate(activeKey);
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflow: 'hidden',
      }}
    >
      <section className={styles.assignmentHeaderWrapper}>
        <TextBody
          type="h4"
          weight={'bold'}
          style={{ margin: `0 ${styles.gapLg} ${styles.gapXss}` }}
        >
          {t('Task Assignment Strategy')}
        </TextBody>
        <div>
          <Tabs
            defaultActiveKey={childRoute}
            items={tabs}
            onTabClick={handleClickTab}
          />
        </div>
      </section>
      <section style={{ overflow: 'hidden', height: '100%' }}>
        <Routes>
          {taskAssignmentStrategyRoutes[0].children.map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <Suspense
                  fallback={
                    <div
                      style={{
                        width: '100%',
                        minHeight: '500px',
                        textAlign: 'center',
                        lineHeight: '500px',
                      }}
                    >
                      <Spin size="large" />
                    </div>
                  }
                >
                  {route.component && <route.component />}
                </Suspense>
              }
            ></Route>
          ))}
        </Routes>
      </section>
    </div>
  );
};
