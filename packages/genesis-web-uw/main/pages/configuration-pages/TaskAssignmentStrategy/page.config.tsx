import { StepStatusEnum } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { Setting, Rule, Team, EditPen } from '@uw/assets/new-icons';
import { TextBody } from '@uw/components/Text';

import { StepProps } from 'antd';

import { Rules } from './components';
import { Strategy } from './components/Strategy/Strategy';
import { TeamManagement } from './components/TeamManagement';

import styles from './TaskAssignmentStrategy.module.scss';
import { StepKey, ContextType } from './interface/common.interface';

export type Step = StepProps & {
  key: StepKey;
  sectionTitle: string;
  desc: string;
  buttonText: string;
  buttonIcon?: JSX.Element;
  Component?: React.FunctionComponent<any>;
  total?: number;
};
export const getSteps = (type: ContextType): Step[] => [
  {
    status: StepStatusEnum.Finish,
    title: (
      <TextBody type="h4" weight={700}>
        {i18nFn('Task Assignment Rule')}
      </TextBody>
    ),
    key: StepKey.RuleStep,
    icon: <Rule />,
    sectionTitle: i18nFn('Start by Creating a Rule'),
    desc: i18nFn(
      'Create task assignment rule to match {{module}}.',
      undefined,
      {
        module: i18nFn(type),
      }
    ),
    buttonText: i18nFn('+ Add New Rule'),
    Component: Rules,
  },
  {
    status: StepStatusEnum.Wait,
    title: (
      <TextBody type="h4" weight={700}>
        {i18nFn('Team Management')}
      </TextBody>
    ),
    key: StepKey.TeamStep,
    icon: <Team />,
    sectionTitle: i18nFn('Now You Can Create the Team'),
    desc: i18nFn(
      'Create team and bind task assignment rule. The {{module}} hit binded rule will be pushed into this team.',
      undefined,
      {
        module: i18nFn(type),
      }
    ),
    buttonText: i18nFn('+ Add New Team'),
    Component: TeamManagement,
  },
  {
    status: StepStatusEnum.Wait,
    title: (
      <TextBody type="h4" weight={700}>
        {i18nFn('Task Push Strategy')}
      </TextBody>
    ),
    key: StepKey.StrategyStep,
    icon: <Setting />,
    sectionTitle: i18nFn('Almost!'),
    desc: i18nFn(
      'Set task push strategy for each team, such as Round Robin and task push by workload.'
    ),
    buttonIcon: <EditPen style={{ marginRight: styles.gapXs }} />,
    buttonText: i18nFn('Edit Strategy'),
    Component: Strategy,
  },
];
