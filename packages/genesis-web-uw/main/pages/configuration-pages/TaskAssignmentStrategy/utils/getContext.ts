import { ContextType } from '../interface/common.interface';
import {
  ClaimTeamContext,
  UnderwritingTeamContext,
  PosTeamContext,
} from '../TeamProvider';

export const getContext = (type: ContextType) => {
  if (type === ContextType.Underwriting) {
    return UnderwritingTeamContext;
  }
  if (type === ContextType.Pos) {
    return PosTeamContext;
  }
  if (type === ContextType.Claim) {
    return ClaimTeamContext;
  }
};
