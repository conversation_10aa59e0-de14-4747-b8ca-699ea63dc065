@import '@uw/styles/variables.scss';

.assignment-header-wrapper {
  padding-top: $gap-lg;
  background-color: white;
  :global {
    .#{$antd-prefix}-tabs-nav {
      margin: 0;
      padding: 0 $gap-lg;
      &::before {
        border-color: var(--border-line-color);
      }
    }
    .#{$antd-prefix}-tabs-tab-btn {
      color: var(--disabled-color);
    }
  }
}

.team-steps-wrapper {
  padding: $gap-lg;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  :global {
    .#{$antd-prefix}-steps-navigation {
      padding-top: 0;
      background-color: var(--primary-disabled-color);
      .#{$antd-prefix}-steps-item-finish {
        background-color: white;
      }
      .#{$antd-prefix}-steps-item-container {
        padding-top: $gap-sm;
      }
      border-radius: var(--border-radius-base);
      .#{$antd-prefix}-steps-item.#{$antd-prefix}-steps-item-active::before {
        height: 0;
      }
      .#{$antd-prefix}-steps-icon {
        top: 4px !important;
      }
      .#{$antd-prefix}-steps-item-title {
        margin-top: 3px;
      }
      .#{$antd-prefix}-steps-item {
        padding-left: 0;
        width: 33.3%;
        flex: none;
        &::after {
          margin-top: 0;
          top: 0;
          display: inline-block;
          width: 40px;
          height: 60px;
          border: 0;
          transform: none;
          background: url('@uw/assets/new-icons/arrow.svg');
          margin-left: -18px;
          z-index: 2;
        }
        &:nth-child(2) {
          width: calc(33.3% + 12px);
        }
        &:last-child::after {
          background: none;
        }
        &:first-child {
          border-bottom-left-radius: var(--border-radius-base);
          border-top-left-radius: var(--border-radius-base);
        }
        &:last-child {
          border-bottom-right-radius: var(--border-radius-base);
          border-top-right-radius: var(--border-radius-base);
          flex: 1;
        }
      }
    }
  }
}
.step-content {
  display: flex;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}
.step-card-wrapper {
  border-radius: var(--border-radius-base);
  margin: $gap-md $gap-md 0 0;
  width: 33.3%;
  flex-shrink: 0;
  flex: 1;
  background-color: white;
  color: var(--text-color-quaternary);
  .card-with-data {
    height: 100%;
    overflow: hidden;
    flex-direction: column;
  }
  .action-wrapper {
    padding: $gap-lg $gap-lg $gap-md;
  }
  .empty-section {
    padding: $gap-lg;
  }
  .desc {
    margin-top: var(--gap-xs);
  }
  &.active {
    color: var(--text-color);
    .desc {
      color: var(--text-color-secondary);
    }
  }
  .data-entry-content {
    overflow: auto;
    padding: $gap-md $gap-lg;
  }
  &:last-child {
    margin-right: 0;
  }
}

.rule-condition-wrapper {
  :global {
    .#{$antd-prefix}-divider-plain::before {
      background-color: white;
      display: block;
      content: 'And';
      position: absolute;
      font-weight: bold;
      top: 50%;
      margin-top: -11px;
      left: -15px;
    }
    .#{$antd-prefix}-form-item-explain
      .#{$antd-prefix}-form-item-explain-error:nth-child(2) {
      display: none;
    }
    .#{$antd-prefix}-input-group-compact {
      > div:first-child {
        height: fit-content;
        .#{$antd-prefix}-select-selector {
          background-color: var(--form-addon-bg);
        }
      }
    }
  }
}

.card-list-wrapper {
  :global {
    .#{$antd-prefix}-card-bordered {
      border-radius: 20px;
      box-shadow: 0px 4px 24px rgba(16, 42, 67, 0.12);
      margin-bottom: $gap-md;
      .#{$antd-prefix}-card-body {
        padding: $gap-lg;
      }
    }
  }
  .card-header {
    display: flex;
  }
}

:export {
  gapLg: $gap-lg;
  gapMd: $gap-md;
  gapXss: var(--gap-xss);
  gapXs: var(--gap-xs);
  textColorTertiary: var(--text-color-tertiary);
}
