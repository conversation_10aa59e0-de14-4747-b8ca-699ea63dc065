import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';

export enum ContextType {
  Underwriting = 'Underwriting',
  Claim = 'Claim',
  Pos = 'Posonline',
}
export enum StepKey {
  RuleStep = 'rule',
  TeamStep = 'team',
  StrategyStep = 'strategy',
}
export type RuleConditionDetail = BizDict & {
  operators: BizDict[];
  valueItems: BizDict[];
};

// 已抽出到 web-component
export const DataTypeFieldType: Record<string, FieldType> = {
  STRING: FieldType.Input,
  ENUM: FieldType.Select,
  NUMBER: FieldType.InputNumber,
  LIST: FieldType.MultiSelect,
  DATATIME: FieldType.Date,
};

export enum TaskPushStrategyType {
  TaskPushWhenUserAskFor = '3',
}

export type FillConditionDetail = BizDict & {
  valueItems: BizDict[];
};

export type GetDictType =
  | 'module'
  | 'stage'
  | 'strategy'
  | 'operator'
  | 'taskPushStrategy'
  | 'memberType';
