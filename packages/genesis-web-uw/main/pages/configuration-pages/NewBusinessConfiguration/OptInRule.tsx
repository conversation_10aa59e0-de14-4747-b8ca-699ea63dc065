import React, { FC, useMemo } from 'react';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { EditTableRule } from '@uw/pages/configuration-pages/NewBusinessConfiguration/components/EditTableRule';
import { useBizDict, usePolicyScenarioType } from '@uw/hook/useBizDict';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { TextBody } from '@uw/components/Text';

import { useDict } from '@uw/hook/useDict';

import styles from './NewBusinessConfiguration.module.scss';
import { optInRulesTable, optInRulesForm } from './config';

interface Props {
  id: string;
  rules?: BizDict[];
  packages?: BizDict[];
  labels?: BizDict[];
  hasRuleEditAuth: boolean;
}
export const OptInRule: FC<Props> = ({
  id,
  rules,
  packages,
  labels,
  hasRuleEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const businessTypeEnums = useBizDict('ruleConfigurationBusinessType');
  const [businessTypeEnumMap] = useDict('ruleConfigurationBusinessType');
  const policyScenarioEnums = usePolicyScenarioType();
  const optInRulesfields = useMemo(
    () =>
      optInRulesForm(
        policyScenarioEnums,
        businessTypeEnums as BizDict[],
        rules,
        packages,
        labels
      ),
    [rules, packages, policyScenarioEnums, labels, businessTypeEnums]
  );

  const optInRuleConfiguration = useMemo(() => {
    const configs = optInRulesTable(
      rules,
      policyScenarioEnums,
      businessTypeEnums as BizDict[],
      businessTypeEnumMap,
      packages
    );
    return configs.map(config => (
      <div key={config.title}>
        <TextBody weight={700} style={{ marginBottom: styles.gapXs }}>
          <span>{config.title}</span>
          {config.tooltip && (
            <Tooltip placement="right" title={config.tooltip}>
              <ExclamationCircleOutlined style={{ marginLeft: styles.gapXs }} />
            </Tooltip>
          )}
        </TextBody>
        <EditTableRule
          tableColumns={config.columns}
          type={config.typeEnum!}
          optInRulesfields={optInRulesfields}
          tooltip={config.tooltip}
          hasRuleEditAuth={hasRuleEditAuth}
        />
      </div>
    ));
  }, [
    rules,
    packages,
    policyScenarioEnums,
    businessTypeEnums,
    businessTypeEnumMap,
    hasRuleEditAuth,
    optInRulesfields,
  ]);

  return (
    <div id={id}>
      <section className={styles['rule-title']}>{t('Opt-In Rules')}</section>
      {optInRuleConfiguration}
    </div>
  );
};
