import { type FormInstance, Form } from 'antd';
import { useCallback, useMemo } from 'react';
import {
  PartnerTypeEnum,
  SalesChannelTypeEnum,
  BizDict,
} from '@uw/interface/enum.interface';

import { GoodsObjType } from '@uw/interface/common.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { GoodsListType } from 'genesis-web-service';

import { channelFields, packageFields } from '../rule.config';
import { getEnumList } from './request';

export const usePackageFields = (
  form: FormInstance,
  disabled: boolean,
  goodsItem: Record<string, GoodsObjType>
): {
  newPackageFields: (index: number) => FieldDataType;
} => {
  const goodsValue = Form.useWatch('goods', form);
  const newPackageFields = useCallback(
    (index: number) => {
      const goodsValues = form.getFieldValue('goods');
      const goodsCode = goodsValues[index]?.goodsCode;
      const packagesList = goodsItem?.[goodsCode]?.packages;
      const isSelectAllPackages =
        goodsValues[index]?.packages?.length === packagesList?.length;
      return packageFields(
        packagesList,
        disabled,
        form,
        goodsCode,
        isSelectAllPackages
      );
    },
    [disabled, goodsValue, goodsItem]
  );

  return { newPackageFields };
};

export const useChannelFields = (
  form: FormInstance,
  disabled: boolean,
  goodsItem: Record<string, GoodsObjType>,
  salesChannelEnum?: BizDict[]
): { newChannelFields: FieldDataType } => {
  const goodsValues = Form.useWatch('goods', form);
  const newChannelFields = useMemo(() => {
    const channelList = goodsValues?.map(
      (item: GoodsListType) => goodsItem?.[item?.goodsId]?.channelType
    );

    const salesEnumObject: Record<string, BizDict> =
      salesChannelEnum?.reduce(
        (out, item) => ({
          ...out,
          [item?.dictValue as string]: item,
        }),
        {}
      ) ?? {};

    const partnerList = channelList?.reduce(
      (out: string[], current: string) =>
        out?.filter((item: string) => current?.includes(item)),
      []
    );

    let newSalesList: BizDict[][];

    const getMap = new Map()
      .set(PartnerTypeEnum.SALE_CHANNEL, [
        salesEnumObject?.[SalesChannelTypeEnum.SALES_PLATFORM],
      ])
      .set(PartnerTypeEnum.AGENCY, [
        salesEnumObject?.[SalesChannelTypeEnum.AGENCY_COMPANY],
      ])
      .set(PartnerTypeEnum.INSTITUTE, [
        salesEnumObject?.[SalesChannelTypeEnum.DIRECT_BUSINESS],
        salesEnumObject?.[SalesChannelTypeEnum.AGENT],
      ])
      .set(PartnerTypeEnum.BANK, [salesEnumObject?.[SalesChannelTypeEnum.BANK]])
      .set(PartnerTypeEnum.LEASE_CHANNEL, [
        salesEnumObject?.[SalesChannelTypeEnum.LEASE_CHANNEL],
      ])
      .set(PartnerTypeEnum.BROKER_COMPANY, [
        salesEnumObject?.[SalesChannelTypeEnum.BROKER_COMPANY],
      ]);

    if (partnerList?.[0] && partnerList.length >= 1) {
      newSalesList =
        partnerList?.map((item: string) => getMap.get(item) || []) || [];
    } else {
      newSalesList = [];
    }
    const salesEnumList = newSalesList?.reduce(
      (out, item) => out.concat(item),
      []
    );
    return channelFields(getEnumList(salesEnumList), disabled);
  }, [disabled, goodsItem, goodsValues]);

  return { newChannelFields };
};
