import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { useWatch } from 'antd/lib/form/Form';

import { uniq } from 'lodash-es';

import {
  AttachmentConfigItem,
  AttachmentConfigList,
  GoodsListType,
  NBConfigWorkflowService,
  NBConfiguration,
  PolicyService,
} from 'genesis-web-service';

import { GoodsObjType } from '@uw/interface/common.interface';
import { BizDict, SelectAllEnum, YesOrNo } from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { messagePopup } from '@uw/util/messagePopup';

import {
  documentFields,
  goodsCategoryFields,
  goodsCodeFields,
  mandatoryFields,
  scopeOfApplicationFields,
} from '../rule.config';

export const useGetSelectedWorkflow = (): [
  NBConfiguration.QueryWorkfowDetail | undefined,
  string[] | undefined,
  () => Promise<void>,
] => {
  const [workflowDetail, setWorkflowDetail] =
    useState<NBConfiguration.QueryWorkfowDetail>();
  const [selectedWorkflow, setSelectedWorkflow] = useState<string[]>();
  const getSelectedWorkflow = useCallback(async () => {
    const res = await NBConfigWorkflowService.queryWorkflowDetail();
    const seletedWorkflows = Object.keys(res?.config || {}).filter(
      workflowItem => res?.config[workflowItem] === YesOrNo.YES
    );
    setSelectedWorkflow(seletedWorkflows);
    setWorkflowDetail(res);
  }, []);

  useEffect(() => {
    getSelectedWorkflow();
  }, []);

  return [workflowDetail, selectedWorkflow, getSelectedWorkflow];
};

export const useGetWorkflowTemplates = (): [
  NBConfiguration.WokflowDetail[] | undefined,
] => {
  const [workflowTemplates, setWorkflowTemplates] =
    useState<NBConfiguration.WokflowDetail[]>();
  const getSelectedWorkflow = useCallback(async () => {
    const res = await NBConfigWorkflowService.queryWorkflowTemplates();
    setWorkflowTemplates(res);
  }, []);

  useEffect(() => {
    getSelectedWorkflow();
  }, []);

  return [workflowTemplates];
};

export const useSubmitWorkflow = (
  getSelectedWorkflow: () => Promise<void>,
  workflow?: NBConfiguration.SubmitWokflowDetail
): [boolean, (data?: NBConfiguration.SubmitWokflowDetail) => Promise<void>] => {
  const [loading, setLoading] = useState(false);

  const submitWorkflow = useCallback<
    (data?: NBConfiguration.SubmitWokflowDetail) => Promise<void>
  >(
    async data => {
      setLoading(true);
      try {
        await NBConfigWorkflowService.submitWorkflow(
          data ?? (workflow as NBConfiguration.SubmitWokflowDetail)
        );
        getSelectedWorkflow();
      } catch (e) {
        messagePopup((e as Error).toString(), 'error');
      }
      setLoading(false);
    },
    [workflow]
  );

  return [loading, submitWorkflow];
};

export const useAttachmentConfiguration = (): {
  attachmentList: (AttachmentConfigList & { key: string })[];
  setAttachmentList: Dispatch<
    SetStateAction<
      (AttachmentConfigList & {
        key: string;
      })[]
    >
  >;
  getAttachmentList: () => void;
} => {
  const [attachmentList, setAttachmentList] = useState<
    (AttachmentConfigList & { key: string })[]
  >([]);

  const getAttachmentList = useCallback(() => {
    PolicyService.getAttachmentConfig().then(res => {
      setAttachmentList(
        res?.map((item, index) => ({
          ...item,
          key: `${item?.goods}${item?.baseInfoId}${index}`,
        }))
      );
    });
  }, []);
  useEffect(() => {
    getAttachmentList();
  }, []);

  return { attachmentList, setAttachmentList, getAttachmentList };
};

export const useAttachmentOperation = (
  getAttachmentList: () => void
): {
  addAttachment: (params: AttachmentConfigItem) => void;
  daleteAttachment: (baseInfoId: number) => void;
} => {
  const addAttachment = useCallback((params: AttachmentConfigItem) => {
    PolicyService.addAttachmentConfig(params)
      .then(() => {
        getAttachmentList();
      })
      .catch(error => {
        messagePopup(error.toString(), 'error');
      });
  }, []);
  const daleteAttachment = useCallback((baseInfoId: number) => {
    PolicyService.deleteAttachmentConfig(baseInfoId)
      .then(() => {
        getAttachmentList();
      })
      .catch(error => {
        messagePopup(error.toString(), 'error');
      });
  }, []);
  return { addAttachment, daleteAttachment };
};

export const useGoodsCode = (): {
  getGoodsCode: (categoryList: number[]) => void;
  goodsCodeList: BizDict[];
} => {
  const [goodsCodeList, setGoodsCodeList] = useState<BizDict[]>([]);
  const getGoodsCode = useCallback((list: number[]) => {
    const params = {
      categoryList: list,
    };
    PolicyService.getGoodsList(params).then(res => {
      setGoodsCodeList(
        res?.map(item => ({
          itemName: `${item?.goodsName}_${item?.goodsCode}` || '',
          enumItemName: item?.goodsCode,
        }))
      );
    });
  }, []);
  return { getGoodsCode, goodsCodeList };
};

export const usePackageOrPartnerType = (
  setGoodsObj: Dispatch<SetStateAction<Record<string, GoodsObjType>>>,
  goodsItem: Record<string, GoodsObjType>
): {
  getValue: (goodsList: GoodsListType[]) => Promise<void[]>;
  getPackageOrPartnerType: (code: string) => void;
} => {
  const getPackageOrPartnerType = useCallback(
    (code: string) => {
      const params = {
        goodsCode: code,
        querySalesAttributes: true,
        queryPackageRelating: {
          queryBasic: true,
        },
      };
      PolicyService.getPackageOrPartnerType(params).then(res => {
        setGoodsObj({
          ...goodsItem,
          [code]: {
            packages: res?.packageList?.map(item => ({
              itemName: `${item?.packageBasicInfo?.packageName}_${item?.packageBasicInfo?.packageCode}`,
              enumItemName: item?.packageBasicInfo?.packageCode,
            })),
            channelType: uniq(
              res?.salesAttributes?.salesPartnerCombineResponses?.map(item =>
                item?.partnerType?.toString()
              )
            ),
          },
        } as Record<string, GoodsObjType>);
      });
    },
    [goodsItem]
  );

  const getValue = async (goodsList: GoodsListType[]) =>
    Promise.all(
      goodsList?.map((item: GoodsListType) =>
        getPackageOrPartnerType(item?.goodsCode || '')
      )
    );
  return {
    getPackageOrPartnerType,
    getValue,
  };
};

export const getEnumList = (enumList: BizDict[] | undefined) =>
  enumList?.map(item => ({
    itemName: item?.dictValueName || '',
    enumItemName: item?.dictValue || '',
  }));

const getNewEnumList = (enumList: BizDict[] | undefined) =>
  enumList?.map(item => ({
    itemName: item?.dictValueName || '',
    enumItemName: item?.itemExtend1 || '',
    dictValue: item?.itemExtend1 || '',
  }));

export const useAttachmentConfigFields = (
  form: FormInstance,
  disabled: boolean,
  setGoodsNum: React.Dispatch<React.SetStateAction<number>>,
  setCategoryId: React.Dispatch<React.SetStateAction<number | undefined>>,
  setGoodsCode: React.Dispatch<React.SetStateAction<string | undefined>>,
  productCategoryEnum?: BizDict[],
  scopeOfApplicationEnum?: BizDict[],
  attachmentDocumentEnum?: BizDict[],
  yesNoEnum?: BizDict[],
  goodsCodeList?: BizDict[]
): {
  categoryFields: FieldDataType;
  codeFields: FieldDataType;
  scopeFields: FieldDataType;
  attachmentFields: FieldDataType;
  newmandatoryFields: FieldDataType;
} => {
  const [t] = useTranslation(['uw', 'common']);
  const attachmentsFormItem = useWatch('attachments', form);
  const goodsValues = useWatch('goods', form);
  const selectAllDict: BizDict[] = [
    {
      itemExtend1: SelectAllEnum.SELECT_ALL,
      dictValueName: t('All goods categories'),
      enumItemName: 0,
      itemName: t('All goods categories'),
    },
  ];
  const supplementProductCategoryEnum = selectAllDict.concat(
    productCategoryEnum as BizDict | ConcatArray<BizDict>
  );
  const categoryFields = useMemo(
    () =>
      goodsCategoryFields(
        getNewEnumList(supplementProductCategoryEnum),
        setGoodsNum,
        setCategoryId,
        disabled,
        form
      ),
    [productCategoryEnum, disabled, form]
  );

  const codeFields = useMemo(() => {
    const goodsIdList = goodsValues?.map(item => item?.goodsId?.toString());
    const newGoodsCodeList = goodsCodeList?.map(item => ({
      ...item,
      disabled: goodsIdList?.includes(item?.enumItemName?.toString()),
    }));
    return goodsCodeFields(newGoodsCodeList, setGoodsCode, disabled, form);
  }, [goodsValues, goodsCodeList, disabled, goodsCodeFields, form]);
  const scopeFields = useMemo(
    () =>
      scopeOfApplicationFields(getEnumList(scopeOfApplicationEnum), disabled),
    [scopeOfApplicationEnum, disabled]
  );

  const attachmentFields = useMemo(() => {
    const attachmentValues: Record<string, unknown>[] =
      form.getFieldValue('attachments');
    const attachmentList = attachmentValues?.map(item =>
      item?.attachmentType?.toString()
    );

    const newAttachmentList = getEnumList(attachmentDocumentEnum)?.map(
      item => ({
        ...item,
        disabled: attachmentList?.includes(item?.enumItemName?.toString()),
      })
    );
    return documentFields(newAttachmentList, disabled, form);
  }, [
    attachmentsFormItem,
    attachmentDocumentEnum,
    disabled,
    documentFields,
    form,
  ]);

  const newmandatoryFields = useMemo(
    () => mandatoryFields(yesNoEnum, disabled),
    [yesNoEnum, disabled]
  );
  return {
    categoryFields,
    codeFields,
    scopeFields,
    attachmentFields,
    newmandatoryFields,
  };
};
