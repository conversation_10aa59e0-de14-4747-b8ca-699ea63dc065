import React, { FC, Suspense } from 'react';
import { Spin } from 'antd';
import { ExtendSecondMenu } from 'genesis-web-component/lib/components/SecondMenu';
import { useTranslation } from 'react-i18next';
import { Route, Routes } from 'react-router-dom';
import { menus, useSecondMenuMap } from '@uw/hook/useMenuData';
import { nbConfigrationRoutes } from '@uw/router';

import styles from './NewBusinessConfiguration.module.scss';

export const NewBusinessConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);

  const { defaultKey, defaultMenuMap, handleMenuClick } =
    useSecondMenuMap(menus);
  return (
    <div style={{ display: 'flex', height: '100%' }}>
      <ExtendSecondMenu
        title={t('New Business & Renewal Configuration')}
        handleMenuClick={handleMenuClick}
        defaultSelected={defaultKey ?? 'quotationConfiguration'}
        showLine={false}
        menuMap={defaultMenuMap}
      />
      <section className={styles['rule-content']}>
        <Routes>
          {nbConfigrationRoutes[0].children.map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <Suspense
                  fallback={
                    <div
                      style={{
                        width: '100%',
                        minHeight: '500px',
                        textAlign: 'center',
                        lineHeight: '500px',
                      }}
                    >
                      <Spin size="large" />
                    </div>
                  }
                >
                  {route.component && <route.component />}
                </Suspense>
              }
            ></Route>
          ))}
        </Routes>
      </section>
    </div>
  );
};
