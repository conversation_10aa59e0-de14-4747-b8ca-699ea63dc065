import React, { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { TextBody } from '@uw/components/Text';
import { useBizDict, usePolicyScenarioType } from '@uw/hook/useBizDict';
import { useDict } from '@uw/hook/useDict';

import styles from './NewBusinessConfiguration.module.scss';
import { CommonEditTable } from './components/CommonEditTable';
import { policyIssuanceConfig } from './rule.config';

interface Props {
  id: string;
  rules?: BizDict[];
  packages?: BizDict[];
  hasRuleEditAuth: boolean;
}
export const PolicyIssuanceRule: FC<Props> = ({
  id,
  rules,
  packages,
  hasRuleEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const policyScenarioEnums = usePolicyScenarioType();
  const businessTypeEnums = useBizDict('ruleConfigurationBusinessType');
  const [businessTypeEnumMap] = useDict('ruleConfigurationBusinessType');

  const policyIssuanceConfiguration = useMemo(() => {
    const configs = policyIssuanceConfig(
      policyScenarioEnums,
      businessTypeEnums as BizDict[],
      businessTypeEnumMap,
      rules,
      packages
    );
    return configs.map(config => (
      <div key={config.title} className="mb-8">
        <TextBody weight={700} style={{ marginBottom: styles.gapXs }}>
          <span>{config.title}</span>
          {config.tooltip && (
            <Tooltip
              placement="right"
              title={
                <div
                  dangerouslySetInnerHTML={{
                    __html: config.tooltip?.replaceAll('\n', '<br/>'),
                  }}
                ></div>
              }
            >
              <ExclamationCircleOutlined style={{ marginLeft: styles.gapXs }} />
            </Tooltip>
          )}
        </TextBody>
        <CommonEditTable
          columns={config.columns}
          type={config.typeEnum!}
          tooltip={config.tooltip}
          hasRuleEditAuth={hasRuleEditAuth}
        />
      </div>
    ));
  }, [
    rules,
    packages,
    policyScenarioEnums,
    hasRuleEditAuth,
    businessTypeEnums,
    businessTypeEnumMap,
  ]);

  return (
    <div id={id}>
      <section className={styles['rule-title']}>
        {t('Policy Issuance Rules')}
      </section>
      {policyIssuanceConfiguration}
    </div>
  );
};
