import React, {
  Ref,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { FormInstance, Tooltip } from 'antd';

import { Proposal, ProposalTabelData } from '@uw/interface/common.interface';
import {
  BizDict,
  QueryPlaceholderEnum,
  TypeEnums,
} from '@uw/interface/enum.interface';

import styles from '../NewBusinessConfiguration.module.scss';
import {
  useProposalTableDataContext,
  useProposalTableDataSourceContext,
} from '../context';
import { useGoodsList, useQueryChannel } from '../request';
import { AssociatedGoodsConfig } from '../rule.config';
import { AssociatedGoodsTable } from './AssociatedGoodsTable';

interface Props {
  id: string;
  form: FormInstance;
  type: TypeEnums;
  hasProposalEditAuth: boolean;
}
export const AssociatedGoods = forwardRef(
  ({ id, form, type, hasProposalEditAuth }: Props, ref: Ref<unknown>) => {
    const [goodsList, setGoodsList] = useState<BizDict[]>([]);

    const { refetch, allGoodsObj } = useGoodsList({}); // 全量good

    const [dataSource, setDataSource] =
      useState<(Proposal & { key: string })[]>();

    const { proposalTableDataSource } = useProposalTableDataSourceContext();

    const { setProposalData } = useProposalTableDataContext();
    const channelList = useQueryChannel(
      QueryPlaceholderEnum.QUERY_CHANNEL_TREE
    );

    useEffect(() => {
      setDataSource(
        type === TypeEnums.SEPERATE_BY_PROPOSAL_STATUS
          ? proposalTableDataSource?.separatedConfig
          : proposalTableDataSource?.accumulatedConfig
      );
    }, [proposalTableDataSource]);

    useEffect(() => {
      setProposalData(prev => ({
        ...prev,
        ...({ [type]: dataSource } as ProposalTabelData),
      }));
    }, [dataSource]);
    useImperativeHandle(
      ref,
      () => ({
        onSubmit() {
          return { type, data: dataSource };
        },
      }),
      [dataSource]
    );

    const getGoodsList = useCallback(
      async channelCode => {
        const params = {
          channelCodeList: [channelCode],
        };
        form.setFieldsValue({ goodsIds: [] });
        setGoodsList([]);
        const goods = await refetch(params);
        setGoodsList(goods);
      },
      [channelList]
    );
    const getNewGoodsList = useCallback<(record: Partial<Proposal>) => void>(
      record => {
        getGoodsList(record.channelCode);
      },
      [channelList]
    );

    // 新增一条时,清空goodsList
    const handleAdd = useCallback(() => {
      setGoodsList([]);
    }, [dataSource]);

    const associatedGoodsConfiguration = useMemo(() => {
      const configs = AssociatedGoodsConfig(
        channelList,
        goodsList,
        allGoodsObj,
        getGoodsList
      );
      return configs.map(config => (
        <div key={config.title}>
          <section className={styles.ruleSubTitle}>
            <span>{config.title}</span>
            {config.tooltip && (
              <Tooltip
                placement="right"
                title={
                  <div
                    dangerouslySetInnerHTML={{
                      __html: config.tooltip?.replaceAll('\n', '<br/>'),
                    }}
                  ></div>
                }
              >
                <ExclamationCircleOutlined
                  style={{ marginLeft: styles.gapXs }}
                />
              </Tooltip>
            )}
          </section>
          <AssociatedGoodsTable
            form={form}
            columns={config.columns}
            tooltip={config.tooltip}
            scroll={800}
            type={type}
            dataSource={dataSource}
            setDataSource={setDataSource}
            getEditRow={getNewGoodsList}
            handleAdd={handleAdd}
            hasProposalEditAuth={hasProposalEditAuth}
          />
        </div>
      ));
    }, [goodsList, channelList, allGoodsObj, dataSource, hasProposalEditAuth]);

    return <div id={id}>{associatedGoodsConfiguration}</div>;
  }
);
