import React, { <PERSON> } from 'react';
import { Anchor } from 'antd';
import { AnchorIdsEnums } from '@uw/interface/enum.interface';

const { Link } = Anchor;

interface Props {
  AnchorIdsMap: Record<string, string>;
  getContainer: () => HTMLElement;
}

export const RuleAnchor: FC<Props> = ({ AnchorIdsMap, getContainer }) => (
  <Anchor
    getContainer={getContainer}
    offsetTop={10}
    style={{ overflow: 'hidden' }}
  >
    {Object.keys(AnchorIdsEnums).map(anchorEnum => (
      <Link
        key={anchorEnum}
        href={`#${anchorEnum}`}
        title={AnchorIdsMap[anchorEnum]}
      />
    ))}
  </Anchor>
);
