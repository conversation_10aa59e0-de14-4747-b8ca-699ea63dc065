import React, { useMemo, FC, useCallback, useState, useEffect } from 'react';
import { Dropdown, Menu, Form } from 'antd';
import { FormInstance } from 'antd/es/form/Form';
import { useTranslation } from 'react-i18next';
import { EllipsisOutlined } from '@ant-design/icons';
import { cssVars } from '@zhongan/nagrand-ui';

interface Props {
  record: Record<string, string>;
  form: FormInstance;
}

export const ActionMenu: FC<Props> = ({ record, form }) => {
  const [t] = useTranslation();
  const [removeAction, setRemoveAction] = useState(false);
  const [needAction, setNeedAction] = useState(true);
  // days=1 && disable actions=[add]
  // days=1  actions=[add,remove all]
  // days=>2  actions=[add,remove,remove all]
  const bizStatusValue = Form.useWatch(record.bizStatus, form);

  useEffect(() => {
    if (form.getFieldValue(record.bizStatus).length >= 2) {
      setRemoveAction(true);
      setNeedAction(true);
    } else if (form.getFieldValue(record.bizStatus)[0] === null) {
      setNeedAction(false);
    } else {
      setNeedAction(true);
    }
  }, [bizStatusValue]);
  const handleAdd = useCallback(() => {
    let addData: number[] | null[] | undefined[];

    if (form.getFieldValue(record.bizStatus)[0] === null) {
      addData = [undefined];
      setNeedAction(true);
    } else {
      addData = [...form.getFieldValue(record.bizStatus), null];
      setRemoveAction(true);
      setNeedAction(true);
    }

    form.setFieldsValue({ [record.bizStatus]: addData });
  }, [bizStatusValue]);

  const handleRemove = useCallback(() => {
    const removeData = [...form.getFieldValue(record.bizStatus)];
    if (removeData.length <= 2) {
      setRemoveAction(false);
    }
    removeData.splice(removeData.length - 1);
    form.setFieldsValue({ [record.bizStatus]: removeData });
  }, [bizStatusValue]);

  const handleNeedReminder = useCallback(() => {
    form.setFieldsValue({ [record.bizStatus]: [null] });
    setRemoveAction(false);
    setNeedAction(false);
  }, [bizStatusValue]);
  const menu = useMemo(
    () => (
      <Menu>
        <Menu.Item>
          <a onClick={handleAdd}>{t('Add a Reminder')}</a>
        </Menu.Item>
        {removeAction && (
          <Menu.Item>
            <a onClick={handleRemove}></a>
            <span>{t('Remove a Reminder')}</span>
          </Menu.Item>
        )}
        {needAction && (
          <Menu.Item>
            <a onClick={handleNeedReminder}>{t("Don't Need Reminder")}</a>
          </Menu.Item>
        )}
      </Menu>
    ),
    [removeAction, needAction, bizStatusValue]
  );

  return (
    <Dropdown overlay={menu}>
      <EllipsisOutlined style={{ marginBottom: cssVars.gapXs }} />
    </Dropdown>
  );
};
