import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Divider, Form, Row } from 'antd';
import type { FormInstance } from 'antd';

import { AttachmentConfigList } from 'genesis-web-service';

import { Delete } from '@uw/assets/new-icons/index';
import { CommonDrawer } from '@uw/components/CommonDrawer';
import { useBizDict } from '@uw/hook/useBizDict';
import { GoodsObjType } from '@uw/interface/common.interface';
import { Mode, SelectAllEnum } from '@uw/interface/enum.interface';
import { getFields } from '@uw/util/getFieldsQueryForm';

import styles from '../NewBusinessConfiguration.module.scss';
import { useChannelFields, usePackageFields } from '../hooks/config';
import {
  useAttachmentConfigFields,
  useGoodsCode,
  usePackageOrPartnerType,
} from '../hooks/request';

interface Props {
  visible: boolean;
  drawerTitle: string;
  stageType: number;
  handleCloseDrawer: () => void;
  onSubmit: (
    args: any & { key: string },
    mode: Mode,
    stageType: number
  ) => void;
  form: FormInstance;
  mode: Mode;
  dataSource: AttachmentConfigList & { key: string };
  categoryId: number | undefined;
  setCategoryId: React.Dispatch<React.SetStateAction<number | undefined>>;
  goodsCode: string | undefined;
  setGoodsCode: React.Dispatch<React.SetStateAction<string | undefined>>;
}

export const AttachmentEditDrawer: FC<Props> = ({
  visible,
  drawerTitle,
  stageType,
  handleCloseDrawer,
  onSubmit,
  form,
  mode,
  dataSource,
  categoryId,
  setCategoryId,
  goodsCode,
  setGoodsCode,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const disabledEdit = useMemo(() => mode === Mode.View, [mode]);
  const goodsCategoryIdValue = Form.useWatch('goodsCategoryId', form);
  const goodsValue = Form.useWatch('goods', form);
  const disabledWithSelectAll = useMemo(
    () => form.getFieldValue('goodsCategoryId') === '0',
    [goodsCategoryIdValue]
  );
  const goodsCategoryEnum = useBizDict('goodsCategory');
  const salesChannelEnum = useBizDict('salesChannelType');
  const [goodsItem, setGoodsObj] = useState<Record<string, GoodsObjType>>({});
  const [goodsNum, setGoodsNum] = useState<number>(0);
  const { getValue, getPackageOrPartnerType } = usePackageOrPartnerType(
    setGoodsObj,
    goodsItem
  );

  const yesNoEnum = useBizDict('yesNo');
  const attachmentDocumentEnum = useBizDict('attachmentDocumentType');
  const scopeOfApplicationEnum = useBizDict('scopeOfApplication');
  const { getGoodsCode, goodsCodeList } = useGoodsCode();
  const { newPackageFields } = usePackageFields(form, disabledEdit, goodsItem);
  const { newChannelFields } = useChannelFields(
    form,
    disabledEdit,
    goodsItem,
    salesChannelEnum
  );

  const {
    categoryFields,
    codeFields,
    scopeFields,
    attachmentFields,
    newmandatoryFields,
  } = useAttachmentConfigFields(
    form,
    disabledEdit,
    setGoodsNum,
    setCategoryId,
    setGoodsCode,
    goodsCategoryEnum,
    scopeOfApplicationEnum,
    attachmentDocumentEnum,
    yesNoEnum,
    goodsCodeList
  );

  useEffect(() => {
    if (mode === Mode.Add) {
      setGoodsNum(0);
    }
  }, [visible, mode]);

  useEffect(() => {
    if (form.getFieldValue('goodsCategoryId') === SelectAllEnum.SELECT_ALL) {
      setGoodsNum(0);
    }
  }, [goodsCategoryIdValue]);

  useEffect(() => {
    if (mode !== Mode.Add) {
      const goodsLength = form.getFieldValue('goods')?.length;
      setGoodsNum(goodsLength);
    }
  }, [mode, goodsValue]);

  useEffect(() => {
    if (mode === Mode.Add) {
      form.resetFields();
      form.setFieldsValue({
        attachments: [{ attachmentType: undefined, mandatory: undefined }],
      });
    } else {
      const newgoods = dataSource?.goods?.map(goodsITem => ({
        ...goodsITem,
        packages: goodsITem?.packages?.map(packItem => packItem.packageCode),
      }));

      form.setFieldsValue({
        goods: newgoods,
        goodsCategoryId:
          dataSource?.goodsCategoryId?.toString() ?? SelectAllEnum.SELECT_ALL,
        attachments: dataSource.attachments,
        scopeOfApplications: dataSource.scopeOfApplications,
        channels: dataSource?.channels,
      });
      getValue(dataSource?.goods);
    }
  }, [visible, form, dataSource, mode]);

  useEffect(() => {
    if (categoryId) {
      getGoodsCode([+categoryId]);
    }
  }, [categoryId]);

  useEffect(() => {
    if (goodsCode) {
      getPackageOrPartnerType(goodsCode);
    }
  }, [goodsCode]);

  const handleSaveRule = useCallback(async () => {
    const values = await form.validateFields();
    onSubmit(
      {
        ...values,
        goodsCategoryId:
          values.goodsCategoryId === SelectAllEnum.SELECT_ALL
            ? undefined
            : values.goodsCategoryId,
      },
      mode,
      stageType
    );
  }, [form, mode]);

  return (
    <CommonDrawer
      title={t(drawerTitle)}
      visible={visible}
      handleCloseDrawer={handleCloseDrawer}
      action={
        <>
          {mode === Mode.Add || mode === Mode.Edit ? (
            <>
              <Button onClick={handleCloseDrawer} style={{ marginRight: 8 }}>
                {t('Cancel')}
              </Button>
              <Button
                type={'primary'}
                onClick={handleSaveRule}
                style={{ marginLeft: styles.gapMd }}
                htmlType="submit"
              >
                {t('Submit')}
              </Button>
            </>
          ) : (
            <Button onClick={handleCloseDrawer} style={{ marginRight: 8 }}>
              {t('Close')}
            </Button>
          )}
        </>
      }
    >
      <Form
        className={styles.attachmentConfigWrapper}
        name="complex-form"
        form={form}
        layout="vertical"
      >
        <Row>
          <Col span={12} key={'goodsCategoryId'}>
            <Form.Item
              label={t('Goods Category')}
              name="goodsCategoryId"
              required={false}
              rules={[
                {
                  required: true,
                  message: categoryFields.placeholder as string,
                },
              ]}
            >
              {getFields(categoryFields)}
            </Form.Item>
          </Col>
        </Row>
        <Row style={{ flexDirection: 'column', position: 'relative' }}>
          <div style={{ marginBottom: styles.gapXs }}>{t('Goods')}</div>
          {goodsNum > 0 ? (
            <Divider
              plain
              type="vertical"
              style={{
                position: 'absolute',
                height: 'calc(100% - 110px)',
                top: '30px',
                left: '16px',
              }}
            />
          ) : null}
          <Form.List name="goods">
            {(fields, { add, remove }) => (
              <>
                {fields?.map(({ key, name, ...restField }) => (
                  <Row
                    key={key}
                    style={{
                      flexWrap: 'nowrap',
                      alignItems: 'flex-start',
                      marginLeft: '56px',
                    }}
                  >
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, 'goodsCode']}
                        rules={[
                          {
                            required: true,
                            message: t('Goods Code/GoodsName'),
                          },
                        ]}
                      >
                        {getFields(codeFields)}
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, 'packages']}
                        rules={[{ required: true, message: t('Package') }]}
                      >
                        {getFields(newPackageFields(name))}
                      </Form.Item>
                    </Col>
                    <Col
                      span={1}
                      style={{
                        marginLeft: styles.gapXs,
                        marginBottom: styles.gapLg,
                        marginTop: '6px',
                      }}
                    >
                      <Delete
                        onClick={() => {
                          remove(name);
                          setGoodsNum(goodsNum - 1);
                        }}
                        style={{ cursor: 'pointer' }}
                        disabled={disabledEdit}
                      />
                    </Col>
                  </Row>
                ))}
                <Row>
                  <Col>
                    <Form.Item>
                      <Button
                        onClick={() => {
                          add();
                          setGoodsNum(goodsNum + 1);
                        }}
                        style={{ marginTop: styles.gapMd }}
                        disabled={disabledEdit || disabledWithSelectAll}
                      >
                        {t('+ Add New')}
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </Form.List>
        </Row>
        <Row>
          <Col span={12} key={'channels'}>
            <Form.Item
              label={t('Channel')}
              name="channels"
              required={false}
              rules={[
                {
                  required: false,
                  message: newChannelFields.placeholder as string,
                },
              ]}
            >
              {getFields(newChannelFields)}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={12} key={'scopeOfApplications'}>
            <Form.Item
              label={t('Scope of Application')}
              name="scopeOfApplications"
              required={false}
              rules={[{ required: true }]}
            >
              {getFields(scopeFields)}
            </Form.Item>
          </Col>
        </Row>
        <Row style={{ flexDirection: 'column', position: 'relative' }}>
          <div style={{ marginBottom: styles.gapXs }}>{t('Attachment')}</div>
          <Divider
            plain
            type="vertical"
            style={{
              position: 'absolute',
              height: 'calc(100% - 110px)',
              top: '30px',
              left: '16px',
            }}
          />
          <Form.List name="attachments">
            {(fields, { add, remove }) => (
              <>
                {fields?.map(({ key, name, ...restField }) => (
                  <Row
                    key={key}
                    style={{
                      flexWrap: 'nowrap',
                      alignItems: 'flex-start',
                      marginLeft: '56px',
                    }}
                  >
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, 'attachmentType']}
                        rules={[
                          {
                            required: true,
                            message: t('Attachment Type'),
                          },
                        ]}
                      >
                        {getFields(attachmentFields)}
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item
                        {...restField}
                        name={[name, 'mandatory']}
                        rules={[{ required: true, message: t('Mandatory') }]}
                      >
                        {getFields(newmandatoryFields)}
                      </Form.Item>
                    </Col>
                    <Col
                      span={1}
                      style={{
                        marginLeft: styles.gapXs,
                        marginBottom: styles.gapLg,
                        marginTop: '6px',
                      }}
                    >
                      {fields.length > 1 && !disabledEdit ? (
                        <Delete
                          onClick={() => {
                            remove(name);
                          }}
                          style={{ cursor: 'pointer' }}
                        />
                      ) : null}
                    </Col>
                  </Row>
                ))}
                <Row>
                  <Col>
                    <Form.Item>
                      <Button
                        onClick={() => add()}
                        style={{ marginTop: styles.gapMd }}
                        disabled={disabledEdit}
                      >
                        {t('+ Add New')}
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </Form.List>
        </Row>
      </Form>
    </CommonDrawer>
  );
};
