import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import Icon, { EyeOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>confirm, Space } from 'antd';
import type { FormInstance } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { ColumnEditingType, Table } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { AttachmentConfigList } from 'genesis-web-service';

import { Delete, Edit } from '@uw/assets/new-icons';
import { ScopeEnum } from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { selectEnums } from '@uw/redux/selector';

import styles from '../NewBusinessConfiguration.module.scss';
import { AttachmentEditDrawer } from './AttachmentEditDrawer';

interface Props<T> {
  tableColumns: ColumnProps<T>[];
  data: (T & { key: string })[];
  title: string;
  stageType: number;
  disableEdit?: boolean;
  scroll?: number;
  setData: (args: (T & { key: string })[]) => void;
  setIsEditing: (isEditing: boolean) => void;
  tableFields?: FieldDataType[] | undefined;
  handleSaveData?: (
    row: T,
    stageType: number,
    uid?: string,
    editTableMode?: string
  ) => void;
  handleDel?: (index: number) => void;
  disableAdd?: boolean;
  showPage?: boolean;
  uKey?: string;
  handleEdit?: (row: T) => void;
  handleCancel?: () => void;
  form: FormInstance;
  categoryId: number | undefined;
  setCategoryId: React.Dispatch<React.SetStateAction<number | undefined>>;
  goodsCode: string | undefined;
  setGoodsCode: React.Dispatch<React.SetStateAction<string | undefined>>;
}

function AttachmentEditTable<T>({
  tableColumns,
  data,
  title,
  stageType,
  disableEdit,
  setData,
  setIsEditing,
  handleSaveData,
  handleDel,
  disableAdd,
  showPage,
  uKey,
  handleEdit,
  handleCancel,
  form,
  categoryId,
  setCategoryId,
  goodsCode,
  setGoodsCode,
}: Props<T>) {
  const enums = useSelector(selectEnums);
  const [t] = useTranslation(['uw', 'common']);
  const [showDrawer, setShowDrawer] = useState(false);
  const [editTableMode, setEditTableMode] = useState<Mode>(Mode.Add);
  const [rowKey, setRowKey] = useState('');
  const [rowItem, setRowItem] = useState<T & { key: string }>();
  const [uid, setUid] = useState<string>();

  const handleClickEdit = useCallback<
    (record: T & { key: string }, mode: Mode) => void
  >((record, mode) => {
    setIsEditing(true);
    setRowKey(record.key);
    setRowItem(record!);
    setShowDrawer(true);
    setEditTableMode(mode);
    setUid(uKey ? (record as Record<string, string>)[uKey] : undefined);
    handleEdit?.(record);
  }, []);

  const deleteRow = useCallback<(baseInfoId: number) => void>(
    baseInfoId => {
      handleDel?.(baseInfoId);
    },
    [tableColumns, data]
  );
  const handleAddColumn = useCallback(() => {
    setIsEditing(true);
    setShowDrawer(true);
    setEditTableMode(Mode.Add);
    setRowKey('');
  }, [tableColumns, data]);

  const onSubmitEditDrawer = useCallback<
    (args: T & { key: string }, mode: Mode, stageType: number) => void
  >(
    (values, mode) => {
      try {
        const row = values;
        const newData = [...data];
        let params = {} as T & Record<string, unknown>;
        const index = newData.findIndex(item => rowKey === item.key);
        if (mode === Mode.Edit) {
          if (index > -1) {
            const item = newData[index];
            const itemTemp = {
              ...item,
              ...row,
            };
            newData.splice(index, 1, itemTemp);
            setData(newData);
            params = { ...itemTemp };
          }
        } else {
          row.key = (Math.random() * 10).toString();
          newData.push(row);
        }
        if (mode === Mode.Add) {
          params = row;
        }
        if (handleSaveData) {
          handleSaveData(params, stageType, uid, mode);
        } else {
          setData(newData);
        }
        setIsEditing(false);
        setUid(undefined);
      } catch (errInfo) {
        // TODO: remove
      }
      setShowDrawer(false);
    },
    [data, rowKey]
  );
  const onCancel = useCallback(() => {
    setShowDrawer(false);
    setIsEditing(false);
    setUid(undefined);
    handleCancel?.();
  }, []);

  const columns = useMemo(() => {
    if (!disableEdit) {
      const newTableEdit = [
        ...tableColumns,
        {
          title: t('Actions'),
          dataIndex: 'action',
          fixed: 'right',
          align: 'right',
          className: 'uw-table-action',
          width: 120,
          render: (
            text: string,
            record: T & { key: string; goods: { scope: string }[] }
          ) => (
            <Space size="middle">
              <>
                <span>
                  <EyeOutlined
                    className={styles.actionIcon}
                    onClick={() =>
                      handleClickEdit(record as T & { key: string }, Mode.View)
                    }
                  />
                </span>
                <span>
                  <Icon
                    className={styles.actionIcon}
                    component={Edit}
                    onClick={() =>
                      handleClickEdit(
                        record as T & {
                          key: string;
                          goods: { scope: string }[];
                        },
                        record?.goods?.some(
                          item => item?.scope === ScopeEnum.LIBRARY
                        )
                          ? Mode.View
                          : Mode.Edit
                      )
                    }
                  />
                </span>
                <span>
                  <Popconfirm
                    title={t('Are you sure to delete?')}
                    onConfirm={() =>
                      deleteRow(
                        (record as unknown as T & { baseInfoId: number })
                          .baseInfoId
                      )
                    }
                    okText={t('Confirm')}
                    placement="topRight"
                  >
                    <Icon className={styles.actionIcon} component={Delete} />
                  </Popconfirm>
                </span>
              </>
            </Space>
          ),
        },
      ];
      return newTableEdit;
    }
    return tableColumns;
  }, [enums, data, tableColumns]);

  return (
    <section className={styles.attachmentTable}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <section className={styles.ruleSubTitle}>{t(title)}</section>
        <Button
          style={{ marginBottom: styles.gapMd, width: '96px' }}
          onClick={handleAddColumn}
          block
          disabled={disableAdd}
        >
          {t('+ Add')}
        </Button>
      </div>

      <Table
        columns={columns as ColumnEditingType<T & { key: string }>[]}
        dataSource={data}
        scroll={{ x: 'max-content' }}
        pagination={typeof showPage !== 'undefined' ? false : undefined}
      />
      <AttachmentEditDrawer
        visible={showDrawer}
        drawerTitle={title}
        stageType={stageType}
        handleCloseDrawer={onCancel}
        onSubmit={onSubmitEditDrawer}
        form={form}
        mode={editTableMode}
        dataSource={
          rowItem as unknown as AttachmentConfigList & { key: string }
        }
        categoryId={categoryId}
        setCategoryId={setCategoryId}
        goodsCode={goodsCode}
        setGoodsCode={setGoodsCode}
      />
    </section>
  );
}

export default AttachmentEditTable;
