import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';

import { Alert } from 'antd';

import { Pagination, cssVars } from '@zhongan/nagrand-ui';
import { ColumnEditingType, EditableTable } from '@zhongan/nagrand-ui';

import { SaveRuleRequestType } from 'genesis-web-service';

import {
  BusinessType,
  ScopeEnum,
  TypeEnums,
} from '@uw/interface/enum.interface';
import { useRuleConfigByType } from '@uw/pages/configuration-pages/NewBusinessConfiguration/request';

import { handleDelete, handleSave } from '../services/nbConfiguration.service';

interface Props {
  columns: ColumnEditingType<SaveRuleRequestType>[];
  type: TypeEnums;
  tooltip?: string;
  tips?: string;
  hasRuleEditAuth: boolean;
  hideOnSinglePage?: boolean;
}

export const CommonEditTable: FC<Props> = ({
  columns,
  type,
  tooltip,
  tips,
  hasRuleEditAuth,
  hideOnSinglePage = false,
}) => {
  const [, setIsEditing] = useState(false);
  const { getDataSource, dataSource, current, total, onChange, setDataSource } =
    useRuleConfigByType(type);

  useEffect(() => {
    getDataSource(1, 10);
  }, []);

  const handleDel = useCallback<(idx: number) => void>(
    async idx => {
      const deleteFlag = await handleDelete(
        idx,
        dataSource as (SaveRuleRequestType & {
          key: string;
        })[]
      );
      if (deleteFlag) {
        getDataSource(1, 10);
      }
    },
    [dataSource, getDataSource]
  );

  const handleSaveData = useCallback<(row: SaveRuleRequestType) => void>(
    async row => {
      const saveFlag = await handleSave(row, type);
      if (saveFlag) {
        getDataSource(1, 10);
        return true;
      }
      return false;
    },
    [getDataSource, type]
  );

  const defaultAddData = useMemo(() => {
    const originData: Record<string, BusinessType[] | undefined> = {};
    columns.forEach(col => {
      if (col.dataIndex === 'businessTypeList') {
        originData.businessTypeList = [
          BusinessType.NEW_BUSINESS,
          BusinessType.RENEWAL,
        ];
      } else {
        originData[col.dataIndex as string] = undefined;
      }
    });
    return originData;
  }, [columns]);

  return (
    <>
      {(tooltip || tips) && dataSource?.length === 0 && (
        <Alert
          description={tooltip || tips}
          type="info"
          closable
          style={{ marginBottom: cssVars.gapMd }}
        />
      )}
      <EditableTable<SaveRuleRequestType>
        pagination={false}
        columns={columns}
        dataSource={
          dataSource as (SaveRuleRequestType & {
            key: string;
          })[]
        }
        setDataSource={setDataSource}
        initializeAddData={defaultAddData}
        addBtnProps={{
          disabled: !hasRuleEditAuth,
        }}
        editBtnProps={{
          disabled: () => !hasRuleEditAuth,
        }}
        deleteBtnProps={{
          disabled: () => !hasRuleEditAuth,
          handleDelete: idx => handleDel(idx),
        }}
        disableEdit={!hasRuleEditAuth}
        disableAdd={!hasRuleEditAuth}
        disabledOfScope={ScopeEnum.LIBRARY}
        disabledRowKey="scope"
        scroll={{ x: 'max-content' }}
        setIsEditing={setIsEditing}
        handleConfirm={row => handleSaveData(row)}
      />
      <Pagination
        className="mt-[18px]"
        showQuickJumper
        current={current}
        total={total}
        onChange={onChange}
        hideOnSinglePage={hideOnSinglePage}
      />
    </>
  );
};
