import React, {
  useState,
  useEffect,
  forwardRef,
  Ref,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Alert, Checkbox, Row, Tooltip } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useRuleConfigByType } from '@uw/pages/configuration-pages/NewBusinessConfiguration/request';
import { BizDict, TypeEnums } from '@uw/interface/enum.interface';

import { cssVars } from '@zhongan/nagrand-ui';

import styles from '../NewBusinessConfiguration.module.scss';

enum NBConfigurationWorkflow {
  VERIFICATION_EDIT = 'VERIFICATION_EDIT',
}

interface Props {
  id: string;
  title: string;
  options: BizDict[];
  tooltipText: string;
  hasRuleEditAuth: boolean;
  selectedWorkflows: string[];
}

export const VerificationCheck = forwardRef(
  (
    {
      id,
      title,
      options,
      tooltipText,
      hasRuleEditAuth,
      selectedWorkflows,
    }: Props,
    ref: Ref<any>
  ) => {
    const [selectedWorkflow, setSelectedWorkflow] = useState<string[]>();
    const { dataSource } = useRuleConfigByType(TypeEnums.PROPOSAL_VERIFICATION);
    useEffect(() => {
      setSelectedWorkflow(selectedWorkflows);
    }, [selectedWorkflows]);
    const onChange = (values: CheckboxValueType[]) => {
      setSelectedWorkflow(values as string[]);
    };

    const allEditProposalOption = useMemo(
      () =>
        options?.filter(
          option =>
            option.enumItemName === NBConfigurationWorkflow.VERIFICATION_EDIT
        ),
      [options]
    );

    useImperativeHandle(ref, () => ({
      onSubmit() {
        return selectedWorkflow;
      },
    }));

    return (
      <div id={id} key={id}>
        <section className={styles['rule-sub-title']}>
          {title}
          {tooltipText && (
            <Tooltip placement="right" title={tooltipText}>
              <ExclamationCircleOutlined style={{ marginLeft: styles.gapXs }} />
            </Tooltip>
          )}
        </section>
        {tooltipText &&
          dataSource &&
          dataSource.length === 0 &&
          selectedWorkflow &&
          selectedWorkflow.length === 0 && (
            <Alert
              description={tooltipText}
              type="info"
              closable
              style={{ marginBottom: cssVars.gapMd }}
            />
          )}

        <Checkbox.Group
          value={selectedWorkflow}
          onChange={onChange}
          disabled={!hasRuleEditAuth}
        >
          {allEditProposalOption?.map(option => (
            <Row
              key={option.enumItemName}
              style={{ paddingBottom: cssVars.gapMd }}
            >
              <Checkbox value={option.enumItemName}>{option.itemName}</Checkbox>
            </Row>
          ))}
        </Checkbox.Group>
      </div>
    );
  }
);
