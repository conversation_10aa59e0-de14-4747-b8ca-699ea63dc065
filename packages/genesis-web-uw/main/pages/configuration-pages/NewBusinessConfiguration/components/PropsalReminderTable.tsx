import React, { FC, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Row } from 'antd';
import { FormInstance } from 'antd/es/form/Form';
import { ColumnProps } from 'antd/es/table';

import { Input, Table } from '@zhongan/nagrand-ui';

import { useBizDict } from '@uw/hook/useBizDict';
import { BizStatusEnum } from '@uw/interface/enum.interface';

import styles from '../NewBusinessConfiguration.module.scss';
import { useProposalReminderConfigContext } from '../context';
import { ActionMenu } from './ActionMenu';

interface Props {
  form: FormInstance;
  hasProposalEditAuth: boolean;
}

interface OriginData {
  name?: string;
  bizStatus?: string;
}

interface SortResultItem {
  enumItemName: BizStatusEnum;
  dictValueName: string | undefined;
  sort: number;
}

const sortBizStatus = [
  {
    enumItemName: BizStatusEnum.DATA_ENTRY_IN_PROGRESS,
    dictValueName: '',
    sort: 2,
  },
  {
    enumItemName: BizStatusEnum.PENDING_PROPOSAL_CHECK,
    dictValueName: '',
    sort: 3,
  },
  {
    enumItemName: BizStatusEnum.WAITING_FOR_ISSUANCE,
    dictValueName: '',
    sort: 4,
  },
];

export const ProposalReminderTable: FC<Props> = ({
  form,
  hasProposalEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { reminderConfig } = useProposalReminderConfigContext();
  const bizStatusEnums = useBizDict('bizStatus');

  const sortResult = useMemo(
    () =>
      bizStatusEnums
        ?.reduce((result: SortResultItem[], prev) => {
          const data = sortBizStatus.find(
            item => item.enumItemName === prev.enumItemName
          );
          if (data) {
            result.push({ ...data, dictValueName: prev.dictValueName });
          }
          return result;
        }, [])
        .sort((a, b) => a.sort - b.sort),
    [bizStatusEnums]
  );

  const originData = useMemo(
    () =>
      sortResult?.map(item => ({
        name: item.dictValueName,
        bizStatus: `${item.enumItemName}_REM`,
      })) ?? [],
    [sortResult]
  );

  const [dataSource] = useState<OriginData[]>(originData);

  useEffect(() => {
    reminderConfig?.forEach(status => {
      const fieldText = originData?.find(
        item => `${status?.bizStatus}_REM` === item?.bizStatus
      );

      if (fieldText) {
        form.setFieldsValue({
          [fieldText.bizStatus]: status?.daysList || [null],
        });
      }
    });
  }, [reminderConfig]);

  const columns: ColumnProps<OriginData>[] = [
    {
      title: t('Proposal Status'),
      dataIndex: 'name',
      key: 'name',
      width: 300,
    },
    {
      title: t('Days'),
      key: 'bizStatus',
      width: 280,
      render: record => (
        <Row>
          <Form.List
            name={record.bizStatus}
            initialValue={[null]}
            rules={[
              {
                validator: async (_, names) => {
                  if (
                    Array.from(new Set(names.map((item: number) => +item)))
                      .length < names.length
                  ) {
                    return Promise.reject(
                      new Error(
                        t(
                          'Proposal reminder days can not duplicate. Please check.'
                        )
                      )
                    );
                  }
                  if (names.find((item: number) => Number(item) <= 0)) {
                    return Promise.reject(
                      new Error(t('Please enter a number greater than 0'))
                    );
                  }
                },
              },
            ]}
          >
            {fields => (
              <>
                {fields.map(field => (
                  <Form.Item className={'mb-0'}>
                    <Form.Item
                      {...field}
                      validateTrigger={['onChange', 'onBlur']}
                      noStyle
                    >
                      <Input
                        className={'w-60'}
                        disabled={
                          form.getFieldValue(record.bizStatus)?.[0] === null ||
                          !hasProposalEditAuth
                        }
                        placeholder={t('Please input')}
                        addonAfter={t('Day(s)')}
                      />
                    </Form.Item>
                  </Form.Item>
                ))}
              </>
            )}
          </Form.List>
        </Row>
      ),
    },
    {
      title: t('Actions'),
      key: 'action',
      width: 100,
      fixed: 'right',
      render: record =>
        hasProposalEditAuth && <ActionMenu record={record} form={form} />,
    },
  ];

  return (
    <>
      <Form form={form} className={styles.proposalContent}>
        <Table
          className={styles.reminderTable}
          style={{ marginTop: styles.gapXs }}
          columns={columns}
          dataSource={dataSource}
          scroll={{ x: 'max-content' }}
        />
      </Form>
    </>
  );
};
