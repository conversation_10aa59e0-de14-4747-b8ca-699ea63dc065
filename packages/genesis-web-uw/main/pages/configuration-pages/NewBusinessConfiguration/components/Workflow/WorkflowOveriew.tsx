import React, {
  FC,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Graph } from '@antv/x6';
import { DagreLayout } from '@antv/layout';
import { Edit, Selected } from '@uw/assets/new-icons';

import { dictMap } from '@uw/hook/useBizDict';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import clsx from 'clsx';

import { NBConfiguration } from 'genesis-web-service';

import styles from './Workflow.module.scss';
import {
  NBWorklowContext,
  UPDATE_WORKFLOWDETAIL,
  UPDATE_WORKFLOWPOPUP,
} from './WorkflowProvider';
import { autoLayout, registerElements } from './util';

interface Props {
  id?: string;
  hideHover?: boolean;
  showSelection?: boolean;
  data: NBConfiguration.WokflowDetail;
  selected?: boolean;
  onClick?: (data: NBConfiguration.WokflowDetail, id?: string) => void;
  bizDictMap: Record<string, BizDict[]> | undefined;
}
export const WorkflowsOveriew: FC<Props> = ({
  onClick,
  selected,
  hideHover,
  showSelection,
  data,
  id,
  bizDictMap,
}) => {
  const container = useRef<HTMLDivElement>();
  const graph = useRef<Graph>({} as Graph);
  const dagreLayout = useRef<DagreLayout>({} as DagreLayout);
  const { dispatch } = useContext(NBWorklowContext);
  const [workflowData, setWorkflowData] =
    useState<NBConfiguration.WokflowDetail>();
  const initLayout = useCallback(() => {
    dagreLayout.current = new DagreLayout({
      type: 'dagre',
      rankdir: 'LR',
      nodesep: 10,
      ranksep: 45,
      controlPoints: false,
    });
  }, []);
  const initWorkflow = useCallback(() => {
    graph.current = new Graph({
      container: container.current,
      panning: false,
      interacting: false,
      mousewheel: false,
      connecting: {
        router: 'manhattan',
      },
    });

    registerElements(true);
  }, [graph]);

  useEffect(() => {
    const newData = {
      ...data,
      nodes: data?.nodes.map(node => ({
        ...node,
        label:
          dictMap(bizDictMap?.[node.data.dictKey] ?? [], node.data.nodeType) ??
          '',
      })),
    };
    setWorkflowData(newData);
  }, [bizDictMap, data]);

  useEffect(() => {
    initWorkflow();
    initLayout();
    autoLayout(
      workflowData as NBConfiguration.WokflowDetail,
      dagreLayout.current,
      graph.current
    );
    return () => {
      graph.current.dispose();
    };
  }, [initLayout, initWorkflow, workflowData]);

  const handleEditWorkflow = useCallback(() => {
    dispatch({
      type: UPDATE_WORKFLOWDETAIL,
      workflowDetail: workflowData!,
    });
    dispatch({
      type: UPDATE_WORKFLOWPOPUP,
      showPopup: true,
    });
  }, [workflowData]);

  return (
    <div
      className={clsx(
        styles.workflowItem,
        !hideHover && styles.showHover,
        showSelection && styles.showSelection,
        selected && styles.selected
      )}
    >
      <div
        className={styles.workflowContent}
        onClick={() => {
          onClick?.(workflowData!, id);
        }}
      >
        <div
          style={{ height: '260px', width: '600px' }}
          ref={container as RefObject<HTMLDivElement>}
          className={styles.workflowGraph}
        />
        {selected && <Selected className={styles.selectedSvg} />}
      </div>
      <Edit className={styles.editSvg} onClick={handleEditWorkflow} />
    </div>
  );
};
