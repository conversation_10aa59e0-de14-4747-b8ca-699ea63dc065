import React, { FC } from 'react';
import { TextBody } from '@uw/components/Text';
import { useTranslation } from 'react-i18next';
import { Collapse } from 'antd';
import { Down } from '@uw/assets/new-icons';
import { NBConfiguration } from 'genesis-web-service';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { WorkflowsOveriew } from './WorkflowOveriew';
import styles from './Workflow.module.scss';

const { Panel } = Collapse;

interface Props {
  data: NBConfiguration.WokflowDetail;
  selectedId?: string;
  handleSelectWorkflow: (
    data: NBConfiguration.WokflowDetail,
    id?: string
  ) => void;
  workflowTemplates: NBConfiguration.WokflowDetail[] | undefined;
  bizDictMap: Record<string, BizDict[]> | undefined;
}

export const WorkflowsSelection: FC<Props> = ({
  data,
  handleSelectWorkflow,
  workflowTemplates,
  selectedId,
  bizDictMap,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    <div className={styles.workflowSelection}>
      <TextBody weight={700}>{t('Current process flow')}</TextBody>
      <WorkflowsOveriew
        data={data}
        showSelection
        selected
        bizDictMap={bizDictMap}
      />
      <Collapse ghost style={{ marginTop: styles.gapBig }}>
        <Panel
          showArrow={false}
          header={
            <TextBody
              weight={700}
              style={{ display: 'flex', alignItems: 'center' }}
            >
              {t(
                'Change the current process flow from the process flow template'
              )}
              <Down className="down-svg" style={{ marginLeft: styles.gapXs }} />
            </TextBody>
          }
          key="1"
        >
          {workflowTemplates?.map((workflow, index) => (
            <WorkflowsOveriew
              id={index.toString()}
              key={index}
              data={workflow}
              onClick={handleSelectWorkflow}
              selected={index.toString() === selectedId}
              bizDictMap={bizDictMap}
            />
          ))}
        </Panel>
      </Collapse>
    </div>
  );
};
