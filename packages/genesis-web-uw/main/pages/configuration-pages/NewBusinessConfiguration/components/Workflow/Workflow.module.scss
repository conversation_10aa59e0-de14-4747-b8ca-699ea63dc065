.add-block {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 390px;
  height: 120px;
  padding: $gap-md 0px;
  background-color: var(--layout-body-background);
  border: 1px dashed var(--border-line-color-secondary);
  text-align: center;
  border-radius: 16px;
  cursor: pointer;
  &:hover {
    border-color: var(--primary-color);
  }
}

.edit-svg {
  margin-left: var(--gap-xs);
  cursor: pointer;
}

// WokflowOveriew
.workflow-item {
  margin-top: $gap-md;
  display: flex;
  align-items: flex-start;
  .edit-svg {
    display: none;
  }
  .workflow-content {
    width: 650px;
    display: flex;
    align-items: center;
  }
  &.show-hover:hover,
  &.show-selection {
    .workflow-content {
      border: 1px solid rgba(16, 42, 67, 0.12);
      box-shadow: 0px 4px 24px rgba(16, 42, 67, 0.12);
      border-radius: var(--border-radius-base);
    }
    .edit-svg {
      display: block;
    }
  }
  &.show-hover.selected {
    .workflow-content {
      border-color: var(--primary-color);
      .selected-svg {
        margin-left: var(--gap-xs);
      }
    }
  }
}

.workflow-selection {
  :global {
    .#{$ant-prefix}-collapse-item-active {
      .down-svg {
        transform: rotate(180deg);
      }
    }
    .#{$ant-prefix}-collapse-header,
    .#{$ant-prefix}-collapse-content-box {
      padding: 0 !important;
    }
  }
}
$circleDiameter: 48px;
$rectWidth: 100px;
$rectHeight: 40px;

.workflow-container {
  display: flex;
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-line-color-secondary);
  .dnd-wrap {
    width: 160px;
    flex-shrink: 0;
    padding: $gap-lg;
    background-color: var(--item-bg-hover);
    border-top-left-radius: var(--border-radius-base);
    border-bottom-left-radius: var(--border-radius-base);
    .dnd-shape {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: $gap-lg;
      .dnd-circle {
        border: 1px solid var(--border-line-color-secondary);
        width: $circleDiameter;
        height: $circleDiameter;
        border-radius: 100%;
        cursor: pointer;
      }
      .dnd-rect {
        background-color: var(--item-bg-selected);
        width: $rectWidth;
        height: $rectHeight;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
}

.stage-container {
  flex-shrink: 0;
  border-left: 1px solid var(--border-line-color-secondary);
  overflow: hidden;
  transition: width ease 500ms;
  .stage-header {
    display: flex;
    align-items: center;
    padding: $gap-md $gap-lg 0 $gap-lg;
    height: 38px;
  }
  .stage-body {
    padding: 0 $gap-lg;
  }
}

:export {
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  gapLg: $gap-lg;
  gapBig: $gap-big;
  circleWidth: $circleDiameter;

  // workflow
  primaryLight: var(--primary-light);
  itemBgSelected: var(--item-bg-selected);
  textColor: var(--text-color);
  formBg: var(--form-bg);
  whiteColor: var(--white);
}
