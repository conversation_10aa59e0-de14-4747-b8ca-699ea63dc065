import React, {
  RefObject,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import {
  AlignCenterOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from '@ant-design/icons';
import { DagreLayout } from '@antv/layout';
import { Graph, Shape } from '@antv/x6';
import { Clipboard } from '@antv/x6-plugin-clipboard';
import { Dnd } from '@antv/x6-plugin-dnd';
import { History } from '@antv/x6-plugin-history';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Selection } from '@antv/x6-plugin-selection';
import { Button, Col, Select } from 'antd';

import { Divider } from 'genesis-web-component/lib/components';
import { NBConfiguration } from 'genesis-web-service';

import { TextBody } from '@uw/components/Text';
import { BizDict, ZOOM } from '@uw/interface/enum.interface';

import styles from './Workflow.module.scss';
import {
  autoLayout, // useInitWorkflow,
  nodeAttrs,
  registerElements,
  transformGraphToData,
} from './util';
import { WorkflowShape } from './workflow.interface';

interface Props {
  workflowDetail: NBConfiguration.WokflowDetail;
  bizDictMap: Record<string, BizDict[]> | undefined;
}

const { Option } = Select;

export const WorkflowCanvas = forwardRef(
  ({ workflowDetail, bizDictMap }: Props, ref) => {
    const [transformedWorkflowDetail, setTransformedWorkflowDetail] =
      useState(workflowDetail);
    useEffect(() => {
      setTransformedWorkflowDetail(workflowDetail);
    }, [workflowDetail]);

    const [t] = useTranslation(['uw', 'common']);
    const container = useRef<HTMLDivElement>();
    const dndContainerRef = useRef<HTMLDivElement>();
    const stageDrawerRef = useRef<HTMLDivElement>();

    const [curNode, setCurNode] = useState<NBConfiguration.SubmitNodeType>();
    const graph = useRef<Graph>({} as Graph);
    const dnd = useRef<Dnd>({} as Dnd);
    const dagreLayout = useRef<DagreLayout>({} as DagreLayout);
    const [currentZoom, setCurrentZoom] = useState(1);

    useImperativeHandle(
      ref,
      () => ({
        onSubmit() {
          const data = graph.current.toJSON();
          const submitData = transformGraphToData(data, true, bizDictMap);
          return submitData;
        },
      }),
      []
    );

    const initGraph = useCallback(() => {
      registerElements();
      // #region 初始化画布
      graph.current = new Graph({
        container: container.current,
        panning: true,
        grid: true,
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3,
        },
        connecting: {
          router: 'manhattan',
          createEdge() {
            return new Shape.Edge({
              shape: WorkflowShape.EDGE,
              attrs: {
                line: {
                  stroke: styles.primaryLight,
                  strokeWidth: 1,
                  targetMarker: {
                    name: 'block',
                    width: 12,
                    height: 8,
                  },
                },
              },
              zIndex: 0,
            });
          },
          validateConnection({ targetMagnet }) {
            return !!targetMagnet;
          },
        },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#5F95FF',
                stroke: '#5F95FF',
              },
            },
          },
        },
      });

      graph.current.zoomToFit({ maxScale: 1 });
      graph.current.centerContent();
      // #endregion
      graph.current
        .use(
          new Selection({
            enabled: true,
            rubberband: true,
            showNodeSelectionBox: true,
          })
        )
        .use(
          new Keyboard({
            enabled: true,
          })
        )
        .use(
          new Clipboard({
            enabled: true,
          })
        )
        .use(
          new History({
            enabled: true,
          })
        );
      // #region 快捷键与事件
      graph.current.bindKey(['meta+c', 'ctrl+c'], () => {
        const cells = graph.current.getSelectedCells();
        if (cells.length) {
          graph.current.copy(cells);
        }
        return false;
      });
      graph.current.bindKey(['meta+x', 'ctrl+x'], () => {
        const cells = graph.current.getSelectedCells();
        if (cells.length) {
          graph.current.cut(cells);
        }
        return false;
      });
      graph.current.bindKey(['meta+v', 'ctrl+v'], () => {
        if (!graph.current.isClipboardEmpty()) {
          const cells = graph.current.paste({ offset: 32 });
          graph.current.paste();
          graph.current.select(cells);
        }
        return false;
      });

      // undo redo
      graph.current.bindKey(['meta+z', 'ctrl+z'], () => {
        if (graph.current.canUndo()) {
          graph.current.undo();
        }
        return false;
      });
      graph.current.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
        if (graph.current.canRedo()) {
          graph.current.redo();
        }
        return false;
      });

      // select all
      graph.current.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = graph.current.getNodes();
        if (nodes) {
          graph.current.select(nodes);
        }
      });

      // delete
      graph.current.bindKey('backspace', () => {
        const cells = graph.current.getSelectedCells();
        if (cells.length) {
          graph.current.removeCells(cells);
        }
      });

      // zoom
      graph.current.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = graph.current.zoom();
        if (zoom < 1.5) {
          graph.current.zoom(0.1);
        }
      });
      graph.current.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = graph.current.zoom();
        if (zoom > 0.5) {
          graph.current.zoom(-0.1);
        }
      });

      // 控制连接桩显示/隐藏
      const showPorts = (
        portsParams: NodeListOf<SVGElement>,
        show: boolean
      ) => {
        for (let i = 0, len = portsParams.length; i < len; i += 1) {
          // eslint-disable-next-line no-param-reassign
          portsParams[i].style.visibility = show ? 'visible' : 'hidden';
        }
      };
      graph.current.on('node:mouseenter', () => {
        const portsArr = container.current?.querySelectorAll(
          '.x6-port-body'
        ) as NodeListOf<SVGElement>;
        showPorts(portsArr, true);
      });
      graph.current.on('node:mouseleave', () => {
        const portsArr = container.current?.querySelectorAll(
          '.x6-port-body'
        ) as NodeListOf<SVGElement>;
        showPorts(portsArr, false);
      });
      graph.current.on(
        'node:dblclick',
        (event: { node: NBConfiguration.SubmitNodeType }) => {
          setCurNode(event.node);
        }
      );
      graph.current.on('blank:click', () => {
        setCurNode(undefined);
      });
      // #endregion
      graph.current.on('scale', ({ sx }) => {
        if (sx) {
          setCurrentZoom(sx);
        }
      });
    }, [container]);

    const handleZoom = useCallback(
      action => {
        switch (action) {
          case ZOOM.Minus:
            graph.current.zoom(-0.2, { minScale: 0.5 });
            setCurrentZoom(prevState => Math.max(prevState - 0.2, 0.5));
            break;
          case ZOOM.Plus:
            graph.current.zoom(0.2, { maxScale: 3 });
            setCurrentZoom(prevState => Math.min(prevState + 0.2, 3));
            break;
          case ZOOM.Fit:
            graph.current.zoomToFit({ maxScale: 3, minScale: 0.5 });
            setCurrentZoom(graph.current.zoom());
            break;
          default:
            graph.current.centerContent();
        }
      },
      [graph]
    );

    const initDnd = useCallback(() => {
      dnd.current = new Dnd({
        target: graph.current,
        scaled: false,
        dndContainer: dndContainerRef.current,
      });
    }, [graph, dndContainerRef]);

    const initLayout = useCallback(() => {
      dagreLayout.current = new DagreLayout({
        type: 'dagre',
        rankdir: 'LR',
        nodesep: 10,
        ranksep: 45,
        controlPoints: false,
      });
    }, []);

    useEffect(() => {
      initGraph();
      initLayout();
      autoLayout(transformedWorkflowDetail, dagreLayout.current, graph.current);
      initDnd();

      return () => {
        graph.current.dispose();
      };
    }, [transformedWorkflowDetail]);

    const [stageVal, setStageVal] = useState<string>();
    const startDrag = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
      const target = e.currentTarget;
      const type = target.getAttribute('data-type');
      const node = graph.current.createNode?.({
        ...nodeAttrs(type as WorkflowShape, false),
        shape: type as string,
      });
      dnd.current.start?.(node, e.nativeEvent);
    };

    useEffect(() => {
      setStageVal(curNode?.data?.nodeType as string);
    }, [curNode]);

    const handleChangeStage = (value: string) => {
      setStageVal(value);
      const data = graph.current.toJSON?.();
      const newData = transformGraphToData(
        data,
        false,
        bizDictMap,
        value,
        curNode?.id
      );

      setTransformedWorkflowDetail(newData);
    };

    useEffect(() => {
      if (curNode) {
        graph.current.resize(646, 554);
      } else {
        graph.current.resize(974, 554);
      }
      graph.current.zoomToFit({ maxScale: 1 });
      graph.current.centerContent();
    }, [graph.current, curNode]);

    return (
      <div className={styles.workflowContainer}>
        <div
          className={styles.dndWrap}
          ref={dndContainerRef as RefObject<HTMLDivElement>}
        >
          <div className={styles.dndShape}>
            <div
              data-type={WorkflowShape.CIRCLE}
              className={styles.dndCircle}
              onMouseDown={startDrag}
            ></div>
            <div>{t('Start | End')}</div>
          </div>
          <div className={styles.dndShape}>
            <div
              data-type={WorkflowShape.RECTANGLE}
              className={styles.dndRect}
              onMouseDown={startDrag}
            ></div>
            <div>{t('Key Node')}</div>
          </div>
        </div>
        <div
          style={{ height: '554px', width: '100%' }}
          ref={container as RefObject<HTMLDivElement>}
          className={styles.workflowGraph}
        />
        <Button.Group
          style={{ position: 'absolute', top: '100px', right: '85px' }}
        >
          <Button
            icon={<ZoomOutOutlined />}
            disabled={currentZoom <= 0.5}
            size="small"
            onClick={() => handleZoom(ZOOM.Minus)}
            style={{ minWidth: '20px' }}
          />
          <Button
            size="small"
            onClick={() => handleZoom(ZOOM.Fit)}
            style={{ minWidth: '68px' }}
          >
            {`${Number(currentZoom * 100).toFixed(0)}%`}
          </Button>
          <Button
            icon={<ZoomInOutlined />}
            disabled={currentZoom >= 3}
            size="small"
            onClick={() => handleZoom(ZOOM.Plus)}
            style={{ minWidth: '20px' }}
          />
        </Button.Group>
        <Button
          icon={<AlignCenterOutlined />}
          size="small"
          onClick={() => handleZoom(ZOOM.Center)}
          style={{
            position: 'absolute',
            top: '100px',
            right: '40px',
            minWidth: '40px',
          }}
        />
        <div
          ref={stageDrawerRef as RefObject<HTMLDivElement>}
          className={styles.stageContainer}
          style={curNode ? { width: '328px' } : { width: 0 }}
        >
          <div className={styles.stageHeader}>
            <TextBody weight={700}>{curNode?.label}</TextBody>
          </div>
          <Divider category="body" />
          <div className={styles.stageBody}>
            <Col span={24}>
              <div>{t('Stage')}</div>
              <Select
                placeholder={t('Please select')}
                value={stageVal}
                onChange={handleChangeStage}
                allowClear
                style={{ width: '100%' }}
                getPopupContainer={triggerNode => triggerNode?.parentNode}
              >
                {bizDictMap?.[curNode?.data?.dictKey ?? '']?.map(option => (
                  <Option
                    key={option?.enumItemName}
                    value={option?.enumItemName}
                  >
                    {option?.itemName}
                  </Option>
                ))}
              </Select>
            </Col>
          </div>
        </div>
      </div>
    );
  }
);
