import React, { useState, FC } from 'react';
import { But<PERSON> } from 'antd';
import { useTranslation } from 'react-i18next';

import { CommonDrawer } from '@uw/components/CommonDrawer';

import { NBConfiguration } from 'genesis-web-service';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import styles from './Workflow.module.scss';
import { WorkflowsSelection } from './WorkflowsSelection';
import { useSubmitWorkflow } from '../../hooks/request';

interface Props {
  visible: boolean;
  data: NBConfiguration.WokflowDetail;
  handleCloseDrawer: () => void;
  workflowTemplates: NBConfiguration.WokflowDetail[] | undefined;
  getSelectedWorkflow: () => Promise<void>;
  bizDictMap: Record<string, BizDict[]> | undefined;
}

export const WorkflowDrawer: FC<Props> = ({
  visible,
  handleCloseDrawer,
  data,
  workflowTemplates,
  getSelectedWorkflow,
  bizDictMap,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [workflowSubmitData, setWorkflowSubmitData] =
    useState<NBConfiguration.SubmitWokflowDetail>();
  const [loading, submitWorkflow] = useSubmitWorkflow(
    getSelectedWorkflow,
    workflowSubmitData!
  );
  const [selectedId, setSelectedId] = useState<string>();

  const handleSelectWorkflow = (
    workflow: NBConfiguration.WokflowDetail,
    id?: string
  ) => {
    setWorkflowSubmitData(workflow as NBConfiguration.SubmitWokflowDetail);
    setSelectedId(id);
  };

  const handleClose = () => {
    setWorkflowSubmitData(undefined);
    handleCloseDrawer();
  };

  return (
    <CommonDrawer
      width="752"
      visible={visible}
      title={t('Verification/Compliance/UW Process Flow Configuration')}
      handleCloseDrawer={handleClose}
      destroyOnClose={true}
      action={
        <>
          <Button onClick={handleClose}>{t('Cancel')}</Button>
          <Button
            type={'primary'}
            onClick={async () => {
              if (workflowSubmitData) {
                await submitWorkflow();
              }
              handleClose();
            }}
            style={{ marginLeft: styles.gapMd }}
            htmlType="submit"
            loading={loading}
          >
            {t('Submit')}
          </Button>
        </>
      }
    >
      <WorkflowsSelection
        data={data}
        workflowTemplates={workflowTemplates}
        selectedId={selectedId}
        handleSelectWorkflow={handleSelectWorkflow}
        bizDictMap={bizDictMap}
      />
    </CommonDrawer>
  );
};
