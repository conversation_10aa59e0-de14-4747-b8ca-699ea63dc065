import React, { FC, useCallback, useContext, useRef } from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { NBConfiguration } from 'genesis-web-service';

import { WorkflowCanvas } from './WorkflowCanvas';
import { NBWorklowContext, UPDATE_WORKFLOWPOPUP } from './WorkflowProvider';
import { useSubmitWorkflow } from '../../hooks/request';

interface Props {
  bizDictMap: Record<string, BizDict[]> | undefined;
  getSelectedWorkflow: () => Promise<void>;
  handleCloseDrawer: () => void;
}

export const WorkflowPopup: FC<Props> = ({
  getSelectedWorkflow,
  bizDictMap,
  handleCloseDrawer,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const canvasRef = useRef<{
    onSubmit: () => NBConfiguration.SubmitWokflowDetail;
  }>();
  const { state, dispatch } = useContext(NBWorklowContext);
  const [loading, submitWorkflow] = useSubmitWorkflow(getSelectedWorkflow);

  const handleClosePopup = useCallback(() => {
    dispatch({ type: UPDATE_WORKFLOWPOPUP, showPopup: false });
  }, []);

  const handleSubmitWorkflow = useCallback(async () => {
    const data = canvasRef.current?.onSubmit();
    await submitWorkflow(data);
    handleClosePopup();
    handleCloseDrawer();
  }, []);

  return (
    <Modal
      open={state?.showPopup}
      closable={false}
      maskClosable={false}
      title={t('Edit Process Flow')}
      onCancel={handleClosePopup}
      onOk={handleSubmitWorkflow}
      okText={t('Submit')}
      width={1200}
      confirmLoading={loading}
      getContainer={() =>
        document.getElementById('proposalRule') as HTMLElement
      }
    >
      <WorkflowCanvas
        workflowDetail={state?.workflowDetail}
        bizDictMap={bizDictMap}
        ref={canvasRef}
      />
    </Modal>
  );
};
