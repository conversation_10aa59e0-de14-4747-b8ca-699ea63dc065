import { NBConfiguration } from 'genesis-web-service';
import React, { Dispatch } from 'react';

// reducer
export const UPDATE_WORKFLOWDETAIL = 'UPDATE_WORKFLOWDETAIL';
export const UPDATE_WORKFLOWPOPUP = 'UPDATE_WORKFLOWPOPUP';

export interface WorkflowDetailType {
  workflowDetail: NBConfiguration.WokflowDetail;
}

export interface WorkflowPopupType {
  showPopup: boolean | undefined;
}

export type NBWorklowState = WorkflowDetailType & WorkflowPopupType;

export type NBWorklowType = WorkflowDetailType | WorkflowPopupType;

export type NBWorklowAction = {
  readonly type: string;
} & NBWorklowType;

export const NBWorklowReducer = (
  state: NBWorklowState,
  action: NBWorklowAction
): NBWorklowState => {
  switch (action.type) {
    case UPDATE_WORKFLOWDETAIL:
      return {
        ...state,
        workflowDetail: (action as WorkflowDetailType).workflowDetail,
      };
    case UPDATE_WORKFLOWPOPUP:
      return {
        ...state,
        showPopup: (action as WorkflowPopupType).showPopup,
      };
    default:
      return state;
  }
};

export const NBWorklowContext = React.createContext<{
  state: NBWorklowState;
  dispatch: Dispatch<NBWorklowAction>;
}>(
  {} as {
    state: NBWorklowState;
    dispatch: Dispatch<NBWorklowAction>;
  }
);

export const initialContextValue = {
  workflowDetail: {
    nodes: [],
    edges: [],
  },
  showPopup: false,
};
