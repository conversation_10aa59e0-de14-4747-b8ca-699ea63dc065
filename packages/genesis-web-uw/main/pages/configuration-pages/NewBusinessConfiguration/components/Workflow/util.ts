import { Cell, Graph } from '@antv/x6';

import { NBConfiguration } from 'genesis-web-service';

import { DagreLayout, Model } from '@antv/layout';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { dictMap } from '@uw/hook/useBizDict';

import styles from './Workflow.module.scss';
import { WorkflowShape } from './workflow.interface';

// #region 初始化图形
export const ports = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: styles.primaryLight,
          strokeWidth: 1,
          fill: styles.whiteColor,
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: styles.primaryLight,
          strokeWidth: 1,
          fill: styles.whiteColor,
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: styles.primaryLight,
          strokeWidth: 1,
          fill: styles.whiteColor,
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: styles.primaryLight,
          strokeWidth: 1,
          fill: styles.whiteColor,
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
  },
  items: [
    {
      group: 'top',
    },
    {
      group: 'right',
    },
    {
      group: 'bottom',
    },
    {
      group: 'left',
    },
  ],
};

export const nodeAttrs = (shape: WorkflowShape, vewOnly: boolean) => {
  switch (shape) {
    case WorkflowShape.RECTANGLE:
      return {
        width: 120,
        height: 48,
        data: { dictKey: 'workflowRectangle' },
        attrs: {
          body: {
            strokeWidth: 0,
            stroke: styles.itemBgSelected,
            fill: styles.itemBgSelected,
            rx: 4,
            ry: 4,
          },
          text: {
            fontSize: 14,
            fill: styles.textColor,
          },
        },
        ...(vewOnly ? {} : { ports: { ...ports } }),
      };
    default:
      return {
        width: 48,
        height: 48,
        data: { dictKey: 'workflowCircle' },
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: styles.formBg,
            fill: styles.formBg,
          },
          text: {
            fontSize: 12,
            fill: styles.textColor,
          },
        },
        ...(vewOnly ? {} : { ports: { ...ports } }),
      };
  }
};

export const registerElements = (vewOnly = false) => {
  Graph.registerNode(
    WorkflowShape.RECTANGLE,
    {
      inherit: 'rect',
      ...nodeAttrs(WorkflowShape.RECTANGLE, vewOnly),
    },
    true
  );
  Graph.registerNode(
    WorkflowShape.CIRCLE,
    {
      inherit: 'circle',
      ...nodeAttrs(WorkflowShape.CIRCLE, vewOnly),
    },
    true
  );
  Graph.registerEdge(
    WorkflowShape.EDGE,
    {
      inherit: 'edge',
      attrs: {
        line: {
          stroke: styles.primaryLight,
          strokeWidth: 1,
          targetMarker: {
            name: 'block',
            width: 12,
            height: 8,
          },
        },
      },
    },
    true
  );
};

export const autoLayout = (
  data: NBConfiguration.WokflowDetail | NBConfiguration.SubmitWokflowDetail,
  dagreLayoutInst: DagreLayout,
  graph: Graph
) => {
  if (data) {
    let newData = data;
    if (!(data as NBConfiguration.SubmitWokflowDetail)?.nodes?.[0]?.x) {
      newData = dagreLayoutInst.layout(
        data as Model
      ) as NBConfiguration.SubmitWokflowDetail;
    }
    graph.fromJSON(newData);
    graph.centerContent();
  }
};

const handleValue = (
  nodeType: string,
  value: string,
  nodeId: string,
  curId?: string
) => (nodeId === curId ? value : nodeType);

export const transformGraphToData = (
  data: { cells: Cell.Properties[] },
  isSubmit: boolean,
  bizDictMap: Record<string, BizDict[]> | undefined,
  value?: string,
  id?: string | undefined
) => {
  const newData: NBConfiguration.SubmitWokflowDetail = {
    nodes: [],
    edges: [],
  };
  data?.cells?.forEach(item => {
    if (item.shape === WorkflowShape.EDGE) {
      newData.edges.push({
        ...item,
        id: undefined,
        source: item.source.cell,
        target: item.target.cell,
      } as NBConfiguration.EdgeType);
    } else {
      newData.nodes.push({
        ...item,
        label: isSubmit
          ? item.data.nodeType
          : dictMap(
              bizDictMap?.[item.data.dictKey ?? ''] ?? [],
              handleValue(
                item.data.nodeType,
                value as string,
                item.id as string,
                id
              )
            ),
        data: {
          ...item.data,
          nodeType: isSubmit
            ? item.data.nodeType
            : handleValue(
                item.data.nodeType,
                value as string,
                item.id as string,
                id
              ),
        },
        ...item.position,
      } as NBConfiguration.SubmitNodeType);
    }
  });
  return newData;
};
