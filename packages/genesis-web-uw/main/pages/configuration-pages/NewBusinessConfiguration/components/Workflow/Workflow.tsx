import React, { FC, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { NBConfiguration } from 'genesis-web-service';

import { AddPlan, Edit } from '@uw/assets/new-icons';
import { useBizDictMapByKeys } from '@uw/biz-dict/hooks';
import { TextBody } from '@uw/components/Text';

import styles from './Workflow.module.scss';
import { WorkflowDrawer } from './WorkflowDrawer';
import { WorkflowsOveriew } from './WorkflowOveriew';
import { WorkflowPopup } from './WorkflowPopup';
import {
  NBWorklowAction,
  NBWorklowContext,
  NBWorklowReducer,
  NBWorklowState,
  initialContextValue,
} from './WorkflowProvider';

interface Props {
  id: string;
  title: string;
  workflowDetail: NBConfiguration.WokflowDetail | undefined;
  workflowTemplates: NBConfiguration.WokflowDetail[] | undefined;
  hasRuleEditAuth: boolean;
  getSelectedWorkflow: () => Promise<void>;
}

export const Workflow: FC<Props> = ({
  id,
  title,
  workflowDetail,
  workflowTemplates,
  hasRuleEditAuth,
  getSelectedWorkflow,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [visible, setVisible] = useState(false);
  const [state, dispatch] = useReducer<
    React.Reducer<NBWorklowState, NBWorklowAction>
  >(NBWorklowReducer, initialContextValue);
  const bizDictMap = useBizDictMapByKeys([
    'workflowCircle',
    'workflowRectangle',
  ]);
  const handleAddWorkflow = () => {
    setVisible(true);
  };

  const handleCloseDrawer = () => {
    setVisible(false);
  };

  return (
    <NBWorklowContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      <div style={{ marginBottom: styles.gapLg }} id={id} key={id}>
        <TextBody weight={700} style={{ marginBottom: styles.gapXs }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {title}
            {hasRuleEditAuth && (
              <Edit className={styles.editSvg} onClick={handleAddWorkflow} />
            )}
          </div>
        </TextBody>
        {workflowDetail ? (
          <WorkflowsOveriew
            data={workflowDetail}
            hideHover
            bizDictMap={bizDictMap}
          />
        ) : (
          <section className={styles.addBlock} onClick={handleAddWorkflow}>
            <AddPlan style={{ marginBottom: styles.gapXs }} />
            <TextBody weight={500} type={'caption'}>
              {t('Select the process flow you want to use')}
            </TextBody>
          </section>
        )}

        <WorkflowDrawer
          visible={visible}
          handleCloseDrawer={handleCloseDrawer}
          data={workflowDetail!}
          workflowTemplates={workflowTemplates}
          getSelectedWorkflow={getSelectedWorkflow}
          bizDictMap={bizDictMap}
        />
      </div>
      <WorkflowPopup
        getSelectedWorkflow={getSelectedWorkflow}
        handleCloseDrawer={handleCloseDrawer}
        bizDictMap={bizDictMap}
      />
    </NBWorklowContext.Provider>
  );
};
