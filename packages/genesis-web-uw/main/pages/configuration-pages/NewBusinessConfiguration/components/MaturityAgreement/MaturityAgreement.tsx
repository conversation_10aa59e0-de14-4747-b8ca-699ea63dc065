import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { EditableTable, TextBody } from '@zhongan/nagrand-ui';

import { MaturityReminderRulesType } from 'genesis-web-service';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import { usePackages } from '@uw/hook/request';

import { getConfig } from './config';

export const MaturityAgreement = ({
  data,
  setData,
  hasRuleEditAuth,
}: {
  data: MaturityReminderRulesType[];
  setData: React.Dispatch<React.SetStateAction<MaturityReminderRulesType[]>>;
  hasRuleEditAuth: boolean;
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const { packages } = usePackages();

  const accountTypeParentEnums = useBizDictByKey('dueDateCompare');

  const columns = useMemo(
    () => getConfig(accountTypeParentEnums!, hasRuleEditAuth, packages),
    [getConfig, accountTypeParentEnums, hasRuleEditAuth, packages]
  );

  return (
    <EditableTable<MaturityReminderRulesType>
      title={<TextBody weight={700}>{t('Maturity Agreement')}</TextBody>}
      columns={columns}
      dataSource={data}
      setDataSource={setData}
      readonly={!hasRuleEditAuth}
      addBtnProps={{
        disabled: !hasRuleEditAuth,
        type: 'default',
        ghost: false,
      }}
    />
  );
};
