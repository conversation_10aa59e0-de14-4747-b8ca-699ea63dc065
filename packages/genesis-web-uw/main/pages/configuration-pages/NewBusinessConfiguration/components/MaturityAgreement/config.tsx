import { Tooltip } from 'antd';

import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';
import { BizDictItem } from 'genesis-web-service';

import { DueDateCompareEnum } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';

import { handleAntdSelectRender } from '../../rule.config';

export const getConfig = (
  dueDateCompareOptions: BizDictItem[],
  hasRuleEditAuth: boolean,
  packages: { label: string; value: string | undefined }[] | undefined
) => [
  {
    title: i18nFn('Maturity Reminder Date Compare to Policy Expiry Date'),
    dataIndex: 'dueDateCompare',
    render: (text: string) => {
      const itemName = dueDateCompareOptions?.find(
        ({ dictValue }) => +text === +dictValue
      )?.dictValueName;
      return itemName ?? i18nFn('--');
    },
    validate: [
      {
        trigger: 'onBlur',
        rules: [
          {
            required: true,
            message: i18nFn('Please select'),
          },
        ],
      },
    ],
    editable: hasRuleEditAuth,
    fieldProps: {
      placeholder: i18nFn('Please select'),
      type: FieldType.Select,
      extraProps: {
        options: dueDateCompareOptions
          ?.filter(item => item.dictValue !== DueDateCompareEnum.AFTER)
          ?.map(reasonItem => ({
            value: Number(reasonItem.dictValue),
            label: reasonItem.dictValueName || reasonItem.itemName,
          })),
      },
    },
  },
  {
    title: i18nFn('Package'),
    dataIndex: 'packageIdList',
    key: 'packageIdList',
    width: 400,
    editable: true,
    ellipsis: true,
    formItemProps: {
      rules: [{ required: true, message: i18nFn('Please select') }],
    },
    fieldProps: {
      placeholder: i18nFn('Please select'),
      type: FieldType.Select,
      extraProps: {
        options: packages,
        mode: 'multiple',
      },
    },
    render: (text: string[]) => (
      <Tooltip
        placement="topLeft"
        title={handleAntdSelectRender(text, packages!)}
      >
        {handleAntdSelectRender(text, packages!)}
      </Tooltip>
    ),
  },
  {
    title: i18nFn('Value'),
    dataIndex: 'dueDateCompareValue',
    render: (text: string) => text || '',
    validate: [
      {
        trigger: 'onBlur',
        rules: [
          {
            pattern: /^\d+$/,
            required: true,
            message: i18nFn('Please input number'),
          },
        ],
      },
    ],
    editable: hasRuleEditAuth,
    fieldProps: {
      addonAfter: i18nFn('Day(s)'),
    },
  },
];
