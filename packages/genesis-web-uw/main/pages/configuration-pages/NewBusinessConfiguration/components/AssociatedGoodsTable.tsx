import React, { FC, useState } from 'react';

import { Alert, FormInstance } from 'antd';

import { cssVars } from '@zhongan/nagrand-ui';
import { EditableTable } from '@zhongan/nagrand-ui';

import { Proposal } from '@uw/interface/common.interface';
import { TypeEnums } from '@uw/interface/enum.interface';

interface Props {
  columns: [];
  tooltip?: string;
  tips?: string;
  scroll?: number;
  form?: FormInstance;
  type: TypeEnums;
  dataSource: (Proposal & { key: string })[];
  setDataSource: (args: Proposal[]) => void;
  getEditRow?: (record: Partial<Proposal>) => void;
  handleAdd?: () => void;
  hasProposalEditAuth: boolean;
}

export const AssociatedGoodsTable: FC<Props> = ({
  columns,
  tooltip,
  tips,
  scroll = 1300,
  getEditRow,
  dataSource = [],
  setDataSource,
  handleAdd,
  hasProposalEditAuth,
}) => {
  const [, setIsEditing] = useState(false);

  return (
    <>
      {(tooltip || tips) && dataSource?.length === 0 && (
        <Alert
          description={tooltip || tips}
          type="info"
          closable
          style={{ marginBottom: cssVars.gapMd }}
        />
      )}
      <EditableTable<Proposal>
        showPagination={false}
        columns={columns}
        dataSource={dataSource}
        disableEdit={!hasProposalEditAuth}
        disableAdd={!hasProposalEditAuth}
        setDataSource={setDataSource}
        scroll={scroll}
        setIsEditing={setIsEditing}
        getEditRow={getEditRow}
        handleAdd={handleAdd}
      />
    </>
  );
};
