import { FC, useCallback, useEffect, useMemo, useState } from 'react';

import Icon from '@ant-design/icons';
import { Checkbox, Col, Form, FormInstance, Row } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

import clsx from 'clsx';

import { Input } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { RandomCheckConfigType } from 'genesis-web-service';
import { useBizDict } from 'genesis-web-shared/lib/hook';

import { CollectiveSymbol } from '@uw/assets/new-icons';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

import styles from '../../NewBusinessConfiguration.module.scss';
import {
  extractPeriodFileds,
  oCRResultFields,
  randomRatioFields,
} from './config';

interface ExtractPolicyConditionType {
  yesNoEnum: BizDict[];
  hasRuleEditAuth: boolean;
  isAdd: boolean;
  form: FormInstance;
  randomCheckConfig: RandomCheckConfigType | undefined;
}

const ExtractPolicyCondition: FC<ExtractPolicyConditionType> = ({
  yesNoEnum,
  hasRuleEditAuth,
  isAdd,
  form,
  randomCheckConfig,
}) => {
  const [ocrResultRadioIsRequired, setOcrResultRadioIsRequired] =
    useState(true);
  const extractPolicyRuleEnum = useBizDict('extractPolicyRule');

  useEffect(() => {
    const flag = !!randomCheckConfig?.updateOcrResult;
    setOcrResultRadioIsRequired(flag);
  }, [randomCheckConfig]);

  const myOCRResultFields = useMemo(() => {
    if (isAdd) {
      return oCRResultFields(
        yesNoEnum,
        !hasRuleEditAuth,
        ocrResultRadioIsRequired
      );
    }
    return (
      randomCheckConfig &&
      oCRResultFields(yesNoEnum, !hasRuleEditAuth, ocrResultRadioIsRequired)
    );
  }, [
    hasRuleEditAuth,
    ocrResultRadioIsRequired,
    isAdd,
    randomCheckConfig,
    yesNoEnum,
  ]);

  const myExtractPeriodFileds = useMemo(
    () => extractPeriodFileds(extractPolicyRuleEnum, !hasRuleEditAuth),
    [hasRuleEditAuth, extractPolicyRuleEnum]
  );

  const myRandomRatioFields = useMemo(
    () => randomRatioFields(!hasRuleEditAuth),
    [hasRuleEditAuth]
  );

  const onChange = (checkedValues: CheckboxValueType[]) => {
    const flag = !!checkedValues.includes('updateOcrResult');
    setOcrResultRadioIsRequired(flag);

    if (!flag) {
      form.resetFields(['updateOcrResult']);
    }
  };

  const validateAftereDays = useCallback(async (rule, value) => {
    const { getFieldValue, setFieldsValue } = form;
    const daysAfter = typeof value === 'number' ? value.toString() : value;
    const beforeDays = getFieldValue('daysBefore');
    if (+beforeDays > +daysAfter) {
      setFieldsValue({
        daysAfter: '',
      });
      messagePopup(
        i18nFn('Extract period was entered error, please check!'),
        'error'
      );
      return Promise.reject();
    }
    return Promise.resolve();
  }, []);

  return (
    <Form.Item
      name={'updateOcrResultContent'}
      initialValue={['extractPeriod', 'randomRatio']}
    >
      <Checkbox.Group onChange={onChange}>
        <Checkbox
          value={'extractPeriod'}
          className={styles.checkBoxLine}
          disabled={true}
        >
          <div className={styles.checkBoxTitleText}>
            {i18nFn('Extract Period')}
          </div>
          <div className={clsx(styles.checkBoxItem, styles.checkBoxNoRadius)}>
            <Col span={4}>
              {myExtractPeriodFileds?.map(field => (
                <Form.Item name={field.key} rules={field.rules}>
                  {getFields({
                    type: field.type,
                    placeholder: field.placeholder,
                    options: field.options,
                    disabled: field.disabled,
                  })}
                </Form.Item>
              ))}
            </Col>
            <Icon component={CollectiveSymbol} className={styles.symbolIcon} />
            <Col span={20}>
              <Row>
                <span className={clsx(styles.symbolText, styles.leftStyle)}>
                  (
                </span>
                <Col span={11}>
                  <Form.Item>
                    <Input.Group compact>
                      <Form.Item className={styles.radiusOne}>
                        <Input
                          defaultValue={i18nFn('Current system date')}
                          disabled={true}
                        />
                      </Form.Item>
                      <Form.Item
                        className={styles.radiusTwo}
                        name={'daysBefore'}
                        rules={[
                          {
                            required: true,
                            message: i18nFn('Please input'),
                          },
                          {
                            pattern: new RegExp(
                              /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
                            ),
                            message: i18nFn(
                              'Please enter a number greater than 0'
                            ),
                          },
                        ]}
                      >
                        <Input
                          addonBefore={'-'}
                          addonAfter={i18nFn('DayS')}
                          type="text"
                          disabled={!hasRuleEditAuth}
                          placeholder={i18nFn('Input')}
                        />
                      </Form.Item>
                    </Input.Group>
                  </Form.Item>
                </Col>
                <span className={styles.spanText}>-</span>
                <Col span={11}>
                  <Form.Item>
                    <Input.Group compact>
                      <Form.Item className={styles.radiusOne}>
                        <Input
                          defaultValue={i18nFn('Current system date')}
                          disabled={true}
                        />
                      </Form.Item>
                      <Form.Item
                        className={styles.radiusTwo}
                        name={'daysAfter'}
                        rules={[
                          {
                            validator: validateAftereDays,
                            validateTrigger: ['onBlur'],
                          },
                          {
                            required: true,
                            message: i18nFn('Please input'),
                          },
                          {
                            pattern: new RegExp(
                              /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
                            ),
                            message: i18nFn(
                              'Please enter a number greater than 0'
                            ),
                          },
                        ]}
                      >
                        <Input
                          addonBefore={'-'}
                          addonAfter={i18nFn('DayS')}
                          type="text"
                          disabled={!hasRuleEditAuth}
                          placeholder={i18nFn('Input')}
                        />
                      </Form.Item>
                    </Input.Group>
                  </Form.Item>
                </Col>
                <span className={clsx(styles.symbolText, styles.rightStyle)}>
                  )
                </span>
              </Row>
            </Col>
          </div>
        </Checkbox>
        <Checkbox
          value={'randomRatio'}
          className={styles.checkBoxLine}
          disabled={true}
          style={{ width: '100%' }}
        >
          <div className={styles.checkBoxTitleText}>
            {i18nFn('Random Ratio')}
          </div>
          <div className={styles.checkBoxItem}>
            <Col span={24}>
              {myRandomRatioFields?.map(field => (
                <Form.Item name={field.key} rules={field.rules}>
                  {getFields({
                    type: field.type,
                    placeholder: field.placeholder,
                    options: field.options,
                    disabled: field.disabled,
                    style: field.style,
                    addOnAfter: field.addOnAfter,
                  })}
                </Form.Item>
              ))}
            </Col>
          </div>
        </Checkbox>
        <Checkbox
          value={'updateOcrResult'}
          className={styles.checkBoxLine}
          disabled={!hasRuleEditAuth}
        >
          <div className={styles.checkBoxItemNoMargin}>
            <div className={styles.checkBoxItemTitle}>
              {i18nFn('Update OCR Result')}
            </div>
            {myOCRResultFields?.map(field => (
              <Form.Item
                name={field.key}
                initialValue={field.defaultValue}
                className={styles.radioItemContent}
                rules={field.rules}
              >
                {getFields({
                  type: field.type,
                  placeholder: field.placeholder,
                  options: field.options,
                  disabled: field.disabled,
                  defaultValue: field.defaultValue,
                })}
              </Form.Item>
            ))}
          </div>
        </Checkbox>
      </Checkbox.Group>
    </Form.Item>
  );
};

export default ExtractPolicyCondition;
