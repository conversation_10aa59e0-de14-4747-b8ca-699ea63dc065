import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import { BizDictItem } from 'genesis-web-service';

import { ExtractPolicyRuleEnum } from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { i18nFn } from '@uw/util/i18nFn';

export const oCRResultFields = (
  yesNoEnum: BizDict[] = [],
  hasEditAuth: boolean,
  ocrResultRadioIsRequired: boolean
): FieldDataType[] => [
  {
    dataIndex: 'updateOcrResult',
    key: 'updateOcrResult',
    type: FieldType.Radio,
    className: 'radio-item-content',
    placeholder: i18nFn('Please select'),
    rules: [
      ...(ocrResultRadioIsRequired
        ? [
            {
              required: true,
              message: i18nFn('Please input'),
            },
          ]
        : []),
    ],
    options: yesNoEnum,
    disabled: hasEditAuth || !ocrResultRadioIsRequired,
  },
];
export const randomRatioFields = (hasEditAuth: boolean): FieldDataType[] => [
  {
    dataIndex: 'ratio',
    key: 'ratio',
    type: FieldType.Input,
    placeholder: i18nFn('Please input'),
    disabled: hasEditAuth,
    addOnAfter: '%',
    style: { width: '300px' },
    rules: [
      {
        required: true,
        message: i18nFn('Please input'),
      },
      {
        pattern: new RegExp(/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/),
        message: i18nFn('Please enter a number greater than 0'),
      },
    ],
  },
];
export const extractPeriodFileds = (
  extractPolicyRuleEnum: BizDictItem[] = [],
  hasEditAuth: boolean
): FieldDataType[] => [
  {
    dataIndex: 'extractPolicyRule',
    key: 'extractPolicyRule',
    type: FieldType.Select,
    placeholder: i18nFn('Please select'),
    options: extractPolicyRuleEnum,
    defaultValue: ExtractPolicyRuleEnum.POLICY_EFFECTIVE_DATE,
    disabled: hasEditAuth,
    rules: [
      {
        required: true,
        message: i18nFn('Please select'),
      },
    ],
  },
];
