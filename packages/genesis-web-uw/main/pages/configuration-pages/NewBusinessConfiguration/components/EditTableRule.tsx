import React, { FC, useCallback, useEffect, useState } from 'react';

import { Alert } from 'antd';

import { ColumnEditingType, Pagination, cssVars } from '@zhongan/nagrand-ui';

import { PolicyService, SaveRuleRequestType } from 'genesis-web-service';

import { TypeEnums } from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';

import FieldEditTable from '../../../../components/EditDrawer/FieldEditTable';
import { handleDelete, handleSave } from '../services/nbConfiguration.service';

interface Props {
  tableColumns: ColumnEditingType<SaveRuleRequestType>[];
  type: TypeEnums;
  optInRulesfields: FieldDataType[];
  tooltip?: string;
  hasRuleEditAuth: boolean;
}

export const EditTableRule: FC<Props> = ({
  tableColumns,
  type,
  optInRulesfields,
  tooltip,
  hasRuleEditAuth,
}) => {
  const [, setIsEditing] = useState(false);
  const [dataSource, setDataSource] = useState<SaveRuleRequestType[]>();
  const [total, setTotal] = useState(0);
  const [, setShowDrawer] = useState(false);
  const [current, setCurrent] = useState(1);
  const [showPage] = useState(false);
  const getDataSource = useCallback(
    (page, pageSize) => {
      PolicyService.queryRuleConfigByType({
        pageSize,
        pageIndex: page,
        typeEnum: type,
      }).then(res => {
        setDataSource(
          res.results?.map((result, idx) => ({
            ...result,
            key: `${result.typeEnum}${result.rule}${idx}`,
          }))
        );
        setTotal(res.total);
      });
    },
    [type]
  );

  useEffect(() => {
    getDataSource(1, 10);
  }, []);

  const onChange = useCallback<(page: number, pageSize?: number) => void>(
    (page, pageSize) => {
      setCurrent(page);
      getDataSource(page, pageSize);
    },
    []
  );

  const handleDel = useCallback<(index: number) => void>(
    async index => {
      const deleteFlag = await handleDelete(
        index,
        dataSource as (SaveRuleRequestType & {
          key: string;
        })[]
      );
      if (deleteFlag) {
        getDataSource(1, 10);
      }
    },
    [dataSource]
  );
  const handleSaveData = useCallback<(row: SaveRuleRequestType) => void>(
    async row => {
      const saveFlag = await handleSave(row, type);
      setShowDrawer(false);
      if (saveFlag) {
        getDataSource(1, 10);
        return true;
      }
      return false;
    },
    [dataSource, type]
  );

  return (
    <>
      {tooltip && dataSource?.length === 0 && (
        <Alert
          description={tooltip}
          type="info"
          closable
          style={{ marginBottom: cssVars.gapMd }}
        />
      )}
      <FieldEditTable<SaveRuleRequestType>
        tableColumns={tableColumns}
        data={dataSource as (SaveRuleRequestType & { key: string })[]}
        setData={setDataSource}
        setIsEditing={setIsEditing}
        tableFields={optInRulesfields}
        handleSaveData={handleSaveData}
        handleDel={handleDel}
        showPage={showPage}
        disableAdd={!hasRuleEditAuth}
        disableEdit={!hasRuleEditAuth}
      />
      <Pagination
        className="mt-[18px]"
        showQuickJumper
        current={current}
        total={total}
        onChange={onChange}
      />
    </>
  );
};
