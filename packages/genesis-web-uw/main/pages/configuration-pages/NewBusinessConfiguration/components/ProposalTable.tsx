import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Col, Form, Row, Table } from 'antd';
import { FormInstance } from 'antd/es/form/Form';

import { v4 as uuid } from 'uuid';

import { Input } from '@zhongan/nagrand-ui';

import { MoreAction } from '@uw/components/MoreAction';
import { useBizDict } from '@uw/hook/useBizDict';
import {
  BizStatusEnum,
  PendingProposalCheckBizStatusEnum,
} from '@uw/interface/enum.interface';

import styles from '../NewBusinessConfiguration.module.scss';
import { APPLY_TO_PARTIAL_PAID_PROPOSAL } from '../config';
import { useProposalSeparatedConfigContext } from '../context';

interface Props {
  form: FormInstance;
  hasProposalEditAuth: boolean;
}

interface OriginData {
  name?: string;
  bizStatus?: string | number;
  id: string;
}

export const ProposalTable: React.FC<Props> = ({
  form,
  hasProposalEditAuth,
}: Props) => {
  const [t] = useTranslation(['uw', 'common']);
  const bizStatusEnums = useBizDict('bizStatus');
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const { separatedConfig } = useProposalSeparatedConfigContext();

  const originData = useMemo(
    () =>
      bizStatusEnums
        ?.filter(item =>
          Object.keys(BizStatusEnum)
            .filter(
              value =>
                value !== BizStatusEnum.ACCU_WAITING_FOR_DATA_ENTRY &&
                value !== BizStatusEnum.WAITING_FOR_DATA_ENTRY
            )
            .includes(item.enumItemName.toString())
        )
        ?.map(item => ({
          name: item.dictValueName,
          bizStatus: item.enumItemName,
          id: '',
          key: item.enumItemName,
        })) ?? [],
    [bizStatusEnums]
  );

  const [dataSource, setDataSource] = useState<OriginData[]>(originData);

  const pendingProposalCheckData = useMemo(
    () =>
      bizStatusEnums
        ?.filter(item =>
          Object.keys(PendingProposalCheckBizStatusEnum).includes(
            item.enumItemName.toString()
          )
        )
        ?.map(item => ({
          name: item.dictValueName,
          bizStatus: item.enumItemName,
          id: '',
        })) ?? [],
    [bizStatusEnums]
  );

  useEffect(() => {
    separatedConfig?.forEach(status => {
      const fieldText = [...originData, ...pendingProposalCheckData]?.find(
        item => status.bizStatus === item.bizStatus
      );
      if (fieldText) {
        form.setFieldsValue({
          [fieldText.bizStatus]: status.days,
        });
      }
    });
  }, [separatedConfig, originData, form, pendingProposalCheckData]);

  useEffect(() => {
    setExpandedRowKeys([BizStatusEnum.PENDING_PROPOSAL_CHECK]);
  }, []);

  const onAction = useCallback(() => {
    setExpandedRowKeys(prev =>
      prev?.length ? [] : [BizStatusEnum.PENDING_PROPOSAL_CHECK]
    );
  }, []);

  const columns = [
    {
      title: t('Proposal Status'),
      dataIndex: 'name',
      key: 'name',
      editable: true,
      width: 100,
    },
    {
      title: t('Waiting Days to Withdraw'),
      key: 'bizStatus',
      editable: true,
      width: 200,
      render: (record: Record<string, unknown>) => (
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={record.bizStatus}
              rules={[
                {
                  pattern: new RegExp(/^[1-9]\d*$/, 'g'),
                  message: t('Please enter a number greater than 0'),
                },
              ]}
              validateTrigger={['onChagne', 'onBlur']}
            >
              <Input
                placeholder={t('Please input')}
                addonAfter={t('Day(s)')}
                disabled={!hasProposalEditAuth}
              />
            </Form.Item>
          </Col>
          {
            // https://jira.zaouter.com/browse/GIS-70605
            // 在当前的proposal withdraw rule中的waiting for issuance下增加Apply to partial paid proposal标签，默认勾选。
            record?.bizStatus === BizStatusEnum.WAITING_FOR_ISSUANCE && (
              <Col span={12}>
                <Form.Item
                  name={`${record?.bizStatus}${APPLY_TO_PARTIAL_PAID_PROPOSAL}`}
                  validateTrigger={['onChagne', 'onBlur']}
                  valuePropName="checked"
                >
                  <Checkbox disabled={!hasProposalEditAuth}>
                    {t('Apply to partial paid proposal?')}
                  </Checkbox>
                </Form.Item>
              </Col>
            )
          }
        </Row>
      ),
    },
    {
      title: t('Actions'),
      key: 'action',
      width: 30,
      render: (record: OriginData) => (
        <MoreAction
          menuList={[
            {
              key: uuid(),
              label: t(
                `${
                  expandedRowKeys?.length
                    ? "Don't Use Sub-item"
                    : 'Use Sub-item'
                }`
              ),
              onClick: () => onAction(),
            },
          ]}
          direction="horizontal"
          disabled={record.bizStatus !== BizStatusEnum.PENDING_PROPOSAL_CHECK}
        />
      ),
    },
  ];

  const pendingProposalCheckSubTableColumns = useMemo(
    () => [
      {
        title: t('Sub-items'),
        dataIndex: 'name',
        key: 'subName',
        editable: true,
        width: 100,
      },
      {
        title: t('Days'),
        key: 'bizSubStatus',
        editable: true,
        width: 100,
        render: (record: Record<string, string>) => (
          <Row>
            <Col span={13}>
              <Form.Item
                name={record.bizStatus}
                rules={[
                  {
                    pattern: new RegExp(/^[1-9]\d*$/, 'g'),
                    message: t('Please enter a number greater than 0'),
                  },
                ]}
                validateTrigger={['onChagne', 'onBlur']}
              >
                <Input
                  placeholder={t('Please input')}
                  addonAfter={t('Day(s)')}
                  disabled={!hasProposalEditAuth}
                />
              </Form.Item>
            </Col>
          </Row>
        ),
      },
    ],
    [hasProposalEditAuth]
  );

  const pendingProposalCheckSubTable = useMemo(
    () => (
      <Table
        style={{ margin: styles.gapXs }}
        columns={pendingProposalCheckSubTableColumns}
        dataSource={pendingProposalCheckData}
        pagination={false}
      />
    ),
    [pendingProposalCheckSubTableColumns, pendingProposalCheckData]
  );

  useEffect(() => {
    setDataSource(originData);
  }, [originData]);

  return (
    <>
      <Form form={form} className={styles.proposalContent}>
        <Table
          className={styles.proposalStatusTable}
          style={{ marginTop: styles.gapXs }}
          columns={columns}
          dataSource={dataSource}
          expandable={{
            fixed: 'right',
            expandedRowKeys,
            expandIcon: () => <span className={styles.hiddenDom} />,
            expandedRowRender: () => pendingProposalCheckSubTable,
          }}
          scroll={{ x: '100%' }}
        />
      </Form>
    </>
  );
};
