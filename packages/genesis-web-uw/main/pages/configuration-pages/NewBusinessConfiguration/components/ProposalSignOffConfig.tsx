import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, Row } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import styles from '../NewBusinessConfiguration.module.scss';

interface Props {
  id: string;
  form: FormInstance;
  hasProposalEditAuth: boolean;
}
export const ProposalSignOffConfig: FC<Props> = ({
  id,
  form,
  hasProposalEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    <div id={id} key={id}>
      <section className={styles.ruleSubTitle}>
        {t('Policy Sign Off Rule')}
      </section>
      <div>
        {t(
          'System will trigger automatically confirm the policy sign off X days after policy issue date.'
        )}
      </div>
      <section className={styles.inputFormContent}>
        <Form form={form}>
          <Row>
            <Form.Item label={''} name={'signOffConfig'} key={'signOffConfig'}>
              <Input
                placeholder={t('Please input')}
                addonAfter={t('Days')}
                disabled={!hasProposalEditAuth}
              />
            </Form.Item>
          </Row>
        </Form>
      </section>
    </div>
  );
};
