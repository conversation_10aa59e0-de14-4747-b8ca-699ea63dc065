import React, { FC } from 'react';
import { Divider } from 'antd';
import { usePermission } from '@uw/hook/permission';
import { usePackages, useRules, useWorkflowMap } from '@uw/hook/request';
import { ProposalConfigAnchorIdsEnums } from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import { OptInRule } from './OptInRule';
import { PolicyIssuanceRule } from './PolicyIssuanceRule';
import { ProposalRule } from './ProposalRule';
import { RuleConfigurationContext } from './context';

interface Props {
  proposalRef: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
}

export const RuleConfiguration: FC<Props> = ({ proposalRef }) => {
  const packages = usePackages();
  const rules = useRules();
  const selectedWorkflows = useWorkflowMap();
  const hasRuleEditAuth = !!usePermission('nb.configuration.proposal.edit');

  return (
    <>
      <RuleConfigurationContext.Provider
        value={{ selectedWorkflows: selectedWorkflows ?? [] }}
      >
        <div className={styles['rule-wrapper']}>
          <section id="ruleContent">
            <div>
              <ProposalRule
                rules={rules}
                packages={packages}
                hasRuleEditAuth={hasRuleEditAuth}
                proposalRef={proposalRef}
              />
              <Divider className={styles.divider} dashed />
              <PolicyIssuanceRule
                key={ProposalConfigAnchorIdsEnums.policyIssuanceRules}
                id={ProposalConfigAnchorIdsEnums.policyIssuanceRules}
                rules={rules}
                packages={packages}
                hasRuleEditAuth={hasRuleEditAuth}
              />
              <Divider className={styles.divider} dashed />
              <OptInRule
                key={ProposalConfigAnchorIdsEnums.optInRules}
                id={ProposalConfigAnchorIdsEnums.optInRules}
                rules={rules}
                packages={packages}
                hasRuleEditAuth={hasRuleEditAuth}
              />
            </div>
          </section>
        </div>
      </RuleConfigurationContext.Provider>
    </>
  );
};
export default RuleConfiguration;
