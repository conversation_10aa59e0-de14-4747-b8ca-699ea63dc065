import React, { FC } from 'react';
import { FormInstance } from 'antd';
import { useTranslation } from 'react-i18next';
import { TypeEnums } from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import { AssociatedGoods } from './components/AssociatedGoods';
import { ProposalTable } from './components/ProposalTable';

interface Props {
  form: FormInstance;
  hasProposalEditAuth: boolean;
  proposalWithdrawRef?: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
}
export const ProposalStatus: FC<Props> = ({
  form,
  hasProposalEditAuth,
  proposalWithdrawRef,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    <>
      <div className={styles.proposalTitle}>
        {t('Seperate by Proposal Status')}
      </div>
      <ProposalTable form={form} hasProposalEditAuth={hasProposalEditAuth} />
      <AssociatedGoods
        type={TypeEnums.SEPERATE_BY_PROPOSAL_STATUS}
        form={form}
        id={TypeEnums.SEPERATE_BY_PROPOSAL_STATUS}
        ref={proposalWithdrawRef}
        hasProposalEditAuth={hasProposalEditAuth}
      />
    </>
  );
};
