import { PolicyService, SaveRuleRequestType } from 'genesis-web-service';
import { TypeEnums } from '@uw/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';

export const handleSave = async (
  data: SaveRuleRequestType & { key?: string },
  type: TypeEnums
) => {
  try {
    const params = { ...data };
    delete params.key;
    const saveRuleResults = await PolicyService.saveRule({
      ...params,
      typeEnum: type,
    });
    return saveRuleResults.success;
  } catch (error) {
    messagePopup((error as unknown).toString(), 'error');
  }
};

export const handleDelete = async (
  index: number,
  data: (SaveRuleRequestType & { key?: string })[]
) => {
  try {
    const deleteRule = data[index];
    const deleteRuleResults = await PolicyService.deleteRule(
      deleteRule.typeEnum,
      deleteRule.rule
    );
    return deleteRuleResults.success;
  } catch (error) {
    messagePopup((error as unknown).toString(), 'error');
  }
};
