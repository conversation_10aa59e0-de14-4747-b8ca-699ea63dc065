import React, { FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { usePermission } from '@uw/hook/permission';
import { Form } from 'antd';

import { getFields } from '@uw/util/getFieldsQueryForm';

import { renderFields } from '@uw/util/renderFields';

import { RandomCheckConfigType } from 'genesis-web-service';

import { useBizDict } from '@uw/hook/useBizDict';

import { FootBar } from '../../../components/FootBar/FootBar';
import styles from './NewBusinessConfiguration.module.scss';
import { useRandomCheckConfig, useSubmitRandomCheckConfig } from './request';
import { doubleCheckConfigurationFields } from './config';
import { extractPolicyConditionFields } from './pageConfig';

const transNumber = (val?: number) => {
  const transValue = typeof val === 'number' ? val.toString() : val;
  if (!transValue) return;
  if (+transValue < 0) {
    return (+transValue * -1).toString();
  }
  return transValue;
};
const RandomCheckConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const { randomCheckConfig, getRandomCheckConfig, isAdd } =
    useRandomCheckConfig();
  const { submitRandomCheckConfig, isRefreshPage } =
    useSubmitRandomCheckConfig();
  const yesNoEnum = useBizDict('yesNo');

  const hasRuleEditAuth = !!usePermission('nb.configuration.proposal.edit');
  const doubleCheckfields = useMemo(() => {
    if (isAdd) {
      return doubleCheckConfigurationFields(
        yesNoEnum,
        !hasRuleEditAuth,
        randomCheckConfig
      );
    }
    return (
      randomCheckConfig &&
      doubleCheckConfigurationFields(
        yesNoEnum,
        !hasRuleEditAuth,
        randomCheckConfig
      )
    );
  }, [randomCheckConfig, hasRuleEditAuth, isAdd, yesNoEnum]);

  useEffect(() => {
    if (isRefreshPage) {
      getRandomCheckConfig();
    }
  }, [isRefreshPage]);

  useEffect(() => {
    const defaultValue = {
      ...randomCheckConfig,
      daysBefore: transNumber(randomCheckConfig?.daysBefore),
      daysAfter: transNumber(randomCheckConfig?.daysAfter),
    };
    if (randomCheckConfig?.updateOcrResult) {
      form.setFieldsValue({
        ...defaultValue,
        updateOcrResultContent: [
          'extractPeriod',
          'randomRatio',
          'updateOcrResult',
        ],
      });
    } else {
      form.setFieldsValue({
        ...defaultValue,
        updateOcrResultContent: ['extractPeriod', 'randomRatio'],
      });
    }
  }, [randomCheckConfig]);

  const onCancel = () => {
    form.resetFields();
  };
  const onSubmit = async () => {
    try {
      const formValue = await form.validateFields();
      let daysBefore;
      let daysAfter;
      if (formValue.daysBefore) {
        daysBefore = +formValue.daysBefore * -1;
      }
      if (formValue.daysAfter) {
        daysAfter = +formValue.daysAfter * -1;
      }
      const params = {
        daysBefore,
        daysAfter,
        doubleCheckFlag: formValue.doubleCheckFlag,
        extractPolicyRule: formValue.extractPolicyRule,
        ratio: formValue.ratio,
      };

      let copyParams = { ...params } as RandomCheckConfigType;
      if (formValue.updateOcrResult) {
        copyParams = {
          ...copyParams,
          updateOcrResult: formValue.updateOcrResult,
        };
      }
      submitRandomCheckConfig(copyParams);
    } catch (error) {
      // Do nothing, just hold error
    }
  };

  return (
    <>
      <div className={styles.randomCheckWrapper}>
        <section className={styles.title}>{t('Process Configuration')}</section>
        <section className={styles.ruleContentWrapper}>
          <Form form={form}>
            <div className={styles.randomCheckTitleContent}>
              <span className={styles.ruleSubTitle}>
                {t('Whether need to double check for rejected case')}
              </span>
              {doubleCheckfields?.map(field => (
                <Form.Item
                  name={field.key}
                  className={styles.radioItemContent}
                  initialValue={field.defaultValue}
                >
                  {getFields({
                    type: field.type,
                    placeholder: field.placeholder,
                    options: field.options,
                    disabled: field.disabled,
                    defaultValue: field.defaultValue,
                  })}
                </Form.Item>
              ))}
            </div>
            <section className={styles.ruleSubTitle}>
              <span>{t('Extract Policy Condition')}</span>
            </section>
            {renderFields(
              extractPolicyConditionFields(
                yesNoEnum,
                hasRuleEditAuth,
                isAdd,
                form,
                randomCheckConfig
              )
            )}
          </Form>
        </section>
      </div>
      <FootBar
        onSubmit={onSubmit}
        onCancel={onCancel}
        hasEditAuth={hasRuleEditAuth}
      />
    </>
  );
};

export default RandomCheckConfiguration;
