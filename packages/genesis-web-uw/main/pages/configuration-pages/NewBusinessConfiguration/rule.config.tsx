import { Dispatch, SetStateAction } from 'react';

import { But<PERSON> } from 'antd';
import type { FormInstance } from 'antd';

import clsx from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  AttachmentConfigList,
  AttachmentsType,
  GoodsListType,
  SaveRuleRequestType,
} from 'genesis-web-service';

import { dictMap } from '@uw/hook/useBizDict';
import I18nInstance from '@uw/i18n';
import { GoodsObj, Proposal } from '@uw/interface/common.interface';
import {
  CategoryType,
  ProposalConfigAnchorIdsEnums,
  SelectProps,
  TypeEnums,
} from '@uw/interface/enum.interface';
import {
  FieldDataType,
  NbConfigurationFields,
} from '@uw/interface/field.interface';
import { handleRenderDictValue } from '@uw/util/handleRenderDictValue';
import { i18nFn } from '@uw/util/i18nFn';

import styles from './NewBusinessConfiguration.module.scss';

const ns = {
  ns: ['uw', 'common'],
};

export const handleRuleName = (text: string, options: BizDict[]) =>
  options?.find(option => text === option.enumItemName)?.itemName ||
  I18nInstance.t('--', ns);

export const newHandleRuleName = (text: string, options: SelectProps[]) =>
  options?.find(option => text === option.value)?.label ||
  I18nInstance.t('--', ns);

export const handleRuleRenderMultipleName = (
  text: string[],
  options: BizDict[],
  valProp?: string
) => {
  const selectObjArr = options?.filter(option =>
    text?.includes(valProp ? option[valProp] : (option?.enumItemName as string))
  );
  return text?.length > 0
    ? selectObjArr?.reduce(
        (cur, next) => `${cur}${cur.length > 0 ? ';' : ''}${next?.itemName}`,
        ''
      )
    : I18nInstance.t('--');
};

export const handleAntdSelectRender = (
  text: string[],
  options: { label: string; value: string | number | undefined }[]
) => {
  const selectObjArr = options?.filter(option =>
    text?.includes(option?.value as string)
  );
  return text?.length > 0
    ? selectObjArr?.reduce(
        (cur, next) => `${cur}${cur.length > 0 ? ';' : ''}${next?.label}`,
        ''
      )
    : I18nInstance.t('--');
};

export const handleAssociatedMultipleName = (
  text: number[],
  options: GoodsObj
) => {
  const goodsItemList = options && text?.map(item => options?.[item] ?? '');
  return text?.length > 0
    ? goodsItemList?.reduce(
        (cur, next) => `${cur}${cur.length > 0 ? ';' : ''}${next?.itemName}`,
        ''
      )
    : I18nInstance.t('--');
};

export const proposalRuleConfig = (
  policyScenarioEnums: BizDict[] = [],
  businessTypeEnums: BizDict[],
  businessTypeEnumMap: Record<string, string>,
  rules: BizDict[] = [],
  packages: BizDict[] = []
): (NbConfigurationFields<SaveRuleRequestType> & { id?: string })[] => {
  const policyScenarioOptions = policyScenarioEnums.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const businessTypeOptions = businessTypeEnums.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const ruleOptions = rules.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const packageOptions = packages.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  return [
    {
      title: '',
      inlineEdit: true,
      typeEnum: TypeEnums.PROPOSAL_VERIFICATION,
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item => item?.code === CategoryType.NB_OPERATION
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item => item?.code === CategoryType.NB_OPERATION
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
    {
      title: I18nInstance.t('Underwriting Check', ns),
      id: ProposalConfigAnchorIdsEnums.underwritingCheck,
      inlineEdit: true,
      typeEnum: TypeEnums.PROPOSAL_UNDERWRITING,
      tooltip: I18nInstance.t('Underwriting Check Text', ns),
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item => item?.code === CategoryType.UNDERWRITING
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item => item?.code === CategoryType.UNDERWRITING
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
    {
      title: I18nInstance.t(
        'Proposal Compliance Check （Before Premium Payment)',
        ns
      ),
      id: ProposalConfigAnchorIdsEnums.complianceCheck,
      tooltip: I18nInstance.t(
        'System will trigger the proposal flow based on the "Compliance Decision"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.',
        ns
      ),
      inlineEdit: true,
      typeEnum: TypeEnums.PROPOSAL_COMPLIANCE,
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item => item?.code === CategoryType.COMPLIANCE
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item => item?.code === CategoryType.COMPLIANCE
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
  ];
};

export const policyIssuanceConfig = (
  policyScenarioEnums: BizDict[] = [],
  businessTypeEnums: BizDict[],
  businessTypeEnumMap: Record<string, string>,
  rules: BizDict[] = [],
  packages: BizDict[] = []
): (NbConfigurationFields<SaveRuleRequestType> & { id?: string })[] => {
  const policyScenarioOptions = policyScenarioEnums.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const businessTypeOptions = businessTypeEnums.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const ruleOptions = rules.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });

  const packageOptions = packages.map(option => {
    return {
      ...option,
      value: option?.enumItemName ?? option?.dictValue,
      label: option?.itemName,
    };
  });
  return [
    {
      title: I18nInstance.t('Policy Effective Check', ns),
      inlineEdit: true,
      tooltip: I18nInstance.t('policyEffectiveRuleTooltip', ns),
      typeEnum: TypeEnums.POLICY_EFFECTIVE,
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item =>
                item?.code === CategoryType.NB_OPERATION ||
                item?.code === CategoryType.UNDERWRITING ||
                item?.code === CategoryType.COMPLIANCE
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item =>
                  item?.code === CategoryType.NB_OPERATION ||
                  item?.code === CategoryType.UNDERWRITING ||
                  item?.code === CategoryType.COMPLIANCE
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
    {
      title: I18nInstance.t(
        'Policy Issuance Compliance Check （After Premium Payment)',
        ns
      ),
      inlineEdit: true,
      typeEnum: TypeEnums.POLICY_ISSUANCE_COMPLIANCE,
      tooltip: I18nInstance.t(
        'System will trigger the proposal flow based on the "Compliance Decision"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.',
        ns
      ),
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item => item?.code === CategoryType.COMPLIANCE
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item => item?.code === CategoryType.COMPLIANCE
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
    {
      title: I18nInstance.t(
        'Policy Issuance UW Check (After Premium Payment)',
        ns
      ),
      inlineEdit: true,
      typeEnum: TypeEnums.POLICY_ISSUANCE_UNDERWRITING,
      tooltip: I18nInstance.t(
        "System will trigger the proposal flow based on the 'Underwriting Decision' of the rule configuration here. If declined, system will reject the proposal. And if manual, system will trigger manual underwriting check for this proposal.",
        ns
      ),
      columns: [
        {
          title: I18nInstance.t('Rule\\Rule Set', ns),
          dataIndex: 'rule',
          key: 'rule',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: rules?.filter(
              item => item?.code === CategoryType.UNDERWRITING
            ),
            extraProps: {
              options: ruleOptions?.filter(
                item => item?.code === CategoryType.UNDERWRITING
              ),
            },
          },
          render: text => handleRuleName(text, rules),
        },
        {
          title: I18nInstance.t('Package', ns),
          dataIndex: 'packageList',
          key: 'packageList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: packages,
            mode: 'multiple',
            extraProps: {
              options: packageOptions,
              mode: 'multiple',
            },
          },
          render: text => handleRuleRenderMultipleName(text, packages),
        },
        {
          title: I18nInstance.t('Policy Type', ns),
          dataIndex: 'policyTypeEnum',
          key: 'policyTypeEnum',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: policyScenarioEnums,
            mode: 'multiple',
            extraProps: {
              options: policyScenarioOptions,
              mode: 'multiple',
            },
          },
          render: text =>
            handleRuleRenderMultipleName(text, policyScenarioEnums),
        },
        {
          title: I18nInstance.t('Business Type'),
          dataIndex: 'businessTypeList',
          key: 'businessTypeList',
          editable: true,
          width: 200,
          fieldProps: {
            placeholder: I18nInstance.t('Please select'),
            type: FieldType.Select,
            options: businessTypeEnums,
            mode: 'multiple',
            extraProps: {
              options: businessTypeOptions,
              mode: 'multiple',
            },
          },
          render: (text: string[]) =>
            handleRenderDictValue(text, businessTypeEnumMap),
        },
      ],
    },
  ];
};

export const AssociatedGoodsConfig = (
  channelList: SelectProps[] = [],
  goodsList: BizDict[] = [],
  allGoodsObj: GoodsObj = {},
  getGoodsList: (code: string) => void
): NbConfigurationFields<Proposal>[] => [
  {
    title: I18nInstance.t('Associated goods', ns),
    inlineEdit: true,
    columns: [
      {
        title: I18nInstance.t('Channel', ns),
        dataIndex: 'channelCode',
        key: 'channelCode',
        editable: true,
        width: 200,
        fieldProps: {
          placeholder: I18nInstance.t('Please select'),
          type: FieldType.Select,
          extraProps: {
            options: channelList,
            onChange: (code: string) => {
              getGoodsList(code);
            },
          },
        },
        render: text => newHandleRuleName(text, channelList),
      },
      {
        title: I18nInstance.t('Goods', ns),
        dataIndex: 'goodsCodes',
        key: 'goodsCodes',
        editable: true,
        width: 200,
        fieldProps: {
          placeholder: I18nInstance.t('Please select'),
          type: FieldType.Select,
          extraProps: {
            options: goodsList,
            mode: 'multiple',
            required: false,
          },
        },
        render: text => handleAssociatedMultipleName(text, allGoodsObj),
      },
    ],
  },
];

export const goodsCategoryFields = (
  productCategoryEnum: BizDict[] = [],
  setGoodsNum: Dispatch<SetStateAction<number>>,
  setCategoryId: Dispatch<SetStateAction<number | undefined>>,
  disabled: boolean,
  form: FormInstance
): FieldDataType => ({
  label: i18nFn(''),
  key: 'goodsCategoryId',
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: i18nFn('Please select'),
    },
  ],
  onChange: (text: number) => {
    setCategoryId(text);
    setGoodsNum(0);
    form.setFieldsValue({
      goods: undefined,
      channels: undefined,
    });
  },
  options: productCategoryEnum,
  disabled,
});

export const goodsCodeFields = (
  goodsCodeList: BizDict[] = [],
  setGoodsCode: Dispatch<SetStateAction<string | undefined>>,
  disabled: boolean,
  form: FormInstance
): FieldDataType => ({
  label: '',
  key: 'goodsId',
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: i18nFn('Please select'),
    },
  ],
  options: goodsCodeList,
  onChange: (text: string) => {
    setGoodsCode(text);
    const values: AttachmentConfigList & { key: string } =
      form.getFieldsValue();
    const newGoodsData = values.goods.map(item => {
      if (item?.goodsCode === text) {
        return { ...item, packages: undefined };
      }
      return item;
    });
    form.setFieldsValue({ goods: newGoodsData, channels: undefined });
  },
  disabled,
});

export const packageFields = (
  packageList: BizDict[] = [],
  disabled: boolean,
  form: FormInstance,
  goodsCode: string,
  isSelectAll: boolean
): FieldDataType => ({
  label: '',
  key: 'packages',
  placeholder: i18nFn('Package'),
  type: FieldType.MultiSelect,
  allowClear: true,
  col: 16,
  required: true,
  diabled: true,
  mode: 'multiple',
  rules: [
    {
      required: true,
      message: i18nFn('Package'),
    },
  ],
  onChange: (packages: number[]) => {
    const values: AttachmentConfigList & { key: string } =
      form.getFieldsValue();
    const newGoodsData = values.goods.map(item => {
      if (item?.goodsCode === goodsCode) {
        return { ...item, packages };
      }
      return item;
    });
    form.setFieldsValue({ goods: newGoodsData });
  },
  maxTagCount: 2,
  maxTagPlaceholder: () => <Icon type="more" />,
  dropdownRender: (menu: string) =>
    packageList?.length > 0 ? (
      <div>
        <Button
          className={clsx(
            styles.selectAllButton,
            isSelectAll && styles.checkSelectAllButton
          )}
          type="text"
          onClick={() => {
            const values: AttachmentConfigList & { key: string } =
              form.getFieldsValue();
            const allPackages = packageList.map(item => item.enumItemName);
            const newGoodsData = values.goods.map(item => {
              if (item?.goodsCode === goodsCode) {
                return { ...item, packages: allPackages };
              }
              return item;
            });
            form.setFieldsValue({ goods: newGoodsData });
          }}
        >
          {I18nInstance.t('Select all')}
        </Button>
        {isSelectAll && (
          <Icon
            type="check"
            className={styles.checkOutLine}
            style={{ color: styles.primaryColor }}
          />
        )}
        {menu}
      </div>
    ) : undefined,
  options: packageList,
  disabled,
});

export const scopeOfApplicationFields = (
  scopeOfApplicationEnum: BizDict[] = [],
  disabled: boolean
): FieldDataType => ({
  label: '',
  key: 'scopeOfApplications',
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  diabled: true,
  mode: 'multiple',
  rules: [
    {
      required: true,
      message: i18nFn('Please select'),
    },
  ],
  options: scopeOfApplicationEnum,
  disabled,
});

export const channelFields = (
  salesChannelEnum: BizDict[] = [],
  disabled: boolean
): FieldDataType => ({
  label: '',
  key: 'channels',
  placeholder: i18nFn('Please select'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: false,
  diabled: true,
  mode: 'multiple',
  rules: [
    {
      required: false,
      message: i18nFn('Please select'),
    },
  ],
  options: salesChannelEnum,
  disabled,
});

export const documentFields = (
  attachmentDocumentEnum: BizDict[] = [],
  disabled: boolean,
  form: FormInstance
): FieldDataType => ({
  label: '',
  key: 'attachmentType',
  placeholder: i18nFn('Attachment Type'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  rules: [
    {
      required: true,
      message: i18nFn('Attachment Type'),
    },
  ],
  options: attachmentDocumentEnum,
  disabled,
  onChange: (text: string) => {
    const values: AttachmentConfigList & { key: string } =
      form.getFieldsValue();
    const newAttachmentData = values.attachments.map(item => {
      if (item?.attachmentType === text) {
        return { ...item, mandatory: undefined };
      }
      return item;
    });
    form.setFieldsValue({ attachments: newAttachmentData });
  },
});

export const mandatoryFields = (
  yesNoEnum: BizDict[] = [],
  disabled: boolean
): FieldDataType => ({
  label: '',
  key: 'mandatory',
  placeholder: i18nFn('Mandatory'),
  type: FieldType.Select,
  allowClear: true,
  col: 16,
  required: true,
  diabled: true,
  rules: [
    {
      required: true,
      message: i18nFn('Mandatory'),
    },
  ],
  options: yesNoEnum,
  disabled,
});

export const policyNumberGenerationFields = (
  PolicyNumberGenerationEnum: BizDict[] = [],
  disabled: boolean
): FieldDataType => ({
  label: '',
  key: 'policyNumberGeneration',
  type: FieldType.Select,
  disabled,
  allowClear: true,
  required: true,
  placeholder: i18nFn('Please select'),
  options: PolicyNumberGenerationEnum,
});

const getGoodsValue = (goodsList: GoodsListType[]) => {
  const goodsData = goodsList?.map(
    item => `${item?.goodsName}_${item?.goodsCode}`
  );
  return goodsData?.join(';');
};

const getValue = (text: string[], enumList: BizDict[]) => {
  const nameData = text?.map((item: string) => {
    const enumItem = enumList?.find(
      items => items?.dictValue === item
    )?.dictValueName;
    return enumItem;
  });
  return nameData?.join(';') ?? i18nFn('--');
};

const getAttachmentValue = (
  text: AttachmentsType[],
  attachmentDocumentEnum: BizDict[]
) => {
  const attachmentList = text?.map(item => {
    const attachName = attachmentDocumentEnum.find(
      items => items?.dictValue === item?.attachmentType
    )?.dictValueName;
    return `${attachName}-${item?.mandatory}`;
  });
  return attachmentList?.join(';') ?? i18nFn('--');
};

export const quotationColumns = (
  productCategoryEnum: BizDict[] = [],
  salesChannelEnum: BizDict[] = [],
  scopeOfApplicationEnum: BizDict[] = [],
  attachmentDocumentEnum: BizDict[] = []
) => [
  {
    title: i18nFn('Goods Category'),
    dataIndex: 'goodsCategoryId',
    key: 'goodsCategoryId',
    width: 180,
    render: (text: string) =>
      dictMap(productCategoryEnum, text) ?? i18nFn('All goods categories'),
  },
  {
    title: i18nFn('Goods'),
    dataIndex: 'goods',
    key: 'goods',
    editable: true,
    width: 180,
    render: (text: GoodsListType[]) =>
      getGoodsValue(text) || i18nFn('All goods'),
  },
  {
    title: i18nFn('Channel'),
    dataIndex: 'channels',
    key: 'channels',
    editable: true,
    width: 180,
    render: (text: string[]) => getValue(text, salesChannelEnum),
  },
  {
    title: i18nFn('Scope of Application'),
    dataIndex: 'scopeOfApplications',
    key: 'scopeOfApplications',
    editable: true,
    width: 180,
    render: (text: string[]) => getValue(text, scopeOfApplicationEnum),
  },
  {
    title: i18nFn('Attachment/Mandatory'),
    dataIndex: 'attachments',
    key: 'attachments',
    editable: true,
    width: 180,
    render: (text: AttachmentsType[]) =>
      getAttachmentValue(text, attachmentDocumentEnum),
  },
];
