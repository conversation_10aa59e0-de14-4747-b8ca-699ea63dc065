import { ExclamationCircleOutlined } from '@ant-design/icons';

import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  BizDictItem,
  PolicyConfigurationType,
  SaveRuleRequestType,
  YesOrNo,
} from 'genesis-web-service';
import { RandomCheckConfigType } from 'genesis-web-service/lib/policy/policy.interface';

import I18nInstance from '@uw/i18n';
import { CategoryType, TypeEnums } from '@uw/interface/enum.interface';
import {
  FieldDataType,
  NbConfigurationFields,
} from '@uw/interface/field.interface';
import { handleRenderDictValue } from '@uw/util/handleRenderDictValue';
import { i18nFn } from '@uw/util/i18nFn';

import { handleRuleName, handleRuleRenderMultipleName } from './rule.config';

const ns = {
  ns: ['uw', 'common'],
};

export const optInRulesForm = (
  policyScenarioEnums: BizDict[] = [],
  businessTypeEnums: BizDict[],
  rules: BizDict[] = [],
  packages: BizDict[] = [],
  labels: BizDict[] = []
): FieldDataType[] => [
  {
    label: I18nInstance.t('Rule\\Rule Set', ns),
    dataIndex: 'rule',
    key: 'rule',
    col: 8,
    type: FieldType.Select,
    placeholder: I18nInstance.t('Please select'),
    options: rules.filter(
      item =>
        item?.code === CategoryType.NB_OPERATION ||
        item?.code === CategoryType.UNDERWRITING ||
        item?.code === CategoryType.COMPLIANCE
    ),
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Package', ns),
    dataIndex: 'packageList',
    key: 'packageList',
    col: 8,
    type: FieldType.Select,
    options: packages,
    placeholder: I18nInstance.t('Please select'),
    mode: 'multiple',
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Policy Type', ns),
    dataIndex: 'policyTypeEnum',
    key: 'policyTypeEnum',
    col: 8,
    type: FieldType.Select,
    placeholder: I18nInstance.t('Please select'),
    options: policyScenarioEnums,
    mode: 'multiple',
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Business Type', ns),
    dataIndex: 'businessTypeList',
    key: 'businessTypeList',
    col: 8,
    type: FieldType.Select,
    placeholder: I18nInstance.t('Please select'),
    options: businessTypeEnums,
    mode: 'multiple',
    rules: [
      {
        required: true,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
  {
    label: I18nInstance.t('Label', ns),
    dataIndex: 'label',
    key: 'label',
    col: 8,
    type: FieldType.Select,
    placeholder: I18nInstance.t('Please select'),
    mode: 'tags',
    options: labels,
    filterProp: 'value',
    rules: [
      {
        required: false,
        message: I18nInstance.t('Please select', ns),
      },
    ],
  },
];

const handleLabels = (text: string[]) => {
  const selectObjArr = text;
  return text?.length > 0
    ? selectObjArr.reduce(
        (cur, next) => `${cur}${cur.length > 0 ? ';' : ''}${next}`,
        ''
      )
    : I18nInstance.t('--');
};

export const optInRulesTable = (
  rules: BizDict[] = [],
  policyScenarioEnums: BizDict[] = [],
  businessTypeEnums: BizDict[],
  businessTypeEnumMap: Record<string, string>,
  packages: BizDict[] = []
): NbConfigurationFields<SaveRuleRequestType>[] => [
  {
    title: I18nInstance.t('Opt-In Check', ns),
    inlineEdit: true,
    tooltip: I18nInstance.t('OPT_IN Check Text', ns),
    typeEnum: TypeEnums.OPT_IN,
    columns: [
      {
        title: I18nInstance.t('Rule\\Rule Set', ns),
        dataIndex: 'rule',
        key: 'rule',
        editable: true,
        width: 200,
        render: text => handleRuleName(text, rules),
      },
      {
        title: I18nInstance.t('Package', ns),
        dataIndex: 'packageList',
        key: 'packageList',
        editable: true,
        width: 200,
        render: text => handleRuleRenderMultipleName(text, packages),
      },
      {
        title: I18nInstance.t('Policy Type', ns),
        dataIndex: 'policyTypeEnum',
        key: 'policyTypeEnum',
        editable: true,
        width: 200,
        render: text => handleRuleRenderMultipleName(text, policyScenarioEnums),
      },

      {
        title: I18nInstance.t('Label', ns),
        dataIndex: 'label',
        key: 'label',
        editable: true,
        width: 200,
        render: text => handleLabels(text),
      },
      {
        title: I18nInstance.t('Business Type'),
        dataIndex: 'businessTypeList',
        key: 'businessTypeList',
        editable: true,
        width: 300,
        fieldProps: {
          placeholder: I18nInstance.t('Please select'),
          type: FieldType.Select,
          options: businessTypeEnums,
          mode: 'multiple',
        },
        render: (text: string[]) =>
          handleRenderDictValue(text, businessTypeEnumMap),
      },
    ],
  },
];

export const policyConfigurationFields = (
  policyMaturityTerminationMethodEnum: BizDictItem[] = [],
  hasEditAuth: boolean,
  policyConfig?: PolicyConfigurationType
): FieldDataType[] => [
  {
    label: i18nFn('Policy Maturity Termination Method'),
    dataIndex: 'terminationMethod',
    key: 'terminationMethod',
    type: FieldType.Select,
    placeholder: i18nFn('Please select'),
    options: policyMaturityTerminationMethodEnum,
    tooltip: {
      placement: 'right',
      title:
        'When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated',
      icon: <ExclamationCircleOutlined />,
    },
    defaultValue: policyConfig?.terminationMethod,
    disabled: hasEditAuth,
    rules: [
      {
        required: true,
        message: i18nFn('Please select'),
      },
    ],
  },
];

export const doubleCheckConfigurationFields = (
  yesNoEnum: BizDict[] = [],
  hasEditAuth: boolean,
  randomCheckConfig?: RandomCheckConfigType
): FieldDataType[] => [
  {
    dataIndex: 'doubleCheckFlag',
    key: 'doubleCheckFlag',
    type: FieldType.Radio,
    className: 'radio-item-content',
    placeholder: i18nFn('Please select'),
    options: yesNoEnum,
    defaultValue: randomCheckConfig?.doubleCheckFlag ?? YesOrNo.YES,
    disabled: hasEditAuth,
  },
];

// https://jira.zaouter.com/browse/GIS-70605
// 在当前的proposal withdraw rule中的waiting for issuance下增加Apply to partial paid proposal标签，默认勾选。
// 新增的复选框 Key
export const APPLY_TO_PARTIAL_PAID_PROPOSAL = '_APPLY_TO_PARTIAL_PAID_PROPOSAL';
