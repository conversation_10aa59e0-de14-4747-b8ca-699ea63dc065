import I18nInstance from '@uw/i18n';
import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { ItemFields } from '@uw/util/renderFields';
import { FormInstance } from 'antd';
import { RandomCheckConfigType } from 'genesis-web-service/lib/policy/policy.interface';

import { ExtractPolicyCondition } from './components/ExtractPolicyCondition';

const ns = { ns: ['uw', 'common'] };

export const fields = (
  seperateChecked: boolean,
  accumulatedChecked: boolean,
  onSeperateSwitch: () => void,
  onAccumulatedSwitch: () => void,
  disabled: boolean
): FieldDataType[] => [
  {
    label: I18nInstance.t('Seperate by Proposal Status', ns),
    key: 'WITHDRAW_BY_SEPARATED_PROPOSAL_STATUS',
    type: FieldType.Switch,
    col: 8,
    disabled: disabled || accumulatedChecked,
    checked: seperateChecked,
    onChange: () => {
      onSeperateSwitch();
    },
  },
  {
    label: I18nInstance.t('Accumulated Value', ns),
    key: 'WITHDRAW_BY_ACCUMULATED_PROPOSAL_STATUS',
    type: FieldType.Switch,
    col: 8,
    disabled: disabled || seperateChecked,
    checked: accumulatedChecked,
    onChange: () => {
      onAccumulatedSwitch();
    },
  },
];
export const extractPolicyConditionFields = (
  yesNoEnum: BizDict[] = [],
  hasRuleEditAuth: boolean,
  isAdd: boolean,
  form: FormInstance,
  randomCheckConfig: RandomCheckConfigType | undefined
): ItemFields[] => [
  {
    type: FieldType.Render,
    span: 24,
    render: () => (
      <ExtractPolicyCondition
        yesNoEnum={yesNoEnum}
        form={form}
        isAdd={isAdd}
        hasRuleEditAuth={hasRuleEditAuth}
        randomCheckConfig={randomCheckConfig}
      />
    ),
  },
];
