import { createContext, Dispatch, SetStateAction, useContext } from 'react';
import {
  ProposalDetail,
  ProposalItem,
  ProposalRule,
  SaveReminderFrequencyRequestType,
} from 'genesis-web-service';
import {
  PendingCaseResponseType,
  ProposalTabelData,
  ProposalTableDataSource,
} from '@uw/interface/common.interface';
import { TypeEnums } from '@uw/interface/enum.interface';

export interface RuleConfigState {
  selectedWorkflows: string[];
}

export const RuleConfigurationContext = createContext<RuleConfigState>(
  null as unknown as RuleConfigState
);

export function useRuleConfigState(): RuleConfigState {
  return useContext(RuleConfigurationContext) as RuleConfigState;
}

export interface ProposalData {
  type: TypeEnums;
  data: ProposalDetail[];
}

export interface ProposalTableDataType {
  proposalData: ProposalTabelData | undefined;
  setProposalData: Dispatch<SetStateAction<ProposalTabelData | undefined>>;
}

export interface ProposalTableDataSourceType {
  proposalTableDataSource: ProposalTableDataSource;
  setProposalTableDataSource: Dispatch<SetStateAction<ProposalTableDataSource>>;
}
export interface ProposalRuleDataType {
  proposalRule: ProposalRule | undefined;
  setProposalRule: Dispatch<SetStateAction<ProposalRule | undefined>>;
}

export interface ProposalAccumulatedConfigType {
  accumulatedConfig: ProposalItem[] | undefined;
  setAccumulatedConfig: Dispatch<SetStateAction<ProposalItem[] | undefined>>;
}

export interface ProposalSeparatedConfigType {
  separatedConfig: ProposalItem[] | undefined;
  setSeparatedConfig: Dispatch<SetStateAction<ProposalItem[] | undefined>>;
}

export interface ProposalReminderConfigType {
  reminderConfig: ProposalItem[] | undefined;
  setReminderConfig: Dispatch<SetStateAction<ProposalItem[] | undefined>>;
}

export const ProposalConfigurationContext = createContext(
  null as unknown as ProposalTableDataType &
    ProposalRuleDataType &
    ProposalSeparatedConfigType &
    ProposalAccumulatedConfigType &
    ProposalReminderConfigType &
    ProposalTableDataSourceType
);

export const useProposalTableDataContext = (): ProposalTableDataType =>
  useContext(ProposalConfigurationContext);

export const useProposalRuleDataContext = (): ProposalRuleDataType =>
  useContext(ProposalConfigurationContext);

export const useProposalSeparatedConfigContext =
  (): ProposalSeparatedConfigType => useContext(ProposalConfigurationContext);

export const useProposalAccumulatedConfigContext =
  (): ProposalAccumulatedConfigType => useContext(ProposalConfigurationContext);

export const useProposalReminderConfigContext =
  (): ProposalReminderConfigType => useContext(ProposalConfigurationContext);

export const useProposalTableDataSourceContext =
  (): ProposalTableDataSourceType => useContext(ProposalConfigurationContext);

export interface PendingOriginDataConfigType {
  originDataSource: PendingCaseResponseType | undefined;
  setOriginDataSource: Dispatch<
    SetStateAction<PendingCaseResponseType | undefined>
  >;
}

export interface PendingDataConfigType {
  pendingDataSource: SaveReminderFrequencyRequestType[] | undefined;
  setPendingDataSource: Dispatch<
    SetStateAction<SaveReminderFrequencyRequestType[] | undefined>
  >;
}

export const PendingCaseConfigurationContext = createContext(
  null as unknown as PendingOriginDataConfigType & PendingDataConfigType
);

export const usePendingCaseOriginDataContext =
  (): PendingOriginDataConfigType =>
    useContext(PendingCaseConfigurationContext);

export const usePendingDataContext = (): PendingDataConfigType =>
  useContext(PendingCaseConfigurationContext);
