import { useState, useCallback } from 'react';
import { FormInstance } from 'antd';
import { PolicyService, ProposalItem, ProposalRule } from 'genesis-web-service';
import { ProposalTableDataSource } from '@uw/interface/common.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { FormatMedicalPlanType } from '@uw/hook/request';
import { YesOrNo, BizStatusEnum } from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import { APPLY_TO_PARTIAL_PAID_PROPOSAL } from './config';

export const useProposalConfig = (
  form: FormInstance
): {
  dataSource: ProposalTableDataSource;
  getDataSource: () => void;
  setDataSource: (args: ProposalTableDataSource) => void;
  separatedConfigItems?: ProposalItem[] | undefined;
  accumulatedConfigItems?: ProposalItem[] | undefined;
  rule?: ProposalRule | undefined;
  reminderItems?: ProposalItem[] | undefined;
} => {
  const [dataSource, setDataSource] = useState<ProposalTableDataSource>({
    separatedConfig: [],
    accumulatedConfig: [],
  });
  const [rule, setRule] = useState<ProposalRule>();
  const [separatedConfigItems, setSeparatedConfigItems] =
    useState<ProposalItem[]>();
  const [accumulatedConfigItems, setAccumulatedConfigItems] =
    useState<ProposalItem[]>();

  const [reminderItems, setReminderItems] = useState<ProposalItem[]>();

  const getDataSource = useCallback(async () => {
    const res = await PolicyService.getProposalConfig();
    const separatedConfig = res?.withdrawSeparatedConfig?.details?.map(
      (item, idx) => ({
        channelCode: item.channelCode,
        goodsCodes: item.goodsCodes,
        key: `separatedConfig${item.channelCode}${item.goodsCodes}${idx}`,
      })
    );

    const accumulatedConfig = res?.withdrawAccumulatedConfig?.details?.map(
      (item, idx) => ({
        channelCode: item.channelCode,
        goodsCodes: item.goodsCodes,
        key: `accumulatedConfig${item.channelCode}${item.goodsCodes}${idx}`,
      })
    );

    const applyToPartialPaidMaps = res?.withdrawSeparatedConfig?.items?.reduce(
      (prev, next) => {
        if (next?.bizStatus === BizStatusEnum.WAITING_FOR_ISSUANCE) {
          return {
            ...prev,
            [`${next?.bizStatus}${APPLY_TO_PARTIAL_PAID_PROPOSAL}`]:
              next?.applyToPartialPaid === YesOrNo.YES,
          };
        }
        return prev;
      },
      {}
    );

    form.setFieldsValue({
      ...applyToPartialPaidMaps,
      signOffConfig: res?.signOffConfig?.items?.[0]?.days,
      proposalReminderRule_basedOnCanlendarDays:
        res?.reminderBasedOnCalendarDays === YesOrNo.YES,
      proposalWithdrawRule_basedOnCanlendarDays:
        res?.withdrawBasedOnCalendarDays === YesOrNo.YES,
      policyNumberGeneration: res?.policyNumberGenerationRule,
    });
    setAccumulatedConfigItems(res?.withdrawAccumulatedConfig?.items);
    setSeparatedConfigItems(res?.withdrawSeparatedConfig?.items);
    setReminderItems(res.reminderSeparatedConfig?.items);
    setRule(res?.rule);
    setDataSource({
      accumulatedConfig: accumulatedConfig ?? [],
      separatedConfig: separatedConfig ?? [],
    });
  }, [form]);
  return {
    dataSource,
    getDataSource,
    setDataSource,
    separatedConfigItems,
    accumulatedConfigItems,
    rule,
    reminderItems,
  };
};

export const medicalPlanColumns = () => [
  {
    title: i18nFn('Medical Plan Name'),
    dataIndex: 'medicalPlanName',
    key: 'medicalPlanName',
    editable: true,
    width: 180,
    render: (text: string) => <div className={styles.ellipse}>{text}</div>,
  },
  {
    title: i18nFn('Medical Plan Code'),
    dataIndex: 'medicalPlanCode',
    key: 'medicalPlanCode',
    editable: true,
    width: 180,
    render: (text: string) => <div className={styles.ellipse}>{text}</div>,
  },
  {
    title: i18nFn('Medical Examination Item'),
    dataIndex: 'examinationItemRelationList',
    key: 'examinationItemRelationList',
    editable: true,
    render: (value: string, record: FormatMedicalPlanType) =>
      record.examinationItemRelationList
        ? record.examinationItemRelationList?.reduce(
            (cur, next) =>
              `${cur}${cur.length > 0 ? ', ' : ''}${next.itemName}`,
            ''
          )
        : i18nFn('--'),
  },
];
