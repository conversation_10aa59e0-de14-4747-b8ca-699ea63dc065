import React, { FC, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row } from 'antd';

import { MaturityReminderRulesType } from 'genesis-web-service';
import { useBizDict } from 'genesis-web-shared/lib/hook';

import { usePermission } from '@uw/hook/permission';
import { getFields } from '@uw/util/getFieldsQueryForm';

import { FootBar } from '../../../components/FootBar/FootBar';
import styles from './NewBusinessConfiguration.module.scss';
import { MaturityAgreement } from './components/MaturityAgreement';
import { policyConfigurationFields } from './config';
import { usePolicyConfig, useSubmitPolicyConfig } from './request';

const PolicyConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const { policyConfig, getPolicyConfig, isAdd } = usePolicyConfig();
  const { submitPolicyConfig, isRefreshPage } = useSubmitPolicyConfig();
  const hasRuleEditAuth = !!usePermission('policy.configuration.edit');
  const policyMaturityTerminationMethodEnum = useBizDict(
    'policyMaturityTerminationMethodEnum'
  );
  const [maturityReminderRules, setMaturityReminderRules] = useState<
    MaturityReminderRulesType[]
  >([]);

  useEffect(() => {
    if (policyConfig?.maturityReminderRules) {
      setMaturityReminderRules(
        policyConfig?.maturityReminderRules?.map((item, index) => ({
          ...item,
          key: `${index}`,
        }))
      );
    }
  }, [policyConfig]);

  const fields = useMemo(() => {
    if (isAdd) {
      return policyConfigurationFields(
        policyMaturityTerminationMethodEnum,
        !hasRuleEditAuth,
        policyConfig
      );
    }
    return (
      policyConfig &&
      policyConfigurationFields(
        policyMaturityTerminationMethodEnum,
        !hasRuleEditAuth,
        policyConfig
      )
    );
  }, [
    policyMaturityTerminationMethodEnum,
    policyConfig,
    hasRuleEditAuth,
    isAdd,
  ]);
  useEffect(() => {
    if (isRefreshPage) {
      getPolicyConfig();
    }
  }, [isRefreshPage]);
  const onCancel = () => {
    form.resetFields();
  };
  const onSubmit = async () => {
    try {
      const formValue = await form.validateFields();
      const params = {
        ...policyConfig,
        terminationMethod: formValue.terminationMethod,
        policyMaturityTerminationDateRule:
          formValue.policyMaturityTerminationDateRule,
        maturityReminderRules: maturityReminderRules?.map(item => ({
          dueDateCompare: item?.dueDateCompare,
          dueDateCompareValue: item?.dueDateCompareValue,
          packageIdList: item?.packageIdList?.map(item => Number(item)),
        })),
      };
      submitPolicyConfig(params);
    } catch (error) {
      // Do nothing, just hold error
    }
  };

  return (
    <>
      <div className={styles.ruleWrapper}>
        <section className={styles.title}>{t('Policy Configuration')}</section>
        <section className={styles.ruleContentWrapper}>
          <Form form={form} layout={'vertical'}>
            {fields?.map(field => (
              <Row>
                <Col span={10}>
                  <Form.Item
                    name={field.key}
                    label={field.label}
                    rules={field.rules}
                    tooltip={field.tooltip}
                  >
                    {getFields({
                      ...field,
                    })}
                  </Form.Item>
                </Col>
              </Row>
            ))}

            <MaturityAgreement
              data={maturityReminderRules}
              setData={setMaturityReminderRules}
              hasRuleEditAuth={hasRuleEditAuth}
            />
          </Form>
        </section>
      </div>
      <FootBar
        onSubmit={onSubmit}
        onCancel={onCancel}
        hasEditAuth={hasRuleEditAuth}
      />
    </>
  );
};

export default PolicyConfiguration;
