import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Form, FormInstance, Row, Checkbox, Tooltip } from 'antd';
import { Icon } from '@zhongan/nagrand-ui';
import { YesOrNo } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { getFields } from '@uw/util/getFields';

import { ProposalConfigAnchorIdsEnums } from '@uw/interface/enum.interface';

import clsx from 'clsx';

import { AccumulatedValue } from './AccumulatedValue ';
import styles from './NewBusinessConfiguration.module.scss';
import { ProposalReminder } from './ProposalReminder';
import { ProposalStatus } from './ProposalStatus';
import { ProposalSignOffConfig } from './components/ProposalSignOffConfig';
import { PolicyNumberGeneration } from './PolicyNumberGeneration';
import { useProposalRuleDataContext } from './context';
import { fields } from './pageConfig';
import { RuleConfiguration as DefaultRuleConfig } from './RuleConfiguration';

interface Props {
  form: FormInstance;
  hasProposalEditAuth: boolean;
  proposalWithdrawRef?: React.MutableRefObject<
    | {
        onSubmit: () => [];
      }
    | undefined
  >;
  proposalRef: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
}
export const ProposalWithdrawRule: FC<Props> = ({
  proposalWithdrawRef,
  form,
  hasProposalEditAuth,
  proposalRef,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { proposalRule } = useProposalRuleDataContext();
  const [seperateChecked, setSeperateChecked] = useState<boolean>(false);
  const [accumulatedChecked, setAccumulatedChecked] = useState<boolean>(false);

  useEffect(() => {
    setSeperateChecked(
      proposalRule?.WITHDRAW_BY_SEPARATED_PROPOSAL_STATUS === YesOrNo.YES
    );
    setAccumulatedChecked(
      proposalRule?.WITHDRAW_BY_ACCUMULATED_PROPOSAL_STATUS === YesOrNo.YES
    );
  }, [proposalRule]);

  useEffect(() => {
    form.setFieldsValue({
      WITHDRAW_BY_SEPARATED_PROPOSAL_STATUS: seperateChecked,
      WITHDRAW_BY_ACCUMULATED_PROPOSAL_STATUS: accumulatedChecked,
    });
  }, [seperateChecked, accumulatedChecked]);

  const onSeperateSwitch = useCallback(() => {
    setSeperateChecked(!seperateChecked);
  }, [seperateChecked]);

  const onAccumulatedSwitch = useCallback(() => {
    setAccumulatedChecked(!accumulatedChecked);
  }, [accumulatedChecked]);

  const queryFields = useMemo(
    () =>
      fields(
        seperateChecked,
        accumulatedChecked,
        onSeperateSwitch,
        onAccumulatedSwitch,
        !hasProposalEditAuth
      ),

    [
      seperateChecked,
      accumulatedChecked,
      onSeperateSwitch,
      onAccumulatedSwitch,
      hasProposalEditAuth,
    ]
  );

  return (
    <div>
      <DefaultRuleConfig proposalRef={proposalRef} />
      <section className={styles['rule-sub-title']}>
        {t('Proposal Withdraw Rule')}
      </section>
      <Form form={form}>
        <Form.Item
          name={'proposalWithdrawRule_basedOnCanlendarDays'}
          valuePropName="checked"
        >
          <Checkbox>
            {t('Based on Calendar Days')}
            <Tooltip
              title={t(
                'If the option is selected, the system will withdraw the proposal based on days only. Otherwise, specific times will be considered.'
              )}
            >
              <Icon
                type="exclamation-circle"
                style={{ color: 'var(--border-default)', marginLeft: '8px' }}
              />
            </Tooltip>
          </Checkbox>
        </Form.Item>
      </Form>
      <section className={styles.ruleTypeContent}>
        <Form form={form}>
          <div id={ProposalConfigAnchorIdsEnums.proposalWithdrawRule}>
            <Row>
              {queryFields.map(field => (
                <Form.Item
                  label={field.label}
                  name={field.key}
                  key={field.key}
                  style={{ marginRight: '100px' }}
                >
                  {getFields({ ...field })}
                </Form.Item>
              ))}
            </Row>
            <div className={clsx(!seperateChecked && styles.hiddenDom)}>
              <ProposalStatus
                form={form}
                proposalWithdrawRef={proposalWithdrawRef}
                hasProposalEditAuth={hasProposalEditAuth}
              />
            </div>

            <div className={!accumulatedChecked ? styles.hiddenDom : ''}>
              <AccumulatedValue
                form={form}
                proposalWithdrawRef={proposalWithdrawRef}
                hasProposalEditAuth={hasProposalEditAuth}
              />
            </div>
          </div>

          <ProposalReminder
            form={form}
            id={ProposalConfigAnchorIdsEnums.proposalReminderRule}
            hasProposalEditAuth={hasProposalEditAuth}
          />
          <ProposalSignOffConfig
            form={form}
            id={ProposalConfigAnchorIdsEnums.policySignOffRule}
            hasProposalEditAuth={hasProposalEditAuth}
          />
          <PolicyNumberGeneration
            form={form}
            id={ProposalConfigAnchorIdsEnums.policyNumberGenerationRule}
            hasProposalEditAuth={hasProposalEditAuth}
          />
        </Form>
      </section>
    </div>
  );
};
