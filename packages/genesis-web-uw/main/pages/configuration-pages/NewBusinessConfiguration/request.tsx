import { useCallback, useEffect, useState } from 'react';

import { uniqBy } from 'lodash-es';

import {
  ChannelService,
  GoodsListRequest,
  PolicyConfigurationType,
  PolicyService,
  SaveRuleRequestType,
} from 'genesis-web-service';
import { RandomCheckConfigType } from 'genesis-web-service/lib/policy/policy.interface';

import { GoodsObj } from '@uw/interface/common.interface';
import {
  BizDict,
  QueryPlaceholderEnum,
  SelectProps,
  TypeEnums,
} from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

export const useRuleConfigByType = (
  type: TypeEnums
): {
  onChange: (page: number, pageSize?: number | undefined) => void;
  dataSource: SaveRuleRequestType[] | undefined;
  current: number;
  total: number;
  getDataSource: (page: number, pageSize?: number | undefined) => void;
  setDataSource: React.Dispatch<
    React.SetStateAction<SaveRuleRequestType[] | undefined>
  >;
} => {
  const [dataSource, setDataSource] = useState<SaveRuleRequestType[]>();
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const getDataSource = useCallback(
    (page, pageSize) => {
      PolicyService.queryRuleConfigByType({
        pageSize,
        pageIndex: page,
        typeEnum: type,
      }).then(res => {
        setDataSource(
          res.results?.map((result, idx) => ({
            ...result,
            key: `${result.typeEnum}${result.rule}${idx}`,
          }))
        );
        setTotal(res.total);
        setCurrent(page);
      });
    },
    [type]
  );

  useEffect(() => {
    getDataSource(1, 10);
  }, []);
  const onChange = useCallback<(page: number, pageSize?: number) => void>(
    (page, pageSize) => {
      setCurrent(page);
      getDataSource(page, pageSize);
    },
    []
  );
  return {
    onChange,
    dataSource,
    current,
    total,
    getDataSource,
    setDataSource,
  };
};

// 获取goods,不传参数 查询全部
export const useGoodsList = (params: GoodsListRequest = {}) => {
  const [allGoodsList, setAllGoodsList] = useState<BizDict[]>();
  const [allGoodsObj, setAllGoodsObj] = useState<GoodsObj>();

  const refetch = useCallback(
    async value => {
      const data = await PolicyService.getGoodsListByChannel(value);
      const res = data?.map(item => ({
        enumItemName: item.goodsCode,
        itemName: item.goodsName,
        value: item.goodsCode,
      }));
      return uniqBy(res, 'value');
    },
    [params]
  );

  // 全量goodsList
  useEffect(() => {
    (async () => {
      const goods = await refetch(params);
      const goodsObj = goods?.reduce(
        (acc, cur) => ({ ...acc, [cur.value]: cur }),
        {}
      );
      setAllGoodsList(goods ?? []);
      setAllGoodsObj(goodsObj);
    })();
  }, []);

  return { allGoodsList, refetch, allGoodsObj };
};

export const useQueryChannel = (operateType: QueryPlaceholderEnum) => {
  const [channelList, setChannelList] = useState<SelectProps[]>();

  useEffect(() => {
    ChannelService.queryChannelsWithoutPageInfo({ operateType }).then(res => {
      const list = res?.value?.results?.map<SelectProps>(item => ({
        value: item?.code,
        label: item?.name,
      }));
      setChannelList(list);
    });
  }, [operateType]);

  return channelList;
};

export const usePolicyConfig = (): {
  policyConfig?: PolicyConfigurationType;
  getPolicyConfig: () => void;
  isAdd: boolean;
} => {
  const [policyConfig, setPolicyConfig] = useState<PolicyConfigurationType>();
  const [isAdd, setIsAdd] = useState(false);
  const getPolicyConfig = useCallback(() => {
    PolicyService.getPolicyConfiguration()
      .then(res => {
        if (!res) {
          setIsAdd(true);
        } else {
          setIsAdd(false);
          setPolicyConfig(res);
        }
      })
      .catch(error => {
        messagePopup(error.toString(), 'error');
      });
  }, []);
  useEffect(() => {
    getPolicyConfig();
  }, []);
  return { policyConfig, getPolicyConfig, isAdd };
};

export const useSubmitPolicyConfig = (): {
  isRefreshPage: boolean;
  submitPolicyConfig: (params: PolicyConfigurationType) => void;
} => {
  const [isRefreshPage, setIsRefreshPage] = useState(false);
  const submitPolicyConfig = useCallback((params: PolicyConfigurationType) => {
    setIsRefreshPage(false);
    PolicyService.submitPolicyConfiguration(params)
      .then(res => {
        if (!res) {
          messagePopup(i18nFn('Submit Successfully'), 'success');
          setIsRefreshPage(true);
        }
      })
      .catch(error => {
        setIsRefreshPage(false);
        messagePopup(error.toString(), 'error');
      })
      .finally(() => {
        setIsRefreshPage(false);
      });
  }, []);

  return { submitPolicyConfig, isRefreshPage };
};

export const useRandomCheckConfig = (): {
  randomCheckConfig?: RandomCheckConfigType;
  getRandomCheckConfig: () => void;
  isAdd: boolean;
} => {
  const [randomCheckConfig, setRandomCheckConfig] =
    useState<RandomCheckConfigType>();
  const [isAdd, setIsAdd] = useState(false);
  const getRandomCheckConfig = useCallback(() => {
    PolicyService.getRandomCheckConfiguration()
      .then(res => {
        if (!res) {
          setIsAdd(true);
        } else {
          setIsAdd(false);
          setRandomCheckConfig(res);
        }
      })
      .catch(error => {
        messagePopup(error.toString(), 'error');
      });
  }, []);
  useEffect(() => {
    getRandomCheckConfig();
  }, []);
  return { randomCheckConfig, getRandomCheckConfig, isAdd };
};

export const useSubmitRandomCheckConfig = (): {
  isRefreshPage: boolean;
  submitRandomCheckConfig: (params: RandomCheckConfigType) => void;
} => {
  const [isRefreshPage, setIsRefreshPage] = useState(false);
  const submitRandomCheckConfig = useCallback(
    (params: RandomCheckConfigType) => {
      setIsRefreshPage(false);
      PolicyService.submitRandomCheckConfiguration(params)
        .then(res => {
          if (res.success) {
            messagePopup(i18nFn('Submit Successfully'), 'success');
            setIsRefreshPage(true);
          }
        })
        .catch(error => {
          setIsRefreshPage(false);
          messagePopup(error.toString(), 'error');
        })
        .finally(() => {
          setIsRefreshPage(false);
        });
    },
    []
  );

  return { submitRandomCheckConfig, isRefreshPage };
};
