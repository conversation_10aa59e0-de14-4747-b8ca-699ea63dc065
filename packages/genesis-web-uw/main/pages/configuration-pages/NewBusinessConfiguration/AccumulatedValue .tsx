import React, { FC, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, FormInstance, Row } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

import { TypeEnums } from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import { AssociatedGoods } from './components/AssociatedGoods';
import { useProposalAccumulatedConfigContext } from './context';

interface Props {
  form: FormInstance;
  hasProposalEditAuth: boolean;
  proposalWithdrawRef?: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
}

export const AccumulatedValue: FC<Props> = ({
  form,
  proposalWithdrawRef,
  hasProposalEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const { accumulatedConfig } = useProposalAccumulatedConfigContext();

  useEffect(() => {
    form.setFieldsValue({
      ACCU_WAITING_FOR_DATA_ENTRY: accumulatedConfig?.[0]?.days,
    });
  }, [accumulatedConfig]);

  return (
    <>
      <div className={styles.divider}></div>
      <div className={styles.ruleSubTitle}>{t('Accumulated Value')}</div>
      <div className={styles.descText}>
        <div> {t('Accumulated days from all pending proposal status')}</div>
        <div>
          {t(
            'Data Entry in Progress, Pending Proposal Check, Waiting for Issuance'
          )}
        </div>
      </div>
      <Row>
        <Col span={8}>
          <Form.Item
            name="ACCU_WAITING_FOR_DATA_ENTRY"
            rules={[
              {
                pattern: new RegExp(/^[1-9]\d*$/, 'g'),
                message: t('Please enter a number greater than 0'),
              },
            ]}
            validateTrigger={['onChange', 'onBlur']}
          >
            <Input
              placeholder={t('Please input')}
              addonAfter={t('Day(s)')}
              disabled={!hasProposalEditAuth}
            />
          </Form.Item>
        </Col>
      </Row>
      <AssociatedGoods
        type={TypeEnums.ACCUMULATED_VALUE}
        form={form}
        id={TypeEnums.ACCUMULATED_VALUE}
        ref={proposalWithdrawRef}
        hasProposalEditAuth={hasProposalEditAuth}
      />
    </>
  );
};
