import React, { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

import { cloneDeep } from 'lodash-es';

import { NBConfiguration } from 'genesis-web-service';

import { TextBody } from '@uw/components/Text';
import { useBizDict, usePolicyScenarioType } from '@uw/hook/useBizDict';
import { useDict } from '@uw/hook/useDict';
import {
  BizDict,
  ProposalConfigAnchorIdsEnums,
} from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import { CommonEditTable } from './components/CommonEditTable';
import { VerificationCheck } from './components/VerificationCheck';
import { Workflow } from './components/Workflow/Workflow';
import {
  useGetSelectedWorkflow,
  useGetWorkflowTemplates,
} from './hooks/request';
import { proposalRuleConfig } from './rule.config';

interface Props {
  rules?: BizDict[];
  packages?: BizDict[];
  proposalRef: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
  hasRuleEditAuth: boolean;
}

export const ProposalRule: FC<Props> = ({
  rules,
  packages,
  proposalRef,
  hasRuleEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const nBConfWorkflowEnums = useBizDict('nBConfWorkflow');
  const policyScenarioEnums = usePolicyScenarioType();
  const businessTypeEnums = useBizDict('ruleConfigurationBusinessType');
  const [businessTypeEnumMap] = useDict('ruleConfigurationBusinessType');
  const [workflowDetail, selectedWorkflow, getSelectedWorkflow] =
    useGetSelectedWorkflow();
  const [workflowTemplates] = useGetWorkflowTemplates();
  const proposalRuleCOnfiguration = useMemo(() => {
    const configs = proposalRuleConfig(
      policyScenarioEnums,
      businessTypeEnums as BizDict[],
      businessTypeEnumMap,
      rules,
      packages
    );
    return configs.map(config => (
      <div key={config.title} id={config.id} className="mb-8">
        <TextBody weight={700} style={{ marginBottom: styles.gapXs }}>
          {config.title}
          {config.tooltip && (
            <Tooltip placement="right" title={config.tooltip}>
              <ExclamationCircleOutlined style={{ marginLeft: styles.gapXs }} />
            </Tooltip>
          )}
        </TextBody>
        <CommonEditTable
          columns={config.columns}
          type={config.typeEnum!}
          tooltip={config.tooltip}
          tips={config.tips}
          hasRuleEditAuth={hasRuleEditAuth}
        />
      </div>
    ));
  }, [
    policyScenarioEnums,
    rules,
    packages,
    hasRuleEditAuth,
    businessTypeEnums,
    businessTypeEnumMap,
  ]);

  const workflowNodesEdges = useMemo<
    NBConfiguration.WokflowDetail | undefined
  >(() => {
    const detail = cloneDeep(workflowDetail);
    if (detail?.config) {
      delete (detail as Partial<NBConfiguration.QueryWorkfowDetail>).config;
    }
    return detail;
  }, [workflowDetail]);

  return (
    <div>
      <Workflow
        id={ProposalConfigAnchorIdsEnums.proposalFlowSetting}
        title={t('Verification/Compliance/UW Process Flow Configuration')}
        workflowDetail={workflowNodesEdges}
        workflowTemplates={workflowTemplates}
        getSelectedWorkflow={getSelectedWorkflow}
        hasRuleEditAuth={hasRuleEditAuth}
      />
      <VerificationCheck
        id={ProposalConfigAnchorIdsEnums.verificationCheck}
        selectedWorkflows={selectedWorkflow ?? []}
        ref={proposalRef}
        title={t('Verification Check')}
        options={nBConfWorkflowEnums || []}
        hasRuleEditAuth={hasRuleEditAuth}
        tooltipText={t(
          'System will trigger the proposal flow based on the "Verification Decision"  of rule configured here. If declined, system will reject the proposal. And if manual, system will send the proposal to manual verification.'
        )}
      />
      {proposalRuleCOnfiguration}
    </div>
  );
};
