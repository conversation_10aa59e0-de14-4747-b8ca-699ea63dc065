import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import { Form } from 'antd';
import {
  PolicyService,
  ProposalDetailItem,
  ProposalItem,
  ProposalRule,
} from 'genesis-web-service';

import { usePermission } from 'genesis-web-shared/lib/hook';
import { useTranslation } from 'react-i18next';
import { useBizDict } from '@uw/hook/useBizDict';
import {
  ProposalTabelData,
  ProposalTableDataSource,
} from '@uw/interface/common.interface';
import {
  YesOrNo,
  TypeEnums,
  BizStatusEnum,
  ProposalSwitchEnum,
  ReminderBizStatusEnum,
  PendingProposalCheckBizStatusEnum,
} from '@uw/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';

import { FootBar } from '../../../components/FootBar/FootBar';
import styles from './NewBusinessConfiguration.module.scss';
import { ProposalWithdrawRule } from './ProposalWithdrawRule';
import { ProposalConfigurationContext } from './context';
import { useProposalConfig } from './requestProposal';
import { APPLY_TO_PARTIAL_PAID_PROPOSAL } from './config';

export interface RefsTable {
  onSubmit: () => [];
}

enum ProposalSignOffStatusEnum {
  EFFECTIVE = 'EFFECTIVE',
}

const ProposalConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const proposalWithdrawRef = useRef<RefsTable[]>([]);
  const [form] = Form.useForm();
  const hasProposalEditAuth = !!usePermission('nb.configuration.proposal.edit');
  const [proposalData, setProposalData] = useState<
    ProposalTabelData | undefined
  >();
  const nBConfWorkflowEnums = useBizDict('nBConfWorkflow');
  const proposalRef = useRef<{
    onSubmit: () => string[];
  }>();

  const [proposalTableDataSource, setProposalTableDataSource] =
    useState<ProposalTableDataSource>({
      separatedConfig: [],
      accumulatedConfig: [],
    });

  const [proposalRule, setProposalRule] = useState<ProposalRule>();
  const [separatedConfig, setSeparatedConfig] = useState<ProposalItem[]>();
  const [accumulatedConfig, setAccumulatedConfig] = useState<ProposalItem[]>();
  const [reminderConfig, setReminderConfig] = useState<ProposalItem[]>();
  const [detailsList] = useState<ProposalDetailItem[]>([]);
  const {
    getDataSource,
    dataSource,
    rule,
    separatedConfigItems,
    accumulatedConfigItems,
    reminderItems,
  } = useProposalConfig(form);

  useEffect(() => {
    getDataSource();
  }, []);

  useEffect(() => {
    setProposalTableDataSource(dataSource);
  }, [dataSource]);

  useEffect(() => {
    setProposalRule(rule);
  }, [rule]);

  useEffect(() => {
    setSeparatedConfig(separatedConfigItems);
  }, [separatedConfigItems]);

  useEffect(() => {
    setAccumulatedConfig(accumulatedConfigItems);
  }, [accumulatedConfigItems]);

  useEffect(() => {
    setReminderConfig(reminderItems);
  }, [reminderItems]);

  const bizStatusEnums = useBizDict('bizStatus');
  const onSubmit = async () => {
    try {
      const formValue = await form.validateFields();
      if (formValue) {
        // 从 formValue 中过滤掉非 applyToPartialPaidProposal 数据
        const applyToPartialPaidProposalMaps: Record<string, YesOrNo> =
          Object?.entries(formValue)?.reduce((prev, [key, value]) => {
            if (key?.includes(APPLY_TO_PARTIAL_PAID_PROPOSAL)) {
              return {
                ...prev,
                [key]: value,
              };
            }
            return prev;
          }, {});

        const separatedConfigItemsData = Object.keys(formValue)
          ?.filter(item =>
            Object.keys({
              ...BizStatusEnum,
              ...PendingProposalCheckBizStatusEnum,
            })
              .filter(
                value => value !== BizStatusEnum.ACCU_WAITING_FOR_DATA_ENTRY
              )
              .includes(item)
          )
          ?.map(value => {
            const data: ProposalItem = {
              days: formValue[value] ? +formValue[value] : undefined,
              bizStatus: value,
            };

            const applyToPartialPaid =
              value === BizStatusEnum?.WAITING_FOR_ISSUANCE
                ? applyToPartialPaidProposalMaps?.[
                    `${BizStatusEnum?.WAITING_FOR_ISSUANCE}${APPLY_TO_PARTIAL_PAID_PROPOSAL}`
                  ]
                : undefined;
            // 目前只有 proposal withdraw rule 中的 waiting for issuance 需要该属性
            if (value === BizStatusEnum?.WAITING_FOR_ISSUANCE) {
              data.applyToPartialPaid = applyToPartialPaid
                ? YesOrNo.YES
                : YesOrNo.NO;
            }

            if (!data.days) {
              delete data.days;
            }
            return data;
          });

        const reminderList = Object.keys(formValue)
          ?.filter(item => ReminderBizStatusEnum.includes(item))
          ?.map(value => {
            if (formValue[value]?.[0] === null) {
              delete formValue[value];
            }

            const data = {
              daysList:
                (formValue[value] as (string | null)[])
                  ?.filter(item => item !== null)
                  ?.map(item => Number(item)) || [],
              bizStatus: bizStatusEnums
                ?.find(item => item?.enumItemName === value?.slice(0, -4))
                ?.enumItemName.toString(),
            };
            return data;
          });
        const reminderSeparatedConfig = { items: [...reminderList] };
        const listData =
          proposalData &&
          Object.keys(proposalData)?.map(item => {
            const details = proposalData[item]?.map<{
              channelCode: string;
              goodsCodes: string[];
            }>(value => ({
              channelCode: value.channelCode,
              goodsCodes: value.goodsCodes,
            }));

            if (item === TypeEnums.SEPERATE_BY_PROPOSAL_STATUS) {
              return {
                type: ProposalSwitchEnum.SEPARATEDCONFIG,
                data: {
                  items: separatedConfigItemsData ?? [],
                  details,
                },
              };
            }
            if (item === TypeEnums.ACCUMULATED_VALUE) {
              return {
                type: ProposalSwitchEnum.ACCUMULATEDCONFIG,
                data: {
                  details,
                  items: [
                    {
                      bizStatus: BizStatusEnum.WAITING_FOR_DATA_ENTRY,
                      days: formValue[BizStatusEnum.ACCU_WAITING_FOR_DATA_ENTRY]
                        ? +formValue[BizStatusEnum.ACCU_WAITING_FOR_DATA_ENTRY]
                        : null,
                    },
                  ],
                },
              };
            }
            return {};
          });

        const withdrawSeparatedConfig = listData?.filter(
          item => item.type === ProposalSwitchEnum.SEPARATEDCONFIG
        )?.[0]?.data ?? { details: detailsList };

        const withdrawAccumulatedConfig = listData?.filter(
          item => item.type === ProposalSwitchEnum.ACCUMULATEDCONFIG
        )?.[0]?.data ?? { details: detailsList };

        const accumulatedSwitch =
          formValue.WITHDRAW_BY_ACCUMULATED_PROPOSAL_STATUS;
        const separatedSwitch = formValue.WITHDRAW_BY_SEPARATED_PROPOSAL_STATUS;
        const submitParams = {
          rule: {
            WITHDRAW_BY_ACCUMULATED_PROPOSAL_STATUS: accumulatedSwitch
              ? YesOrNo.YES
              : YesOrNo.NO,
            WITHDRAW_BY_SEPARATED_PROPOSAL_STATUS: separatedSwitch
              ? YesOrNo.YES
              : YesOrNo.NO,
            REMINDER_BY_SEPARATED_PROPOSAL_STATUS: YesOrNo.YES,
          },

          withdrawSeparatedConfig,
          withdrawAccumulatedConfig,
          reminderSeparatedConfig,
          signOffConfig:
            formValue.signOffConfig?.length > 0
              ? {
                  items: [
                    {
                      bizStatus: ProposalSignOffStatusEnum.EFFECTIVE,
                      days: +formValue.signOffConfig,
                    },
                  ],
                }
              : {},
          reminderBasedOnCalendarDays:
            formValue?.proposalReminderRule_basedOnCanlendarDays
              ? YesOrNo.YES
              : YesOrNo.NO,
          withdrawBasedOnCalendarDays:
            formValue?.proposalWithdrawRule_basedOnCanlendarDays
              ? YesOrNo.YES
              : YesOrNo.NO,
          policyNumberGenerationRule: formValue?.policyNumberGeneration,
        };

        if (
          (separatedSwitch && !withdrawSeparatedConfig.details.length) ||
          (accumulatedSwitch && !withdrawAccumulatedConfig.details.length)
        ) {
          messagePopup(t('Please add at least one piece of data'), 'error');
          return;
        }
        const selectedIds = proposalRef?.current?.onSubmit();
        if (!selectedIds) {
          return;
        }
        const workflowMap = nBConfWorkflowEnums?.reduce<Record<string, string>>(
          (cur, next) => {
            const curTemp = { ...cur };
            curTemp[next.enumItemName] = selectedIds?.includes(
              next.enumItemName as string
            )
              ? YesOrNo.YES
              : YesOrNo.NO;
            return curTemp;
          },
          {}
        );
        PolicyService.saveProposalConfig({
          ...submitParams,
          workflow: { ...workflowMap },
        })
          .then(() => {
            getDataSource?.();
            messagePopup(t('Submit Successfully'), 'success');
          })
          .catch(error => {
            messagePopup(error.toString(), 'error');
          });
      }
    } catch (error) {
      messagePopup(
        error ? error?.errorFields[0]?.errors[0] : t('Invalid input format'),
        'error'
      );
    }
  };

  const getRefs = useCallback((dom: RefsTable) => {
    if (dom) {
      proposalWithdrawRef.current?.push(dom);
    }
  }, []);

  return (
    <ProposalConfigurationContext.Provider
      value={{
        proposalTableDataSource,
        setProposalTableDataSource,
        proposalData,
        setProposalData,
        proposalRule,
        setProposalRule,
        separatedConfig,
        setSeparatedConfig,
        accumulatedConfig,
        setAccumulatedConfig,
        reminderConfig,
        setReminderConfig,
      }}
    >
      <div className={styles['rule-wrapper']}>
        <section className={styles.title}>
          {t('Proposal Configuration')}
        </section>
        <section
          className={styles['rule-content-wrapper']}
          id="proposalContent"
        >
          <div>
            <Form>
              <ProposalWithdrawRule
                form={form}
                proposalWithdrawRef={getRefs}
                proposalRef={proposalRef}
                hasProposalEditAuth={hasProposalEditAuth}
              />
            </Form>
          </div>
        </section>
      </div>
      <FootBar onSubmit={onSubmit} hasEditAuth={hasProposalEditAuth} />
    </ProposalConfigurationContext.Provider>
  );
};
export default ProposalConfiguration;
