import React, { FC } from 'react';
import { Form, FormInstance, Checkbox, Tooltip } from 'antd';
import { Icon } from '@zhongan/nagrand-ui';
import { useTranslation } from 'react-i18next';

import styles from './NewBusinessConfiguration.module.scss';
import { ProposalReminderTable } from './components/PropsalReminderTable';

interface Props {
  id: string;
  form: FormInstance;
  hasProposalEditAuth: boolean;
}
export const ProposalReminder: FC<Props> = ({
  id,
  form,
  hasProposalEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  return (
    <div id={id} key={id}>
      <div className={styles.proposalTitle}>{t('Proposal Reminder Rule')}</div>

      <Form.Item
        name={'proposalReminderRule_basedOnCanlendarDays'}
        valuePropName="checked"
      >
        <Checkbox>
          {t('Based on Calendar Days')}
          <Tooltip
            title={t(
              'If the option is selected, the system will generate reminder based on days only. Otherwise, specific times will be considered.'
            )}
          >
            <Icon
              type="exclamation-circle"
              style={{ color: 'var(--border-default)', marginLeft: '8px' }}
            />
          </Tooltip>
        </Checkbox>
      </Form.Item>

      <div>
        {t(
          'System will trigger reminder notification when the proposal stays in below status after X days.'
        )}
      </div>
      <ProposalReminderTable
        form={form}
        hasProposalEditAuth={hasProposalEditAuth}
      />
    </div>
  );
};
