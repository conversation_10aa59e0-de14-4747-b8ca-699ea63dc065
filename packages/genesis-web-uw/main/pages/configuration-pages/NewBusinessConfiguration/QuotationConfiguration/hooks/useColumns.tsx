import { useTranslation } from 'react-i18next';

import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';

import { useDict } from '@uw/hook/useDict';
import { CategoryType } from '@uw/interface/enum.interface';
import { handleRenderDictValue } from '@uw/util/handleRenderDictValue';

import {
  handleRuleName,
  handleRuleRenderMultipleName,
} from '../../rule.config';

export const useColumns = (
  rules: BizDict[],
  packages: BizDict[],
  policyScenarioEnums: BizDict[],
  businessTypeEnums: BizDict[]
) => {
  const { t } = useTranslation(['uw', 'common']);
  const [businessTypeEnumMap] = useDict('issuanceTaskType');

  const columns = [
    {
      title: t('Rule\\Rule Set'),
      dataIndex: 'rule',
      key: 'rule',
      editable: true,
      width: 200,
      fieldProps: {
        placeholder: t('Please select'),
        type: FieldType.Select,
        options: rules?.filter(
          item => item?.code === CategoryType.UNDERWRITING
        ),
        extraProps: {
          options: rules?.filter(
            item => item?.code === CategoryType.UNDERWRITING
          ),
        },
      },
      render: (text: string) => handleRuleName(text, rules),
    },
    {
      title: t('Package'),
      dataIndex: 'packageList',
      key: 'packageList',
      editable: true,
      width: 200,
      fieldProps: {
        placeholder: t('Please select'),
        type: FieldType.Select,
        options: packages,
        extraProps: {
          options: packages,
        },
        mode: 'multiple',
      },
      render: (text: string[]) => handleRuleRenderMultipleName(text, packages),
    },
    {
      title: t('Policy Type'),
      dataIndex: 'policyTypeEnum',
      key: 'policyTypeEnum',
      editable: true,
      width: 200,
      fieldProps: {
        placeholder: t('Please select'),
        type: FieldType.Select,
        options: policyScenarioEnums,
        extraProps: {
          options: policyScenarioEnums,
        },
        mode: 'multiple',
      },
      render: (text: string[]) =>
        handleRuleRenderMultipleName(text, policyScenarioEnums),
    },
    {
      title: t('Business Type'),
      dataIndex: 'businessTypeList',
      key: 'businessTypeList',
      editable: true,
      width: 200,
      fieldProps: {
        placeholder: t('Please select'),
        type: FieldType.Select,
        options: businessTypeEnums,
        extraProps: {
          options: businessTypeEnums,
        },
        mode: 'multiple',
      },
      render: (text: string[]) =>
        handleRenderDictValue(text, businessTypeEnumMap),
    },
  ];

  return { columns };
};
