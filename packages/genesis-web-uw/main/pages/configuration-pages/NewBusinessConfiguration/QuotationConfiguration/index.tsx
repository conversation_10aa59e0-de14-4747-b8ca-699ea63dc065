import React, { FC, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { usePermission } from '@uw/hook/permission';
import { usePackages, useRules, useWorkflowMap } from '@uw/hook/request';

import styles from '../NewBusinessConfiguration.module.scss';
import { UnderWritingCheck } from './UnderWritingCheck';
import { RuleConfigurationContext } from '../context';

const QuotationConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const packages = usePackages();
  const rules = useRules();
  const selectedWorkflows = useWorkflowMap();
  const proposalRef = useRef<{
    onSubmit: () => string[];
  }>();
  const hasRuleEditAuth = !!usePermission('nb.configuration.quotation.edit');

  return (
    <>
      <RuleConfigurationContext.Provider
        value={{ selectedWorkflows: selectedWorkflows ?? [] }}
      >
        <div className={styles.ruleWrapper}>
          <section className={styles.title}>
            {t('Quotation Configuration')}
          </section>
          <section className={styles.ruleContentWrapper} id="ruleContent">
            <div>
              <UnderWritingCheck
                rules={rules}
                packages={packages}
                hasRuleEditAuth={hasRuleEditAuth}
                proposalRef={proposalRef}
              />
            </div>
          </section>
        </div>
      </RuleConfigurationContext.Provider>
    </>
  );
};
export default QuotationConfiguration;
