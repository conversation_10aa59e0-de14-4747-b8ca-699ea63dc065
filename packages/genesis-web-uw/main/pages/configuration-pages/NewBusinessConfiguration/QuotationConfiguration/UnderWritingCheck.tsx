import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { TextBody } from '@zhongan/nagrand-ui';

import { useBizDict, usePolicyScenarioType } from '@uw/hook/useBizDict';
import { BizDict, TypeEnums } from '@uw/interface/enum.interface';

import { CommonEditTable } from '../components/CommonEditTable';
import { useColumns } from './hooks/useColumns';

interface Props {
  rules?: BizDict[];
  packages?: BizDict[];
  proposalRef: React.MutableRefObject<
    | {
        onSubmit: () => void;
      }
    | undefined
  >;
  hasRuleEditAuth: boolean;
}

export const UnderWritingCheck: FC<Props> = ({
  rules,
  packages,
  hasRuleEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const policyScenarioEnums = usePolicyScenarioType();
  const businessTypeEnums = useBizDict('ruleConfigurationBusinessType');
  const { columns } = useColumns(
    rules as BizDict[],
    packages as BizDict[],
    policyScenarioEnums as BizDict[],
    businessTypeEnums as BizDict[]
  );

  return (
    <div>
      <TextBody weight={700} style={{ marginBottom: 14, marginTop: 14 }}>
        {t('Quotation Information Pre-check')}
      </TextBody>
      <CommonEditTable
        columns={columns}
        type={TypeEnums.QUOTATION_UNDERWRITING}
        tooltip={t('Underwriting Check Text')}
        tips={undefined}
        hideOnSinglePage={true}
        hasRuleEditAuth={hasRuleEditAuth}
      />
    </div>
  );
};
