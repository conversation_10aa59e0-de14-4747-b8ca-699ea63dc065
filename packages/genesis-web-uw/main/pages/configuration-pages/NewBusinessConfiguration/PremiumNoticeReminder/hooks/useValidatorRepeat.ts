/**
 * 验证是否存在重复的行数据
 * <AUTHOR>
 * @since 2024-11-18 11:22
 */
import { useCallback } from 'react';

import { message } from 'antd';

import type { INoticeConfigDetail } from 'genesis-web-service';

export const useValidatorRepeat = (t: any, data: INoticeConfigDetail[]) =>
  useCallback(
    (record: INoticeConfigDetail) => {
      const { noticeConfigId, dateCompareSource, dateValue, dateCompare } =
        record;
      const repeatRecord = data?.find(
        item =>
          item.noticeConfigId !== noticeConfigId &&
          item.dateCompareSource === dateCompareSource &&
          item.dateValue === dateValue &&
          item.dateCompare === dateCompare
      );
      // 提示不可重复
      if (repeatRecord) {
        message.error(t('Duplicate configuration, please check.'));
      }
      return !!repeatRecord;
    },
    [data, t]
  );
