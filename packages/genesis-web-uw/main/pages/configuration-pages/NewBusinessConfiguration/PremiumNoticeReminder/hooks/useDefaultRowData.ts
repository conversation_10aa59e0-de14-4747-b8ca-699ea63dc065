/**
 *
 * <AUTHOR>
 * @since 2024-11-18 11:13
 */
import { useMemo } from 'react';

import type { ColumnEditingType } from '@zhongan/nagrand-ui';

export const useDefaultRowData = <T = any>(columns: ColumnEditingType<T>[]) =>
  useMemo(() => {
    const originData: Record<string, any> = {};
    columns.forEach(col => {
      col.fieldProps?.groupItems?.forEach((child: any) => {
        originData[child.key as string] = undefined;
      });
      originData[col.dataIndex as string] = undefined;
    });
    return originData;
  }, [columns]);
