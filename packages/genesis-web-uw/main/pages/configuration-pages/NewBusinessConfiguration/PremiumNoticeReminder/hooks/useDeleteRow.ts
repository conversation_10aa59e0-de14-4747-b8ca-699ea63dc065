/**
 *
 * <AUTHOR>
 * @since 2024-11-18 11:19
 */
import { useCallback } from 'react';

import { message } from 'antd';

import { type INoticeConfigDetail, RenewService } from 'genesis-web-service';

export const useDeleteRow = (
  data: INoticeConfigDetail[],
  requestData: () => void
) =>
  useCallback<(idx: number) => Promise<boolean>>(
    async idx => {
      const record = data[idx];
      if (!record) return false;
      try {
        await RenewService.deleteNoticeConfig(record.noticeConfigId!);
        requestData();
        return true;
      } catch (e) {
        message.error((e as Error)?.toString());
        return false;
      }
    },
    [data, requestData]
  );
