/**
 *
 * <AUTHOR>
 * @since 2024-11-18 10:27
 */
import { useCallback, useEffect, useState } from 'react';

import {
  type INoticeConfigDetail,
  type INoticeConfigQuery,
  RenewService,
} from 'genesis-web-service';

export const useNoticeData = (): [
  INoticeConfigQuery | undefined,
  () => void,
] => {
  const [dataSource, setDataSource] = useState<INoticeConfigQuery>();

  const fetchDataSource = useCallback(() => {
    RenewService.queryNoticeConfig().then(data => {
      const newData: Record<string, any> = {};
      Object.entries(data).forEach(([key, value]) => {
        newData[key] = value?.map((item: INoticeConfigDetail) => ({
          ...item,
          key: item.noticeConfigId,
        }));
      });
      setDataSource(newData as INoticeConfigQuery);
    });
  }, []);

  useEffect(() => {
    fetchDataSource();
  }, []);

  return [dataSource, fetchDataSource];
};
