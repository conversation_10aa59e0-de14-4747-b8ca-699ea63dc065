import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';

import { FieldType } from '@zhongan/nagrand-ui';

import type { INoticeConfigDetail } from 'genesis-web-service';

import { useBizDictByKey, useDict } from '@uw/biz-dict/hooks';
import { usePackages } from '@uw/hook/request';

import { handleAntdSelectRender } from '../../rule.config';
import { DateCompare } from '../constant';

export const useColumns = () => {
  const { t } = useTranslation(['uw', 'common']);
  const [disabledValue, setDisabledValue] = useState(false);
  const packagesList = usePackages();
  const packages = useMemo(
    () =>
      packagesList?.map(pkg => ({
        label: pkg.itemName,
        value: pkg.dictValue,
      })),
    [packagesList]
  );

  const dateCompares = useBizDictByKey('dateCompare');
  const dateCompareMap = useDict('dateCompare')?.[0];

  const handleAddCol = useCallback(() => {
    setDisabledValue(false);
  }, []);

  const columns = useMemo(
    () => [
      {
        title: t('Renewal Reminder Date Compare to Policy Expiry Date'),
        dataIndex: 'dateCompare',
        key: 'dateCompare',
        editable: true,
        formItemProps: { rules: [] },
        fieldProps: {
          type: FieldType.Select,
          key: 'dateCompare',
          rules: [{ required: true, message: t('Please select') }],
          extraProps: {
            options: dateCompares,
            onChange: (value: string) => {
              setDisabledValue(value === DateCompare.SameAs);
            },
          },
        },
        render: (text: string) => <span>{dateCompareMap?.[text]}</span>,
      },
      {
        title: t('Package'),
        dataIndex: 'renewNoticePackageList',
        key: 'renewNoticePackageList',
        width: 400,
        editable: true,
        ellipsis: true,
        formItemProps: {
          rules: [{ required: true, message: t('Please select') }],
        },
        fieldProps: {
          placeholder: t('Please select'),
          type: FieldType.Select,
          extraProps: {
            options: packages,
            mode: 'multiple',
          },
        },
        render: (text: string[]) => {
          // 后端返回text为数字，但是packages中的value为string，handleAntdSelectRender对比不上，导致table渲染不出Package
          const packageList = text?.map(item => item?.toString());
          return (
            <Tooltip
              placement="topLeft"
              title={handleAntdSelectRender(packageList, packages!)}
            >
              {handleAntdSelectRender(packageList, packages!)}
            </Tooltip>
          );
        },
      },
      {
        title: t('Value'),
        dataIndex: 'dateValue',
        key: 'dateValue',
        editable: true,
        width: 240,
        formItemProps: {
          rules: [{ required: !disabledValue, message: t('Please input') }],
        },
        fieldProps: {
          placeholder: t('Please input'),
          type: disabledValue ? FieldType.Customized : FieldType.InputNumber,
          extraProps: {
            addonAfter: t('Msg_Days'),
            min: 1,
          },
          render: () => t('--'),
        },
        render: (value: string, record: INoticeConfigDetail) =>
          record.dateCompare === DateCompare.SameAs ? t('--') : value,
      },
    ],
    [t, dateCompares, packages, disabledValue]
  );

  return { columns, onAddCol: handleAddCol };
};
