/**
 *
 * <AUTHOR>
 * @since 2024-04-08 15:05
 */
import React, { type FC } from 'react';
import { useTranslation } from 'react-i18next';

import { usePermission } from '@uw/hook/permission';

import styles from '../NewBusinessConfiguration.module.scss';
import { InstallmentView } from './Installment/InstallmentView';
import { PremiumNoticeRule } from './PremiumNoticeRule/PremiumNoticeRule';
import { RenewalReminderRule } from './RenewalReminderRule/RenewalReminderRule';
import { useNoticeData } from './hooks/useNoticeData';

const PremiumNoticeReminder: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const hasRuleEditAuth = !!usePermission('configuration.renew.notice.edit');

  const [dataSource, requestData] = useNoticeData();

  return (
    <div className={styles.ruleWrapper}>
      <section className={styles.title}>{t('Notice Reminder')}</section>
      <section className={styles.ruleContentWrapper}>
        <InstallmentView
          hasRuleEditAuth={hasRuleEditAuth}
          data={dataSource?.renewNoticeConfigList}
          requestData={requestData}
        />
      </section>
      <section className={styles.ruleContentWrapper}>
        <PremiumNoticeRule
          hasRuleEditAuth={hasRuleEditAuth}
          data={dataSource?.nbNoticeConfigList}
          requestData={requestData}
        />
      </section>
      <section className={styles.ruleContentWrapper}>
        <RenewalReminderRule
          hasRuleEditAuth={hasRuleEditAuth}
          data={dataSource?.renewalNoticeConfigList}
          requestData={requestData}
        />
      </section>
    </div>
  );
};

export default PremiumNoticeReminder;
