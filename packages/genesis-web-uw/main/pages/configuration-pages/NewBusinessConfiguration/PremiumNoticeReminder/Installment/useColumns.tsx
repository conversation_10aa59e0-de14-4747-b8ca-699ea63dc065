/**
 *
 * <AUTHOR>
 * @since 2024-04-08 15:44
 */
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';

import { FieldType } from '@zhongan/nagrand-ui';

import type { INoticeConfigDetail } from 'genesis-web-service';

import { useBizDictByKeys, useDict } from '@uw/biz-dict/hooks';
import { usePackages } from '@uw/hook/request';

import { handleAntdSelectRender } from '../../rule.config';
import {
  DateCompare,
  DateCompareDictKey,
  DateCompareSourceDictKey,
} from '../constant';

export const useColumns = () => {
  const { t } = useTranslation(['uw', 'common']);
  const [disabledValue, setDisabledValue] = useState(false);

  const packagesList = usePackages();
  const packages = useMemo(
    () =>
      packagesList?.map(pkg => ({
        label: pkg.itemName,
        value: pkg.dictValue,
      })),
    [packagesList]
  );

  const [dateCompareOptions, dataCompareSourceOptions] = useBizDictByKeys([
    DateCompareDictKey,
    DateCompareSourceDictKey,
  ]);

  const [dateCompareMap, dataCompareSourceMap] = useDict([
    DateCompareDictKey,
    DateCompareSourceDictKey,
  ]);

  const handleAddCol = useCallback(() => {
    setDisabledValue(false);
  }, []);

  const columns = useMemo(
    () => [
      {
        title: t('Premium Notice Date'),
        dataIndex: 'dateCompareTitle',
        key: 'dateCompareTitle',
        editable: true,
        width: 400,
        formItemProps: { rules: [] },
        fieldProps: {
          type: FieldType.InputGroup,
          groupItems: [
            {
              key: 'dateCompare',
              type: FieldType.Select,
              rules: [{ required: true, message: t('Please select') }],
              extraProps: {
                options: dateCompareOptions,
                onChange: (value: string) => {
                  setDisabledValue(value === DateCompare.SameAs);
                },
              },
            },
            {
              key: 'dateCompareSource',
              type: FieldType.Select,
              rules: [{ required: true, message: t('Please select') }],
              extraProps: {
                options: dataCompareSourceOptions,
              },
            },
          ],
        },
        render: (_: string, record: Record<string, unknown>) => {
          const { dateCompare, dateCompareSource } = record;
          return (
            <div>
              <span>{dateCompareMap?.[dateCompare]}</span>
              {' - '}
              <span>{dataCompareSourceMap?.[dateCompareSource]}</span>
            </div>
          );
        },
      },
      {
        title: t('Package'),
        dataIndex: 'renewNoticePackageList',
        key: 'renewNoticePackageList',
        width: 400,
        editable: true,
        ellipsis: true,
        formItemProps: {
          rules: [{ required: true, message: t('Please select') }],
        },
        fieldProps: {
          placeholder: t('Please select'),
          type: FieldType.Select,
          extraProps: {
            options: packages,
            mode: 'multiple',
          },
        },
        render: (text: string[]) => (
          <Tooltip
            placement="topLeft"
            title={handleAntdSelectRender(text, packages!)}
          >
            {handleAntdSelectRender(text, packages!)}
          </Tooltip>
        ),
      },
      {
        title: t('Value'),
        dataIndex: 'dateValue',
        key: 'dateValue',
        editable: true,
        width: 240,
        formItemProps: {
          rules: [{ required: !disabledValue, message: t('Please input') }],
        },
        fieldProps: {
          placeholder: t('Please input'),
          type: disabledValue ? FieldType.Customized : FieldType.InputNumber,
          extraProps: {
            addonAfter: t('Msg_Days'),
            min: 1,
          },
          render: () => t('--'),
        },
        render: (value: string, record: INoticeConfigDetail) =>
          record.dateCompare === DateCompare.SameAs ? t('--') : value,
      },
    ],
    [t, dateCompareOptions, dataCompareSourceOptions, packages, disabledValue]
  );

  return { columns, onAddCol: handleAddCol };
};
