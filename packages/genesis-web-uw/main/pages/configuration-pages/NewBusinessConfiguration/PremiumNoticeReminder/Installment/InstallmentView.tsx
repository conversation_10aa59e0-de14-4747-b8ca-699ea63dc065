/**
 *
 * <AUTHOR>
 * @since 2024-04-08 15:45
 */
import React, { type FC, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { useDeepCompareEffect } from 'ahooks';

import { EditableTable, TextBody } from '@zhongan/nagrand-ui';

import {
  type INoticeConfigDetail,
  type INoticeConfigQuery,
  RenewService,
} from 'genesis-web-service';

import { InstallmentTypeEnum } from '@uw/interface/enum.interface';

import { DateCompare } from '../constant';
import { useDefaultRowData } from '../hooks/useDefaultRowData';
import { useDeleteRow } from '../hooks/useDeleteRow';
import { useValidatorRepeat } from '../hooks/useValidatorRepeat';
import { useColumns } from './useColumns';

interface IProps {
  hasRuleEditAuth: boolean;
  data?: INoticeConfigQuery['renewNoticeConfigList'];
  requestData: () => void;
}

export const InstallmentView: FC<IProps> = props => {
  const { hasRuleEditAuth, data = [], requestData } = props;
  const [t] = useTranslation(['uw', 'common']);

  const [dataSource, setDataSource] = useState<INoticeConfigDetail[]>([]);

  const { columns, onAddCol } = useColumns();

  useDeepCompareEffect(() => {
    const newData = data?.map(item => ({
      ...item,
      renewNoticePackageList: item.renewNoticePackageList?.map(pkg =>
        pkg.packageId?.toString()
      ),
    }));
    setDataSource(newData);
  }, [data]);

  const defaultAddData = useDefaultRowData(columns);
  const handleDel = useDeleteRow(dataSource, requestData);
  const validatorRepeat = useValidatorRepeat(t, dataSource);

  const handleSaveData = useCallback<
    (row: INoticeConfigDetail & { key?: number }) => Promise<boolean>
  >(
    async row => {
      const params = { ...row };
      delete params.key;
      if (params.dateCompare === DateCompare.SameAs) {
        params.dateValue = 0;
      }
      params.renewNoticePackageList = (
        params.renewNoticePackageList as unknown as number[]
      )?.map(packageId => ({
        packageId,
      }));
      // 做重复校验
      if (validatorRepeat(params)) return Promise.reject();
      try {
        const { noticeConfigId } = params;
        // 修改
        if (noticeConfigId) {
          delete params.noticeConfigId;
          await RenewService.updateNoticeConfig(noticeConfigId, params);
        } else {
          await RenewService.addNoticeConfig({
            ...params,
            bizType: InstallmentTypeEnum.INSTALLMENT,
          } as INoticeConfigDetail);
        }
        requestData();
      } catch (e) {
        message.error((e as Error)?.toString());
        return Promise.reject();
      }
    },
    [requestData, validatorRepeat]
  );

  const handleAdd = useCallback(() => {
    onAddCol();
  }, [onAddCol]);

  return (
    <EditableTable
      scroll={{ x: 'max-content' }}
      title={<TextBody weight={700}>{t('Installment')}</TextBody>}
      addBtnStyle={{ width: 'initial' }}
      pagination={false}
      needReserveEditing
      columns={columns}
      dataSource={dataSource}
      initializeAddData={defaultAddData}
      addBtnProps={{
        disabled: !hasRuleEditAuth,
        type: 'default',
        ghost: false,
        handleAdd,
      }}
      editBtnProps={{ disabled: () => !hasRuleEditAuth }}
      deleteBtnProps={{ handleDelete: idx => handleDel(idx) }}
      setDataSource={setDataSource}
      handleConfirm={handleSaveData}
    />
  );
};
