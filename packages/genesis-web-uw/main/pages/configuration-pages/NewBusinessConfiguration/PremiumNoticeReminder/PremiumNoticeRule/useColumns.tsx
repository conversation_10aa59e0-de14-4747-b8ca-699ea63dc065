import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';

import { FieldType } from '@zhongan/nagrand-ui';

import type { INoticeConfigDetail } from 'genesis-web-service';

import { useBizDictByKey, useDict } from '@uw/biz-dict/hooks';
import { usePackages } from '@uw/hook/request';

import { handleAntdSelectRender } from '../../rule.config';
import { DateCompare } from '../constant';

export const useColumns = () => {
  const { t } = useTranslation(['uw', 'common']);
  const [disabledValue, setDisabledValue] = useState(false);

  const packages = usePackages();

  const dateCompares = useBizDictByKey('dateCompare');
  const [dateCompareMap] = useDict('dateCompare');

  const handleAddCol = useCallback(() => {
    setDisabledValue(false);
  }, []);

  const columns = useMemo(
    () => [
      {
        title: t('Premium Notice Date Compare with Due Date'),
        dataIndex: 'dateCompare',
        key: 'dateCompare',
        width: 400,
        editable: true,
        formItemProps: { rules: [] },
        fieldProps: {
          type: FieldType.Select,
          key: 'dateCompare',
          rules: [{ required: true, message: t('Please select') }],
          extraProps: {
            options: dateCompares,
            onChange: (value: string) => {
              setDisabledValue(value === DateCompare.SameAs);
            },
          },
        },
        render: (text: string) => dateCompareMap?.[text],
      },
      {
        title: t('Package'),
        dataIndex: 'renewNoticePackageList',
        key: 'renewNoticePackageList',
        width: 400,
        editable: true,
        ellipsis: true,
        formItemProps: {
          rules: [{ required: true, message: t('Please select') }],
        },
        fieldProps: {
          placeholder: t('Please select'),
          type: FieldType.Select,
          extraProps: {
            options: packages,
            mode: 'multiple',
          },
        },
        render: (text: string[]) => (
          <Tooltip
            placement="topLeft"
            title={handleAntdSelectRender(text, packages!)}
          >
            {handleAntdSelectRender(text, packages!)}
          </Tooltip>
        ),
      },
      {
        title: t('Value'),
        dataIndex: 'dateValue',
        key: 'dateValue',
        editable: true,
        width: 240,
        formItemProps: {
          rules: [{ required: !disabledValue, message: t('Please input') }],
        },
        fieldProps: {
          placeholder: t('Please input'),
          type: disabledValue ? FieldType.Customized : FieldType.InputNumber,
          extraProps: {
            addonAfter: t('Msg_Days'),
            min: 1,
          },
          render: () => t('--'),
        },
        render: (value, record: INoticeConfigDetail) =>
          record.dateCompare === DateCompare.SameAs ? t('--') : value,
      },
    ],
    [t, dateCompares, packages, disabledValue, dateCompareMap]
  );

  return { columns, onAddCol: handleAddCol };
};
