import React, { FC, useMemo } from 'react';
import { Form, FormInstance, Row } from 'antd';
import { useTranslation } from 'react-i18next';

import { useBizDict } from '@uw/hook/useBizDict';
import { getFields } from '@uw/util/getFieldsQueryForm';

import { policyNumberGenerationFields } from './rule.config';
import styles from './NewBusinessConfiguration.module.scss';

interface Props {
  id: string;
  form: FormInstance;
  hasProposalEditAuth: boolean;
}

export const PolicyNumberGeneration: FC<Props> = ({
  id,
  form,
  hasProposalEditAuth,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const PolicyNumberGenerationEnum = useBizDict('policyNumberGenerationRule');

  const fields = useMemo(
    () =>
      policyNumberGenerationFields(
        PolicyNumberGenerationEnum,
        !hasProposalEditAuth
      ),
    [PolicyNumberGenerationEnum, hasProposalEditAuth]
  );
  return (
    <div id={id} key={id}>
      <section className={styles.ruleSubTitle}>
        {t('Policy Number Generation Rule')}
      </section>
      <section className="mt-[16px]">
        <Form form={form}>
          <Row>
            <Form.Item
              name="policyNumberGeneration"
              key="policyNumberGeneration"
              className="w-44"
            >
              {getFields(fields)}
            </Form.Item>
          </Row>
        </Form>
      </section>
    </div>
  );
};
