import React, { FC, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';
import { cloneDeep } from 'lodash-es';

import { AttachmentConfigList } from 'genesis-web-service';

import { useBizDict } from '@uw/hook/useBizDict';
import { useDict } from '@uw/hook/useDict';
import { ConfigTypeEnum } from '@uw/interface/enum.interface';

import styles from './NewBusinessConfiguration.module.scss';
import AttachmentEditTable from './components/AttachmentEditTable';
import {
  useAttachmentConfiguration,
  useAttachmentOperation,
} from './hooks/request';
import { quotationColumns } from './rule.config';

const AttachmentConfiguration: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();
  const [stageTypeMap] = useDict('stageType', 'dictValue');
  const { attachmentList, setAttachmentList, getAttachmentList } =
    useAttachmentConfiguration();
  const quotationList: (AttachmentConfigList & { key: string })[] = [];
  const proposalList: (AttachmentConfigList & { key: string })[] = [];
  attachmentList.forEach(item => {
    if (+item.stageType! === ConfigTypeEnum.Quotation) {
      quotationList.push({ ...item });
    } else if (+item.stageType! === ConfigTypeEnum.Proposal) {
      proposalList.push({ ...item });
    }
  });
  const quotationTitle = stageTypeMap[ConfigTypeEnum.Quotation];
  const proposalTitle = stageTypeMap[ConfigTypeEnum.Proposal];
  const goodsCategoryEnum = useBizDict('goodsCategory');
  const salesChannelEnum = useBizDict('salesChannelType');
  const scopeOfApplicationEnum = useBizDict('scopeOfApplication');
  const attachmentDocumentEnum = useBizDict('attachmentDocumentType');
  const [categoryId, setCategoryId] = useState<number>();
  const [goodsCode, setGoodsCode] = useState<string>();
  const { addAttachment, daleteAttachment } =
    useAttachmentOperation(getAttachmentList);
  const tableColumns = useMemo(
    () =>
      quotationColumns(
        goodsCategoryEnum,
        salesChannelEnum,
        scopeOfApplicationEnum,
        attachmentDocumentEnum
      ),
    [
      goodsCategoryEnum,
      salesChannelEnum,
      scopeOfApplicationEnum,
      attachmentDocumentEnum,
    ]
  );
  const [, setIsEditing] = useState(false);
  const handleSaveData = useCallback<
    (
      values: AttachmentConfigList & {
        key?: React.Key;
      },
      stageType: number,
      uid?: string,
      editTableMode?: string
    ) => void
  >((values, stageType) => {
    const newData = cloneDeep(values);
    if (newData?.key) {
      delete newData?.key;
    }

    newData.goods = values?.goods?.map(item => ({
      goodsCode: item?.goodsCode,
      packages: item?.packages?.map(packageItem => ({
        packageCode: packageItem,
      })),
    }));

    const submitData = { ...newData, stageType };
    addAttachment(submitData);
  }, []);

  const handleEdit = useCallback((row: AttachmentConfigList) => {
    setCategoryId(row?.goodsCategoryId);
  }, []);
  const handleTableDelete = useCallback(
    baseInfoId => {
      daleteAttachment(baseInfoId);
    },
    [attachmentList]
  );

  return (
    <>
      <section className={styles.title}>
        {t('Attachment Configuration')}
      </section>
      <section className={styles.attachmentContent}>
        <AttachmentEditTable
          tableColumns={tableColumns}
          title={quotationTitle as unknown as string}
          stageType={ConfigTypeEnum.Quotation}
          data={quotationList}
          setData={setAttachmentList}
          setIsEditing={setIsEditing}
          handleSaveData={handleSaveData}
          handleEdit={handleEdit}
          form={form}
          handleDel={handleTableDelete}
          categoryId={categoryId}
          setCategoryId={setCategoryId}
          goodsCode={goodsCode}
          setGoodsCode={setGoodsCode}
        />

        <AttachmentEditTable
          tableColumns={tableColumns}
          title={proposalTitle as unknown as string}
          stageType={ConfigTypeEnum.Proposal}
          data={proposalList}
          setData={setAttachmentList}
          setIsEditing={setIsEditing}
          handleSaveData={handleSaveData}
          handleEdit={handleEdit}
          form={form}
          handleDel={handleTableDelete}
          categoryId={categoryId}
          setCategoryId={setCategoryId}
          goodsCode={goodsCode}
          setGoodsCode={setGoodsCode}
        />
      </section>
    </>
  );
};

export default AttachmentConfiguration;
