@import '@uw/styles/variables.scss';
@import '@uw/styles/common.scss';

.rule-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .rule-content-wrapper {
    padding: $gap-lg;
    overflow: auto;
    > div {
      width: calc(100% - 200px);
    }
    .divider {
      margin: 48px 0 0;
      border: 1px dashed var(--border-default);
    }
    :global {
      .#{$antd-prefix}-form-item-label > label {
        font-weight: bold;
      }
    }
  }
  .anchor-wrapper {
    position: fixed;
    top: 96px;
    right: 16px;
    width: 200px;
  }
  :global {
    .#{$ant-prefix}-modal-mask,
    .#{$ant-prefix}-modal-wrap {
      z-index: 1100;
    }
  }
}

.rule-content {
  width: calc(100% - 248px - 32px);
  background: var(--white);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .title {
    font-size: $font-size-root;
    font-weight: 500;
    line-height: 16px;
    padding: $gap-md $gap-lg;
    border-bottom: 1px solid #d9e2ec;
  }
  .rule-title {
    padding: 14px 0;
    font-size: $font-size-lg;
    color: var(--text-color);
    font-weight: bold;
    line-height: 20px;
  }
  .rule-sub-title {
    margin: 14px 0;
    font-size: $font-size-root;
    color: var(--text-color);
    font-weight: bold;
    line-height: 20px;
  }
  .divider {
    width: 100%;
    height: 1px;
    border-top: 1px dashed var(--border-default);
  }
  .rule-type-content {
    padding: 0 0 $gap-big;
    border-bottom: 1px dashed var(--border-default);
  }
  .input-form-content {
    margin-top: $gap-md;
  }
  .proposal-title {
    margin: $gap-md 0;
    font-weight: 700;
    font-size: $font-size-root;
    line-height: 20px;
  }
  .desc-text {
    margin: var(--gap-xs) 0 $gap-md;
    font-size: $font-size-root;
    line-height: 20px;
  }
}

.proposal-content {
  :global {
    .antd-uw-col {
      .antd-uw-form-item {
        margin-bottom: 0;
      }
    }
  }
}
.hidden-dom {
  display: none;
}

.proposal-status-table {
  :global {
    .antd-uw-table-expand-icon-col {
      width: 0;
    }
  }
}

.reminder-table {
  :global(.ant-table-thead) {
    td {
      white-space: nowrap;
    }
  }
}
.random-check-wrapper {
  :global {
    .#{$antd-prefix}-checkbox-wrapper {
      margin-left: 0;
    }
  }
  .rule-content-wrapper {
    padding: $gap-lg;
    overflow: auto;
  }
  .random-check-title-content {
    display: flex;
    align-items: center;
    height: 80px;
    border-bottom: 1px solid var(--disabled-color);
  }
  .check-box-title-text {
    color: var(--text-color);
  }
  .radio-item-content {
    margin-bottom: 0;
    margin-left: $gap-md;
  }
  .check-box-line {
    .check-box-item {
      display: flex;
      margin-top: var(--gap-xs);
    }
    .check-box-item-no-margin {
      display: flex;
      .check-box-item-title {
        margin-top: var(--gap-xss);
      }
    }
  }
  .span-text {
    margin: var(--gap-xss) 13px 0;
    color: #000;
  }
  .symbol-icon {
    font-size: $font-size-lg;
    margin: 6px $gap-sm 0;
    svg {
      align-self: baseline;
    }
  }
  .symbol-text {
    font-size: $font-size-lg;
    color: var(--text-color);
    font-weight: 700;
  }
  .left-style {
    margin-right: 6px;
  }
  .right-style {
    margin-left: 6px;
  }
  .radius-one {
    width: 40%;
    :global {
      .#{$antd-prefix}-input {
        border-radius: var(--gap-xs) 0 0 var(--gap-xs);
        background-color: var(--form-addon-bg);
      }
    }
  }
  .radius-two {
    width: 60%;
    :global {
      .#{$antd-prefix}-input-group-wrapper {
        top: 0px;
      }

      .#{$antd-prefix}-input-group-addon:first-child {
        border-radius: 0;
      }
    }
  }
}

:export {
  gapXs: var(--gap-xs);
  fontSizeLg: $font-size-lg;
  gapLg: $gap-lg;
  gapMd: $gap-md;
  gapXss: var(--gap-xss);
  gapXs: var(--gap-xs);
  textColorTertiary: var(--text-color-tertiary);
  primaryColor: var(--primary-color);
}

.attachment-config-wrapper {
  :global {
    .#{$antd-prefix}-divider-plain::before {
      background-color: white;
      display: block;
      content: 'And';
      position: absolute;
      font-weight: bold;
      top: 50%;
      margin-top: -11px;
      left: -15px;
    }
    .#{$antd-prefix}-form-item-explain
      .#{$antd-prefix}-form-item-explain-error:nth-child(2) {
      display: none;
    }
    .#{$antd-prefix}-input-group-compact {
      > div:first-child {
        height: fit-content;
        .#{$antd-prefix}-select-selector {
          background-color: var(--form-addon-bg);
        }
      }
    }
  }
}

.attachment-table {
  background: var(--table-body-bg);
  margin: $gap-md 0;
  width: '100%';
}

.select-all-button {
  width: 100%;
  text-align: left;
  padding: 5px $gap-sm;
  font-weight: 500;
  font-size: $font-size-lg;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.check-out-line {
  float: right;
  position: absolute;
  right: $gap-sm;
  top: $gap-md;
}
.check-select-all-button,
.check-select-all-button:hover,
.check-select-all-button:focus {
  font-weight: 700;
  background-color: var(--select-item-selected-bg);
}

.attachment-content {
  padding: $gap-lg;
  overflow: auto;
}
