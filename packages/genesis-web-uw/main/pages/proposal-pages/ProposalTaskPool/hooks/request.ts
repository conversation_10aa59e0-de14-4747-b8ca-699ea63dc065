import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { PaginationProps } from 'antd';

import {
  ChannelService,
  OldPolicyService,
  QueryParams,
  QueryProposalRequest,
  QueryProposalResponse,
  QueryQuotationRequest,
  QueryQuotationResponse,
  QuotationQueryService,
  QuotationService,
} from 'genesis-web-service';

import { QueryStatus } from '@uw/components/QueryBiMap';
import { ErrorType } from '@uw/interface/common.interface';
import {
  BizDict,
  QueryPlaceholderEnum,
  QueryTypeEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

import { useSearchActions } from './useSearchActions';

// Proposal 条件查询
export const useQueryProposalByCondition = (
  setPagination: (pagination: PaginationProps) => void,
  domain: QueryTypeEnum
): [
  (
    newParams: QueryProposalRequest & Record<string, unknown>,
    queryParams: QueryParams,
    setQueryStatus?: Dispatch<SetStateAction<QueryStatus>>
  ) => void,
  QueryProposalResponse[],
  boolean,
] => {
  const [t] = useTranslation(['uw', 'common']);
  const [loading, setLoading] = useState<boolean>(false);
  const [policies, setPolicies] = useState<QueryProposalResponse[]>([]);

  const queryProposalList = useCallback<
    (
      newParams: QueryProposalRequest & Record<string, unknown>,
      queryParams: QueryParams,
      setQueryStatus?: Dispatch<SetStateAction<QueryStatus>>
    ) => void
  >(
    async (newParams, queryParams, setQueryStatus) => {
      const params = { ...(newParams || {}) };
      setLoading(true);
      try {
        const { content, totalElements } =
          await OldPolicyService.queryProposalByCondition(
            {
              ...params,
            },
            queryParams,
            domain === QueryTypeEnum?.QUOTATION ? YesOrNo?.YES : YesOrNo?.NO
          );
        if ((totalElements || 0) > 10000) {
          messagePopup(t('Msg_query_more10000'), 'error');
        } else {
          setPolicies(content);
          setPagination(originPagination => ({
            ...originPagination,
            pageSize: queryParams.size,
            current: queryParams.page + 1,
            total: totalElements,
          }));
          if (setQueryStatus && content.length === 0) {
            setQueryStatus('NoContent');
          }
        }
      } catch (e: unknown) {
        messagePopup((e as ErrorType).message || 'System Error', 'error');
      }
      setLoading(false);
    },
    [domain, setPagination, t]
  );

  return [queryProposalList, policies, loading];
};

// Quotation 条件查询
export const useQueryQuotationByCondition = (
  paginationParams: PaginationProps,
  setPagination: (pagination: PaginationProps) => void
): [
  (
    newParams: QueryQuotationRequest & Record<string, unknown>,
    queryParams: QueryParams,
    setQueryStatus?: Dispatch<SetStateAction<QueryStatus>>
  ) => void,
  QueryQuotationResponse[],
  boolean,
] => {
  const [loading, setLoading] = useState<boolean>(false);
  const [policies, setPolicies] = useState<QueryQuotationResponse[]>([]);

  const queryProposalList = useCallback<
    (
      newParams: QueryQuotationRequest & Record<string, unknown>,
      queryParams: QueryParams,
      setQueryStatus?: Dispatch<SetStateAction<QueryStatus>>
    ) => void
  >(async (newParams, queryParams, setQueryStatus) => {
    const params = { ...(newParams || {}) };
    setLoading(true);
    try {
      const { results, total } = await QuotationQueryService.queryQuotationList(
        {
          ...params,
        },
        queryParams
      );
      setPolicies(results);
      setLoading(false);
      setPagination(originPaginationParams => ({
        ...originPaginationParams,
        pageSize: queryParams.size,
        current: queryParams.page + 1,
        total,
      }));
      if (setQueryStatus && results.length === 0) {
        setQueryStatus('NoContent');
      }
    } catch (e: unknown) {
      messagePopup((e as ErrorType).message || 'System Error', 'error');
      setLoading(false);
    }
  }, []);

  return [queryProposalList, policies, loading];
};

// 获取channel

export const useQueryChannel = (operateType: QueryPlaceholderEnum) => {
  const [channelList, setChannelList] = useState<BizDict[]>();

  useEffect(() => {
    ChannelService.queryChannelsWithoutPageInfo({
      operateType,
    }).then(res => {
      const list = res?.value?.results?.map<BizDict>(item => ({
        enumItemName: item?.code,
        itemName: item.name,
        value: item.goodsId,
      }));
      setChannelList(list);
    });
  }, [operateType]);

  return channelList;
};

export const useBoundTask = () => {
  const [boundLoading, setLoading] = useState(false);
  const { handleQuoteBound } = useSearchActions();
  const boundTask = useCallback<(proposalNo: string) => void>(proposalNo => {
    setLoading(true);
    QuotationService.boundTask({
      issuanceNo: proposalNo,
    })
      .then(() => {
        handleQuoteBound(proposalNo);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);
  return { boundLoading, boundTask };
};

export const useIssueTask = () => {
  const [issueLoading, setLoading] = useState(false);
  const issueTask = useCallback<(proposalNo: string) => Promise<boolean>>(
    async proposalNo => {
      setLoading(true);
      try {
        await OldPolicyService.issueProposalTask(proposalNo);
        messagePopup(i18nFn('Issue successfully'), 'success');
        setLoading(false);
        return true;
      } catch (e) {
        messagePopup((e as Error).message, 'error');
        setLoading(false);
        return false;
      }
    },
    []
  );

  return {
    issueLoading,
    issueTask,
  };
};
