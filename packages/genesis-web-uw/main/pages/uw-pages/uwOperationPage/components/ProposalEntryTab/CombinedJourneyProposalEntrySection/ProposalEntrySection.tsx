import { forwardRef, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { useAtomValue } from 'jotai';

import { PageTemplateTypes } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { ProposalEntryStateProvider } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntry.Provider';
import { ProposalEntryDetailContext } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntryDetailProvider';
import { ProposalEntryLayout } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntryLayout';
import { queryDetailMetaDataAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { useProposalValidation } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useProposalValidation';
import { useUwOperationRef } from '@uw/pages/uw-pages/uwOperationPage/UwOperation.Provider';

import { useProposalEntrySection } from '../hooks/useCombinedJourneyProposalEntrySection';

interface Props {
  applicationNo?: string;
  policyNo?: string;
  proposalNo?: string;
  hasEditAuth: boolean;
  goodsCategoryId?: number;
  setIsEditing?: SetState<boolean>;
}

export type ProposalEntryRef = { handleSave: () => void };
export const CombinedJourneyProposalEntrySection = forwardRef(
  ({
    applicationNo,
    policyNo,
    proposalNo,
    hasEditAuth,
    setIsEditing,
  }: Props) => {
    const { state, dispatch, proposalDetail, handleSave, form, detailLoading } =
      useProposalEntrySection({
        applicationNo,
        policyNo,
        proposalNo,
        setIsEditing,
      });

    const { validateSaveValues } = useProposalValidation(state);

    const actionRef = useUwOperationRef();
    const queryDetailMetaData = useAtomValue(queryDetailMetaDataAtom);
    const [isUw, setIsUw] = useState(false);

    const { pathname } = useLocation();
    useEffect(() => {
      if (pathname.includes('/uw-operation/')) {
        setIsUw(true);
      }
    }, []);

    useEffect(() => {
      actionRef.current = {
        handleSave,
        validateSaveValues,
      };
    }, [handleSave, validateSaveValues]);

    const pageType = (queryDetailMetaData?.pageCode ??
      proposalDetail?.pageCode) as PageTemplateTypes.PageType;
    return (
      <ProposalEntryDetailContext.Provider
        value={{
          state,
          dispatch,
        }}
      >
        <ProposalEntryStateProvider
          form={form}
          detail={proposalDetail}
          detailLoading={detailLoading}
        >
          {pageType && (
            <ProposalEntryLayout
              hasEditAuth={hasEditAuth}
              asComponent={true}
              applicationNo={applicationNo}
              form={form}
              pageType={pageType}
              proposalDetail={proposalDetail}
              isUw={isUw}
            />
          )}
        </ProposalEntryStateProvider>
      </ProposalEntryDetailContext.Provider>
    );
  }
);
