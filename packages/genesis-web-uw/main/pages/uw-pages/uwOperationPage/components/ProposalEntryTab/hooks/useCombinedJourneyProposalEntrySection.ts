import { useCallback, useEffect, useReducer } from 'react';
import { useSearchParams } from 'react-router-dom';

import { Form } from 'antd';

import { useAtomValue, useSetAtom } from 'jotai';

import {
  PolicyService,
  ProposalCombinedTypes,
  ProposalEntryTypes,
  SystemService,
  UnderwritingService,
} from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { LogLevel } from '@uw/interface/enum.interface';
import {
  ProposalEntryDetailAction,
  ProposalEntryDetailReducer,
  ProposalEntryDetailState,
  initialContextValue,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/ProposalEntryDetailProvider';
import {
  multiGoodsFeeConfigAtom,
  pageCodeAtom,
  temporaryIdAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { useGetSaveValues } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useGetSaveValues';
import { useProposalValidation } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useProposalValidation';
import { useQueryProposalDetail } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/hooks/useQueryProposalDetail';
import { usePremiumFeeConfig } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/SPECIFIC_INFO/sub-sections/COVERAGE_PLAN/hooks/usePremiumFeeConfig';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

export const useProposalEntrySection = ({
  applicationNo,
  policyNo,
  proposalNo,
  setIsEditing,
}: {
  applicationNo?: string;
  proposalNo?: string;
  policyNo?: string;
  setIsEditing?: SetState<boolean>;
}) => {
  const [searchParams] = useSearchParams();
  const isTemp = searchParams.get('isTemp');
  const [form] = Form.useForm();
  const multiGoodsFeeConfig = useAtomValue(multiGoodsFeeConfigAtom);
  const policyVersion = searchParams.get('policyVersion')!;

  const getProposalInfo = useCallback(
    // 如果在核保中，调用核保combine的接口
    () => {
      if (applicationNo) {
        return UnderwritingService.queryCombinedProposalDetail(applicationNo);
      }
      if (policyNo) {
        return PolicyService.getPolicyDetail(policyNo, policyVersion);
      }
      if (proposalNo) {
        return PolicyService.getProposalDetail(proposalNo, isTemp);
      }
    },
    [applicationNo, policyVersion]
  );
  const { patchPremiumData } = usePremiumFeeConfig(undefined);
  const setTemporaryId = useSetAtom(temporaryIdAtom);
  const setPageCode = useSetAtom(pageCodeAtom);

  const [state, dispatch] = useReducer<
    React.Reducer<ProposalEntryDetailState, ProposalEntryDetailAction>
  >(ProposalEntryDetailReducer, initialContextValue);
  const { validateBeneficiary } = useProposalValidation(state);

  const {
    data: proposalDetail,
    mutate: hanldeProposalInfo,
    isValidating: detailLoading,
  } = useQueryProposalDetail({
    getProposalInfo,
  });

  useEffect(() => {
    if (proposalDetail?.temporaryId) {
      setTemporaryId(proposalDetail.temporaryId);
    }
  }, [proposalDetail]);

  useEffect(() => {
    if (proposalDetail?.pageCode) {
      setPageCode(proposalDetail?.pageCode);
    }
  }, [proposalDetail?.pageCode]);

  const { getSaveValues } = useGetSaveValues();

  const handleSave = useCallback(
    async ({
      onlySave = false,
      needValidate = false,
    }: {
      onlySave?: boolean;
      needValidate?: boolean;
    } = {}) => {
      let savedValues: ProposalCombinedTypes.SaveCombinedProposalRequestBody;
      let patchPremiumValues:
        | ProposalCombinedTypes.SaveCombinedProposalRequestBody
        | undefined;
      try {
        savedValues = getSaveValues({
          form,
          detail: proposalDetail,
        });

        if (needValidate) {
          await validateBeneficiary(savedValues);
        }
        if (savedValues) {
          patchPremiumValues = await patchPremiumData(
            multiGoodsFeeConfig || [],
            savedValues
          );

          await UnderwritingService.saveCombinedProposalDetail(
            applicationNo,
            patchPremiumValues
          );
          if (!onlySave) {
            messagePopup(i18nFn('Save successfully'), 'success');
            await hanldeProposalInfo();
          }
        }
      } catch (err) {
        SystemService.traceLog({
          logLevel: LogLevel.Info,
          data: [
            `browser-log-savedValues: ${JSON.stringify(savedValues)} ############# browser-log-patchPremiumValues: ${JSON.stringify(patchPremiumValues)}`,
          ],
        });
        if (onlySave) {
          throw err;
        }
        messagePopup(err instanceof Error ? err.message : String(err), 'error');
      } finally {
        setIsEditing?.(false);
      }
    },
    [
      getSaveValues,
      form,
      proposalDetail,
      patchPremiumData,
      multiGoodsFeeConfig,
      applicationNo,
      hanldeProposalInfo,
      setIsEditing,
    ]
  );

  return {
    state,
    dispatch,
    proposalDetail,
    form,
    handleSave,
    detailLoading,
  };
};
