import { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Space, Spin } from 'antd';

import cls from 'clsx';
import { isUndefined } from 'lodash-es';

import { cssVars } from '@zhongan/nagrand-ui';

import { BasicInfoData, YesOrNo } from 'genesis-web-service';

import { useUwOperationRef } from '@uw/pages/uw-pages/uwOperationPage/UwOperation.Provider';
import { motor } from '@uw/util/goodsCategoryClassify';

import { CombinedJourneyProposalEntrySection } from './CombinedJourneyProposalEntrySection';
import { ProposalEntrySection } from './ProposalEntrySection';
import Info from './assets/info.svg';
import { useProposalEntryTab } from './hooks/useProposalEntryTab';
import styles from './index.module.scss';

interface Props {
  applicationNo: string;
  isWorkSheet?: boolean;
  goodsCategoryId?: number;
  isCombinedJourney?: boolean;
  editable?: boolean;
  basicInfo?: BasicInfoData;
}

export const ProposalEntryTab: FC<Props> = ({
  applicationNo,
  isWorkSheet = false,
  goodsCategoryId,
  isCombinedJourney,
  editable,
  basicInfo,
}) => {
  const { t } = useTranslation(['uw', 'common']);

  const {
    isEditing,
    hasEditAuth,
    handleEdit,
    handleCancelEdit,
    setIsEditing,
    loading,
  } = useProposalEntryTab();
  const actionRef = useUwOperationRef();
  const isNewProposalEntryTab = useMemo(
    () => motor.some(item => goodsCategoryId === +item) || isCombinedJourney,
    [goodsCategoryId, isCombinedJourney]
  );
  const isReferralTask = basicInfo?.referralTask === YesOrNo.YES;

  const workSheetEditInfo = useMemo(() => {
    if (!hasEditAuth) {
      return null;
    }
    return (
      <section className="my-xs mx-big py-md px-md bg-infoBg flex rounded-big">
        <Info />
        <span className="text-infoTextDark ml-sm">
          {t(
            'You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.'
          )}
        </span>
        <span
          className="text-infoTextDark underline cursor-pointer font-semibold ml-xs"
          onClick={handleEdit}
        >
          {' '}
          {t('Edit Now')}→
        </span>
      </section>
    );
  }, [handleEdit, hasEditAuth, t]);

  const editInfo = useMemo(() => {
    if (!hasEditAuth) {
      return null;
    }
    return (
      <section className={styles.editInfo}>
        {t(
          'You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.'
        )}
        <span className={styles.editButton} onClick={handleEdit}>
          {' '}
          {t('Edit Now')}→
        </span>
      </section>
    );
  }, [handleEdit, hasEditAuth, t]);

  const submitArea = useMemo(() => {
    if (!isEditing || !hasEditAuth) {
      return null;
    }
    return (
      <Space
        style={{ marginLeft: cssVars.gapBig, marginBottom: cssVars.gapLg }}
      >
        <Button
          disabled={!hasEditAuth}
          type="primary"
          onClick={() => actionRef?.current?.handleSave?.()}
          style={{ marginRight: cssVars.gapXs }}
        >
          {t('Save')}
        </Button>
        <Button disabled={!hasEditAuth} onClick={handleCancelEdit}>
          {t('Cancel')}
        </Button>
      </Space>
    );
  }, [isEditing, hasEditAuth, t, handleCancelEdit, actionRef]);

  return (
    <section
      className={cls([
        styles.proposalEntryTab,
        'proposal-entry-data-container',
      ])}
    >
      {!isNewProposalEntryTab && (isWorkSheet ? workSheetEditInfo : editInfo)}
      <Spin spinning={loading}>
        {!loading && !isUndefined(isCombinedJourney) && (
          <>
            {isCombinedJourney ? (
              <CombinedJourneyProposalEntrySection
                applicationNo={applicationNo}
                hasEditAuth={hasEditAuth && editable && !isReferralTask}
                setIsEditing={setIsEditing}
                goodsCategoryId={goodsCategoryId}
              />
            ) : (
              <ProposalEntrySection
                applicationNo={applicationNo}
                isEditing={isEditing}
                hasEditAuth={hasEditAuth && editable && !isReferralTask}
                setIsEditing={setIsEditing}
                goodsCategoryId={goodsCategoryId}
              />
            )}
            {!isNewProposalEntryTab && submitArea}
          </>
        )}
      </Spin>
    </section>
  );
};
