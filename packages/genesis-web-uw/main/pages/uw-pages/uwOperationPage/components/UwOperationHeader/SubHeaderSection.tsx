import React, { FC, useMemo } from 'react';
import { Row, Col, Tag } from 'antd';
import { Table } from '@zhongan/nagrand-ui';
import { ColumnType } from 'antd/es/table';
import {
  BasicInfoData,
  TaskTypeEnum,
  RuleResult,
  BizDictItem,
  QuotationInfoType,
  QuotationProductInfoType,
  MultiCurrency,
} from 'genesis-web-service';
import { getAmountCurrencyString } from '@uw/util/formatAmountCurrency';
import { formatDateTime } from '@uw/util/queryDate';
import {
  convertStatus,
  ComponentWithFallback,
} from 'genesis-web-component/lib/components';
import { amountFormatInstance } from 'genesis-web-shared/lib/l10n';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useDict } from '@uw/hook/useDict';
import {
  PremiumPeriodTypeEnum,
  CoveragePeriodTypeEnum,
} from '@uw/interface/enum.interface';
import { selectEnums } from '@uw/redux/selector';
import { GoodsCategoryIcon } from 'genesis-web-component/lib/components/GoodsCategoryIcon';

import { useBizDict } from '@uw/hook/useBizDict';

import { concurrentCaseColumns } from './page.config';

import { useConcurrentCase } from './hooks';
import styles from '../../UwOperationPage.module.scss';

interface Props {
  basicInfo: BasicInfoData;
  ruleResult: RuleResult[];
  quotationInfo?: QuotationInfoType;
  currency: string;
  multiCurrency?: MultiCurrency;
}

export const UwOperationSubHeaderSection: FC<Props> = ({
  basicInfo,
  ruleResult,
  quotationInfo,
  currency,
  multiCurrency,
}) => {
  const enums = useSelector(selectEnums);
  const premiumPeriod = basicInfo?.premiumPeriod;
  const { concurrentCaseList } = useConcurrentCase(
    basicInfo?.taskId?.toString()
  );
  const uwTaskTypeEnums = useBizDict('UwTaskTypeEnum');
  const certiTypeEnums = useBizDict('certiType');
  const [t] = useTranslation(['uw', 'common']);
  const [PosItem] = useDict('customerServiceItem');
  const zoneId = useMemo(() => basicInfo?.zoneId, [basicInfo]);

  const issuancePremiumObject = quotationInfo?.issuanceInfo.issuancePremium;

  const coveragePeriodType = useMemo(
    () =>
      enums?.coveragePeriodType?.find(
        type => type.enumItemName === basicInfo.coveragePeriodTypeEnum
      )?.dictValueName,
    [enums, basicInfo]
  );

  const payPeriodType = useMemo(
    () =>
      enums?.premiumPeriodType?.find(
        type => type.enumItemName === basicInfo.premiumPeriodType
      )?.dictValueName,
    [enums, basicInfo]
  );

  const basicInfoColumns: ColumnType<QuotationProductInfoType>[] = useMemo(
    () => [
      {
        title: t('Product Name'),
        dataIndex: 'productName',
        ellipsis: true,
      },
      {
        title: t('Product Category'),
        dataIndex: 'productCategoryId',
        ellipsis: true,
        render: (text: number) => (
          <ComponentWithFallback>
            {convertStatus(
              enums as Record<string, BizDictItem[]>,
              String(text),
              'productCategory'
            )}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Sum Assured'),
        dataIndex: 'sumAssured',
        ellipsis: true,
        render: (text: string) =>
          amountFormatInstance?.getAmountCurrencyString(
            text,
            multiCurrency?.saCurrency || (currency as string)
          ),
      },
    ],
    [t, enums, multiCurrency?.saCurrency, currency]
  );

  const concurrentColumns = useMemo(
    () => concurrentCaseColumns(uwTaskTypeEnums, certiTypeEnums),
    [uwTaskTypeEnums, certiTypeEnums]
  );

  return (
    <section className={styles.basicInfoDetailWrapper}>
      <section className={styles.basicInfoDetail}>
        <GoodsCategoryIcon
          iconKey={basicInfo?.goodsCategoryId?.toString()}
          goodsCategory={basicInfo?.goodsCategoryId}
          style={{
            marginRight: styles.gapXs,
            fontSize: '80px',
          }}
        />
        <div className={styles.basicInfoDetailCenter}>
          <section className={styles.infoSectionBox}>
            <div className={styles.infoSectionTitle}>
              {t('Goods Name')}: {basicInfo?.goodsName}
            </div>
            <Row>
              <Col span={8}>
                <div className={styles.contentFieldBox}>
                  <div className={styles.contentFieldTitle}>
                    {t('Policy Effective Date')}
                  </div>
                  <div>
                    {formatDateTime(basicInfo?.policyEffectiveDate, zoneId)}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.contentFieldBox}>
                  <div className={styles.contentFieldTitle}>
                    {t('Coverage Period')}
                  </div>
                  <div>
                    {basicInfo?.coveragePeriodTypeEnum ===
                    CoveragePeriodTypeEnum.USERINPUT
                      ? t('--')
                      : `${
                          basicInfo?.coveragePeriodValue ?? ''
                        } (${coveragePeriodType})`}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.contentFieldBox}>
                  <div className={styles.contentFieldTitle}>
                    {t('Premium Period')}
                  </div>
                  {basicInfo?.premiumPeriodType ===
                  PremiumPeriodTypeEnum.SINGLE ? (
                    <div>{payPeriodType}</div>
                  ) : (
                    <div>
                      {premiumPeriod
                        ? `${premiumPeriod} (${payPeriodType})`
                        : t('--')}
                    </div>
                  )}
                </div>
              </Col>
              {!!quotationInfo?.issuanceInfo?.productInfoList?.length && (
                <Col span={24}>
                  <Table
                    className={styles.basicInfoTable}
                    columns={basicInfoColumns}
                    dataSource={quotationInfo?.issuanceInfo?.productInfoList}
                    pagination={false}
                    style={{ width: '100%' }}
                  />
                </Col>
              )}
              {issuancePremiumObject?.periodNetPremium && (
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>
                      {t('Net Premium')}
                    </div>
                    <div>
                      {getAmountCurrencyString(
                        issuancePremiumObject?.periodNetPremium as string,
                        currency
                      )}
                    </div>
                  </div>
                </Col>
              )}
              {issuancePremiumObject?.periodTotalTax && (
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>{t('Tax')}</div>
                    <div>
                      {getAmountCurrencyString(
                        issuancePremiumObject?.periodTotalTax as string,
                        currency
                      )}
                    </div>
                  </div>
                </Col>
              )}
              {issuancePremiumObject?.periodFinalPremium && (
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>
                      {t('Actual Payable Amount')}
                    </div>
                    <div>
                      {getAmountCurrencyString(
                        issuancePremiumObject?.periodFinalPremium as string,
                        currency
                      )}
                      {multiCurrency &&
                      multiCurrency?.saCurrency !==
                        multiCurrency?.premiumCurrency
                        ? ` (${multiCurrency?.saCurrency} ${issuancePremiumObject?.baseCurrencyPeriodNetPremium})`
                        : ''}
                    </div>
                  </div>
                </Col>
              )}
            </Row>
          </section>
          {basicInfo?.taskType === TaskTypeEnum.POS && (
            <section className={styles.infoSectionBox}>
              <Row>
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>
                      {t('POS Item')}
                    </div>
                    <div>
                      {basicInfo.posItemList
                        ? basicInfo.posItemList?.map(item => (
                            <div>{PosItem[item]}</div>
                          ))
                        : t('--')}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>
                      {t('POS Application Date')}
                    </div>
                    <div>
                      {formatDateTime(
                        basicInfo.posApplicationDate || '',
                        zoneId
                      )}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles.contentFieldBox}>
                    <div className={styles.contentFieldTitle}>
                      {t('POS Effective Date')}
                    </div>
                    <div>
                      {formatDateTime(basicInfo.posEffectiveDate || '', zoneId)}
                    </div>
                  </div>
                </Col>
              </Row>
            </section>
          )}
          {concurrentCaseList?.[0] && (
            <div>
              <div>{t('Concurrent Case')}</div>
              <Col span={24}>
                <Table
                  className={styles.basicInfoTable}
                  columns={concurrentColumns}
                  dataSource={concurrentCaseList}
                  pagination={false}
                  style={{ width: '100%' }}
                  scroll={{ x: 'max-content' }}
                />
              </Col>
            </div>
          )}
        </div>
        <div className={styles.basicInfoRule}>
          <div className={styles.infoSectionTitle}>
            {t('Auto UW Rule Result')}
          </div>
          {ruleResult?.map(rule => (
            <div>
              <Tag
                color={styles.InfoColorBg}
                style={{
                  marginBottom: styles.gapXs,
                  height: styles.gapLg,
                  lineHeight: styles.gapLg,
                  color: styles.TextColor,
                }}
              >
                {rule?.notice}
              </Tag>
            </div>
          ))}
        </div>
      </section>
    </section>
  );
};
