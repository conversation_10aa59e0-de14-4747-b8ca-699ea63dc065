import { Ref, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';

import { FormInstance } from 'antd';

import {
  BasicInfoData,
  ConcurrentCaseType,
  DecisionListType,
  TaskDetailResponseData,
  TaskTypeEnum,
  UnderwritingService,
} from 'genesis-web-service';

import { usePermission } from '@uw/hook/permission';
import { ErrorType, SetState } from '@uw/interface/common.interface';
import {
  ComponentButtonType,
  Mode,
  QuotationOperateType,
  UwTaskStatusEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { useUwOperationRef } from '@uw/pages/uw-pages/uwOperationPage/UwOperation.Provider';
import { selectUserId } from '@uw/redux/selector';
import { messagePopup } from '@uw/util/messagePopup';

import styles from '../../UwOperationPage.module.scss';
import { ButtonType, ButtonsProps } from '../../interface';
import { RefAdHocNotificationDrawerProps } from '../AdHocNotification/AdHocNotificationDrawer';

const poolRoute = '/uw/task-pool';
const posRoute = '/pos-online-frontend/task-pool';
const decisionRoute = '/uw/uw-decision';

enum SendBackType {
  POOL = 'POOL',
  POS = 'POS',
}

export const useGetUwHeaderButtons = (
  basicInfo: BasicInfoData,
  queryEscalateUsers: () => void,
  setLoading: SetState<boolean>,
  setQuotationLoading: SetState<boolean>,
  queryUwTaskDetail: () => Promise<void>,
  queryQuotationInfo: () => void,
  saveQuotationLoading: boolean,
  handleQuotationOperate: (type: QuotationOperateType) => void,
  setShowReturnVisible: SetState<boolean>,
  setModalVisible: SetState<boolean>,
  setDecisionList: SetState<DecisionListType[] | undefined>,
  setNotifyhistoryOpen: SetState<boolean>,
  returnForm?: FormInstance,
  taskInfo?: TaskDetailResponseData
): {
  buttons: ButtonsProps;
  AdHocNotificationDrawerRef: Ref<RefAdHocNotificationDrawerProps>;
} => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const { mode, taskId, source } = useParams();
  const [optMode, setOptMode] = useState(mode);
  const [adHocButtonloading, setAdHocButtonLoading] = useState(false);

  const AdHocNotificationDrawerRef = useRef<RefAdHocNotificationDrawerProps>();

  const hasEditAuth = !!usePermission('uw.manual.task.edit');
  const hasSendBackAuth = !!usePermission('uw.manual.task.send-back');
  const userId = useSelector(selectUserId);
  const isMyTask = basicInfo.currentHandler === userId;
  const isQueryPage = !!source;
  const isEditMode = optMode === Mode.Edit;
  const isViewMode = optMode === Mode.View;
  const isWaitingForProcess =
    basicInfo?.status === UwTaskStatusEnum.WAITING_FOR_PROCESS;
  const inProcessStatus = basicInfo?.status === UwTaskStatusEnum.UW_IN_PROCESS;
  const isEffectiveStatus = basicInfo?.status === UwTaskStatusEnum.CLOSED;
  const isClaim = basicInfo?.taskType === TaskTypeEnum.CLAIM;

  const canShowBtn = hasEditAuth && isEditMode && !isQueryPage;
  const withdrawedByOtherProcess = taskInfo?.taskPoolNoticeType;
  const actionRef = useUwOperationRef();

  const handleGetDecisionList = useCallback(() => {
    UnderwritingService.getDecisionList(taskId).then(res => {
      if (res) {
        setModalVisible(true);
        setDecisionList(res);
      }
    });
  }, [setDecisionList, setModalVisible, taskId]);

  const handleShowReturn = useCallback(() => {
    setShowReturnVisible(true);
    returnForm?.resetFields();
  }, [setShowReturnVisible, returnForm]);

  const handleSave = useCallback(async () => {
    if (actionRef?.current?.handleSave) {
      await actionRef?.current?.handleSave?.();
    } else {
      await handleQuotationOperate(QuotationOperateType.SAVE);
    }
  }, [actionRef, handleQuotationOperate]);

  const handleUwDecision = useCallback(async () => {
    await actionRef?.current?.validateSaveValues?.();

    await handleSave();

    navigate(
      source
        ? `${decisionRoute}/${mode}/${taskId}/${source}`
        : `${decisionRoute}/${mode}/${taskId}`
    );
  }, [actionRef, handleSave, mode, navigate, source, taskId]);

  const handleMenuClick = useCallback(
    ({ key }) => {
      let backType = SendBackType.POOL;
      if (key === posRoute) {
        backType = SendBackType.POS;
      }
      UnderwritingService.sendBackTask(taskId, backType).then(res => {
        if (res) {
          navigate(poolRoute); // 这块不区分了,都跳回UW task pool
        }
      });
    },
    [navigate, taskId]
  );

  const showSendBackToOriginUWer = useMemo(
    () =>
      basicInfo?.escalationTaskFlag === YesOrNo.YES &&
      !!basicInfo?.escalateHandlerId,
    [basicInfo?.escalateHandlerId, basicInfo?.escalationTaskFlag]
  );

  const handleClaimTask = useCallback(() => {
    setLoading(true);
    setQuotationLoading(true);
    UnderwritingService.queryUWClaimTask(taskId)
      .then(async () => {
        await queryUwTaskDetail();
        queryQuotationInfo();
        setOptMode(Mode.Edit);
        navigate(
          source
            ? `/uw/uw-operation/${Mode.Edit}/${taskId}/${source}`
            : `/uw/uw-operation/${Mode.Edit}/${taskId}`,
          { replace: true }
        );
        messagePopup(t('Process Successfully'), 'success');
      })
      .catch(() => {
        messagePopup(t('Process Failed'), 'error');
      });
  }, [
    setLoading,
    setQuotationLoading,
    taskId,
    queryUwTaskDetail,
    queryQuotationInfo,
    navigate,
    source,
    t,
  ]);

  const handleSendBackToOriginUWer = useCallback(() => {
    UnderwritingService.sendBackToOriginUWer(taskId as unknown as number)
      .then(res => {
        if (res) {
          navigate(poolRoute); // 这块不区分了,都跳回UW task pool
        }
      })
      .catch((e: unknown) => {
        messagePopup((e as ErrorType).message?.toString?.(), 'error');
      });
  }, [basicInfo?.escalateHandlerId, navigate, taskId]);

  const openDrawer = useCallback(() => {
    try {
      setAdHocButtonLoading(true);
      AdHocNotificationDrawerRef.current?.openDrawer();
    } finally {
      setAdHocButtonLoading(false);
    }
  }, []);

  const buttons: ButtonsProps = {
    [ButtonType.SendBackToOriginUWer]: {
      text: t('Send Back to Origin UWer'),
      show: isEditMode && showSendBackToOriginUWer,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      onClick: handleSendBackToOriginUWer,
      others: {
        style: { marginLeft: styles.gapMd },
      },
    },
    [ButtonType.Process]: {
      show: isViewMode && isWaitingForProcess && !isQueryPage,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      text: t('Process'),
      onClick: handleClaimTask,
      others: {
        type: 'primary',
        style: { marginLeft: styles.gapMd },
      },
    },
    [ButtonType.SendBackToUwPool]: {
      show: canShowBtn && isMyTask && hasSendBackAuth,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      text: t('Send Back to UW Pool'),
      onClick: () =>
        handleMenuClick({
          key: source ? `${poolRoute}/${source}` : poolRoute,
        }),
      others: {
        style: { marginLeft: styles.gapMd },
      },
    },
    [ButtonType.Return]: {
      show: canShowBtn && isMyTask && isClaim,
      text: t('Return'),
      others: {
        style: { marginLeft: styles.gapMd },
        type: 'default',
        danger: true,
      },
      onClick: () => handleShowReturn(),
    },
    [ButtonType.PendingCase]: {
      show: inProcessStatus && !isQueryPage && isMyTask,
      disabled: !!withdrawedByOtherProcess,
      showType: ComponentButtonType.PendingCase,
      others: {},
    },
    [ButtonType.Escalate]: {
      show: canShowBtn && isMyTask,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      text: t('Escalate or Reassign'),
      onClick: queryEscalateUsers,
      others: {
        style: { marginLeft: styles.gapMd },
        type: 'primary',
        ghost: true,
      },
    },
    [ButtonType.NotificationHistory]: {
      show: canShowBtn && isMyTask,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      text: t('Notification History'),
      onClick: () => setNotifyhistoryOpen(true),
      others: {
        style: { marginLeft: styles.gapMd },
        type: 'primary',
        ghost: true,
      },
    },
    [ButtonType.AdHocNotification]: {
      show: canShowBtn && isMyTask,
      disabled: !!withdrawedByOtherProcess || isEffectiveStatus,
      text: t('Ad-hoc Notification'),
      onClick: openDrawer,
      others: {
        style: { marginLeft: styles.gapMd },
        type: 'primary',
        ghost: true,
        loading: adHocButtonloading,
      },
    },
    [ButtonType.Save]: {
      show: canShowBtn && isMyTask,
      disabled:
        !!withdrawedByOtherProcess || saveQuotationLoading || isEffectiveStatus,
      text: t('Save'),
      onClick: handleSave,
      others: {
        loading: saveQuotationLoading,
        style: { marginLeft: styles.gapMd },
        type: 'primary',
        ghost: true,
      },
    },
    [ButtonType.Decision]: {
      show: isMyTask || isQueryPage,
      disabled:
        (!!withdrawedByOtherProcess && !isMyTask) ||
        (isEffectiveStatus && !isQueryPage),
      text: t('UW Decision'),
      others: {
        style: { marginLeft: styles.gapMd },
        type: 'primary',
      },
      onClick: () => (isClaim ? handleGetDecisionList() : handleUwDecision()),
    },
  };

  return {
    buttons,
    AdHocNotificationDrawerRef:
      AdHocNotificationDrawerRef as Ref<RefAdHocNotificationDrawerProps>,
  };
};

export const useConcurrentCase = (
  taskId: string
): { concurrentCaseList: ConcurrentCaseType[] } => {
  const [concurrentCaseList, setConcurrentCaseList] = useState<
    ConcurrentCaseType[]
  >([]);

  useEffect(() => {
    if (taskId) {
      UnderwritingService.getConcurrentCaseList(taskId).then(res => {
        setConcurrentCaseList(res);
      });
    }
  }, [taskId]);

  return { concurrentCaseList };
};
