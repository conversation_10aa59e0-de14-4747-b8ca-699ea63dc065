import {
  FC,
  type PropsWithChildren,
  createContext,
  useContext,
  useRef,
} from 'react';

import { Provider } from 'jotai';

type AsyncFunctionType = (params?: {
  onlySave?: boolean;
  needValidate?: boolean;
}) => Promise<void>;

type ValidateSaveValues = (hideHash?: boolean) => Promise<unknown>;

const noop: AsyncFunctionType = (params?) => Promise.resolve(undefined);

export const UwOperationRefContext = createContext({
  current: {
    handleSave: noop,
    validateSaveValues: noop,
  },
});

export function useUwOperationRef(): {
  current: {
    handleSave: AsyncFunctionType;
    validateSaveValues?: ValidateSaveValues;
  };
} {
  return useContext(UwOperationRefContext);
}

/**
 * 只共享录单页面的handleSave方法
 * 也没什么更好的办法了
 */
export const UwOperationRefProvider: FC<PropsWithChildren> = ({ children }) => {
  const actionRef = useRef<{
    handleSave: AsyncFunctionType;
    validateSaveValues?: ValidateSaveValues;
  }>({
    handleSave: noop,
    validateSaveValues: noop,
  });

  return (
    <UwOperationRefContext.Provider value={actionRef}>
      <Provider>{children}</Provider>
    </UwOperationRefContext.Provider>
  );
};
