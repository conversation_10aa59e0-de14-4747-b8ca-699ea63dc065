import { useEffect, useMemo, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { Spin } from 'antd';

import { orderBy, size } from 'lodash-es';
import useSWR from 'swr';

import { Tabs } from '@zhongan/nagrand-ui';

import {
  Mode,
  YesOrNo,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  BasicInfoData,
  PageTemplateTypes,
  PersonInfoType,
  TaskTypeEnum,
  UnderwritingWorkSheetService,
} from 'genesis-web-service';

import { InfoDisplay } from '@uw/components/InfoDisplay';
import { InfoDisplayV2 } from '@uw/components/InfoDisplayV2/InfoDisplayV2';
import { CustomermizationProvider } from '@uw/customizedImplements';
import { useQueryUwTaskDetail } from '@uw/hook/request';
import { useQueryBothSections } from '@uw/hook/useQuerySchema';
import { getUwFactors } from '@uw/util/getUwFactors';

import {
  SET_BASIC_INFO,
  SET_QUERY_UW_TASK,
  SET_RULE_RESULT,
  WorkSheetPageAction,
  WorkSheetPageState,
  WorksheetPageContext,
  WorksheetPageReducer,
  initialContextValue,
} from './UwWorksheetPageProvider';
import { Footer, Header } from './components';
import { Sections } from './enum';
import styles from './index.module.scss';
import { SectionComponent } from './interface';
import { UwSections, sections } from './sections';

export const UwWorksheetPage = () => {
  const [searchParams] = useSearchParams();
  const newSearchParams = new URLSearchParams(searchParams.toString());
  const [currentTab, setCurrentTab] = useState<UwSections>(
    (newSearchParams.get('currentTab') as UwSections) || undefined
  );
  const { taskId, mode, source } = useParams();
  const navigate = useNavigate();
  const [t] = useTranslation(['uw', 'common']);
  const [state, dispatch] = useReducer<
    React.Reducer<WorkSheetPageState, WorkSheetPageAction>
  >(WorksheetPageReducer, initialContextValue);
  const { task, loading, queryUwTaskDetail } = useQueryUwTaskDetail(taskId);

  const basicInfo = task?.basicInfo as BasicInfoData;

  const policyHolder = task?.personInfo.holder as PersonInfoType;

  const factors = useMemo(() => getUwFactors(basicInfo), [basicInfo]);

  const { sections: queriedSections, querySection } = useQueryBothSections({
    basicPageCode: PageTemplateTypes.PageType.UNDERWRITING_V3,
    factors,
    needGetInitSection: false,
  });
  const applicationNo = state?.basicInfo?.applicationNo;
  const { data: insuredObject } = useSWR(
    applicationNo ? [`/external/template/pages/${applicationNo}`] : null,
    () =>
      UnderwritingWorkSheetService.queryInsuredObject(
        applicationNo as string,
        basicInfo?.taskType === TaskTypeEnum.POS
      )
  );

  // coc配置标识 editable为NO时不可编辑
  useEffect(() => {
    if (basicInfo) {
      querySection({
        pageCode: PageTemplateTypes.PageType.UNDERWRITING_V3,
        factors,
      });
    }
    if (!source && basicInfo?.editable === YesOrNo.NO && mode === Mode.Edit) {
      navigate(`/uw/uw-operation/view/${taskId}`);
    }
  }, [basicInfo, source, mode, taskId, factors]);

  const tabList = useMemo(() => {
    const result: ((
      | PageTemplateTypes.SectionDetailResponse
      | PageTemplateTypes.SubSectionDetailResponse
    ) & {
      Component: SectionComponent;
    })[] = [];

    // https://jira.zaouter.com/browse/GIS-112986
    // 经讨论，提前调用insuredObject接口，如果没有insuredObject的时候，不显示这个section tab
    let excludeInsuredObjectProfile = true;
    if (size(insuredObject?.insuredObjectList) > 0) {
      excludeInsuredObjectProfile = false;
    }
    console.log(queriedSections, 'queriedSections');

    queriedSections
      ?.filter(queriedSection => {
        if (excludeInsuredObjectProfile) {
          return queriedSection.section !== Sections.INSURED_OBJECT_PROFILE;
        }
        return true;
      })
      ?.forEach(queriedSection => {
        if (
          (sections as Record<string, SectionComponent>)[
            queriedSection.section
          ] &&
          queriedSection.section !== Sections.TOOLS
        ) {
          result.push({
            ...queriedSection,
            Component: (sections as Record<string, SectionComponent>)[
              queriedSection.section
            ],
          });
        }
      });
    return orderBy(result, 'order');
  }, [queriedSections, insuredObject]);

  const headerInfo = useMemo(() => {
    const info = queriedSections.filter(
      section => section.section === Sections.HEADER_MORE_INFO
    );
    if (info && info[0]) {
      return info[0];
    }
    return null;
  }, [queriedSections]);

  const headerMoreInfoPos = useMemo(() => {
    const info = queriedSections.filter(
      section => section.section === Sections.HEADER_MORE_INFO_POS
    );
    if (info && info[0]) {
      return info[0];
    }
    return null;
  }, [queriedSections]);

  const handleTabChange = (activeKey: string) => {
    setCurrentTab(activeKey as UwSections);
    newSearchParams.set('currentTab', activeKey);
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };
  const tabs = useMemo(
    () => (
      <div className={styles.uwWorksheetTabs}>
        <Tabs
          activeKey={currentTab}
          className={currentTab}
          destroyInactiveTabPane
          type="line-with-bg"
          onChange={handleTabChange}
        >
          {tabList.map(Tab => (
            <Tabs.TabPane tab={t(Tab.section)} key={Tab.section}>
              <div className="h-[calc(100vh-232px)] overflow-auto">
                <Tab.Component section={Tab.section} />
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs>
      </div>
    ),
    [t, tabList, currentTab]
  );

  useEffect(() => {
    if (dispatch && task?.basicInfo) {
      dispatch({
        type: SET_BASIC_INFO,
        basicInfo: task?.basicInfo,
      });

      dispatch({
        type: SET_RULE_RESULT,
        ruleResult: task?.ruleResult,
      });
    }
    dispatch({
      type: SET_QUERY_UW_TASK,
      queryUwTaskDetail,
    });
  }, [task?.basicInfo, dispatch]);

  return (
    <CustomermizationProvider
      pageCode={PageTemplateTypes.PageType.UNDERWRITING_V3}
    >
      <WorksheetPageContext.Provider
        value={{
          state,
          dispatch,
        }}
      >
        <Spin spinning={loading}>
          <div className="bg-white overflow-hidden">
            <div className="pb-[72px]">
              <Header />
              {headerInfo && (
                <InfoDisplay
                  basicInfo={basicInfo}
                  policyHolder={policyHolder}
                />
              )}
              {headerMoreInfoPos && (
                <InfoDisplayV2
                  basicInfo={basicInfo}
                  policyHolder={policyHolder}
                />
              )}
              {tabs}
            </div>
            <Footer className="z-10" />
          </div>
        </Spin>
      </WorksheetPageContext.Provider>
    </CustomermizationProvider>
  );
};
