import {
  FC,
  createElement,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { Button, Divider, Form, Input, Popconfirm, message } from 'antd';

import { useAtomValue } from 'jotai';
import useSWR from 'swr';

import { Icon, Modal } from '@zhongan/nagrand-ui';

import { CircleRadio } from 'genesis-web-component/lib/components';
import {
  TaskTypeEnum,
  UnderwritingService,
  UwTaskStatusEnum,
} from 'genesis-web-service';

import { ApprovalModal } from '@uw/components/ApprovalModal/ApprovalSuccessModal';
import {
  DecisionAction,
  Mode,
  SaveMode,
  UwDecisionEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import {
  isPageReadyAtom,
  proposalSaveLoadingsAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom/loading';
import { useUwOperationRef } from '@uw/pages/uw-pages/uwOperationPage/UwOperation.Provider';
import { useGetAssociatedUWTaskList } from '@uw/pages/uw-pages/uwOperationPage/hooks/useGetAssociatedUWTaskList';
import { SelectType } from '@uw/pages/uw-pages/uwWorksheetPage/hooks/useReferralOrReassign';
import { UwSections } from '@uw/pages/uw-pages/uwWorksheetPage/sections/index';
import { messagePopup } from '@uw/util/messagePopup';

import { WorksheetPageContext } from '../../../UwWorksheetPageProvider';
import { DateSourceData } from '../../../hooks/useReferralOrReassign';
import EscalatePopup from '../../EscalatePopup';
import styles from './style.module.scss';

enum WorksheetDecision {
  Accept = 'ACCEPT',
  Decline = 'REJECT',
  Return = 'RETURN',
  ReturnCurrentProductToDataEntry = 'RETURN_CURRENT_PRODUCT_TO_DATA_ENTRY',
  ReturnEntireSubmissionToDataEntry = 'RETURN_ENTIRE_SUBMISSION_TO_DATA_ENTRY',
}

export const Decision: FC = () => {
  const { taskId = '', source, mode } = useParams();
  const [form] = Form.useForm();
  const [t] = useTranslation(['uw']);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const currentTab = searchParams.get('currentTab');
  const { state } = useContext(WorksheetPageContext);
  const { basicInfo } = state;
  const saveActionRef = useUwOperationRef();
  const isUwInProcess = useMemo(
    () => basicInfo?.status === UwTaskStatusEnum.UW_IN_PROCESS,
    [basicInfo?.status]
  );

  const isEditMode = mode === Mode.Edit;
  const isTaskTypePOS = basicInfo?.taskType === TaskTypeEnum.POS;

  const isReferralTask = basicInfo?.referralTask === YesOrNo.YES;

  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [hasBeenDecided, setHasBeenDecided] = useState<boolean>(false);
  const [approvalModalVisible, setApprovalModalVisible] =
    useState<boolean>(false);

  const { taskList = [] } = useGetAssociatedUWTaskList(taskId);
  const [escalateUsers, setEscalateUsers] = useState<DateSourceData[]>([]);
  const [showModal, setShowModal] = useState(false);

  const isPageReady = useAtomValue(isPageReadyAtom);
  const saveLoadings = useAtomValue(proposalSaveLoadingsAtom);
  const disabled = useMemo(() => {
    if (currentTab === UwSections.POLICY_DETAILS && isUwInProcess) {
      return (
        isPageReady ||
        saveLoadings[SaveMode.MULTIPLE_GOODS_CALCULATE] ||
        isReferralTask
      );
    }
    if (isReferralTask) {
      return true;
    }
    return false;
  }, [currentTab, isUwInProcess, isPageReady, saveLoadings, isReferralTask]);

  useSWR(
    () => ['decisionDetail', taskId],
    () => UnderwritingService.getDecisionDetail(taskId),
    {
      onSuccess: res => {
        if (res.uwDecision) {
          setHasBeenDecided(true);

          form.setFieldsValue({
            decision: res.uwDecision,
            reason: res?.decisionReason,
          });
        }
      },
    }
  );

  const decisionOptions = [
    {
      label: t('Accept'),
      value: WorksheetDecision.Accept,
      backgroundColor: 'var(--info-color-bg)',
      icon: <Icon type="done" className={styles.decisionIcon} />,
    },
    {
      label: t('Decline'),
      backgroundColor: 'var(--info-color-bg)',
      value: WorksheetDecision.Decline,
      icon: <Icon type="done" className={styles.decisionIcon} />,
    },
    {
      label: t('Return to Data Entry'),
      backgroundColor: 'var(--info-color-bg)',
      value: WorksheetDecision.Return,
      icon: <Icon type="done" className={styles.decisionIcon} />,
      isHidden: !isTaskTypePOS,
    },
    {
      label: t('Return Current Product to Data Entry'),
      backgroundColor: 'var(--info-color-bg)',
      value: WorksheetDecision.ReturnCurrentProductToDataEntry,
      icon: <Icon type="done" className={styles.decisionIcon} />,
      // TODO: 下个迭代再处理这个按钮的业务逻辑，本迭代在taskType为POS时隐藏
      isHidden: isTaskTypePOS,
    },
    // TODO: 下个迭代对接
    // {
    //   label: t('Return Entire Submission to Data Entry'),
    //   value: WorksheetDecision.ReturnEntireSubmissionToDataEntry,
    // },
  ];

  const alertInfoMap: Partial<
    Record<WorksheetDecision, { content: string; confirmText: string }>
  > = {
    [WorksheetDecision.Accept]: {
      confirmText: t('Approve'),
      content: t('Are you sure you want to approve the insurance application?'),
    },
    [WorksheetDecision.Decline]: {
      confirmText: t('Decline'),
      content: t('Are you sure you want to decline the insurance application?'),
    },

    [WorksheetDecision.Return]: {
      confirmText: t('Return to Data Entry'),
      content: t(
        'Are you sure you want to return the application for the current product?'
      ),
    },

    [WorksheetDecision.ReturnCurrentProductToDataEntry]: {
      confirmText: t('Return Current Product to Data Entry'),
      content: t(
        'Are you sure you want to return the application for the current product?'
      ),
    },
    [WorksheetDecision.ReturnEntireSubmissionToDataEntry]: {
      confirmText: t('Return Entire Submission to Data Entry'),
      content: t('Are you sure you want to return the entire submission?'),
    },
  };

  const decisionValue = Form.useWatch('decision', form) as WorksheetDecision;

  const DecisionInfo = () => (
    <div className="w-[640px]">
      <Form form={form} layout="vertical" disabled={!isEditMode}>
        <Form.Item name={'decision'} rules={[{ required: true }]} label={null}>
          <CircleRadio
            options={decisionOptions.filter(option => !option.isHidden)}
          />
        </Form.Item>

        <Form.Item name={'reason'} label={t('Reason')} className="font-medium">
          <Input.TextArea
            placeholder={t('Please input')}
            className="!h-[82px]"
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </div>
  );

  const PopTitle = () => (
    <>
      <div className="font-bold text-lg">{t('Decision')}</div>
      <Divider className="my-4"></Divider>
    </>
  );

  const onConfirm = () => {
    if (!decisionValue) {
      messagePopup(t('Please select decision'), 'warn');
      return;
    }

    setConfirmVisible(true);
  };
  const onCancel = () => {
    if (hasBeenDecided) {
      return;
    }
    form.resetFields();
  };

  const queryEscalateUsers = useCallback(() => {
    UnderwritingService.queryUWEscalateUsers(taskId as string)
      .then(users => {
        setEscalateUsers(
          users?.map(user => ({
            ...user,
            id: `${SelectType.Escalate}_${user?.id}`,
            key: `${SelectType.Escalate}_${user?.id}`,
          }))
        );

        setShowModal(true);
        setConfirmVisible(false);
      })
      .catch(() => {
        message.error(t('Query Escalate Users Failed'));
      });
  }, [taskId, setEscalateUsers, t]);

  const onSubmitDecision = useCallback(async () => {
    try {
      setConfirmLoading(true);

      if (currentTab === UwSections.POLICY_DETAILS) {
        await saveActionRef?.current?.handleSave?.({
          needValidate: true,
          onlySave: true,
        });
      }

      const res = await UnderwritingService.updateDecision(
        DecisionAction.SUBMITTING,
        taskId,
        {
          decisionReason: form.getFieldValue('reason'),
          taskId,
          uwDecision: decisionValue,
          checkAuthorityLevelFlag: true,
        }
      );

      if (res?.finalDecision === UwDecisionEnum.DECLINE) {
        Modal.confirm({
          title: t('warning'),
          content: t('Decision Fail Decline'),
          onOk: () => {
            UnderwritingService.updateDecision(
              DecisionAction.SUBMITTING,
              taskId,
              {
                decisionReason: form.getFieldValue('reason'),
                taskId,
                uwDecision: UwDecisionEnum.REJECT,
                checkAuthorityLevelFlag: true,
              }
            ).then(() => setApprovalModalVisible(true));
          },
          okText: t('Submit'),
          cancelText: t('Cancel'),
          icon: createElement(Icon, {
            className: 'rotate-180 !text-error !mt-0',
            type: 'info-fill',
          }),
        });
        return;
      }
      /**
       * submit提交核保
       * compliance check === stop => 如下提示1
       * compliance check === hold => 如下提示2
       */
      if (res?.complianceDecision === UwDecisionEnum.DECLINE) {
        messagePopup(
          t(
            'The modification of the policy information did not pass the compliance verification. The proposal is canceled, and the underwriting task is closed. Please confirm.'
          ),
          'warn'
        );
      }
      if (res?.complianceDecision === UwDecisionEnum.MANUAL) {
        messagePopup(
          t(
            'The modification of the policy information has triggered a manual compliance task. Please wait for the submission of the compliance task before continuing with the underwriting task.'
          ),
          'warn'
        );
      }

      // 后端返回这个字段才进行警告提示
      if (res?.authorityErrorMessage) {
        messagePopup(res?.authorityErrorMessage, 'warn');
      }

      if (res?.authorityFlag === YesOrNo.NO) {
        queryEscalateUsers();
      } else {
        setApprovalModalVisible(true);
      }
    } catch (err) {
      messagePopup(err?.message, 'error');
    } finally {
      setConfirmLoading(false);
    }
  }, [
    saveActionRef,
    taskId,
    form,
    decisionValue,
    t,
    queryEscalateUsers,
    currentTab,
  ]);

  const backToSearch = () => {
    navigate(source ? `/uw/task-pool/${source}` : '/uw/task-pool');
  };

  return (
    <>
      <Popconfirm
        title={<PopTitle />}
        description={<DecisionInfo />}
        onConfirm={onConfirm}
        icon={null}
        onCancel={onCancel}
        okText={t('Submit')}
        cancelText={t('Cancel')}
        placement="topLeft"
        overlayClassName={styles.decisionConfirmPop}
        okButtonProps={{
          size: 'middle',
          disabled: !decisionValue || hasBeenDecided,
        }}
        cancelButtonProps={{
          size: 'middle',
        }}
      >
        <Button size="large" type="primary" disabled={disabled}>
          {t('Decision')}
        </Button>
      </Popconfirm>

      <Modal
        title={t('Alert')}
        width={500}
        open={confirmVisible}
        onCancel={() => setConfirmVisible(false)}
        onOk={onSubmitDecision}
        okText={alertInfoMap[decisionValue]?.confirmText}
        okButtonProps={{
          loading: confirmLoading,
        }}
      >
        <>{alertInfoMap[decisionValue]?.content}</>
      </Modal>

      <ApprovalModal
        backToSearch={backToSearch}
        open={approvalModalVisible}
        onCancel={() => setApprovalModalVisible(false)}
        maxCount={2}
        taskList={taskList}
        source={source}
      />

      {showModal && (
        <EscalatePopup
          escalateUsers={escalateUsers}
          showModal={showModal}
          setShowModal={setShowModal}
        ></EscalatePopup>
      )}
    </>
  );
};
