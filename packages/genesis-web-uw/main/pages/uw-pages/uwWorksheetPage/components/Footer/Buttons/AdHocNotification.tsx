import { Ref, useContext, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import { AdHocNotificationDrawer } from '@uw/pages/uw-pages/uwOperationPage/components/AdHocNotification';
import { WorksheetPageContext } from '@uw/pages/uw-pages/uwWorksheetPage/UwWorksheetPageProvider';

import { useIsReferralTask } from '../../../hooks/useIsReferralTask';

export interface RefAdHocNotificationDrawerProps {
  openDrawer: () => void;
}

export const AdHocNotification = () => {
  const { state } = useContext(WorksheetPageContext);
  const { basicInfo } = state;
  const { taskId, taskNo } = basicInfo || {};
  const { t } = useTranslation(['uw', 'common']);
  const AdHocNotificationDrawerRef = useRef<RefAdHocNotificationDrawerProps>();
  const isReferralTask = useIsReferralTask();

  return (
    <>
      <Button
        size="large"
        disabled={isReferralTask}
        onClick={() => AdHocNotificationDrawerRef.current?.openDrawer()}
      >
        {t('Ad-hoc Notification')}
      </Button>
      {taskId && taskNo && (
        <AdHocNotificationDrawer
          caseNo={taskNo}
          taskId={taskId}
          ref={
            AdHocNotificationDrawerRef as Ref<RefAdHocNotificationDrawerProps>
          }
        />
      )}
    </>
  );
};
