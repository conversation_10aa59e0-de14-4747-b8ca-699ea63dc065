import React, {
  FC,
  useState,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from 'react';
import { Switch, Form, message } from 'antd';
import {
  UnderwritingService,
  QueryExclusionType,
  LiabilityInfoType,
  ProductInfoEntity,
} from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { EditTableWithSelectTable } from '@uw/components/EditDrawerWithSelectTable';
import { SelectableTable } from '@uw/components/SelectableTable';
import { useUwTaskPermission } from '@uw/hook/permission';
import { useGetExclusion } from '@uw/hook/request';
import { Mode, UwDecisionEnum, YesOrNo } from '@uw/interface/enum.interface';
import { useUwDecisionDetail } from '@uw/pages/uw-pages/uwDecisionPage/UwDecision.service';
import {
  SELECTED_LIABILITYLIST,
  UPDATE_EXCLUSIONDETAIL,
  UwDecisionContext,
} from '@uw/pages/uw-pages/uwDecisionPage/UwDecisionProvider';
import {
  exclusionColumns,
  exclusionFields,
  exclusionLiabilityColumns,
  getLibilityList,
} from '@uw/pages/uw-pages/uwDecisionPage/components/exclusion.config';

import { size } from 'lodash-es';

import styles from '../UwDecisionPage.module.scss';
import { handleSaveExclusionDetail } from './exclusion.request';

export const DecisionExclusion: FC = () => {
  const { taskId, mode, source } = useParams();
  const [, setIsEditing] = useState(false);
  const { state, dispatch } = useContext(UwDecisionContext);
  const { getDecisionDetail } = useUwDecisionDetail(taskId, dispatch);
  const [productCodeList, setProductCode] = useState<ProductInfoEntity[]>([]);
  const { exclusionItem, activeExclusionItem } = useGetExclusion();
  const [liabilityList, setLiabilityList] = useState<LiabilityInfoType[]>([]);
  const hasEditAuth = useUwTaskPermission(mode as Mode, source);
  const [exclusionContentDetail, setExclusionContent] = useState('');
  const [form] = Form.useForm();
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const handleIfCreate = useCallback<(arg: boolean) => void>(value => {
    setIsCreate(value);
  }, []);
  const disableDelBtn = useMemo(
    () => state.decision !== UwDecisionEnum.CONDITIONAL_ACCEPT || !hasEditAuth,
    [state.decision, hasEditAuth]
  );

  useEffect(() => {
    if (state.decision !== UwDecisionEnum.CONDITIONAL_ACCEPT) {
      handleIfCreate(false);
    }
  }, [state.decision]);

  const [t] = useTranslation(['uw', 'common']);
  const handleCancel = () => {
    setLiabilityList([]);
    dispatch({ type: SELECTED_LIABILITYLIST, selectedLiability: [] });
    setExclusionContent('');
  };

  useEffect(() => {
    form.setFieldsValue({ exclusionContent: exclusionContentDetail });
    if (state.exclusionDetail?.length > 0) {
      setIsCreate(true);
    }
  }, [exclusionContentDetail, state.exclusionDetail]);
  const handleSaveData = useCallback<
    (row: QueryExclusionType) => Promise<boolean>
  >(
    async row => {
      if (state.selectedLiability?.length === 0) {
        message.warn({
          prefixCls: 'antd-uw-message',
          content: t('Please Select At Least One Liability'),
        });
        return false;
      }
      try {
        await handleSaveExclusionDetail(
          row,
          state.uwDecisionId,
          state.selectedLiability
        );
        getDecisionDetail([UPDATE_EXCLUSIONDETAIL]);
        handleCancel();
        return true;
      } catch (e) {
        return false;
      }
    },
    [state.uwDecisionId, state.selectedLiability, state.insuredInfoList]
  );

  const handleEdit = useCallback<(row: QueryExclusionType) => void>(
    async row => {
      const newProductList = state.insuredInfoList
        .find(item => item.partyId === row.insuredCustomerId)
        ?.productList?.filter(product => product.exclusionFlag === YesOrNo.YES);
      const liabities = row?.liabilityList?.map(
        liability => liability.liabilityId
      );
      setProductCode(newProductList || []);
      setLiabilityList(
        getLibilityList(row?.productId, newProductList || []) || []
      );
      dispatch({
        type: SELECTED_LIABILITYLIST,
        selectedLiability: liabities as number[],
      });
    },
    [state.insuredInfoList]
  );

  const handleDel = useCallback<(val: number) => void>(
    async val => {
      const { id } = state.exclusionDetail?.[val];
      await UnderwritingService.deleteExclusionDetail(id);
      getDecisionDetail([UPDATE_EXCLUSIONDETAIL]);
    },
    [state.exclusionDetail]
  );
  return size(exclusionItem) ? (
    <div style={{ padding: `${styles.gapLg} 0` }}>
      <section className={styles['final-decision-title']}>
        <span style={{ fontWeight: 'bold' }}>{t('Exclusion')}</span>
        <Switch
          disabled={disableDelBtn}
          checked={isCreate}
          onChange={handleIfCreate}
          style={{ marginLeft: '10px' }}
        />
        {isCreate && (
          <EditTableWithSelectTable
            uKey={'id'}
            headline={'Exclusion'}
            disableAdd={disableDelBtn}
            disableEdit={disableDelBtn}
            tableColumns={exclusionColumns(state.insuredInfoList)}
            data={state.exclusionDetail as []}
            setData={() => {}}
            outerForm={form}
            setIsEditing={setIsEditing}
            handleSaveData={handleSaveData}
            handleDel={handleDel}
            handleEdit={handleEdit}
            handleCancel={handleCancel}
            tableFields={exclusionFields(
              activeExclusionItem,
              state.insuredInfoList,
              setProductCode,
              productCode => {
                const liabilities =
                  getLibilityList(productCode, productCodeList) || [];
                setLiabilityList(liabilities);
                dispatch({
                  type: SELECTED_LIABILITYLIST,
                  selectedLiability: liabilities.map(liability => liability.id),
                });
              },
              productCodeList,
              setExclusionContent,
              () => (
                <SelectableTable<LiabilityInfoType>
                  rowKey={'id'}
                  onChange={values => {
                    dispatch({
                      type: SELECTED_LIABILITYLIST,
                      selectedLiability: values as number[],
                    });
                  }}
                  dataSource={
                    liabilityList as (LiabilityInfoType &
                      Record<string, unknown>)[]
                  }
                  columns={exclusionLiabilityColumns()}
                  initialValues={state.selectedLiability}
                />
              )
            )}
          />
        )}
      </section>
    </div>
  ) : null;
};
