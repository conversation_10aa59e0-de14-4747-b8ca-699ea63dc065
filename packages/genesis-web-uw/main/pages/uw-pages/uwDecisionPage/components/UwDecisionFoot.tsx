import React, { FC, useCallback, useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { Radio, RadioChangeEvent } from 'antd';

import { orderBy } from 'lodash-es';

import { Input } from '@zhongan/nagrand-ui';

import { useBizDict } from '@uw/hook/useBizDict';
import { Mode, UwDecisionEnum } from '@uw/interface/enum.interface';

import styles from '../UwDecisionPage.module.scss';
import {
  UPDATE_DECISION,
  UPDATE_INSUREDINFOLIST,
  UPDATE_REASON,
  UpdateProductLevel,
  UwDecisionContext,
} from '../UwDecisionProvider';

const { TextArea } = Input;
export const UwDecisionFoot: FC = () => {
  const { mode, source } = useParams();
  const isQueryPage = source === 'query';
  const isViewMode = mode === Mode.View;
  const [t] = useTranslation(['uw', 'common']);
  const uwDecisionEnums = useBizDict('uwDecision');
  const { state, dispatch } = useContext(UwDecisionContext);
  const decisionOptions = useMemo(
    () =>
      orderBy(
        uwDecisionEnums?.filter(decisionEnum =>
          isQueryPage || isViewMode
            ? decisionEnum.enumItemName === state.decision
            : state?.decisionsWithAuthority?.includes(
                decisionEnum.enumItemName as string
              )
        ),
        ['orderNo'],
        ['asc']
      ),
    [
      uwDecisionEnums,
      isQueryPage,
      isViewMode,
      state.decision,
      state?.decisionsWithAuthority,
    ]
  );
  const handleChangeDecision = useCallback<(e: RadioChangeEvent) => void>(
    e => {
      const { value } = e.target;
      let productLevelDecision = '' as UwDecisionEnum;
      if (value !== UwDecisionEnum.CONDITIONAL_ACCEPT) {
        productLevelDecision = value;
      }
      dispatch({
        type: UPDATE_INSUREDINFOLIST,
        insuredInfoList: UpdateProductLevel(
          state.insuredInfoList,
          productLevelDecision
        ),
      });
      dispatch({ type: UPDATE_DECISION, decision: value });
    },
    [state.insuredInfoList]
  );
  const handleChangeDesc: React.ChangeEventHandler<HTMLTextAreaElement> =
    useCallback(e => {
      const { value } = e.target;
      dispatch({ type: UPDATE_REASON, reason: value });
    }, []);
  const disabledOpt = useMemo(() => mode === Mode.View, [mode]);

  return (
    <div className={styles['uw-decision-foot']}>
      <section className={styles['final-decision-title']}>
        {t('Final Decision')}
      </section>
      <section className={styles['final-decision-radios']}>
        <Radio.Group onChange={handleChangeDecision} value={state.decision}>
          {decisionOptions?.map(decisionEnum => (
            <Radio
              disabled={disabledOpt}
              value={decisionEnum.enumItemName}
              key={decisionEnum.enumItemName}
            >
              {decisionEnum.itemName}
            </Radio>
          ))}
        </Radio.Group>
        {(isQueryPage || isViewMode) && !state.decision ? (
          <span>{t('Underwriting case is under review.')}</span>
        ) : null}
      </section>
      <section>
        <div style={{ marginBottom: '9px' }}>{t('Reason')}</div>
        <TextArea
          disabled={disabledOpt}
          value={state.reason}
          onChange={handleChangeDesc}
          rows={4}
        />
      </section>
    </div>
  );
};
