import { Form } from 'antd';

import { FieldDataType, FieldType } from '@zhongan/nagrand-ui';

import { SearchSelect } from 'genesis-web-component/lib/components/SearchSelect';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { ProposalService, YesOrNo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import I18nInstance from '@uw/i18n';
import { i18nFn } from '@uw/util/i18nFn';

const ns = { ns: ['uw', 'common'] };

export const fields = (
  YesNoEnum: BizDict[] = [],
  goodsInfo: BizDict[] = [],
  packageInfo: BizDict[] = [],
  uwTaskTypeEnum: BizDict[],
  uwTaskStatusEnum: BizDict[],
  underwritingTaskPriorityEnum: BizDict[] = []
): FieldDataType[] => [
  {
    label: I18nInstance.t('Application No', ns),
    key: 'applicationNo',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Business Type', ns),
    key: 'taskType',
    type: FieldType.Select,
    extraProps: {
      options: uwTaskTypeEnum,
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Task No.', ns),
    key: 'taskNo',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Task Status', ns),
    key: 'taskStatus',
    type: FieldType.Select,
    extraProps: {
      options: uwTaskStatusEnum,
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
      mode: 'multiple',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Task Type', ns),
    key: 'referralTask',
    type: FieldType.Select,
    extraProps: {
      options: [
        {
          enumItemName: YesOrNo.NO,
          itemName: i18nFn('Underwriting Task'),
        },
        {
          enumItemName: YesOrNo.YES,
          itemName: i18nFn('Referral Task'),
        },
      ],
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Policy No', ns),
    key: 'policyNo',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Submission No.', ns),
    key: 'submissionNo',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Goods Name', ns),
    key: 'goodsId',
    type: FieldType.Select,
    extraProps: {
      options: goodsInfo,
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Product Name', ns),
    key: 'packageId',
    type: FieldType.Select,
    extraProps: {
      options: packageInfo,
      fieldNames: { label: 'itemName', value: 'dictValue' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Policyholder Name', ns),
    key: 'policyHolderName',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Policyholder ID No', ns),
    key: 'policyHolderId',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Insured Name', ns),
    key: 'insuredName',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Insured ID No', ns),
    key: 'insuredId',
    type: FieldType.Input,
    col: 8,
  },
  {
    label: I18nInstance.t('Task Create Date', ns),
    key: 'taskCreateDate',
    type: FieldType.DateRange,
    col: 8,
    extraProps: {
      format: dateFormatInstance.dateFormat,
    },
  },
  {
    label: I18nInstance.t('With Open Pending Case', ns),
    key: 'withOpenPendingCase',
    type: FieldType.Select,
    extraProps: {
      options: YesNoEnum,
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    type: FieldType.Customized,
    key: 'channelCode',
    label: I18nInstance.t('Sales Channel', ns),
    col: 8,
    render: (
      <Form.Item name={'channelCode'} label={i18nFn('query-Sales Channel')}>
        <SearchSelect
          labelKey="name"
          valueKey="code"
          placeholder={i18nFn('Please select')}
          searchKey="salesCodeOrNameLike"
          optionLabel={['label', 'data.code']}
          apiParams={{ sort: 'ASC' }}
          paging={{ pageKey: 'pageIndex', sizeKey: 'pageSize' }}
          apiService={ProposalService.queryChannelPage}
        />
      </Form.Item>
    ),
  },
  {
    label: I18nInstance.t('Priority', ns),
    key: 'taskPriority',
    type: FieldType.Select,
    extraProps: {
      options: underwritingTaskPriorityEnum,
      fieldNames: { label: 'itemName', value: 'enumItemName' },
      optionFilterProp: 'itemName',
    },
    col: 8,
  },
  {
    label: I18nInstance.t('Relation_Policy_No', ns),
    key: 'relationNo',
    type: FieldType.Input,
    col: 8,
  },
];
