import React, { FC, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  CardV2 as Card,
  CardActionsContainer,
  CardBodyHeader,
  CardBodyPrimaryInfo,
  CardFooter,
  CardLayoutV2 as CardLayout,
  CardTagList,
  CommonIconAction,
  DeleteAction,
  EditAction,
  Icon,
  Pagination,
  ProfilePhoto,
  QueryBitMap,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { TaskPoolDataType, VerificationTaskPool } from 'genesis-web-service';
import { YesOrNo } from 'genesis-web-service/lib/common.interface';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import RectangleSvg from '@uw/assets/icons/Rectangle.svg';
import { useDict } from '@uw/biz-dict/hooks';
import { StatusType } from '@uw/components/StatusTag';
import { WithdrawModal } from '@uw/components/WithdrawModal/WithdrawModal';
import { uwTaskStatusTagMapping } from '@uw/constant';
import { Mode, UwTaskStatusEnum } from '@uw/interface/enum.interface';
import { selectUserId } from '@uw/redux/selector';

interface CommonProps {
  loading: boolean;
  handleGoToOperation: (
    mode: Mode,
    taskId: number,
    goodsCategoryId?: number
  ) => void;
  handleActivate: (currentItem: TaskPoolDataType) => void;
  hasEditAuth: boolean;
  source?: string;
  handleWithdrawnTask: (taskId: number) => void;
  withdrawnLoading: boolean;
}
interface Props extends CommonProps {
  pageSize: number;
  pageNum: number;
  tasks: VerificationTaskPool[];
  total: number;
  handlePageChange: (pageNum: number, pageSize: number) => void;
}

export const TaskPoolCard: FC<Props> = ({
  pageSize,
  tasks,
  loading,
  handleGoToOperation,
  handleActivate,
  hasEditAuth,
  source,
  total,
  pageNum,
  handlePageChange,
  handleWithdrawnTask,
  withdrawnLoading,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [UwTaskTypeEnum] = useDict('UwTaskTypeEnum');
  const [UwTaskStatusEnumDict] = useDict('UwTaskStatusEnum');
  const [task] = useState<TaskPoolDataType>();
  const userId = useSelector(selectUserId);
  const [showModal, setShowModal] = useState(false);

  // TODO: 跟pos那边打通后新增withdraw按钮操作
  const cancelWithdraw = useCallback(() => {}, []);
  const confirmWithdraw = useCallback(() => {}, []);
  const handleCloseWithdrawnTaskModal = () => {
    setShowModal(false);
  };

  const bodySection = useCallback<(record: TaskPoolDataType) => JSX.Element>(
    record => (
      <>
        {record?.withOpenPendingCase === YesOrNo.YES && (
          <div className="absolute right-md top-0">
            <RectangleSvg />
          </div>
        )}
        <CardBodyHeader
          leftContent={dateFormat.getDateString(record.createDate)}
        />
        <CardBodyPrimaryInfo
          title={t('Application No.')}
          content={record.applicationNo}
          right={
            <ProfilePhoto
              users={record.caseOwnerName}
              tooltipLabel={t('Case Owner')}
            />
          }
        />
        <CardTagList
          tagList={[
            {
              type: TagType.Tag,
              tagProps: {
                statusI18n:
                  UwTaskStatusEnumDict?.[record.taskStatus]?.toUpperCase(),
                type: uwTaskStatusTagMapping[
                  record?.taskStatus as UwTaskStatusEnum
                ] as StatusType,
                needBgColor: true,
              },
            },
            {
              type: TagType.Label,
              tagProps: {
                statusI18n: UwTaskTypeEnum?.[record.taskType],
                type: 'no-status',
                needBgColor: true,
              },
            },
            ...(record.referralTask === YesOrNo.YES
              ? [
                  {
                    type: TagType.Label,
                    tagProps: {
                      statusI18n: (
                        <Icon
                          type="more-with-border"
                          style={{ fontSize: 16 }}
                        />
                      ),
                      type: 'no-status',
                      needBgColor: true,
                      style: { background: 'var(--layout-body-background)' },
                      tooltip: t('Referral'),
                    },
                  },
                ]
              : []),
          ]}
        />
      </>
    ),
    [UwTaskStatusEnumDict, UwTaskTypeEnum, dateFormat, t]
  );

  const actionSection = useCallback<
    (record: VerificationTaskPool) => JSX.Element
  >(
    record => {
      let viewOrEdit = (
        <ViewAction
          onClick={() =>
            handleGoToOperation(
              Mode.View,
              record.taskId,
              record?.goodsCategoryId,
              record?.taskType
            )
          }
        />
      );

      if (
        record?.currentHandler === userId &&
        hasEditAuth &&
        !source &&
        record?.taskActiveFlag === YesOrNo.YES
      ) {
        viewOrEdit = (
          <EditAction
            onClick={() =>
              handleGoToOperation(
                Mode.Edit,
                record.taskId,
                record?.goodsCategoryId,
                record?.taskType
              )
            }
          />
        );
      }

      return (
        <CardActionsContainer>
          {!source && record.taskActiveFlag === YesOrNo.NO && (
            <CommonIconAction
              icon={<Icon type="clipboard-check" />}
              tooltipTitle={t('Activate')}
              onClick={() => handleActivate(record)}
            />
          )}
          {viewOrEdit}

          {/* TODO: 先暂时隐藏withdraw按钮，等跟pos打通后开放 */}
          {
            // isMyTask && task.taskStatus === UwTaskStatusEnum.CANCEL
            false && (
              <DeleteAction
                tooltipTitle={t('Withdraw')}
                doubleConfirmType="popconfirm"
                deleteConfirmContent={t(
                  'You can query out the task in UW query after being withdrawn . But no further action allowed. Confirm to withdraw it?'
                )}
                onClick={confirmWithdraw}
              />
            )
          }
        </CardActionsContainer>
      );
    },
    [
      cancelWithdraw,
      confirmWithdraw,
      handleGoToOperation,
      hasEditAuth,
      source,
      t,
      userId,
    ]
  );

  return (
    <>
      {tasks?.length || loading ? (
        <CardLayout loading={loading}>
          {tasks.map(record => (
            <Card
              key={record.taskId}
              body={bodySection(record)}
              footer={
                <CardFooter
                  list={[
                    {
                      label: t('Task No.'),
                      value: record.taskNo,
                    },
                    {
                      label: t('Policy No.'),
                      value: record.policyNo,
                    },
                    {
                      label: t('Process UWer Level'),
                      value: record.taskCurrentLevelName,
                    },
                    {
                      label: t('UW Case Required Level'),
                      value: record.taskLevelName,
                    },
                  ]}
                />
              }
              actions={actionSection(record)}
            />
          ))}
        </CardLayout>
      ) : (
        <QueryBitMap queryStatus="NoContent" />
      )}
      <WithdrawModal
        showModal={showModal}
        handleCloseWithdrawnTaskModal={handleCloseWithdrawnTaskModal}
        policyData={task as TaskPoolDataType}
        withdrawnLoading={withdrawnLoading}
        handleWithdrawnTask={handleWithdrawnTask}
        taskType={(task as TaskPoolDataType)?.taskType}
      />
      <Pagination
        pageSize={pageSize}
        current={pageNum}
        total={total}
        showQuickJumper
        onChange={(page, pagesize) => {
          handlePageChange(page, pagesize as number);
        }}
      />
    </>
  );
};
