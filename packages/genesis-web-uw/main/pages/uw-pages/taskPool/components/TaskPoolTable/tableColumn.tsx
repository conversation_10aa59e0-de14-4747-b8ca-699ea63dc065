import React from 'react';

import { ColumnProps } from 'antd/es/table';
import { map } from 'lodash-es';

import { StatusTag } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { TaskPoolDataType } from 'genesis-web-service';
import { YesOrNo } from 'genesis-web-service/lib/common.interface';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import VectorSvg from '@uw/assets/icons/Vector.svg';
import { StatusType } from '@uw/components/StatusTag';
import { uwTaskStatusTagMapping } from '@uw/constant';
import { dictMap } from '@uw/hook/useBizDict';
import I18nInstance from '@uw/i18n';
import { UwTaskStatusEnum } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';
import { isShowPolicyNo } from '@uw/util/isShowPolicyNo';

const ns = { ns: ['uw', 'common'] };

export const tableColumns = (
  enums: Record<string, BizDict[]>
): ColumnProps<TaskPoolDataType>[] => [
  {
    title: I18nInstance.t('Application No', ns),
    dataIndex: 'applicationNo',
    key: 'applicationNo',
    render: (text, record) => (
      <span>
        {text}
        {record?.withOpenPendingCase === YesOrNo.YES && (
          <VectorSvg style={{ marginLeft: '6px' }} />
        )}
      </span>
    ),
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Business Type', ns),
    dataIndex: 'taskType',
    key: 'taskType',
    render: text =>
      enums.UwTaskTypeEnum?.find(status => status.enumItemName === text)
        ?.itemName || text,
    width: 150,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Task Status', ns),
    dataIndex: 'taskStatus',
    key: 'taskStatus',
    render: text => (
      <StatusTag
        needDot
        statusI18n={
          enums.UwTaskStatusEnum?.find(status => status.enumItemName === text)
            ?.itemName || text
        }
        type={uwTaskStatusTagMapping[text as UwTaskStatusEnum] as StatusType}
      />
    ),
    width: 150,
  },
  {
    title: I18nInstance.t('Task Type', ns),
    dataIndex: 'referralTask',
    key: 'referralTask',
    render: text => (
      <StatusTag
        needDot
        statusI18n={
          text === YesOrNo.NO
            ? i18nFn('Underwriting Task')
            : i18nFn('Referral Task')
        }
        type={'no-status'}
      />
    ),
    width: 150,
  },
  {
    title: I18nInstance.t('Task No.', ns),
    dataIndex: 'taskNo',
    key: 'taskNo',
    width: 150,
  },
  {
    title: I18nInstance.t('Policy No', ns),
    dataIndex: 'policyNo',
    key: 'policyNo',
    render: (text: string, record) =>
      isShowPolicyNo(record.isPolicyCreated, record.policyNo),
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Priority', ns),
    dataIndex: 'taskPriority',
    key: 'taskPriority',
    render: text => dictMap(enums.underwritingTaskPriority, text),
    sorter: true,
    width: 200,
  },
  {
    title: I18nInstance.t('Submission No.', ns),
    dataIndex: 'submissionNo',
    key: 'submissionNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Auto UW Rule Result', ns),
    dataIndex: 'uwInfo',
    key: 'uwInfo',
    render: (text: string, record) =>
      record.uwInfo
        ?.reduce((cur: string[], next) => {
          const curArr: string[] = [...cur];
          curArr.push(next.notice);
          return curArr;
        }, [])
        .join(';'),
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('With Open Pending Case', ns),
    dataIndex: 'withOpenPendingCase',
    key: 'withOpenPendingCase',
    render: text =>
      enums?.yesNo?.find(status => status.enumItemName === text)?.itemName ||
      text,
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Create Date', ns),
    dataIndex: 'createDate',
    key: 'createDate',
    render: text =>
      dateFormatInstance.getDateString(
        text,
        dateFormatInstance.defaultTimeZone
      ),
    width: 150,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Current Handler', ns),
    dataIndex: 'currentHandlerName',
    key: 'currentHandlerName',
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Case Owner', ns),
    dataIndex: 'caseOwnerName',
    key: 'caseOwnerName',
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Process Underwriter Level', ns),
    dataIndex: 'taskCurrentLevelName',
    key: 'taskCurrentLevelName',
    width: 220,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('UW Case Required Level', ns),
    dataIndex: 'taskLevelName',
    key: 'taskLevelName',
    width: 200,
    ellipsis: true,
  },
  {
    title: I18nInstance.t('Sales Channel', ns),
    dataIndex: 'channels',
    key: 'channels',
    width: 200,
    ellipsis: true,
    render: text => map(text, item => item?.channelName)?.join('; '),
  },
];
