import { FC, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { But<PERSON>, Divider, TableProps } from 'antd';
import { FormInstance } from 'antd/lib';

import { size } from 'lodash-es';

import {
  Modal,
  OperationContainer,
  QueryForm,
  QueryOperationSelect,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
} from '@zhongan/nagrand-ui';

import {
  FilterTaskPoolType,
  TrackingUwOperationEnum,
  UnderwritingCommentsService,
  UnderwritingService,
  UwCaseOperationEnum,
  VerificationFilterType,
  VerificationTaskPool,
} from 'genesis-web-service';

import { usePermission } from '@uw/hook/permission';
import {
  usePackages,
  useSimpleGoodsInfo,
  useWithdrawnTask,
} from '@uw/hook/request';
import { useBizDict } from '@uw/hook/useBizDict';
import { useRouterState } from '@uw/hook/useRouterState';
import {
  AntdTableChangeExtraAction,
  Mode,
  SortByUserEnum,
  SortDirectionEnum,
  SortLowerCase,
  SortMapName,
  UwDetailPath,
  UwTaskStatusEnum,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { selectEnums } from '@uw/redux/selector';
import { orderedSorting } from '@uw/util/orderedSorting';
import { handleDate, stringTimeToMoment } from '@uw/util/queryDate';

import { TaskPoolCard } from './components/TaskPoolCard';
import { TaskPoolTable } from './components/TaskPoolTable';
import { useDrawerState } from './hooks/drawer';
import { useQueryUnderwritingTaskPool, useTaskPickUp } from './hooks/request';
import { fields } from './pageConfig';

const uwTaskStatusList = [
  UwTaskStatusEnum.UW_IN_PROCESS,
  UwTaskStatusEnum.WAITING_FOR_PROCESS,
];

const defaultPageSize = 12;
const defaultPageNum = 1;

export const TaskPool: FC = () => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const location = useLocation();
  const enums = useSelector(selectEnums);
  const [goodsNameList] = useSimpleGoodsInfo();
  const { packages: packageList } = usePackages();
  const formRef = useRef<FormInstance>();
  const { source } = useParams();

  const { isWithdrawnRefresh, handleWithdrawn, withdrawnLoading } =
    useWithdrawnTask();
  const { taskPickUpShow, getTaskPickUpDays } = useTaskPickUp();
  const { queryUnderwritingTaskPool, loading, total, tasks } =
    useQueryUnderwritingTaskPool();

  const pageParams = useMemo(
    () => ({
      pageSize: defaultPageSize,
      pageNum: defaultPageNum,
    }),
    []
  );

  const YesNoEnum = useBizDict('yesNo');

  const hasEditAuth = !!usePermission('uw.manual.task.edit');

  const {
    showActivateModule,
    handleCloseActivateModule,
    taskId: curTaskId,
    modal: open,
  } = useDrawerState();

  const [queryParams, setQueryParams] = useRouterState<
    FilterTaskPoolType & { showMode: RenderMode }
  >({
    ...pageParams,
    // 打开Task Pool页面时，默认展示 Private pool – Active
    myTask: true,
    taskActiveFlag: YesOrNo.YES,
    sortMap: [
      { fieldName: SortMapName.UW_GMT_CREATED, sortType: SortLowerCase.ASC },
    ],
    showMode: RenderMode.Card,
  });

  const taskValue = useMemo(() => {
    if (queryParams.myTask) {
      return queryParams.taskActiveFlag === YesOrNo.YES
        ? SortByUserEnum.PRIVATE_TASK_ACTIVE
        : SortByUserEnum.PRIVATE_TASK_INACTIVE;
    }
    return SortByUserEnum.PUBLIC_TASK;
  }, [queryParams.taskActiveFlag, queryParams.myTask]);

  const queryFields = useMemo(
    // GIS-33392 先隐藏
    () =>
      fields(
        YesNoEnum,
        goodsNameList,
        packageList,
        enums.UwTaskTypeEnum,
        enums?.UwTaskStatusEnum?.filter(
          status =>
            ![UwTaskStatusEnum.INITIAL, UwTaskStatusEnum.ESCALATED].includes(
              status.enumItemName as UwTaskStatusEnum
            )
        ),
        enums?.underwritingTaskPriority
        // onSearchSalesChannel
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [enums, goodsNameList, source, YesNoEnum]
  );

  const queryUwTaskPool = () => {
    const newParams = {
      ...queryParams,
      taskCategory: 'UNDERWRITING',
    };

    const params = stringTimeToMoment(
      newParams,
      ['taskCreateDateStart', 'taskCreateDateEnd'],
      'taskCreateDate'
    );
    formRef.current?.setFieldsValue(params);
    // 如果是 public task 并且非 query 页， 写死 taskStatus 入参
    // 放在setFormValue下面，只在接口上体现，不用在form上体现
    if (!source && !newParams.myTask && !size(newParams.taskStatus)) {
      newParams.taskStatus = uwTaskStatusList;
    }
    queryUnderwritingTaskPool(newParams as VerificationFilterType);
  };

  useEffect(() => {
    queryUwTaskPool();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, isWithdrawnRefresh]);

  const handleSortByTime = useCallback(
    (sortType: string) => {
      const sortMap = orderedSorting(
        [...queryParams.sortMap],
        sortType,
        sortType === SortMapName.UW_GMT_CREATED
          ? SortLowerCase.ASC
          : SortLowerCase.DESC
      );
      setQueryParams({
        ...queryParams,
        sortMap: sortMap,
        pageNum: defaultPageNum,
      });
    },
    [queryParams, setQueryParams]
  );

  const handleSortByUser = useCallback(
    (sortByUser: SortByUserEnum) => {
      const taskActive =
        sortByUser === SortByUserEnum.PRIVATE_TASK_ACTIVE
          ? YesOrNo.YES
          : YesOrNo.NO;

      const params = {
        ...queryParams,
        ...pageParams,
        myTask: true,
        taskActiveFlag: taskActive,
      };

      if (sortByUser === SortByUserEnum.PUBLIC_TASK) {
        params.myTask = false;
        // 如果选择 task 为 public task 移除 taskActiveFlag 参数
        // 不能使用delete，否则setQueryParams内mergeWith内无该字段时，merge后为旧值
        params.taskActiveFlag = undefined;
      }

      setQueryParams({
        ...params,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [queryParams, pageParams, source]
  );

  const transferQueryParams = useCallback<(args: FilterTaskPoolType) => void>(
    values => {
      const searchValues = { ...values };
      const createTimeArr = values.taskCreateDate;
      const taskCreateDates = createTimeArr
        ? handleDate('taskCreateDate', createTimeArr)
        : {
            taskCreateDateStart: null,
            taskCreateDateEnd: null,
          };
      delete searchValues.taskCreateDate;
      const params = {
        myTask: queryParams.myTask,
        taskActiveFlag: queryParams.taskActiveFlag,
        sortMap: queryParams.sortMap,
        ...searchValues,
        ...pageParams,
        ...taskCreateDates,
      };
      // 如果选择 task 为 public task 移除 taskActiveFlag 参数
      if (!queryParams.myTask) {
        delete params.taskActiveFlag;
      }
      if (source) {
        delete params.myTask;
      }
      setQueryParams(params);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      queryParams.myTask,
      queryParams.taskActiveFlag,
      queryParams.sortMap,
      pageParams,
      source,
    ]
  );

  const handleGoToOperation = useCallback<
    (mode: Mode, taskId: number, goodsCategoryId?: number) => void
  >(
    (mode, taskId) => {
      UnderwritingService.taskPoolPageTracking(taskId.toString(), {
        uwOperation:
          mode === Mode.View
            ? TrackingUwOperationEnum.VIEW_CASE
            : TrackingUwOperationEnum.START_UNDERWRITING,
      });
      // 暂时全都切换回老的uw页面，隐藏uw-worksheet
      // const isLife =
      //   goodsCategoryId && life.some(item => +item === goodsCategoryId);
      // const isNb = taskType === TaskTypeEnum.NEW_BUSINESS;
      const path = UwDetailPath.NEW;
      navigate(
        source
          ? `/uw/${path}/${mode}/${taskId}/${source}`
          : `/uw/${path}/${mode}/${taskId}`,
        { state: location.state }
      );
    },
    [navigate, source, location.state]
  );

  const approvalChangeActive = useCallback(() => {
    UnderwritingCommentsService.uwCaseOperationsSubmit(curTaskId, {
      operation: UwCaseOperationEnum.MANUAL_ACTIVATE_UW_TASK,
    }).then(() => {
      handleCloseActivateModule();
      queryUwTaskPool();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curTaskId]);

  const model = useMemo(
    () => (
      <Modal
        title={t('Activate')}
        open={open}
        onCancel={() => handleCloseActivateModule()}
        onOk={approvalChangeActive}
        okText={t('Yes')}
        cancelText={t('No')}
      >
        {t('Are you sure to activate this case?')}
      </Modal>
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [open]
  );

  const handlePageChange = (page: number, pagesize: number) => {
    const params = {
      ...queryParams,
      pageSize: pagesize,
      pageNum: page,
    };
    setQueryParams(params);
  };

  const handleTableChange: TableProps<VerificationTaskPool>['onChange'] =
    useCallback(
      (pagination, filters, sorter, actionInfo) => {
        if (actionInfo.action === AntdTableChangeExtraAction.Sort) {
          const sortMap = orderedSorting(
            [...queryParams.sortMap],
            SortMapName.UW_TASK_PRIORITY_ORDER,
            sorter.order &&
              (sorter.order === SortDirectionEnum.ASCEND
                ? SortLowerCase.ASC
                : SortLowerCase.DESC)
          );
          setQueryParams({
            ...queryParams,
            sortMap,
            pageNum: defaultPageNum,
          });
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [queryParams]
    );

  const handleWithdrawnTask = useCallback(
    (taskId: number) => {
      handleWithdrawn(taskId);
    },
    [handleWithdrawn]
  );

  return (
    <div>
      {/* === task pool query form start === */}
      <QueryForm
        title={source ? t('Manual UW Query') : t('Manual UW Task Pool')}
        queryFields={queryFields}
        onSearch={transferQueryParams}
        formProps={{
          ref: formRef,
        }}
        onClear={() => formRef.current?.resetFields()}
        loading={loading}
      />
      {/* === task pool query form end === */}

      <QueryResultContainer>
        {/* === task pool filter bar start === */}
        <OperationContainer>
          <OperationContainer.Right>
            {taskPickUpShow ? (
              <Button onClick={getTaskPickUpDays}>{t('Task Pick Up')}</Button>
            ) : null}
            <QueryOperationSelect
              value={queryParams.sortMap?.[0]?.fieldName}
              options={[
                {
                  label: t('Sort by Operation Time'),
                  value: SortMapName.UW_GMT_MODIFIED,
                },
                {
                  label: t('Sort by Create Time'),
                  value: SortMapName.UW_GMT_CREATED,
                },
              ]}
              onChange={handleSortByTime}
            />
            {!source && (
              <QueryOperationSelect
                value={taskValue}
                options={[
                  {
                    label: t('Private Task - Active'),
                    value: SortByUserEnum.PRIVATE_TASK_ACTIVE,
                  },
                  {
                    label: t('Private Task - Inactive'),
                    value: SortByUserEnum.PRIVATE_TASK_INACTIVE,
                  },
                  {
                    label: t('Public Task'),
                    value: SortByUserEnum.PUBLIC_TASK,
                  },
                ]}
                onChange={handleSortByUser}
              />
            )}
            <Divider type="vertical" />
            <RenderModeSwitch
              value={queryParams.showMode}
              onChange={e => {
                setQueryParams({
                  ...queryParams,
                  showMode: e,
                });
              }}
            />
          </OperationContainer.Right>
        </OperationContainer>
        {/* === task pool filter bar end === */}
        {queryParams.showMode === RenderMode.Card ? (
          /* === task pool card start === */
          <TaskPoolCard
            pageSize={queryParams.pageSize!}
            pageNum={queryParams.pageNum!}
            total={total}
            tasks={tasks}
            loading={loading}
            handleGoToOperation={handleGoToOperation}
            handleActivate={showActivateModule}
            hasEditAuth={hasEditAuth}
            source={source}
            handlePageChange={handlePageChange}
            handleWithdrawnTask={handleWithdrawnTask}
            withdrawnLoading={withdrawnLoading}
          />
        ) : (
          /* === task pool card end === */
          <TaskPoolTable
            pageSize={queryParams.pageSize!}
            pageNum={queryParams.pageNum!}
            total={total}
            tasks={tasks}
            loading={loading}
            handleGoToOperation={handleGoToOperation}
            handleActivate={showActivateModule}
            hasEditAuth={hasEditAuth}
            source={source}
            handlePageChange={handlePageChange}
            handleTableChange={handleTableChange}
          />
        )}
        {model}
      </QueryResultContainer>
    </div>
  );
};
