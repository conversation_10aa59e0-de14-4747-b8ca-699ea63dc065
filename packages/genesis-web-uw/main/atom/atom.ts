import { AxiosResponse } from 'axios';
import { atom } from 'jotai';

import {
  CommonRespWithPagination,
  CommonResponse,
  PolicyStage,
  ProposalCommonTypes,
  UploadFileResponse,
  YesOrNo,
} from 'genesis-web-service';

/** policyList.uniqueKey */
type UniqueKey = string;

// 上传文件组件相关api
export interface UploadFileRelatedService {
  uploadFileAndCheckData?: () => Promise<void>;
  handleSave: (redirect?: boolean) => Promise<void>;
  uploadFile: (params: FormData) => Promise<UploadFileResponse>;
  checkFile: (
    data: ProposalCommonTypes.UploadFileBody
  ) => Promise<CommonResponse>;
  downloadBusinessData: (
    params: ProposalCommonTypes.DownloadBusinessDataBody
  ) => Promise<AxiosResponse<Blob>>;
  downloadAllBusinessData?: (busiNo: string) => Promise<AxiosResponse<Blob>>;
  submitService: (data: ProposalCommonTypes.UploadFileBody) => Promise<void>;
}

export interface RelatedServicePayload
  extends Partial<UploadFileRelatedService> {
  queryInsuredList: (
    page: number,
    pageSize: number,
    busiNo: number | string,
    params: Record<string, unknown>,
    stage?: PolicyStage
  ) => Promise<CommonRespWithPagination<Record<string, unknown>>>;
  saveInsuredRecord?: (
    temporaryId: number,
    goodsId: number,
    planId: number,
    packageId: number,
    params: Record<string, unknown>,
    stage?: PolicyStage
  ) => Promise<void>;
  deleteInsuredRecord?: (id: string, stage?: PolicyStage) => Promise<void>;
  clearAllData?: (
    businessType: ProposalCommonTypes.BusinessType,
    temporaryId: number,
    objectUniqueKey?: string, // BusinessType === eb_insured时不存在
    stage?: PolicyStage,
    planCode?: string
  ) => Promise<AxiosResponse<void>>;
}

export interface UnnamedInsuredPayload {
  unnamedInsuredFlag?: YesOrNo;
  policyUnnamedInsuredList?: Record<string, unknown>[];
}

export interface EBInsuredPayload {
  unnamedInsuredInfo: Record<UniqueKey, UnnamedInsuredPayload>;
  relatedService: RelatedServicePayload;
  stage?: PolicyStage;
}
/** EB Insured */
export const ebInsuredAtom = atom<EBInsuredPayload>({
  unnamedInsuredInfo: {},
  relatedService: {} as RelatedServicePayload,
});
