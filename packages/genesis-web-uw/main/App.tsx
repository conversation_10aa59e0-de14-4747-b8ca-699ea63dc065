import React, { FC, Suspense, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import {
  Route,
  Routes,
  matchRoutes,
  useLocation,
  useNavigate,
} from 'react-router-dom';

import { StyleProvider } from '@ant-design/cssinjs';
import { ThemeConfig } from 'antd';

import { SWRConfig as SWRConfigProvider } from 'swr';

import '@zhongan/nagrand-ui/dist/antd-reset.scss';
import '@zhongan/nagrand-ui/dist/resources/styles/fonts/font.css';
import '@zhongan/nagrand-ui/dist/style.css';

import {
  CommonAntdConfigProvider,
  CommonNagrandConfigProvider,
  Copyright,
} from 'genesis-web-component/lib/components';
import { LazySuspense } from 'genesis-web-component/lib/components/Lazy';
import { useCanActivateRoutes } from 'genesis-web-component/lib/components/RouteSwitch';
import { useUpdateThemeAntd5 } from 'genesis-web-component/lib/hook';
import { BizDictItem, Permission } from 'genesis-web-service';
import copyrightWhiteList from 'genesis-web-shared/lib/constant/copyrightWhiteList';
import { L10nProvider } from 'genesis-web-shared/lib/l10n';
import {
  AppRoute,
  selectGrantedPermissionMap,
} from 'genesis-web-shared/lib/redux';

import { ReduxProps } from '@uw/interface/redux.interface';
import {
  selectClientConfig,
  selectCurrencies,
  selectDateFormat,
  selectLocale,
  selectTimeZone,
  selectZeusConfig,
} from '@uw/redux/selector';
import '@uw/styles/main.css';
import '@uw/styles/reset.scss';

import { FormilyProvider } from './provider/FormilyProvider';
import routers, { basename } from './router';

const uwAntdPrefix = 'antd-uw';

interface Props {
  rootRedux: ReduxProps;
  [key: string]: unknown;
}

const App: FC<Props> = ({ rootRedux, loadPage, ...restProps }): JSX.Element => {
  const dateTimeFormat = useSelector(selectDateFormat);
  const zoneInfoList = useSelector(selectTimeZone);
  const zeusConfig = useSelector(selectZeusConfig);
  const config = useSelector(selectClientConfig);
  const fillLocale = useSelector(selectLocale);
  const currencyInfoList = useSelector(selectCurrencies);
  const token = useUpdateThemeAntd5(rootRedux.theme, uwAntdPrefix);
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const permissionMicro: Record<string, Permission> = useSelector(
    selectGrantedPermissionMap
  );
  const permissionMap = rootRedux.permissions ?? permissionMicro ?? {};

  const activatedRoutes = useCanActivateRoutes(
    routers as AppRoute[],
    rootRedux.permissions || {},
    !!rootRedux.state?.clientConfig?.security
  );

  const styleProviderProps = useMemo(
    () =>
      // eslint-disable-next-line no-underscore-dangle
      window.__POWERED_BY_QIANKUN__
        ? { container: document.querySelector('[data-name="genesis-web-uw"]') }
        : {},
    []
  );

  useEffect(() => {
    const matchRouters = matchRoutes(routers ?? [], pathname, basename) || [];
    matchRouters.forEach(selectedRoute => {
      const { oneOfPermissions } =
        (selectedRoute.route as { oneOfPermissions?: string[] }) || {};

      if (
        !selectedRoute ||
        (permissionMap &&
          oneOfPermissions?.length &&
          oneOfPermissions.every(permission => !permissionMap?.[permission]))
      ) {
        window.history.pushState(null, '', '/404');
      }
    });
  }, [pathname, routers]);

  useEffect(() => {
    loadPage && navigate(loadPage);
  }, [loadPage, navigate]);

  return (
    <L10nProvider
      tenantDateTimeFormat={dateTimeFormat}
      zoneInfoList={zoneInfoList}
      currencyInfoList={currencyInfoList as BizDictItem[]}
      timeFormat={zeusConfig?.timeFormat}
      timeRangeFormat={zeusConfig?.timeRangeFormat}
    >
      <SWRConfigProvider
        value={{
          shouldRetryOnError: false,
          revalidateOnFocus: false,
        }}
      >
        <CommonNagrandConfigProvider
          prefixCls={uwAntdPrefix}
          currentLang={fillLocale?.lang}
          componentsConfig={{
            tablePageSizeOptions: zeusConfig.tablePageSizeOptions,
            cardPageSizeOptions: zeusConfig?.cardPageSizeOptions,
          }}
        >
          <CommonAntdConfigProvider
            prefixCls={uwAntdPrefix}
            currentLang={fillLocale?.lang}
            currentTheme={rootRedux.theme}
            messageDuration={rootRedux.messageDuration}
            theme={
              {
                ...token,
                components: {
                  ...(token.components ?? {}),
                  Badge: { ...(token.components?.Badge ?? {}), statusSize: 8 },
                },
                hashed: false,
              } as ThemeConfig
            }
          >
            <StyleProvider {...styleProviderProps}>
              <FormilyProvider>
                <Routes>
                  <Route path={basename}>
                    {activatedRoutes.map(route => (
                      <Route
                        key={route.path}
                        path={route.path}
                        element={
                          <Suspense fallback={<LazySuspense />}>
                            {route.component && (
                              <route.component {...restProps} />
                            )}
                            <Copyright
                              whiteList={copyrightWhiteList}
                              version={config?.version}
                            />
                          </Suspense>
                        }
                      ></Route>
                    ))}
                  </Route>
                </Routes>
              </FormilyProvider>
            </StyleProvider>
          </CommonAntdConfigProvider>
        </CommonNagrandConfigProvider>
      </SWRConfigProvider>
    </L10nProvider>
  );
};

export default App;
