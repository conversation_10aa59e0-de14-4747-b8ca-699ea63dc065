@import '@uw/styles/common.scss';

.upload-text {
  font-weight: bold;
}
.upload-hint {
  color: var(--text-color-tertiary);
}
.upload-desc-box {
  color: var(--text-color-tertiary);
}
.input-file-box {
  display: inline-block;
  min-width: 240px;
  height: 32px;
  margin-right: $gap-md;
  line-height: 32px;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-default);
}
// .create-policy-section {
.upload-transparent {
  visibility: hidden;
  opacity: 0;
  :global {
    .antd-uw-upload-list {
      display: none;
    }
  }
}
// }

:export {
  textColorTertiary: var(--text-color-tertiary);
}
