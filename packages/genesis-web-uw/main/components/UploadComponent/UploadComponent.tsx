import React, { useMemo, FC, useCallback } from 'react';
import { Row, Col, Button, Upload } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface.d';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import { useTranslation } from 'react-i18next';
import { UploadOutlined } from '@ant-design/icons';

import styles from './UploadComponent.module.scss';

interface Props {
  hideTitle?: boolean;
  style: Record<string, string>;
  fileList: UploadFile<any>[];
  disabled: boolean;
  handleUploadFiles: (formData: FormData, filename: string) => void;
  uploadText?: string;
  uploadType?: string;
}
export const UploadComponent: FC<Props> = ({
  hideTitle,
  style,
  disabled,
  handleUploadFiles,
  fileList,
  uploadText,
  uploadType,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const handleConfirmToUpload = useCallback(() => {
    (
      document.querySelector(
        '#uploadWrapper .antd-uw-upload>.antd-uw-btn'
      ) as HTMLElement
    ).click();
  }, []);

  const uploadProps = useMemo<UploadProps>(
    () => ({
      name: 'file',
      accept: uploadType ?? '.xls, .xlsx',
      multiple: false,
      fileList,
      customRequest: (cb: UploadRequestOption) => {
        const formData = new FormData();
        formData.append('file', cb.file);
        handleUploadFiles(formData, (cb.file as File).name);
      },
    }),
    [fileList]
  );

  return (
    <section style={style} id="uploadWrapper">
      <Row>
        <Col span="24" key="uploadform">
          {!hideTitle && (
            <div
              style={{
                color: styles.textColorTertiary,
              }}
            >
              {t('Medical Examination Item ')}
            </div>
          )}
          <span className={styles['input-file-box']}>
            {fileList && fileList.length > 0
              ? decodeURIComponent(fileList[0]?.name)
              : ''}{' '}
          </span>
          {(fileList || []).length > 0 && (
            <Button
              disabled={disabled}
              icon={<UploadOutlined />}
              onClick={handleConfirmToUpload}
            >
              {t('Upload')}
            </Button>
          )}
          <Upload
            id="uploadFile"
            disabled={disabled}
            className={
              (fileList || []).length > 0 ? styles['upload-transparent'] : ''
            }
            {...uploadProps}
            fileList={fileList}
          >
            <Button icon={<UploadOutlined />}>{t('Upload')}</Button>
          </Upload>
          <div className={styles['upload-desc-box']}>
            {uploadText ?? t('You can only upload XLSX')}
          </div>
        </Col>
      </Row>
    </section>
  );
};
