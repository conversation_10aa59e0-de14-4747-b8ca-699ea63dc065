import React, { FC, ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { LeftOutlined, UpOutlined } from '@ant-design/icons';
import { Button } from 'antd';

import { useToggle } from 'ahooks';

import { Divider } from 'genesis-web-component/lib/components';

import styles from './PageDetailHeader.module.scss';

interface Props {
  backText?: string;
  backUrl?: string;
  topTipContent?: ReactNode;
  optionButtons?: ReactNode;
  heading?: string | ReactNode;
  tags?: ReactNode;
  showMore?: boolean;
  moreText?: string;
  moreInfo?: ReactNode;
  initialExpand?: boolean;
  handleBack?: () => void;
  backButtonDisabled?: boolean;
}
export const PageDetailHeader: FC<Props> = ({
  backText,
  backUrl,
  topTipContent,
  optionButtons,
  heading,
  tags,
  showMore,
  moreText,
  moreInfo,
  initialExpand,
  handleBack,
  backButtonDisabled,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const [isExpanded, toggleExpand] = useToggle(initialExpand);
  const handleMore = useCallback(() => {
    toggleExpand.toggle();
  }, []);
  const handleGoBack = () => {
    if (!backUrl) return;
    navigate(backUrl);
  };
  return (
    <div className={styles.pageHeaderContainer}>
      <section className={styles.topTipContent}>{topTipContent}</section>
      <section className={styles.optionsContainer}>
        <div style={{ flexGrow: 1 }}>
          <Button
            onClick={handleBack ?? handleGoBack}
            icon={<LeftOutlined />}
            disabled={backButtonDisabled}
          >
            {backText ?? t('Back')}
          </Button>
        </div>
        {optionButtons}
      </section>

      {heading && (
        <section className={styles.headingContainer}>
          <div className={styles.headingBox}>{heading}</div>
          <div className={styles.headingTagWrapper}>
            <div className={styles.tagBox}>{tags}</div>
            {showMore && (
              <Button
                type="text"
                style={{
                  padding: 0,
                  height: '22px',
                  lineHeight: '1em',
                  fontWeight: 700,
                }}
                onClick={handleMore}
              >
                {moreText ?? t('More Info')}
                <UpOutlined className={isExpanded ? '' : 'collaped'} />
              </Button>
            )}
          </div>
        </section>
      )}

      {showMore && isExpanded && (
        <>
          <Divider
            dashed={true}
            style={{
              margin: '0',
            }}
          />
          <section className={styles.moreInfo}>{moreInfo}</section>
        </>
      )}

      <Divider
        style={{
          margin: '0',
          position: 'absolute',
          width: '100%',
          left: 0,
        }}
      />
    </div>
  );
};
