@import '@uw/styles/variables.scss';

.page-header-container {
  background-color: var(--white);
  position: relative;
  padding: 0 $gap-lg;
  .top-tip-content {
    margin: 0 (-$gap-lg);
  }
  .options-container {
    display: flex;
    padding: $gap-md 0;
    align-items: center;
    border-bottom: 1px solid var(--border-line-color);
  }
  .heading-container {
    padding: $gap-md 0;
    .heading-box {
      font-weight: 700;
      font-size: $font-size-lg;
      @include line-height-24;
    }
    .heading-tag-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: $gap-md;
    }
  }
  :global {
    .anticon {
      transition: all ease 300ms;
      &.collaped {
        transform: rotate(180deg);
      }
    }
  }

  .more-info {
    padding: $gap-md 0;
  }
}
