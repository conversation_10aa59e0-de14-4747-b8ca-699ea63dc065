import type { CSSProperties } from 'react';
import { FC, MouseEvent } from 'react';

import { Button, ButtonProps, Tooltip } from 'antd';

import { ArrayBase } from '@formily/antd-v5';
import { observer } from '@formily/react';

import cls from 'clsx';
import { isNumber } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

interface EditButtonProps extends ButtonProps {
  onClick: (evt: MouseEvent) => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
}
/**
 * @description: 编辑按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const EditButton: FC<EditButtonProps> = ({
  onClick,
  iconStyle,
  tooltipTitle,
  className,
  ...props
}) => (
  <Tooltip placement="top" title={tooltipTitle}>
    <Button
      type="text"
      size="small"
      onClick={onClick}
      icon={<Icon type="edit" style={iconStyle} />}
      className={cls(styles.buttonLink, className)}
      {...props}
    />
  </Tooltip>
);

// 为 onEdit 函数传递 当前选中行的 record
interface ArrayRowEditActionProps extends ButtonProps {
  onEdit: (index: number | undefined) => void;
}
export const ArrayRowEditAction = observer((props: ArrayRowEditActionProps) => {
  const { onEdit } = props;
  const index = ArrayBase?.useIndex?.();
  const array = ArrayBase?.useArray?.();

  const handleEdit = (event: MouseEvent) => {
    // 事件不应冒泡，避免非必要麻烦
    event.stopPropagation();

    if (onEdit && typeof onEdit === 'function' && isNumber(index)) {
      const rowData = array?.field?.initialValue[index];
      onEdit(rowData);
    }
  };

  return <EditButton {...props} onClick={handleEdit} />;
});
