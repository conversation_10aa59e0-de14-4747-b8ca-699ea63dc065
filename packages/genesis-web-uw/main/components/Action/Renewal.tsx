import type { CSSProperties } from 'react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { ButtonProps } from 'antd';

import cls from 'clsx';

import { CommonIconAction } from '@zhongan/nagrand-ui';

import { TableRenew } from '@uw/assets/new-icons';

import styles from './style.module.scss';

interface RenewalButtonProps extends ButtonProps {
  onClick: () => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
}
/**
 * @description: 查看按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const RenewalButton: FC<RenewalButtonProps> = ({
  onClick,
  tooltipTitle,
  className,
  ...props
}) => {
  const { t } = useTranslation(['common']);

  return (
    <CommonIconAction
      icon={<TableRenew />}
      tooltipTitle={tooltipTitle || t('Renewal')}
      onClick={onClick}
      className={cls(styles.buttonLink, className)}
      {...props}
    />
  );
};
