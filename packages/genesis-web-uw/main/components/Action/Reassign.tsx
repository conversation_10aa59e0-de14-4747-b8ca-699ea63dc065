import type { CSSProperties } from 'react';
import { FC } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import cls from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

interface ReassignButtonProps extends ButtonProps {
  onClick: () => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
}
/**
 * @description: 查看按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const ReassignButton: FC<ReassignButtonProps> = ({
  onClick,
  iconStyle,
  tooltipTitle,
  className,
  ...props
}) => {
  const { t } = useTranslation(['common']);

  return (
    <Tooltip placement="top" title={tooltipTitle || t('Reassign')}>
      <Button
        type="text"
        size="small"
        onClick={onClick}
        icon={<Icon type="reassign" style={iconStyle} />}
        className={cls(styles.buttonLink, className)}
        {...props}
      />
    </Tooltip>
  );
};
