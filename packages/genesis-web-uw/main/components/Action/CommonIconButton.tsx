import { FC, MouseEvent } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import cls from 'clsx';

import styles from './style.module.scss';

interface CommonIconButtonProps extends ButtonProps {
  onClick?: (env?: MouseEvent) => void;
  tooltipTitle?: string;
}

/**
 * @description: 普通的icon按钮
 * @param onClick: 点击事件
 * @param icon: icon图标
 */

export const CommonIconButton: FC<CommonIconButtonProps> = ({
  onClick,
  tooltipTitle,
  className,
  ...props
}) => (
  <Tooltip placement="top" title={tooltipTitle}>
    <Button
      type="text"
      size="small"
      onClick={onClick}
      {...props}
      className={cls(styles.buttonLink, className)}
    />
  </Tooltip>
);
