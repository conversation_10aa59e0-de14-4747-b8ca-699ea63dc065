import { FC } from 'react';
import type { CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, ButtonProps, TooltipProps } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import { Popconfirm } from './Popconfirm';

interface PopconfirmnProps extends ButtonProps {
  onClick: () => void;
  deleteConfirmContent?: string;
  iconStyle?: CSSProperties;
  overlayClassName?: string;
  placement?: TooltipProps['placement'];
  getContainer?: () => HTMLElement;
  disabled?: boolean;
}

/**
 * @description: 删除气泡确认框
 * @param onClick: 点击事件
 * @param deleteConfirmContent: 气泡提示文案
 * @param iconStyle: icon图标的样式
 * @param overlayClassName: 卡片类名
 * @param placement: 气泡位置
 */
export const DeletePopconfirm: FC<PopconfirmnProps> = ({
  onClick,
  deleteConfirmContent,
  iconStyle,
  overlayClassName,
  placement,
  disabled,
  ...props
}) => {
  const { t } = useTranslation(['common']);

  return (
    <Popconfirm
      placement={placement}
      disabled={disabled}
      onConfirm={onClick}
      title={deleteConfirmContent || t('Are you sure to delete this record?')}
      overlayClassName={overlayClassName}
    >
      <Button
        type="text"
        size="small"
        disabled={disabled}
        icon={<Icon type="delete" style={iconStyle} />}
        {...props}
      />
    </Popconfirm>
  );
};
