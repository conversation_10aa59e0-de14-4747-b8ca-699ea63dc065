import type { CSSProperties } from 'react';
import { FC, MouseEvent } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import cls from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

interface ConfirmButtonProps extends ButtonProps {
  onClick: (evt: MouseEvent) => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
  iconType?: string;
}
/**
 * @description: 确认按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const ConfirmButton: FC<ConfirmButtonProps> = ({
  onClick,
  iconStyle,
  tooltipTitle,
  className,
  iconType = 'check',
  ...props
}) => (
  <Tooltip placement="top" title={tooltipTitle}>
    <Button
      type="text"
      size="small"
      onClick={onClick}
      icon={<Icon type={iconType} style={iconStyle} />}
      className={cls(styles.buttonLink, className)}
      {...props}
    />
  </Tooltip>
);
