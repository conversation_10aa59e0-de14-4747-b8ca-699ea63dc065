import type { CSSProperties } from 'react';
import { FC, MouseEvent } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import cls from 'clsx';
import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

interface CancelButtonProps extends ButtonProps {
  onClick: (evt: MouseEvent) => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
}
/**
 * @description: 取消按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const CancelButton: FC<CancelButtonProps> = ({
  onClick,
  iconStyle,
  tooltipTitle,
  className,
  ...props
}) => (
  <Tooltip placement="top" title={tooltipTitle}>
    <Button
      type="text"
      size="small"
      onClick={onClick}
      icon={<Icon type="close" style={iconStyle} />}
      className={cls(styles.buttonLink, className)}
      {...props}
    />
  </Tooltip>
);
