import type { CSSProperties } from 'react';
import { FC } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import cls from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

interface ViewButtonProps extends ButtonProps {
  onClick: () => void;
  iconStyle?: CSSProperties;
  tooltipTitle?: string;
}
/**
 * @description: 查看按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 */

export const ViewButton: FC<ViewButtonProps> = ({
  onClick,
  iconStyle,
  tooltipTitle,
  className,
  ...props
}) => (
  <Tooltip placement="top" title={tooltipTitle}>
    <Button
      type="text"
      size="small"
      onClick={onClick}
      icon={<Icon type="view" style={iconStyle} />}
      className={cls(styles.buttonLink, className)}
      {...props}
    />
  </Tooltip>
);
