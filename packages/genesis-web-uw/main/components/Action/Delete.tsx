import { FC } from 'react';
import type { CSSProperties } from 'react';
import { Button, ButtonProps } from 'antd';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm/DeleteConfirm';

import { Icon } from '@zhongan/nagrand-ui';

interface DeleteButtonProps extends ButtonProps {
  onClick: () => void;
  needStopPropagation?: boolean;
  deleteConfirmContent?: string;
  iconStyle?: CSSProperties;
}

/**
 * @description: 删除按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 * @param deleteConfirmContent: 点击删除btn后弹窗中的文案
 */
export const DeleteButton: FC<DeleteButtonProps> = ({
  onClick,
  deleteConfirmContent,
  iconStyle,
  needStopPropagation,
  ...props
}) => (
  <DeleteConfirm
    onOk={onClick}
    content={deleteConfirmContent}
    needStopPropagation={needStopPropagation}
  >
    <Button
      type="text"
      size="small"
      icon={<Icon type="delete" style={iconStyle} />}
      {...props}
    />
  </DeleteConfirm>
);
