import { Icon } from '@zhongan/nagrand-ui';
import { Button, ButtonProps } from 'antd';
import { FC, MouseEvent } from 'react';
import type { CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';

export interface AddButtonProps extends ButtonProps {
  onClick?: (env?: MouseEvent) => void;
  buttonText?: string;
  iconStyle?: CSSProperties;
}

/**
 * @description: 新增按钮
 * @param onClick: 点击事件
 * @param iconStyle: icon图标的样式
 * @param buttonText: add按钮的文案
 */

export const AddButton: FC<AddButtonProps> = ({
  onClick,
  buttonText,
  iconStyle,
  ...props
}) => {
  const { t } = useTranslation(['common']);
  return (
    <Button
      onClick={onClick}
      icon={<Icon type="add" style={iconStyle} />}
      {...props}
    >
      {buttonText || t('Add New')}
    </Button>
  );
};
