import { Button, ButtonProps } from 'antd';
import { FC, MouseEvent } from 'react';
import { useTranslation } from 'react-i18next';

interface WithdrawButtonProps extends ButtonProps {
  onClick?: (env?: MouseEvent) => void;
}

/**
 * @description: withdraw按钮
 * @param onClick: 点击事件
 */
export const WithdrawButton: FC<WithdrawButtonProps> = ({
  onClick,
  ...props
}) => {
  const { t } = useTranslation(['common']);

  return (
    <Button danger onClick={onClick} {...props}>
      {t('Withdraw')}
    </Button>
  );
};
