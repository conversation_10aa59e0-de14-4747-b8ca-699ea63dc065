import { Popconfirm as Confirm, PopconfirmProps } from 'antd';
import cls from 'clsx';

import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.module.scss';

/**
 * @description: 气泡确认框
 */
export const Popconfirm: FC<PopconfirmProps> = ({
  children,
  overlayClassName,
  ...props
}) => {
  const { t } = useTranslation();
  return (
    <Confirm
      {...props}
      overlayClassName={cls(styles.popconfirm, overlayClassName)}
      okText={t('Confirm')}
      cancelText={t('Cancel')}
      icon={
        <Icon
          type="info-fill"
          style={{
            transForm: 'rotate(180deg)',
          }}
          className="gc-warning-color"
        />
      }
    >
      {children}
    </Confirm>
  );
};
