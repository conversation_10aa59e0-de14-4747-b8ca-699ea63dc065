import { Card } from 'antd';
import { CSSProperties, FC, ReactNode } from 'react';
import clsx from 'clsx';

import { AvatarCircle } from '@uw/pages/policy-query-pages/components/AvatarCircle';

import { TextBody } from '@zhongan/nagrand-ui';

import WarningSvg from '@uw/assets/icons/warning.svg';

import styles from './PersonCard.module.scss';

interface Props {
  name: string;
  extra: ReactNode;
  isActive: boolean;
  style?: CSSProperties;
  onClick?: () => void;
  isShowWarningSvg?: boolean;
}

export const PersonCard: FC<Props> = ({
  name,
  extra,
  isActive,
  children,
  style,
  onClick,
  isShowWarningSvg,
}) => (
  <Card
    className={clsx(styles.personCardWrapper, isActive && styles.active)}
    title={
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <AvatarCircle
          name={name}
          style={{ width: 32, height: 32, lineHeight: '32px' }}
        />
        <TextBody type="h5" weight={700} className={styles.cardName}>
          {name}
        </TextBody>
        {isShowWarningSvg && (
          <WarningSvg style={{ marginLeft: styles.gapXs }} />
        )}
      </div>
    }
    extra={extra}
    bordered={false}
    style={style}
    onClick={onClick}
  >
    {children}
  </Card>
);
