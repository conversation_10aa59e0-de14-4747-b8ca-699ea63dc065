import { CSSProperties, FC, useCallback, useEffect, useState } from 'react';
import Slider from 'react-slick';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { PersonCard } from './PersonCard';
import styles from './PersonCard.module.scss';
import { PersonInfoType } from './interface';

interface Props {
  personInfoList: PersonInfoType[];
  extra: (index: number, isDisabledBtn?: boolean) => JSX.Element;
  selectedIdx?: number;
  handleSelectPerson: (index: number) => void;
  style?: CSSProperties;
}
const slickSettings = {
  speed: 1000,
  slidesToShow: 4,
  variableWidth: true,
  infinite: false,
  slidesToScroll: 1,
  className: styles.zaSlider,
};
export const PersonCardSlick: FC<Props> = ({
  personInfoList,
  extra,
  style,
  selectedIdx,
  handleSelectPerson,
}) => {
  const [curIndex, setCurIndex] = useState(selectedIdx);
  useEffect(() => {
    setCurIndex(selectedIdx);
  }, [selectedIdx]);
  const handleSelect = useCallback<(index: number) => void>(
    index => {
      setCurIndex(index);
      handleSelectPerson(index);
    },
    [handleSelectPerson]
  );
  return (
    <div style={style}>
      <Slider {...slickSettings}>
        {personInfoList?.map((personInfo, index) => {
          const { name, leftItem, rightItem, isShowWarningSvg, isDisabledBtn } =
            personInfo;
          return (
            <div style={{ width: 316 }} key={index}>
              <PersonCard
                name={name}
                extra={extra(index, isDisabledBtn)}
                isActive={curIndex === index}
                isShowWarningSvg={isShowWarningSvg}
                onClick={() => {
                  handleSelect(index);
                }}
              >
                <div className={styles.cardContent}>
                  <div>{leftItem}</div>
                  <div>{rightItem}</div>
                </div>
              </PersonCard>
            </div>
          );
        })}
      </Slider>
    </div>
  );
};
