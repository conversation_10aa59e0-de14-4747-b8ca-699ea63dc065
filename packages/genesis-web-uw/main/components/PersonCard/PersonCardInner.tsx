import { Divider } from 'antd';
import { FC } from 'react';

import { AvatarCircle } from '@uw/pages/policy-query-pages/components/AvatarCircle';

import { TextBody } from '@zhongan/nagrand-ui';

import styles from './PersonCard.module.scss';
import { PersonInfoType } from './interface';

interface Props {
  extra: (isDisabledBtn?: boolean) => JSX.Element;
  personInfoItem?: PersonInfoType;
}

export const PersonCardInner: FC<Props> = ({ extra, personInfoItem }) => {
  const { name, leftItem, rightItem, isDisabledBtn } = personInfoItem ?? {};
  return (
    <section
      className={styles.personCardInner}
      style={{ display: 'flex', alignItems: 'center' }}
    >
      <AvatarCircle
        name={name ?? ''}
        style={{ width: 32, height: 32, lineHeight: '32px' }}
      />
      <TextBody type="h5" weight={700} style={{ marginLeft: styles.gapXs }}>
        {name ?? ''}
      </TextBody>
      {leftItem && (
        <TextBody style={{ marginLeft: styles.gapMd }}>{leftItem}</TextBody>
      )}
      {rightItem && (
        <TextBody style={{ marginLeft: styles.gapMd }}>{rightItem}</TextBody>
      )}
      <Divider type="vertical" style={{ margin: `0 ${styles.gapMd}` }} />
      {extra(isDisabledBtn)}
    </section>
  );
};
