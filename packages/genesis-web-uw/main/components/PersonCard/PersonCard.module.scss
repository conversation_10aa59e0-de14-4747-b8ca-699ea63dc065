.person-card-wrapper {
  height: 110px;
  border-radius: var(--border-radius-big);
  background-color: var(--table-header-bg);
  &.active {
    background: var(--white);
  }
  :global {
    .#{$ant-prefix}-card-head {
      border-bottom: 0;
    }
    .#{$ant-prefix}-card-body {
      padding: $gap-md $gap-lg;
    }
  }
  .card-content {
    display: flex;
    justify-content: space-between;
  }
  .card-name {
    margin-left: var(--gap-xs);
    max-width: 128px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.za-slider {
  :global {
    .slick-track {
      margin-left: 0;
    }
  }
}

:global {
  .slick-track {
    display: flex;
    .slick-slide {
      flex-shrink: 1;
      padding-right: $gap-md;
    }
  }
  .slick-prev,
  .slick-next {
    width: 64px !important;
    height: 100% !important;
    right: 0 !important;
    background: linear-gradient(
      268.15deg,
      rgba(255, 255, 255, 0.8) 40%,
      rgba(255, 255, 255, 0) 100%
    ) !important;
    &::before {
      display: block;
      width: 32px !important;
      height: 32px !important;
      line-height: 30px !important;
      font-family: inherit !important;
      color: var(--text-color) !important;
      border: 1px solid var(--divider-color) !important;
      border-radius: 100%;
      box-shadow: 0px 4px 24px 0px rgba(16, 42, 67, 0.12);
      background: var(--white) !important;
    }
    overflow: hidden;
    padding: 0 $gap-md !important;
  }

  .slick-prev {
    left: 0 !important;
    z-index: 10;
    background: linear-gradient(
      -268.15deg,
      rgba(255, 255, 255, 0.8) 40%,
      rgba(255, 255, 255, 0) 100%
    ) !important;
  }
}

:export {
  gapMd: $gap-md;
  gapXs: var(--gap-xs);
}
