.time-line-style {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--gap-xss);
}

.associated-box {
  background: var(--white);
  margin-top: $gap-md;
  padding: $gap-md 10px;
  height: auto;
  box-sizing: content-box;
  .associated-container {
    padding: 0 8px;
    overflow-y: auto;
    .associated-list-box {
      .application-no {
        display: inline-flex;
        align-items: center;
        text-decoration: underline;
        cursor: pointer;
        font-size: $font-size-sm;
        font-weight: 500;
        padding-right: $gap-md;
        white-space: nowrap;
        max-width: 74%;
        & > span {
          display: inline-block;
          max-width: 60%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-decoration: underline;
        }
      }

      .application-no-block {
        display: flex;
        max-width: 100%;
        padding-right: 0;
        text-decoration: underline;
        cursor: pointer;
        font-size: $font-size-sm;
        font-weight: 500;
        white-space: nowrap;
        & > span {
          display: inline-block;
          max-width: 60%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-decoration: underline;
        }
      }
      .associated-goods-name {
        margin-bottom: $gap-md;
        font-size: $font-size-sm;
        color: $label;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:last-of-type {
        .associated-goods-name {
          margin-bottom: 0 !important;
        }
      }
    }
  }
}

:export {
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  White: var(--white);
}
