import React, { FC } from 'react';
import { Skeleton, Tooltip } from 'antd';
import { useBizDict } from '@uw/hook/useBizDict';
import { StatusTag } from '@zhongan/nagrand-ui';

import {
  AssociatedUWTaskRecord,
  AssociatedBasicRecord,
} from 'genesis-web-service';

import clsx from 'clsx';
import { StatueEnumType } from '@uw/interface/enum.interface';

import styles from './index.module.scss';

interface Props {
  classNames?: string;
  sectionTitle: string;
  associatedList: AssociatedBasicRecord[] | AssociatedUWTaskRecord[];
  associatedNoTitle: string;
  loading: boolean;
  currentLoading?: boolean;
  isStatusShowInNewLine?: boolean;
  statusEnumType: StatueEnumType;
  handleClickNo: (
    associatedItem: AssociatedBasicRecord | AssociatedUWTaskRecord
  ) => void;
}

export const AssociatedList: FC<Props> = ({
  classNames,
  sectionTitle,
  associatedList,
  associatedNoTitle,
  loading,
  currentLoading,
  isStatusShowInNewLine,
  statusEnumType,
  handleClickNo,
}) => {
  const applicationTypeEnum = useBizDict(statusEnumType);

  if (associatedList?.length === 0) {
    return null;
  }

  return (
    <div className={clsx(styles.associatedBox, classNames)}>
      <Skeleton loading={loading || currentLoading} active>
        <div>
          <h2
            style={{
              fontWeight: '700',
              fontSize: styles.gapMd,
            }}
          >
            <span
              style={{
                display: 'block',
                marginBottom: '28px',
              }}
            >
              {sectionTitle}
            </span>
          </h2>
          <div
            className={styles.associatedContainer}
            style={{ maxHeight: isStatusShowInNewLine ? '273px' : '248px' }}
          >
            {associatedList?.map((item, index) => {
              const AssociatedStatusTag = (
                <StatusTag
                  statusI18n={
                    applicationTypeEnum?.find(
                      type => type?.enumItemName === (item?.status as string)
                    )?.dictValueName ?? item?.status
                  }
                  style={{
                    padding: `0 ${styles.gapXs}`,
                    display: 'block',
                    fontWeight: 500,
                    whiteSpace: 'nowrap',
                    marginTop: isStatusShowInNewLine ? styles.gapXs : 0,
                    marginBottom: isStatusShowInNewLine ? styles.gapMd : 0,
                  }}
                />
              );
              return (
                <div key={index} className={styles.associatedListBox}>
                  <div
                    className={styles.timeLineStyle}
                    style={{ display: isStatusShowInNewLine ? 'block' : '' }}
                  >
                    <span
                      className={
                        isStatusShowInNewLine
                          ? styles.applicationNoBlock
                          : styles.applicationNo
                      }
                      onClick={() => handleClickNo(item)}
                    >
                      {associatedNoTitle}:{' '}
                      <Tooltip title={item?.applicationNo}>
                        {item?.applicationNo}
                      </Tooltip>
                    </span>
                    {!isStatusShowInNewLine && AssociatedStatusTag}
                  </div>
                  <div
                    className={styles.associatedGoodsName}
                    style={{
                      marginBottom: isStatusShowInNewLine
                        ? styles.gapXs
                        : styles.gapMd,
                    }}
                  >
                    <Tooltip title={item?.goodsName}>
                      <span>{item?.goodsName}</span>
                    </Tooltip>
                  </div>
                  {isStatusShowInNewLine && AssociatedStatusTag}
                </div>
              );
            })}
          </div>
        </div>
      </Skeleton>
    </div>
  );
};
