import React, { VFC, useMemo } from 'react';

import { NamePath } from 'antd/es/form/interface';
import cx from 'clsx';
import { get } from 'lodash-es';

import { DynamicFormSchemaConfigs } from 'genesis-web-component/lib/components/PaymentMethodFormV4/interface';
import { LayoutEnum, UWRefundPaymentAccount } from 'genesis-web-service';

import { generateFormNamePath } from '@uw/components/PaymentAccount/util';
import { useBizDict } from '@uw/hook/useBizDict';
import { BizDict, SelectEnumOption } from '@uw/interface/enum.interface';
import { generateDataIndexForTableFromSchema } from '@uw/util/generateDataIndexForTableFromSchema';

import { SchemaFormItem, SchemaFormRow } from './components/SchemaFormRow';
import { DynamicFormItemContext, SchemaFormContextProps } from './context';
import { SchemaFormContext } from './context/schemaFormContext';

type RecordType = UWRefundPaymentAccount;
interface BasicProps extends SchemaFormContextProps {
  afterRecord?: RecordType;
  className?: string;
  formFieldPrefix: NamePath;
  rowClassName?: string;
  tableItemClassName?: string;
  schema: DynamicFormSchemaConfigs;
  showBeforeColumn?: boolean;
}

type Props = BasicProps;

export const SchemaForm: VFC<Props> = props => {
  const {
    afterRecord,
    className,
    formFieldPrefix,
    layout = 'horizontal',
    rowClassName,
    tableItemClassName,
    schema,
    showBeforeColumn = true,
  } = props;

  const contextValue = useMemo(
    () => ({
      layout,
      showBeforeColumn,
    }),
    [showBeforeColumn, layout]
  );

  const accountTypeParentEnums = useBizDict('accountType') as BizDict[];
  const bankCityEnums = useBizDict('bankCity');

  const accountSubTypeEnums = accountTypeParentEnums?.reduce(
    (pre, cur) => [...pre, ...(cur.childList as BizDict[])],
    [] as BizDict[]
  );
  const accountSubTypeOptions = accountSubTypeEnums?.map(item => ({
    label: item?.dictValueName,
    value: item?.enumItemName,
  }));

  const bankCityOptions = bankCityEnums?.map(item => ({
    label: item?.dictValueName,
    value: item?.dictValue,
  }));

  const options = (data: string | number): SelectEnumOption[] => {
    let result;

    switch (data) {
      case 'accountSubType':
        result = accountSubTypeOptions;
        break;
      case 'bankCity':
        result = bankCityOptions;
        break;
      default:
        break;
    }

    return result as SelectEnumOption[];
  };

  return (
    <div className={cx(className)}>
      <SchemaFormContext.Provider value={contextValue}>
        {schema?.map(item => {
          // dataType !== FILE
          const modulePropertiesWithoutFile = item?.moduleProperties?.filter(
            moduleProps => ![LayoutEnum.File]?.includes(moduleProps?.dataType)
          );
          return modulePropertiesWithoutFile?.map(property => {
            const { propertyCode } = property;
            const propertyPath = generateDataIndexForTableFromSchema(
              item?.moduleValueField,
              propertyCode
            );
            const { dataType } = property;
            const label = property.displayName;
            const afterValue = get(afterRecord, propertyPath);

            return (
              <DynamicFormItemContext.Provider
                key={propertyCode}
                value={{
                  formFieldPrefix,
                  propertyCode,
                  valuePath: generateFormNamePath(
                    formFieldPrefix,
                    propertyPath
                  ),
                }}
              >
                <SchemaFormRow
                  className={rowClassName}
                  tableItemClassName={tableItemClassName}
                  label={label}
                  afterComponent={
                    <SchemaFormItem
                      afterValue={afterValue}
                      options={options(propertyPath[0])}
                      dataType={dataType}
                    />
                  }
                />
              </DynamicFormItemContext.Provider>
            );
          });
        })}
      </SchemaFormContext.Provider>
    </div>
  );
};