import { FormItemProps } from 'antd';
import { Rule } from 'antd/es/form';
import { NamePath } from 'antd/lib/form/interface';
import { LayoutEnum } from 'genesis-web-service';
import React, { ReactNode } from 'react';

export interface SchemaFormItemContextProps
  extends Pick<FormItemProps, 'rules'> {
  beforeComponent?: ReactNode;
  bizDictKey?: string;
  cascade?: Record<string, string | number>;
  className?: string;
  /**
   * 币种
   */
  currency?: string;
  dataType?: LayoutEnum;
  formFieldPrefix: NamePath;
  isDisabled?: boolean;
  isRequired?: boolean;
  onFieldChange?: (value?: string | number) => void;
  onCascadeFieldsChange?: (
    values: { name: NamePath; value: string | number }[]
  ) => void;
  propertyCode: string;
  rules?: Rule[];
  showUpdateHighlight?: boolean;
  valuePath: NamePath;
  /**
   * 时区, 某些dateTime类型的字段需要额外的时区信息而非浏览器时区
   */
  zoneId?: string;
}
export const DynamicFormItemContext =
  React.createContext<SchemaFormItemContextProps>({
    propertyCode: '',
    valuePath: '',
    formFieldPrefix: '',
  });

export interface SchemaFormContextProps {
  layout?: 'vertical' | 'horizontal';
  showBeforeColumn?: boolean;
}
