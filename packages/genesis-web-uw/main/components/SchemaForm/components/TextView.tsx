import cx from 'clsx';
import React, { ReactNode, VFC } from 'react';
import { i18nFn } from '@uw/util/i18nFn';

import { isEmpty } from 'lodash-es';

interface Props {
  className?: string;
  style?: React.CSSProperties;
  value?: ReactNode;
}

export const TextView: VFC<Props> = props => {
  const { className, value, style } = props;
  const text = isEmpty(value) ? i18nFn('No record') : value;

  return (
    <span className={cx(className, 'break-words')} style={style}>
      {text}
    </span>
  );
};
