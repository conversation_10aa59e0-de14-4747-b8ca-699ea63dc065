import React, { VFC, useContext } from 'react';
import { i18nFn } from '@uw/util/i18nFn';
import { LayoutEnum } from 'genesis-web-service';
import { SelectEnumOption } from '@uw/interface/enum.interface';
import { isEmpty } from 'lodash-es';

import { transformValueToLabel } from '@uw/components/PaymentAccount/util';

import { DynamicFormItemContext } from '../../context';
import { TextView } from '../TextView';

export interface BaseItemRendererProps {
  dataType: LayoutEnum;
  value: string | number | undefined;
  options?: SelectEnumOption[];
}

interface SchemaFormItemProps extends Omit<BaseItemRendererProps, 'value'> {
  afterValue?: BaseItemRendererProps['value'];
}

export const SchemaFormItem: VFC<SchemaFormItemProps> = props => {
  const { afterValue, dataType, options } = props;
  const { currency, isDisabled, zoneId } = useContext(DynamicFormItemContext);

  // todo: GIS-25124, refactor needed
  if (isEmpty(afterValue) && isDisabled) {
    return i18nFn('No record');
  }

  const afterComponent = transformValueToLabel(afterValue, {
    dataType,
    currency,
    list: options,
    zoneId,
  });

  return <TextView value={afterComponent} />;
};
