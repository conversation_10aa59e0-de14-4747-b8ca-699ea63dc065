import { Space } from 'antd';
import cx from 'clsx';
import React, { ReactNode, VFC } from 'react';
import { twMerge } from 'tailwind-merge';

interface Props {
  label: ReactNode;
  afterComponent?: ReactNode;
  className?: string;
  tableItemClassName?: string;
  extraLabel?: ReactNode;
}

export const SchemaFormRow: VFC<Props> = props => {
  const { className, tableItemClassName, label, afterComponent, extraLabel } =
    props;
  const changeTableItem =
    'inline-block w-60 h-auto text-root leading-5 text-textSecondary min-w-[200px]';

  return (
    <div
      className={twMerge(
        'flex items-center py-5 pl-[50px] border border-solid border-borderLight border-t-0 first-of-type:border-1',
        cx(className)
      )}
    >
      <span className={cx(changeTableItem, tableItemClassName)}>
        <Space size={8}>
          {label}
          {extraLabel}
        </Space>
      </span>
      <span className={cx(changeTableItem, 'ml-10', tableItemClassName)}>
        {afterComponent}
      </span>
    </div>
  );
};
