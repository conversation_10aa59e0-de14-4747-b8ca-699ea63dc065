import { Icon } from '@zhongan/nagrand-ui';
import { observable } from '@formily/reactive';
import { Tooltip } from 'antd';

interface TooltipHTMLProps {
  id: string;
  guide?: string;
}

export const TooltipHTML = observable(({ guide, id }: TooltipHTMLProps) => (
  <Tooltip
    getPopupContainer={() => document.getElementById(id) as HTMLElement}
    title={
      <div
        dangerouslySetInnerHTML={{
          __html: guide as string,
        }}
      />
    }
  >
    <Icon type="exclamation-circle" className="cursor-pointer ml-xs" />
  </Tooltip>
));
