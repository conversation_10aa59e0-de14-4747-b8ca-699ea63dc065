import React, { FC } from 'react';

import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';

import iconAuto from '@uw/assets/icons/auto.svg?asset';
import { insuredObjects } from '@uw/pages/uw-pages/uwOperationPage/interface';

interface Props {
  objectType: string;
  style?: React.CSSProperties;
}

const getIcon = (
  objectType: string
): React.ComponentType<
  CustomIconComponentProps | React.SVGProps<SVGSVGElement>
> => insuredObjects[objectType]?.svg ?? iconAuto;

export const ObjectIcon: FC<Props> = ({ objectType, style = {} }) => (
  <img
    src={getIcon(objectType) as unknown as string}
    width="40"
    style={style}
  />
);
