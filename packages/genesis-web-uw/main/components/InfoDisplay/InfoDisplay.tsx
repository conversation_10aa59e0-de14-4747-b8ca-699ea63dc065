import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { PreviewText } from '@zhongan/nagrand-ui';

import { BasicInfoData, PersonInfoType } from 'genesis-web-service';

import { PartyType } from '@uw/interface/enum.interface';

export const InfoDisplay: FC<{
  basicInfo: BasicInfoData;
  policyHolder: PersonInfoType;
}> = ({ basicInfo, policyHolder }) => {
  const [t] = useTranslation(['uw', 'common']);

  const infoItems = [
    {
      label: t('Submission No'),
      value: basicInfo?.batchNo,
      hidden: !basicInfo?.batchNo,
    },
    {
      label: t('Proposal No'),
      value: basicInfo?.applicationNo,
    },
    {
      label: t('Goods Name'),
      value: basicInfo?.goodsName,
    },
    {
      label: t('Policyholder'),
      value:
        policyHolder?.partyType === PartyType.COMPANY
          ? policyHolder?.company?.organizationName
          : policyHolder?.fullName,
    },
  ];
  return (
    <div className="px-6 h-[48px] flex items-center justify-start border-b border-b-solid border-tableHeaderSortBg text-textTertiary">
      {infoItems
        .filter(item => !item.hidden)
        .map(item => (
          <span className="text-sm font-medium flex-1 flex items-center">
            <span className="!leading-5">{`${item.label}:`}</span>
            <PreviewText
              value={item.value}
              className="flex !leading-5 !text-sm ml-2 !text-textTertiary"
            ></PreviewText>
          </span>
        ))}
    </div>
  );
};
