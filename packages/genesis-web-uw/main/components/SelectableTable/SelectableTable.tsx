import React, { useEffect, useMemo, useState } from 'react';

import { TablePaginationConfig } from 'antd';
import { ColumnProps } from 'antd/es/table';
import { FilterValue, RowSelectionType } from 'antd/lib/table/interface';

import { Table } from '@zhongan/nagrand-ui';

import { SelectedTableTypeEnum } from '@uw/interface/enum.interface';

/**
 * @param value形参接受的是Form表单initialValues初始化的值
 * @param onChange是函数, 绑定给了Form表单
 */
// 对这个组件进行了改造，支持分页
interface Props<T> {
  rowKey: string;
  onChange: (selectedRowKeys: React.Key[]) => void;
  initialValues: React.Key[] | undefined;
  dataSource: (T & Record<string, unknown>)[];
  columns?: ColumnProps<T & Record<string, unknown>>[];
  disabled?: boolean;
  loading?: boolean;
  pagination?: false | TablePaginationConfig;
  scroll?: {
    x?: number | true | string;
    y?: number | string;
  };
  selectedType?: RowSelectionType;
  onTableChange?: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>
  ) => void;
}

export function SelectableTable<T>({
  initialValues,
  rowKey,
  dataSource,
  columns,
  disabled,
  onChange,
  onTableChange,
  pagination = false,
  scroll,
  selectedType = SelectedTableTypeEnum.CHECKBOX,
}: Props<T>): JSX.Element {
  const [selectedLiabilities, setSelectedLiabilities] = useState(initialValues);

  useEffect(() => {
    setSelectedLiabilities(initialValues);
  }, [initialValues]);
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selectedLiabilities,
      onChange: (values: React.Key[]) => {
        setSelectedLiabilities(values);
        onChange(values);
      },
      getCheckboxProps: (record: T & Record<string, unknown>) => ({
        name: record[rowKey] as string,
        disabled,
      }),
    }),
    [disabled, onChange, rowKey, selectedLiabilities]
  );

  return (
    <div>
      <Table
        id="selectableTable"
        rowSelection={{
          type: selectedType,
          ...rowSelection,
        }}
        scroll={scroll}
        onChange={onTableChange}
        rowKey={rowKey}
        columns={columns}
        dataSource={dataSource}
        pagination={pagination}
        getPopupContainer={() =>
          document.getElementById('selectableTable') ?? document.body
        }
      />
    </div>
  );
}
