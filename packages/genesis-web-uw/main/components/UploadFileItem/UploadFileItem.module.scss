@import '@uw/styles/common.scss';

.upload-item-wrapper {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 64px;
  margin-right: 50px;
  padding: 18px 10px;
  border: 1px solid var(--border-default);
  &.upload-item-wrapper-hover {
    border-color: var(--primary-color);
    box-shadow: 0px 0px 4px rgba(var(--primary-color), 0.5);
  }
  .upload-img-item {
    position: relative;
    width: 48px;
    height: 48px;
    overflow: hidden;
    text-align: center;
    .cover {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3);
      .upload-img-preview {
        cursor: pointer;
      }
    }
    &:hover {
      .cover {
        display: flex;
      }
    }
  }
  &:hover {
    background-color: transparent;
  }
  .upload-content {
    flex-grow: 1;
    margin-left: 13px;
    padding: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .upload-name {
      display: inline-block;
      width: 100%;
      max-width: 100%;
      margin: 0;
      vertical-align: middle;
      .active {
        cursor: pointer;
      }
      &:hover .active {
        color: var(--primary-color);
      }
    }
  }
  .upload-info-box {
    flex-shrink: 0;
    flex-shrink: 0;
    color: var(--text-color-tertiary);
    text-align: right;
  }
  .upload-file-btns {
    display: flex;
    > * {
      margin-left: $gap-md;
      font-size: $font-size-lg;
      cursor: pointer;
    }
  }
}

:export {
  white: var(--white);
  textColor: var(--text-color);
}
