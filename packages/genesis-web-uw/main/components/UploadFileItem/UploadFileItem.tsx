import React, { FC, useState, useCallback, CSSProperties } from 'react';
import { Tooltip } from 'antd';
import clsx from 'clsx';
import Icon, { SearchOutlined } from '@ant-design/icons';
import { IMG } from '@uw/interface/common.interface';
import { FileType } from '@uw/interface/field.interface';

import {
  getFileIcon,
  getFileType,
  FileType as FileExtension,
} from '@uw/util/getFileIcon';

import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ImgPreview } from './ImgPreview';
import styles from './UploadFileItem.module.scss';

interface Props {
  fileInfo: FileType;
  canClickDownload?: boolean;
  icon?: JSX.Element;
  showHover?: boolean;
  hoverHtml?: JSX.Element;
  toolTip?: string;
  style?: CSSProperties;
  onDownload?: (fileUniqueCode: string) => void;
  hideShowRightText?: boolean;
  format?: string;
}
export const UploadFileItem: FC<Props> = ({
  fileInfo,
  canClickDownload,
  icon,
  showHover,
  hoverHtml,
  toolTip,
  style,
  onDownload,
  hideShowRightText, // 上传文件后展示：右侧时间+操作人 是否隐藏
}) => {
  const [hover, setHover] = useState(false);
  const [picUrl, setPicUrl] = useState<string>();
  const [showPreview, setShowPreview] = useState(false);

  const uploadIcon = useCallback<(file: FileType) => JSX.Element>(file => {
    const type = getFileType((file.fileName || file.name) ?? '');
    if (IMG.includes(type) || type === 'IMAGE') {
      return (
        <div key={file.uid} className={styles.uploadImgItem}>
          <div className={styles.cover}>
            <SearchOutlined
              className={styles.uploadImgPreview}
              style={{ fontSize: 16, color: styles.white }}
              onClick={() => {
                setPicUrl(file.url);
                setShowPreview(true);
              }}
            />
          </div>
          <img src={file.url} height="100%" />
        </div>
      );
    }
    return (
      <Icon
        component={getFileIcon(type as FileExtension)}
        style={{ fontSize: 32, color: styles.white }}
      />
    );
  }, []);

  return (
    <>
      <Tooltip
        placement="right"
        title={<div style={{ color: styles.textColor }}>{toolTip}</div>}
        color={styles.white}
        overlayInnerStyle={{ display: toolTip ? 'block' : 'none' }}
      >
        <div
          className={clsx(
            styles.uploadItemWrapper,
            hover && showHover ? styles.uploadItemWrapperHover : ''
          )}
          style={style}
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
        >
          {icon || uploadIcon(fileInfo)}
          <div className={styles.uploadContent}>
            <span
              onClick={() => {
                if (fileInfo.type === 'IMAGE' || !canClickDownload) {
                  return;
                }
                if (onDownload) {
                  onDownload(fileInfo.fileUniqueCode as string);
                } else {
                  window.open(fileInfo.url);
                }
              }}
              className={clsx(
                styles.uploadName,
                fileInfo.type === 'IMAGE' ? '' : 'active'
              )}
            >
              {decodeURIComponent(fileInfo.name)}
            </span>
          </div>
          {showHover && !hover && !hideShowRightText && (
            <div className={styles.uploadInfoBox}>
              {fileInfo.gmtCreated && (
                <div>
                  {dateFormatInstance.getDateTimeString(fileInfo.gmtCreated)}
                </div>
              )}
              <div>{fileInfo.creator}</div>
            </div>
          )}
          {showHover && hover && (
            <div className={styles.uploadFileBtns}>{hoverHtml}</div>
          )}
        </div>
      </Tooltip>

      <ImgPreview
        url={picUrl}
        visible={showPreview}
        setVisible={setShowPreview}
      />
    </>
  );
};
