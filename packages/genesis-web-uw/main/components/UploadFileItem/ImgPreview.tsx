import React, { FC, Dispatch, SetStateAction } from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';

interface Props {
  url?: string;
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
}
export const ImgPreview: FC<Props> = ({ url, visible, setVisible }) => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    (<Modal
      title={t('Preview')}
      open={visible}
      closable={true}
      maskClosable={true}
      footer={null}
      width={'80%'}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <div style={{ fontWeight: 700 }}>
        <img src={url || ''} width="100%" />
      </div>
    </Modal>)
  );
};
