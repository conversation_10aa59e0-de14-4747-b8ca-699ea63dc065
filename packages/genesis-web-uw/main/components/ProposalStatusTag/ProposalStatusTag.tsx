import { CSSProperties, useCallback, useEffect, useState } from 'react';

import { isEmpty } from 'lodash-es';
import useSWR from 'swr';

import { ProposalService } from 'genesis-web-service';

import { useDict } from '@uw/biz-dict/hooks';
import { StatusTag, StatusType } from '@uw/components/StatusTag';
import { ProposalStatusEnum } from '@uw/interface/enum.interface';

interface Props {
  pendingCheckList: string[] | undefined;
  proposalNo: string;
  proposalStatus: string;
  statusI18n: string;
  type?: StatusType;
  needDot?: boolean;
  style?: CSSProperties;
}

export const ProposalStatusTag = ({
  pendingCheckList,
  proposalNo,
  proposalStatus,
  statusI18n,
  type,
  needDot,
  style,
}: Props) => {
  const [bizStatusEnum] = useDict('bizStatus');
  const [finalPendingCheckList, setFinalPendingCheckList] =
    useState<string[]>();
  const [curProposalNo, setCurProposalNo] = useState<string>();

  const { data } = useSWR(
    curProposalNo ? `/proposal/${proposalNo}/pending-check-status` : null,
    () => ProposalService.getPendingCheckStatus(proposalNo),
    {
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  useEffect(() => {
    if (data && curProposalNo) {
      setFinalPendingCheckList(data.pendingCheckList);
    }
  }, [data, curProposalNo]);

  const getPendingCheckStatus = useCallback(() => {
    if (
      proposalStatus === ProposalStatusEnum.DATA_ENTRY_IN_PROGRESS &&
      proposalNo
    ) {
      setCurProposalNo(proposalNo);
    } else {
      setCurProposalNo(undefined);
      setFinalPendingCheckList(pendingCheckList ?? []);
    }
  }, [proposalStatus, proposalNo, pendingCheckList]);

  const onMouseLeave = useCallback(() => {
    setCurProposalNo(undefined);
    setFinalPendingCheckList([]);
  }, []);

  return (
    <div
      onMouseEnter={() => getPendingCheckStatus()}
      onMouseLeave={onMouseLeave}
    >
      <StatusTag
        needDot={needDot}
        statusI18n={statusI18n}
        type={type}
        style={style}
        tooltipExtraProps={{
          open: !!finalPendingCheckList?.length,
        }}
        tooltip={
          isEmpty(finalPendingCheckList) ? undefined : (
            <>
              {finalPendingCheckList?.map(pendingCheck => (
                <div>{bizStatusEnum?.[pendingCheck]}</div>
              ))}
            </>
          )
        }
      />
    </div>
  );
};
