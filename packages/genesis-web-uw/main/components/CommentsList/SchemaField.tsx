import { ArrayTable, FormItem } from '@formily/antd-v5';
import { createSchemaField } from '@formily/react';

import { PreviewText } from 'genesis-web-component/lib/components/Formily';

import { AddButton } from '@uw/components';
import { SectionWrapper } from '@uw/components/SectionWrapper';

export const SchemaField = createSchemaField({
  components: {
    PreviewText,
    ArrayTable,
    FormItem,
    SectionWrapper,
    AddButton,
  },
});
