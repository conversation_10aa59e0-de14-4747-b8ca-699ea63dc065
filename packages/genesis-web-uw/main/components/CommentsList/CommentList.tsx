import { FC, useCallback, useMemo, useState } from 'react';
import { Button, Form, Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import { Divider } from 'genesis-web-component/lib/components/Divider';
import { createForm } from '@formily/core';
import { FormProvider } from '@formily/react';

import { cssVars, Input } from '@zhongan/nagrand-ui';

import { CommonSectionProps } from '@uw/interface/common.interface';

import { ProposalEntryTypes } from 'genesis-web-service';

import { useSelector } from 'react-redux';
import { selectUserUniqName } from '@uw/redux/selector';

import { useTenantTimeZone } from '@uw/hook/useTenantTimeZone';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { SchemaField } from './SchemaField';

interface Props
  extends Partial<
    CommonSectionProps<{
      commentList: ProposalEntryTypes.CommentType[];
    }>
  > {
  initialComments: ProposalEntryTypes.CommentType[];
  readOnly?: boolean;
  zoneId?: string;
  handleSave?: (commentList: ProposalEntryTypes.CommentType[]) => void;
}
export const CommentList: FC<Props> = ({
  readOnly = true,
  zoneId,
  title,
  id,
  render,
  initialComments,
  handleSave,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [commentForm] = Form.useForm();
  const [addVisible, setAddvisible] = useState(false);
  const userName = useSelector(selectUserUniqName);
  const tenantZoneId = useTenantTimeZone();

  const handleAdd = useCallback(() => {
    setAddvisible(true);
  }, []);

  const handleCancel = useCallback(() => {
    setAddvisible(false);
    commentForm.setFieldValue('commentList', undefined);
  }, [commentForm]);

  const handleComplete = useCallback(async () => {
    const inputComments = await commentForm.getFieldValue('commentList');
    const initZoneId = zoneId || tenantZoneId;
    const formatNow = dateFormatInstance.formatTz(
      dateFormatInstance.l10nMoment(new Date(), initZoneId),
      initZoneId
    );
    if (inputComments) {
      const curCommentList = initialComments.concat([
        {
          comments: inputComments,
          creator: userName,
          time: formatNow,
        },
      ]);
      handleSave?.(curCommentList);
    }
    handleCancel();
  }, [
    commentForm,
    zoneId,
    tenantZoneId,
    handleCancel,
    initialComments,
    userName,
    handleSave,
  ]);

  const form = useMemo(
    () =>
      createForm({
        disabled: readOnly,
        initialValues: { commentList: initialComments },
      }),
    [initialComments, readOnly]
  );

  return (
    <section className="mb-big" id={id}>
      <section className="mb-md">
        <FormProvider form={form}>
          <SchemaField
            scope={{
              t,
              id,
              title,
              zoneId,
              handleAdd,
              readOnly,
              userName,
            }}
            schema={render}
          />
        </FormProvider>
      </section>
      <Modal
        open={addVisible}
        closable={false}
        maskClosable={false}
        width={600}
        bodyStyle={{
          padding: `0 ${cssVars.gapXs} ${cssVars.gapMd}`,
        }}
        footer={
          <div>
            <Button
              className="mr-md"
              onClick={() => {
                handleCancel();
              }}
            >
              {t('Cancel')}
            </Button>
            <Button type="primary" onClick={() => handleComplete()}>
              {t('Complete')}
            </Button>
          </div>
        }
      >
        <span className="inline-block !mb-md font-bold">
          {t('Add New Comments')}
        </span>
        <Divider className="my-md h-0 border border-line border-solid" />
        <Form
          layout="vertical"
          form={commentForm}
          name="selection-form"
          className="mt-md"
        >
          <Form.Item label={t('Comments')} name={'commentList'}>
            <Input.TextArea
              allowClear={true}
              rows={4}
              showCount
              maxLength={100}
            />
          </Form.Item>
        </Form>
      </Modal>
    </section>
  );
};
