import { Schema } from '@formily/react';

export const schema = new Schema({
  type: 'object',
  properties: {
    sectionWrapper: {
      type: 'void',
      'x-component': 'SectionWrapper',
      'x-component-props': {
        title: `{{title}}`,
        id: `{{id}}`,
        className: 'relative',
      },
      'x-designable-id': 'sectionWrapper',
      'x-index': 0,
      properties: {
        add: {
          type: 'void',
          'x-component': 'AddButton',
          'x-hidden': '{{readOnly}}',
          'x-component-props': {
            onClick: `{{handleAdd}}`,
            buttonText: "{{ t('Add New') }}",
            className: '!w-fit block ml-auto mb-md absolute right-0 top-0',
          },
        },
        commentList: {
          type: 'array',
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-designable-id': 'commentList',
          'x-component-props': {
            scroll: { x: 'max-content' },
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: "{{ t('User') }}",
                  dataIndex: 'creator',
                },
                properties: {
                  creator: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'PreviewText',
                    'x-designable-id': 'creator',
                  },
                },
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: "{{ t('Time') }}",
                  dataIndex: 'time',
                },
                properties: {
                  time: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'PreviewText.DatePicker',
                    'x-designable-id': 'time',
                    'x-component-props': {
                      showTime: true,
                      zoneId: '{{zoneId}}',
                    },
                  },
                },
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: "{{ t('Comment') }}",
                  dataIndex: 'comments',
                },
                properties: {
                  comments: {
                    type: 'string',
                    'x-component': 'PreviewText',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});
