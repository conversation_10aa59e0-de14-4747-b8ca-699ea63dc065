import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import Icon from '@ant-design/icons';

import { cssVars } from '@zhongan/nagrand-ui';

import NoDataImg from './nodata.svg';

interface Props {
  icon?: React.SVGProps<SVGSVGElement>;
  isPureTxt?: boolean;
  isPureIcon?: boolean;
  style?: Record<string, string>;
  text?: string | JSX.Element;
  imgSize?: number;
}
export const NoData: FC<Props> = ({
  icon,
  isPureTxt,
  style,
  isPureIcon,
  text,
  imgSize,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  return (
    <div
      style={{
        width: '100%',
        textAlign: 'center',
        color: 'var(--text-color-tertiary)',
        minHeight: '320px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...style,
      }}
    >
      <div style={{ display: 'inline-block' }}>
        {!isPureTxt && (
          <Icon
            component={icon ?? NoDataImg}
            style={{
              width: imgSize ?? '88px',
              height: imgSize ?? '88px',
              fontSize: imgSize ?? '88px',
            }}
            width={88}
            height={104}
          />
        )}
        {!isPureIcon && (
          <div
            style={{ fontSize: cssVars.fontSizeRoot, marginTop: cssVars.gapSm }}
          >
            {text ?? t('No Data')}
          </div>
        )}
      </div>
    </div>
  );
};
