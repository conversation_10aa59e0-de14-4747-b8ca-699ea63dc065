import { VFC } from 'react';
import { useTranslation } from 'react-i18next';

import clsx from 'clsx';

import { PaymentOrCollection, TransactionType } from 'genesis-web-service';

import { usePaymentOrCollectionFlatOptions } from '@uw/components/PaymentMethodForm/usePaymentSchema';

import { findValueFromOption } from './AccountBankCardInfo';

interface Props {
  accountType?: string;
  cardHolderName?: string;
  cardNumber?: string;
  className?: string;
  paymentMethod?: string;
  paymentOrCollection?: PaymentOrCollection;
  transactionType?: TransactionType;
}

export const PayMethodCard: VFC<Props> = props => {
  const {
    accountType,
    cardHolderName,
    cardNumber,
    className,
    paymentMethod,
    paymentOrCollection = PaymentOrCollection.Payment,
    transactionType,
  } = props;
  const [t] = useTranslation(['uw', 'common']);

  const { paymentMethodFlatOptions, accountTypeFlatOptions } =
    usePaymentOrCollectionFlatOptions(paymentOrCollection, transactionType);

  return (
    <div
      className={clsx(
        'group w-[400px] border border-disabled border-solid rounded-lg relative overflow-hidden font-medium text-textPrimary bg-white transition-colors',
        className
      )}
    >
      <section className="h-8 leading-8  pl-6 text-white bg-gradient-to-r from-[#0272FF] via-[#379FFF] to-[#55BFE8]">
        {`${findValueFromOption(
          paymentMethodFlatOptions,
          paymentMethod
        )} / ${findValueFromOption(accountTypeFlatOptions, accountType)}`}
      </section>
      <div className="p-6 w-[367px] overflow-hidde">
        <div className="text-defaultTextDark">{t('Account No.')}</div>
        <div className="leading-7 text-xl break-all">{cardNumber}</div>
        <div className="mt-xs text-defaultTextDark">
          {t('Account Holder Name')}
        </div>
        <div>{cardHolderName}</div>
      </div>
    </div>
  );
};
