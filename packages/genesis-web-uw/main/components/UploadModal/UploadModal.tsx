import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  CommonIconAction,
  Icon,
  Modal,
  UploadFileItem,
} from '@zhongan/nagrand-ui';

import { UploadStatus } from '@uw/interface/enum.interface';
import { getContainer } from '@uw/util/config';

interface IProps {
  title: string;
  description: string;
  visible: boolean;
  fileName: string;
  uploadStatus: UploadStatus;
  downloading: boolean;
  errorMessage?: string;
  isSubmitBtnDisabled?: boolean;
  onCancel: () => void;
  onSubmit: () => Promise<void> | void;
  onErrorExcelDownload: () => Promise<void> | void;
}

/**
 * @description 上传弹窗
 * @param visible 控制弹窗显隐
 * @param fileName 文件名
 * @param uploadStatus 上传状态
 * @param errorMessage 错误信息
 * @param isSubmitBtnDisabled 提交按钮是否禁用
 * @param onErrorExcelDownload excel校验不通过时，下载有校验信息的excel文件
 * @param onCancel 取消回调
 * @param onSubmit 提交回调
 */
export const UploadModal = (props: IProps) => {
  const {
    title,
    description,
    visible,
    fileName,
    uploadStatus,
    errorMessage,
    isSubmitBtnDisabled,
    downloading,
    onCancel,
    onSubmit,
    onErrorExcelDownload,
  } = props;
  const [t] = useTranslation(['uw', 'common']);

  const [loading, setLoading] = useState(false);

  const errorIcon = (
    <Icon
      type="info-fill"
      className="transform rotate-180 text-error text-base flex items-center"
    />
  );

  const uploadStatusBtn = useMemo(() => {
    if (uploadStatus === UploadStatus.Uploading) {
      return {
        icon: <Icon type="loading" className="text-primary text-base" />,
      };
    }

    if (uploadStatus === UploadStatus.Done) {
      return {
        icon: <Icon type="done" className="text-base" />,
      };
    }

    if (uploadStatus === UploadStatus.Error) {
      return {
        icon: errorIcon,
      };
    }

    if (uploadStatus === UploadStatus.PartialSuccessful) {
      return {
        icon: (
          <CommonIconAction
            className="flex items-center justify-end"
            tooltipTitle="download"
            icon={<Icon type="download" className="w-4" />}
            loading={downloading}
            onClick={onErrorExcelDownload}
          />
        ),
      };
    }

    return undefined;
  }, [uploadStatus]);

  const uploadFileItemProps = useMemo(() => {
    if (!uploadStatus) return {};
    const hoverInfoList = uploadStatusBtn ? [uploadStatusBtn] : [];

    return [UploadStatus.Error, UploadStatus.PartialSuccessful].includes(
      uploadStatus
    )
      ? {
          showIconDirectly: uploadStatus === UploadStatus.Error,
          hoverInfoList,
          customerDom: errorIcon,
        }
      : {
          showIconDirectly: true,
          hoverInfoList,
          className:
            uploadStatus === UploadStatus.Done ? '!border-success' : '',
        };
  }, [uploadStatus, uploadStatusBtn]);

  const onConfirm = async () => {
    setLoading(true);
    await onSubmit();
    setLoading(false);
  };

  return (
    <Modal
      closable={false}
      title={title}
      open={visible}
      className="&_.antd-modal-footer:mt-0"
      okText={t('Submit')}
      okButtonProps={{ disabled: isSubmitBtnDisabled, loading }}
      onOk={onConfirm}
      onCancel={onCancel}
      getContainer={getContainer}
    >
      <>
        <div className="mb-4">{description}</div>
        <UploadFileItem
          needPreview={false}
          fileName={fileName}
          failed={!!errorMessage}
          {...uploadFileItemProps}
        />
        <div className="text-error">{errorMessage}</div>
      </>
    </Modal>
  );
};
