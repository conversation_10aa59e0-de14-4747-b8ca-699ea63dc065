.countdown-time {
  text-align: right;
}

.task-id-link {
  color: var(--primary-color);
  text-decoration: underline;
  cursor: pointer;
  margin-bottom: var(--gap-xs);
}

.content-area {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.background-image {
  display: block;
  margin: $gap-lg 0 $gap-huge;
}

.turn-back-button {
  display: block;
  width: 280px;
  margin: 50px 0 $gap-md * 4;
}

:export {
  primaryColor: var(--primary-color);
  white: var(--white);
}
