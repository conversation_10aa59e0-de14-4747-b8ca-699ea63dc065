import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ModalProps } from 'antd';
import { Button, Modal, Progress } from 'antd';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { AssociatedUWTaskRecord } from 'genesis-web-service';

import groupImg from '@uw/assets/images/group.png';
import { tc } from '@uw/util/tailwindUtil';

import styles from './index.module.scss';

interface ApprovalModalProp extends ModalProps {
  onCancel: () => void;
  maxCount: number;
  backToSearch: () => void;
  taskList?: AssociatedUWTaskRecord[];
  source?: string;
  isVerification?: boolean;
}
const ONE_SECOND = 1000;

export const ApprovalModal: React.FC<ApprovalModalProp> = ({
  open,
  onCancel,
  maxCount,
  backToSearch,
  taskList = [],
  source,
  isVerification,
}) => {
  const { t } = useTranslation('uw');
  const [count, setCount] = useState(1);

  // 定时器
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const intervalRef = useRef<any>(null);

  // destory
  useEffect(
    () => () => {
      clearInterval(intervalRef.current);
    },
    []
  );

  useEffect(() => {
    if (!open) {
      return;
    }
    // 如果有关联核保任务的场景，任务完成弹出页不会3秒自动关闭
    if (taskList?.length) {
      clearInterval(intervalRef?.current);
      return;
    }
    intervalRef.current = setInterval(() => {
      if (count > maxCount) {
        clearInterval(intervalRef.current);
        backToSearch();
        onCancel();
      }
      setCount(count + 1);
    }, ONE_SECOND);

    // eslint-disable-next-line consistent-return
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [count, maxCount, onCancel, backToSearch, open, taskList, isVerification]);

  const jumpToAssociatedUWTaskPage = useCallback(
    (id: string | number) => {
      window.open(
        source
          ? `/uw/uw-operation/${Mode.View}/${id}/${source}`
          : `/uw/uw-operation/${Mode.View}/${id}`,
        '_blank'
      );
      // 历史逻辑 先注释
      // 只跳转 view 页面
      // navigate(
      //   source
      //     ? `/uw/uw-operation/${Mode.View}/${id}/${source}`
      //     : `/uw/uw-operation/${Mode.View}/${id}`
      // );
    },
    [source]
  );

  return (
    <Modal
      closable={false}
      maskClosable={false}
      open={open}
      onCancel={onCancel}
      footer={null}
      bodyStyle={{ padding: 18 }}
    >
      <div className={styles.countdownTime}>
        {maxCount && taskList?.length <= 0 && (
          <Progress
            width={38}
            format={() => maxCount + 1 - count}
            type="circle"
            percent={(count - 1) * (100 / maxCount)}
            trailColor="#D8D8D8"
            strokeColor={styles.white}
          />
        )}
      </div>
      <div
        className={tc(styles.contentArea, isVerification ? 'pb-[40px]' : '')}
      >
        <img
          width={100}
          height={100}
          className={styles.backgroundImage}
          src={groupImg}
        />

        {isVerification
          ? t('Verification task will be submitted.')
          : t('UW Task will be submitted.')}
        {taskList?.length > 0 && (
          <React.Fragment>
            <span>{t('Associated UW Task Application No.')}</span>
            {taskList?.map(item => (
              <span
                className={styles?.taskIdLink}
                key={item?.applicationNo}
                onClick={() => jumpToAssociatedUWTaskPage(item?.taskId)}
              >
                {item?.applicationNo ?? item?.taskId}
              </span>
            ))}
          </React.Fragment>
        )}
        {!isVerification && (
          <Button
            type="primary"
            className={styles.turnBackButton}
            onClick={() => backToSearch()}
          >
            {t('Turn back to Manual UW Task Pool')}
          </Button>
        )}
      </div>
    </Modal>
  );
};
