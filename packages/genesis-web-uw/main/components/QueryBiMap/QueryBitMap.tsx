import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';
import { NoData } from '@zhongan/nagrand-ui';

import styles from './QueryBitMap.module.scss';

export type QueryStatus = 'NoContent' | 'ChooseCondition';

interface Props {
  queryStatus: QueryStatus;
}
export const QueryBitMap: FC<Props> = ({ queryStatus }) => {
  const { t } = useTranslation(['uw', 'common']);
  return (
    <div className={styles.bitMapWrapper}>
      <NoData
        type="active"
        emptyText={
          queryStatus === 'ChooseCondition'
            ? t('You must choose a search condition')
            : t('No content yet')
        }
        size="large"
      />
    </div>
  );
};
