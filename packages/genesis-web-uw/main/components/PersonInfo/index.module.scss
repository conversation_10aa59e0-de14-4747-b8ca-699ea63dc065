.basic-info-content {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $gap-md;
  .basic-title {
    display: flex;
    align-items: center;
    font-size: $font-size-root;
    color: var(--text-color-tertiary);
    .user-info-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: $gap-md;
    }
  }
  .basic-content {
    font-size: $font-size-root;
    color: var(--text-color);
    font-weight: 700;
  }
}
