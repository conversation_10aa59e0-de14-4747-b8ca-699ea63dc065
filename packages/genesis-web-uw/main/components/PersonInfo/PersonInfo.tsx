import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { FactorsType } from 'genesis-web-service';

import styles from './index.module.scss';

interface Props {
  label: string;
  value?: string | JSX.Element;
  name: string;
  factor?: FactorsType;
}

export const PersonInfo: FC<Props> = ({ label, value, factor }) => {
  const [t] = useTranslation(['uw', 'common']);
  return (
    <div className={styles.basicInfoContent}>
      <div className={styles.basicTitle}>
        <span>{label}</span>
      </div>
      {factor && <div className={styles.basicContent}>{value ?? t('--')}</div>}
    </div>
  );
};
