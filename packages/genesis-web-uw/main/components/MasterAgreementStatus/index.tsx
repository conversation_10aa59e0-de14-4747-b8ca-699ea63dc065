import React from 'react';
import { StatusTag as NagrandStatusTag } from '@zhongan/nagrand-ui';
import { i18nFn } from '@uw/util/i18nFn';
import { getMasterAgreementMap } from '@uw/pages/nb-pages/MasterPolicy/components/MasterPolicyTable/tableColumn';
import { useDict } from '@uw/hook/useDict';

export const MasterAgreementStatus = (props: React.PropsWithChildren<any>) => {
  const [policyStatusEnums] = useDict('masterPolicyStatus');
  return (
    <>
      {props?.value ? (
        <NagrandStatusTag
          needDot
          statusI18n={<>{policyStatusEnums?.[props?.value]}</>}
          type={getMasterAgreementMap.get(props.value)}
        />
      ) : (
        i18nFn('--')
      )}
    </>
  );
};
