import React, { FC } from 'react';

import Icon from '@ant-design/icons';
import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';

import iconEB from '@uw/assets/icons/iconEB.svg';
import icon_accident from '@uw/pages/policy-query-pages/asset/svg/Accident.svg';
import icon_auto from '@uw/pages/policy-query-pages/asset/svg/Auto.svg';
import icon_life from '@uw/pages/policy-query-pages/asset/svg/Life.svg';
import icon_medical from '@uw/pages/policy-query-pages/asset/svg/Medical.svg';
import icon_property from '@uw/pages/policy-query-pages/asset/svg/Property.svg';
import icon_travel from '@uw/pages/policy-query-pages/asset/svg/Travel.svg';

interface Props {
  goodsCategory: number;
  iconKey: string;
  style?: React.CSSProperties;
}

const getIcon = (
  goodsCategory: number
): React.ComponentType<
  CustomIconComponentProps | React.SVGProps<SVGSVGElement>
> => {
  if ([11, 12, 13].includes(goodsCategory)) {
    return icon_life;
  }
  if ([16, 17].includes(goodsCategory)) {
    return icon_medical;
  }
  if ([18].includes(goodsCategory)) {
    return icon_accident;
  }
  if ([22].includes(goodsCategory)) {
    return icon_auto;
  }
  if ([24].includes(goodsCategory)) {
    return icon_property;
  }
  if ([26].includes(goodsCategory)) {
    return icon_travel;
  }
  if ([15].includes(goodsCategory)) {
    return icon_life;
  }
  if ([33].includes(goodsCategory)) {
    return iconEB;
  }
  return icon_life;
};

export const GoodsCategoryIcon: FC<Props> = ({
  goodsCategory,
  iconKey,
  style = {},
}) => (
  <Icon
    key={iconKey}
    style={{ fontSize: '80px', color: 'transparent', ...style }}
    component={getIcon(goodsCategory)}
  />
);
