/* eslint-disable no-param-reassign */
import React, { useCallback, useEffect, useState } from 'react';

import type { PaginationProps } from 'antd';
import type { ColumnsType } from 'antd/es/table';

import { cloneDeep, isBoolean } from 'lodash-es';

import { Table } from '@zhongan/nagrand-ui';

interface Props<T> {
  data: T[];
  mergeKey: string;
  pageSize: number | boolean;
  columns: ColumnsType<T>;
  rowKey?: string;
  className?: string;
}

export function MergeRowTable<T>(props: Props<T>): JSX.Element {
  const {
    columns = [],
    data = [],
    mergeKey = '',
    pageSize = 12,
    rowKey = 'index',
    className,
  } = props;
  const [dataSource, setDataSource] = useState<T[]>([]);
  const [totalDataSource, setTotalDataSource] = useState<T[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>({
    current: 1,
    pageSize,
  } as PaginationProps);

  const mergeRows = useCallback(
    (rows: T[]) => {
      const cloneRow = cloneDeep(rows);
      if (!rows?.length) {
        return rows;
      }
      // 默认占据为 1
      (cloneRow[0] as T).rowSpan = 1;

      let idx = 0;

      return cloneRow.slice(1).reduce(
        (mergedRows, item, index) => {
          if (item[`${mergeKey}`] === mergedRows[idx][`${mergeKey}`]) {
            mergedRows[idx].rowSpan += 1;
            item.colSpan = 0;
          } else {
            item.rowSpan = 1;
            idx = index + 1;
          }
          return [...mergedRows, item];
        },
        [cloneRow[0]]
      );
    },
    [mergeKey]
  );

  useEffect(() => {
    // 所有的数据
    setTotalDataSource(data);
    if (isBoolean(pageSize)) {
      setDataSource(mergeRows(data));
    } else {
      setDataSource(mergeRows(data?.slice(0, pageSize)));
    }
    // 当前页展示的数据
  }, [data, mergeKey, mergeRows, pageSize]);

  const onTableChange = (page: PaginationProps) => {
    setPagination(page);
    // 结束下标
    const end = (page?.current as number) * (page?.pageSize as number);
    // 开始下标 = 结束下标 - 减去每页个数
    let start = end - (page?.pageSize as number);
    // 当开始下标小于 0 的时候，设置为 0
    start = start > 0 ? start : 0;
    // 截取对应的长度的数据
    setDataSource(mergeRows(totalDataSource?.slice(start, end)));
  };

  return (
    <Table
      columns={columns}
      rowKey={rowKey}
      onChange={onTableChange}
      pagination={
        !!pageSize && {
          current: pagination?.current,
          pageSize: pagination?.pageSize,
          total: totalDataSource?.length,
          size: 'small',
        }
      }
      dataSource={dataSource}
      className={className}
    />
  );
}
