import { FC, ReactNode, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button } from 'antd';

import { Icon, MoreAction } from '@zhongan/nagrand-ui';

interface Props {
  backText?: string | ReactNode;
  content?: ReactNode;
  handleBack?: () => void;
  backUrl?: string;
  buttons?: {
    icon: string | ReactNode;
    describe: string;
    key: string;
    handle?: () => void;
  }[];
}

export const DetailHeader: FC<Props> = ({
  backText,
  content,
  handleBack,
  backUrl,
  buttons,
}) => {
  const navigate = useNavigate();
  const [t] = useTranslation(['uw', 'common']);

  const { current: defaultText } = useRef(
    <div className="flex items-center h-[22px]">
      <span className="mr-2">
        <Icon type="left" className="text-lg" />
      </span>
      {t('Back To task pool')}
    </div>
  );

  const more = useMemo(() => {
    if (!buttons || buttons?.length <= 3) {
      return null;
    }
    const menuList = buttons.slice(3).map(item => ({
      label:
        typeof item.icon === 'string' ? (
          <>
            <Icon className="mr-2" type={item.icon} />
            <span>{t(item.describe)}</span>
          </>
        ) : (
          item.icon
        ),
      key: item.key,
      disabled: false,
      onClick: item.handle || function () {},
    }));
    return <MoreAction direction="horizontal" menuList={menuList}></MoreAction>;
  }, [buttons, t]);

  const handleGoBack = useCallback(() => {
    if (!backUrl) return;
    navigate(backUrl);
  }, [backUrl, navigate]);

  return (
    <div className="flex justify-between items-center px-6 h-[48px] border-b border-b-solid border-tableHeaderSortBg">
      <section className={'&_.antd-btn:pl-xss &_.antd-btn:pr-xs'}>
        <Button type="text" onClick={handleBack ?? handleGoBack}>
          <span className="font-medium text-md">{backText || defaultText}</span>
        </Button>
        {content}
      </section>
      <section className="space-x-md flex items-center [&_svg]:cursor-pointer">
        {buttons
          ?.slice(0, 3)
          ?.map(item =>
            typeof item.icon === 'string' ? (
              <Icon key={item.key} type={item.icon} onClick={item?.handle} />
            ) : (
              item.icon
            )
          )}
        {more}
      </section>
    </div>
  );
};
