@import '@uw/styles/common.scss';

.upload-item-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 405px;
  height: 64px;
  margin-right: 50px;
  padding: 18px 10px;
  border: 1px solid rgba(247, 66, 88, 1);
  border-radius: 4px;
  .upload-img-item {
    position: relative;
    width: 48px;
    height: 48px;
    overflow: hidden;
    text-align: center;
  }
  &:hover {
    background-color: transparent;
  }
  .upload-content {
    flex-grow: 1;
    margin-left: 13px;
    padding: 0;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: rgba(247, 66, 88, 1);
  }
  .upload-file-btns {
    display: flex;
    > * {
      margin-left: $gap-md;
      font-size: $font-size-lg;
      cursor: pointer;
    }
  }
}
.error-reason {
  width: 405px;
  margin-top: 5px;
  margin-bottom: $gap-lg;
  color: var(--error-color);
  font-size: $font-size-root;
  line-height: 20px;
}
:export {
  whiteColor: var(--white);
  labelColor: var(--label-color);
}
