import React, { FC } from 'react';
import { Tooltip } from 'antd';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { ExclamationCircleOutlined } from '@ant-design/icons';

import styles from './style.module.scss';

interface Props {
  icon?: JSX.Element;
  hoverHtml?: JSX.Element;
  toolTip?: string;
}
export const ErrorFileUpload: FC<Props> = ({ hoverHtml, toolTip }) => {
  const [t] = useTranslation(['uw']);

  return (<>
    <Tooltip
      placement="right"
      title={<div style={{ color: styles.labelColor }}>{toolTip}</div>}
      color={styles.whiteColor}
      open={false}
    >
      <div className={clsx(styles['upload-item-wrapper'])}>
        <div className={clsx(styles['upload-content'])}>
          <ExclamationCircleOutlined />
          <span style={{ marginLeft: '9px' }}>{t('Error File')}</span>
        </div>
        <div className={styles['upload-file-btns']}>{hoverHtml}</div>
      </div>
    </Tooltip>
    <div className={clsx(styles['error-reason'])}>
      {t('Error happened during upload.')}
      <br></br>
      {t('You can download the file and check detail reason.')}
    </div>
  </>);
};
