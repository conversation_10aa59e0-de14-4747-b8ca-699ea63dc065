@import '@uw/styles/variables.scss';
.right-mataince-mate-sales {
  display: block;
  width: 100%;
  padding: 0 $gap-lg 0 $gap-lg;
  background: var(--white);
  .number-box {
    border-bottom: 1px dashed var(--border-default);
    > div {
      width: 100%;
      padding: $gap-md 0;
      color: var(--text-color);
      font-weight: bold;
      font-size: $font-size-lg;
      line-height: 24px;
      word-wrap: break-word;
      word-break: break-all;
      &:nth-child(2) {
        padding-top: 0;
      }
      &.policy-no {
        a {
          color: var(--text-color);
          text-decoration: underline;
          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
  }
}
.sales-mate {
  .master-policy-section-channel:nth-of-type(1) {
    border-bottom: 1px dashed var(--border-default);
  }
  :nth-child(2) div {
    .master-policy-icon {
      background: #b3bcf5; /* todo  shard 暂无 */
    }
  }
}
.policy-content {
  padding: var(--gap-xs); /* todo  shard 暂无 */
}
.master-policy-section-channel {
  width: 100%;
  padding: $gap-lg 0;
  :nth-child(1) {
    flex-shrink: 0;
    color: #000c17; /* todo  shard 暂无 */
    font-weight: 700;
    font-size: $font-size-lg;
  }
  .sales-box {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    min-height: 48px;
    margin-top: 11px;
    line-height: 48px;
    align-items: center;
    .master-policy-icon {
      display: block;
      width: 48px;
      height: 48px;
      margin-right: var(--gap-xs);
      color: white;
      font-size: 28px;
      line-height: 48px;
      text-align: center;
      background: #5b64a1; /* todo  shard 暂无 */
      border-radius: 50%;
    }
    .sales-agreement-box {
      display: block;
      line-height: normal;

      span {
        width: 200px;
        overflow: hidden;
        color: var(--text-color-tertiary);
        font-weight: 400;
        font-size: $font-size-root;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        display: block;
      }
      :nth-child(1) {
        color: var(--label-color);
      }
      :nth-child(2) {
        color: var(--primary-light);
        margin-top: var(--gap-xs);
      }
    }
  }
}
