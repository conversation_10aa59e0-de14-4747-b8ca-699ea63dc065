import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Row } from 'antd';

import { groupBy } from 'lodash-es';

import { PlaceholderEnum, YesOrNo } from 'genesis-web-service';
import {
  ChannelRole,
  ChannelTypeEnums,
  PolicyChannelListType,
} from 'genesis-web-service/lib/query/query.interface';

import {
  PartnerTypeKeyEnum,
  QueryModuleEnum,
} from '@uw/interface/enum.interface';

import { AgentListTree } from '../AgentListTree';
import styles from './SalesChannelAgencyWrapper.module.scss';

interface Props {
  name?: string;
  type?: string;
  // 兼容groupPolicy 跟 proposal
  agencyName?: string;
  orderNo?: string;
  policyNo?: string;
  existPolicy?: YesOrNo;
  branchName?: string;
  salesAgreementCode?: string;
  policyChannelList?: PolicyChannelListType[];
  module?: string;
}
const handleList = (list: PolicyChannelListType[]) =>
  Object.values(groupBy(list, 'channelCode'));

export const SalesChannelAgencyWrapper: FC<Props> = ({
  name,
  type,
  agencyName,
  orderNo,
  policyNo,
  existPolicy,
  branchName,
  salesAgreementCode,
  policyChannelList,
  module,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  // channelype为TIER_AGENT 生成Agent模块的数据集合
  const [agentList, setAgentList] = useState<PolicyChannelListType[][]>([]);
  // channelype为TIER_AGENT以外类型 生成 Sale Channel 模块的数据集合
  const [salesChannelList, setSalesChannelList] = useState<
    PolicyChannelListType[][]
  >([]);

  useEffect(() => {
    const newAgentList =
      policyChannelList?.filter(
        policyChannelItem =>
          policyChannelItem.channelType === ChannelTypeEnums.TIED_AGENT
      ) || [];
    const newSalesChannelList =
      policyChannelList?.filter(
        policyChannelItem =>
          policyChannelItem.channelType !== ChannelTypeEnums.TIED_AGENT
      ) || [];
    setAgentList(handleList(newAgentList));
    setSalesChannelList(handleList(newSalesChannelList));
  }, [policyChannelList]);

  const getValue = (roleType: PlaceholderEnum, roleName?: string) =>
    type === roleType || !type ? (name ?? roleName) : '';

  const masterPolicyRightBox = useCallback(
    masterItem => (
      <div>
        {masterItem.value ? (
          <Col span={24} className={styles['master-policy-section-channel']}>
            <p>{masterItem.name}</p>
            {masterItem.key === PartnerTypeKeyEnum.AGENT && branchName && (
              <span>
                {t('Team Name: {{name}}', {
                  name: branchName,
                })}
              </span>
            )}
            {masterItem.key === PartnerTypeKeyEnum.AGENCY && (
              <div className={styles.salesBox}>
                <span className={styles['master-policy-icon']}>
                  {masterItem.value.slice(0, 1).toUpperCase()}
                </span>
                <div className={styles.salesAgreementBox}>
                  <span title={masterItem.value}>
                    {policyChannelList?.[0].channelRole === ChannelRole.OFFICE
                      ? t('Direct sales')
                      : masterItem.value}
                  </span>
                  {salesAgreementCode && (
                    <span title={salesAgreementCode}>{salesAgreementCode}</span>
                  )}
                </div>
              </div>
            )}
            {salesChannelList?.length > 0 &&
              masterItem.key === PartnerTypeKeyEnum.SALE_CHANNEL &&
              salesChannelList.map(salesChannelItem => (
                <AgentListTree
                  agentTypeList={salesChannelItem}
                  agentTypeItem={salesChannelItem[0]}
                />
              ))}
            {agentList?.length > 0 &&
              masterItem.key === PartnerTypeKeyEnum.AGENT &&
              agentList.map(agentItem => (
                <AgentListTree
                  agentTypeList={agentItem}
                  agentTypeItem={agentItem[0]}
                />
              ))}
          </Col>
        ) : undefined}
      </div>
    ),
    [agentList, branchName, salesAgreementCode, salesChannelList, t]
  );

  const masterPolicyRightList = useMemo(
    () => [
      {
        name: t('Sales_Channel'),
        value:
          policyChannelList?.[0].channelRole === ChannelRole.OFFICE
            ? t('Direct sales')
            : getValue(
                PlaceholderEnum.SALE_CHANNEL,
                salesChannelList?.[0]?.[0]?.channelName
              ),
        key: PartnerTypeKeyEnum.SALE_CHANNEL,
      },
      {
        name: t('query-Agent'),
        value:
          policyChannelList?.[0].channelRole === ChannelRole.OFFICE
            ? undefined
            : getValue(PlaceholderEnum.AGENT, agentList?.[0]?.[0]?.agentName),
        key: PartnerTypeKeyEnum.AGENT,
      },
      {
        name: t('Agency'),
        value:
          policyChannelList?.[0].channelRole === ChannelRole.OFFICE
            ? undefined
            : getValue(PlaceholderEnum.AGENCY, agencyName),
        key: PartnerTypeKeyEnum.AGENCY,
      },
    ],
    [type, name, salesChannelList, agencyName, agentList, policyChannelList]
  );
  return (
    <>
      {(masterPolicyRightList.find(item => item.value) || orderNo) &&
        (module === QueryModuleEnum.PolicyQuery ? (
          <>
            {orderNo && (
              <div className="break-all">{t('Order No.', { no: orderNo })}</div>
            )}
            <div className={styles['sales-mate']}>
              {masterPolicyRightList.map(masterItem =>
                masterPolicyRightBox(masterItem)
              )}
            </div>
          </>
        ) : (
          <div className={styles.policyContent}>
            <Row className={styles['right-mataince-mate-sales']}>
              <div className={styles.numberBox}>
                {orderNo && <div>{t('Order No.', { no: orderNo })}</div>}
                {policyNo && existPolicy === YesOrNo.YES && (
                  <div className={styles.policyNo}>
                    <a
                      href={`/uw/query-policy/detail?policyNo=${policyNo}`}
                      target="_blank"
                    >
                      {t('Policy No. {{policyNo}}', { policyNo })}
                    </a>
                  </div>
                )}
              </div>
              <div className={styles['sales-mate']}>
                {masterPolicyRightList.map(masterItem =>
                  masterPolicyRightBox(masterItem)
                )}
              </div>
            </Row>
          </div>
        ))}
    </>
  );
};
