import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { InfoCircleOutlined } from '@ant-design/icons';

import styles from './InfoNotCompleted.module.scss';

export const InfoNotCompleted: FC = () => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    <div className={styles.tempTag}>
      <InfoCircleOutlined style={{ color: styles.textColorTertiary }} />
      {t('Info not Completed')}
    </div>
  );
};
