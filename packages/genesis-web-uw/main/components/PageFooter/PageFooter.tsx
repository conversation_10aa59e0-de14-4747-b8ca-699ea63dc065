import React, { CSSProperties, FC, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, ButtonProps, Tooltip } from 'antd';

import { Icon, ModalConfirm } from '@zhongan/nagrand-ui';

import { SaveMode } from '@uw/interface/enum.interface';

export interface ConfirmBtnProps {
  confirmBtn?: boolean;
  title?: string;
  okText?: string;
  cancelText?: string;
  content?: string;
  handleOk?: () => void;
  handleCancel?: () => void;
  tooltip?: string | boolean; // boolean为了兼容&&写法。
}

export interface FooterButtonProps extends ButtonProps {
  buttonType?: SaveMode;
  sortOrder?: number; // 越小越靠前，不填在最后。
  tooltip?: string | boolean; // boolean为了兼容&&写法。
}

export interface FooterProps {
  backText?: string;
  handleBack?: () => void;
  rightActionButtonProps: (FooterButtonProps & ConfirmBtnProps)[];
  style?: CSSProperties;
  hideBackButton?: boolean;
}

export const PageFooter: FC<FooterProps> = ({
  backText,
  handleBack,
  rightActionButtonProps = [],
  style = {},
  hideBackButton = false,
}) => {
  const [t] = useTranslation(['uw', 'common']);

  const getCurButton = useCallback(
    (actionButton, idx: number) => {
      if (actionButton.component) {
        return actionButton.component;
      }
      if (actionButton.confirmBtn) {
        return (
          <>
            <ModalConfirm
              onOk={actionButton.handleOk}
              onCancel={actionButton.handleCancel}
              title={actionButton.title ?? t('warning')}
              okText={actionButton.okText ?? t('yes')}
              cancelText={actionButton.cancelText ?? t('no')}
              content={actionButton.content}
              className="&>*_.antd-modal-confirm-title:contents"
            >
              {actionButton.tooltip ? (
                <Tooltip title={actionButton?.disabled && actionButton.tooltip}>
                  <Button
                    key={idx}
                    size="large"
                    type="primary"
                    {...actionButton}
                  />
                </Tooltip>
              ) : (
                <Button
                  key={idx}
                  size="large"
                  type="primary"
                  {...actionButton}
                />
              )}
            </ModalConfirm>
          </>
        );
      }
      return actionButton.tooltip ? (
        <Tooltip title={actionButton?.disabled && actionButton.tooltip}>
          <Button key={idx} size="large" type="primary" {...actionButton} />
        </Tooltip>
      ) : (
        <Button key={idx} size="large" type="primary" {...actionButton} />
      );
    },
    [t]
  );

  return (
    <section
      style={style}
      className={`h-[72px] !sticky flex items-center ${hideBackButton ? 'justify-end' : 'justify-between'} w-full bottom-0 inset-x-0 bg-white py-0 px-lg z-10 border-0 border-t border-solid border-t-defaultBg [&_button]:font-medium`}
    >
      {!hideBackButton && handleBack && (
        <Button size="large" onClick={handleBack} icon={<Icon type="left" />}>
          <span className="!ml-0">{backText ?? t('Back')}</span>
        </Button>
      )}
      <div className="flex gap-x-md">
        {rightActionButtonProps.map((actionButton, idx) =>
          getCurButton(actionButton, idx)
        )}
      </div>
    </section>
  );
};
