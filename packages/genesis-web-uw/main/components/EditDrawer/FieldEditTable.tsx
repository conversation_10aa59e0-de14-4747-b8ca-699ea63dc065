import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import Icon from '@ant-design/icons';
import { <PERSON><PERSON>, Popconfirm, Space } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { cloneDeep } from 'lodash-es';

import { ColumnEditingType, Table } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { Delete, Edit } from '@uw/assets/new-icons';
import EditDrawer from '@uw/components/EditDrawer/EditDrawer';
import { FieldDataType } from '@uw/interface/field.interface';
import { selectEnums } from '@uw/redux/selector';

import styles from './style.module.scss';

interface Props<T> {
  tableColumns: ColumnProps<T>[];
  data: (T & { key: string })[];
  disableEdit?: boolean;
  scroll?: number;
  setData: (args: (T & { key: string })[]) => void;
  setIsEditing: (isEditing: boolean) => void;
  tableFields?: FieldDataType[] | undefined;
  handleSaveData?: (row: T, uid?: string) => void;
  handleDel?: (index: number) => void;
  disableAdd?: boolean;
  showPage?: boolean;
  selectTable?: JSX.Element;
  uKey?: string;
  handleEdit?: (row: T) => void;
  handleCancel?: () => void;
}

function FieldEditTable<T>({
  tableColumns,
  data,
  disableEdit,
  setData,
  setIsEditing,
  tableFields,
  handleSaveData,
  handleDel,
  disableAdd,
  showPage,
  selectTable,
  uKey,
  handleEdit,
  handleCancel,
}: Props<T>) {
  const enums = useSelector(selectEnums);
  const [t] = useTranslation(['uw', 'common']);
  const [showDrawer, setShowDrawer] = useState(false);
  const [editTableMode, setEditTableMode] = useState<Mode>(Mode.Add);
  const [rowKey, setRowKey] = useState('');
  const [rowItem, setRowItem] = useState<T & { key: string }>();
  const [uid, setUid] = useState<string>();

  const handleClickEdit = useCallback<
    (record: T & { key: string }, mode: Mode) => void
  >(
    (record, mode) => {
      setIsEditing(true);
      setRowKey(record.key);
      setRowItem(record!);
      setShowDrawer(true);
      setEditTableMode(mode);
      setUid(uKey ? (record as Record<string, string>)[uKey] : undefined);
      handleEdit?.(record);
    },
    [setEditTableMode, setRowKey]
  );

  const deleteRow = useCallback<(key: string) => void>(
    key => {
      const dataClone = cloneDeep(data);
      if (dataClone) {
        const index = dataClone.findIndex(item => item.key === key);
        if (handleDel) {
          dataClone.splice(index, 1);
          handleDel(index);
        } else {
          dataClone.splice(index, 1);
          setData(dataClone);
        }
      }
    },
    [tableColumns, data]
  );
  const handleAddColumn = useCallback(() => {
    setIsEditing(true);
    setShowDrawer(true);
    setEditTableMode(Mode.Add);
    setRowKey('');
  }, [tableColumns, data, setData]);

  const onSubmitEditDrawer = useCallback<(args: T & { key: string }) => void>(
    values => {
      try {
        const row = values;
        const newData = [...data];
        let params = {} as T & Record<string, unknown>;
        const index = newData.findIndex(item => rowKey === item.key);
        if (editTableMode === Mode.Edit) {
          if (index > -1) {
            const item = newData[index];
            const itemTemp = {
              ...item,
              ...row,
            };
            newData.splice(index, 1, itemTemp);
            setData(newData);
            params = { ...itemTemp };
          }
        } else {
          row.key = (Math.random() * 10).toString();
          newData.push(row);
        }
        if (editTableMode === Mode.Add) {
          params = row;
        }
        if (handleSaveData) {
          handleSaveData(params, uid);
        } else {
          setData(newData);
        }
        setIsEditing(false);
        setUid(undefined);
      } catch (errInfo) {
        // TODO: remove
      }
      setShowDrawer(false);
    },
    [data, rowKey, editTableMode, setData, handleSaveData]
  );
  const onCancel = useCallback(() => {
    setShowDrawer(false);
    setIsEditing(false);
    setUid(undefined);
    handleCancel?.();
  }, [setShowDrawer]);

  const columns = useMemo(() => {
    if (!disableEdit) {
      const newTableEdit = [
        ...tableColumns,
        {
          title: t('Actions'),
          dataIndex: 'action',
          fixed: 'right',
          align: 'right',
          className: 'uw-table-action',
          width: 120,
          render: (text: string, record: T & { key: string }) => (
            <Space size="middle">
              <>
                <span>
                  <Icon
                    className={styles.actionIcon}
                    component={Edit}
                    onClick={() =>
                      handleClickEdit(record as T & { key: string }, Mode.Edit)
                    }
                  />
                </span>
                <span>
                  <Popconfirm
                    title={t('Are you sure to delete?')}
                    onConfirm={() =>
                      deleteRow((record as T & { key: string }).key)
                    }
                  >
                    <Icon className={styles.actionIcon} component={Delete} />
                  </Popconfirm>
                </span>
              </>
            </Space>
          ),
        },
      ];
      return newTableEdit;
    }
    return tableColumns;
  }, [enums, data, tableColumns]);
  return (
    <section
      style={{
        background: 'white',
        margin: `${styles.gapMd} 0`,
        width: '100%',
      }}
    >
      <Button
        style={{ marginBottom: styles.gapMd }}
        onClick={handleAddColumn}
        block
        disabled={disableAdd}
      >
        {t('+ Add')}
      </Button>
      <Table
        columns={columns as ColumnEditingType<T & { key: string }>[]}
        dataSource={data}
        scroll={{ x: 'max-content' }}
        pagination={typeof showPage !== 'undefined' ? false : undefined}
      />
      <EditDrawer<T>
        showDrawer={showDrawer}
        setShowDrawer={setShowDrawer}
        mode={editTableMode}
        setMode={setEditTableMode}
        onSubmit={onSubmitEditDrawer}
        tableFields={tableFields}
        onClose={onCancel}
        dataSource={rowItem}
        selectTable={selectTable}
      />
    </section>
  );
}

export default FieldEditTable;
