import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { Form, Row, Col, Button, Drawer } from 'antd';
import { useTranslation } from 'react-i18next';
import { BusinessType, Mode } from '@uw/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { getFields } from '@uw/util/getFields';

interface Props<T> {
  showDrawer: boolean;
  setShowDrawer: Dispatch<SetStateAction<boolean>>;
  mode: Mode;
  setMode: Dispatch<SetStateAction<Mode>>;
  onSubmit: (args: T & { key: string }) => void;
  onClose: () => void;
  dataSource?: T & { key: string };
  tableFields?: FieldDataType[] | [];
  selectTable?: JSX.Element;
}
const layout = {
  labelCol: { span: 16 },
  wrapperCol: { span: 16 },
};
function EditDrawer<T>({
  showDrawer,
  mode,
  onSubmit,
  onClose,
  dataSource,
  tableFields,
  selectTable,
}: Props<T>) {
  const [t] = useTranslation(['uw', 'common']);
  const [form] = Form.useForm();

  useEffect(() => {
    if (mode === Mode.Add) {
      form.resetFields();
      form?.setFieldsValue({
        businessTypeList: [BusinessType.NEW_BUSINESS, BusinessType.RENEWAL],
      });
    } else {
      form.setFieldsValue(dataSource);
    }
  }, [mode, showDrawer]);
  return (
    <section>
      <Drawer
        title={mode === Mode.Add ? t('Add') : t('Edit')}
        width={1100}
        closable={false}
        open={showDrawer}
        bodyStyle={{ paddingBottom: 80 }}
        className="uw-list-wrap"
        footer={
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            {mode === Mode.Add || mode === Mode.Edit ? (
              <>
                <Button onClick={onClose} style={{ marginRight: 8 }}>
                  {t('Cancel')}
                </Button>
                <Button
                  onClick={() => {
                    form.submit();
                  }}
                  type="primary"
                  style={{ marginRight: 8 }}
                >
                  {mode === Mode.Edit ? t('Save') : t('Sure')}
                </Button>
              </>
            ) : (
              <Button onClick={onClose} style={{ marginRight: 8 }}>
                {t('Close')}
              </Button>
            )}
          </div>
        }
      >
        <Form
          layout="vertical"
          form={form}
          name="selection-form"
          onFinish={onSubmit}
        >
          <Row style={{ marginTop: 24 }}>
            {tableFields?.map(field => (
              <Col span={field.col as number} key={field.key}>
                <Form.Item
                  label={field.label}
                  name={field.key}
                  rules={field.rules}
                  {...layout}
                >
                  {getFields({ ...field })}
                </Form.Item>
              </Col>
            ))}
            <Col span={24}>{selectTable}</Col>
          </Row>
        </Form>
      </Drawer>
    </section>
  );
}

export default EditDrawer;
