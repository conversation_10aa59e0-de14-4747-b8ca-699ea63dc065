import {
  Dispatch,
  FC,
  SetStateAction,
  createContext,
  useContext,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { FunctionalIconType, ShortcutItemProps } from '@zhongan/nagrand-ui';

import { PageTemplateTypes } from 'genesis-web-service';

interface ToolPanelsProps {
  toolbarConfigs: PageTemplateTypes.SectionDetailResponse[];
  toolBarKeySectionMap: Record<string, FunctionalIconType>;
}

interface ToolPanelsValue {
  activeKey?: FunctionalIconType;
  setActiveKey: Dispatch<SetStateAction<FunctionalIconType | undefined>>;
  toolbarConfigs: ShortcutItemProps[];
}

const ToolPanelsContext = createContext(null as unknown as ToolPanelsValue);

export const useToolPanelsContext = () => useContext(ToolPanelsContext);

export type ToolbarConfigItem = ShortcutItemProps & { order: number };

export const ToolPanelsProvider: FC<ToolPanelsProps> = ({
  children,
  toolBarKeySectionMap,
  toolbarConfigs: configProps,
}) => {
  const [activeKey, setActiveKey] = useState<FunctionalIconType>();
  const [t] = useTranslation(['uw', 'common']);

  const toolbarConfigs: ToolbarConfigItem[] = useMemo(
    () =>
      configProps.map(({ section, order }) => ({
        type: toolBarKeySectionMap[section],
        title: t(section),
        keepActive: true,
        order,
      })),
    [t, configProps, toolBarKeySectionMap]
  );

  return (
    <ToolPanelsContext.Provider
      value={{ activeKey, setActiveKey, toolbarConfigs }}
    >
      {children}
    </ToolPanelsContext.Provider>
  );
};
