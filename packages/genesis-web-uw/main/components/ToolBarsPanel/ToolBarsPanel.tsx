import { ComponentProps, memo, useMemo } from 'react';

import { sortBy } from 'lodash-es';

import {
  FunctionalIconProps,
  FunctionalIconType,
  Shortcuts,
} from '@zhongan/nagrand-ui';

import { Switcher } from 'genesis-web-component/lib/components/Switcher';

import { getContainer } from '@uw/util/config';

import { useToolPanelsContext } from './ToolPanelsProvider';

interface Item {
  type: string;
  title: string;
  order: number;
  keepActive?: boolean;
  // Modal模式的传onChange用于打开弹窗
  onChange?: () => void;
}

interface IExtraConfigsProps {
  // 对应的content跟动态配置的一起传在ToolBarsContent的参数中
  extraConfigItems?: Item[];
}

type ItemDetails = {
  onChange?: () => void;
  keepActive: boolean;
} & FunctionalIconProps;

export const ToolBarsPanel = memo(
  ({
    extraConfigItems,
    ...restProps
  }: IExtraConfigsProps & Partial<ComponentProps<typeof Shortcuts>>) => {
    const { activeKey, toolbarConfigs, setActiveKey } = useToolPanelsContext();

    const formattedItems = useMemo(
      () =>
        sortBy(
          toolbarConfigs.concat(extraConfigItems ?? []).map(item => ({
            ...item,
            tooltipProps: {
              // 这里为了解决一个诡异的bug。主屏幕悬浮显示滚动条，外接屏幕不显示。可能跟屏幕分辨率相关
              // nagrand设置了子项中最后一项mr-0, 一层getContainer会拼接成最后一个子项，导致最后一个icon mr-0 -> mr-6px，产生位移。
              getPopupContainer: (triggerNode: HTMLElement) =>
                getContainer(getContainer(triggerNode)),
            },
          })),
          'order'
        ) as ItemDetails[],
      [toolbarConfigs, extraConfigItems]
    );

    const handleChange = (key: FunctionalIconType) => {
      const onChange = formattedItems.find(
        ({ type }) => key === type
      )?.onChange;
      onChange?.();
      setActiveKey(key);
    };

    return (
      <Switcher.Alone visible={!!formattedItems?.length}>
        <div className="-mr-6">
          <Shortcuts
            activeKey={activeKey}
            items={formattedItems}
            onChange={handleChange}
            {...restProps}
          />
        </div>
      </Switcher.Alone>
    );
  }
);
