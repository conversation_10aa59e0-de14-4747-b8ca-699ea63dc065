import { memo, useMemo } from 'react';

import { Switcher } from 'genesis-web-component/lib/components/Switcher';

import { ToolBarContentConfig } from '@uw/interface/common.interface';

import { useToolPanelsContext } from './ToolPanelsProvider';

const HeaderWrapper = memo(({ title }: { title: string }) => (
  <div className="text-text font-bold text-lg leading-6 px-4 py-3 border-b-[1px] border-tableHeaderSortBg border-b-solid">
    {title}
  </div>
));

interface ToolBarsContentProps {
  // extra的content也要传进来
  contentConfig: ToolBarContentConfig[];
}

export const ToolBarsContent = memo(
  ({ contentConfig }: ToolBarsContentProps) => {
    const { activeKey } = useToolPanelsContext();

    const currentConfig = useMemo(
      () => contentConfig.find(({ key }) => key === activeKey),
      [contentConfig, activeKey]
    );

    return (
      <>
        <Switcher.Alone visible={!!currentConfig && !currentConfig?.isModal}>
          <section className="w-[336px] bg-white border-l-solid border-tableHeaderSortBg border-[1px] overflow-x-hidden overflow-y-auto z-10">
            <HeaderWrapper title={currentConfig?.title ?? ''} />
            {currentConfig?.component}
          </section>
        </Switcher.Alone>
        <Switcher.Alone visible={!!currentConfig?.isModal}>
          {currentConfig?.component}
        </Switcher.Alone>
      </>
    );
  }
);
