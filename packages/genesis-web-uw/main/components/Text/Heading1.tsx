import React, { FC, CSSProperties } from 'react';
import clsx from 'clsx';

import styles from './Text.module.scss';

export type WeightType = 'regular' | 'medium' | 'bold';
interface Props {
  weight?: WeightType;
  style?: CSSProperties;
  className?: string;
}

export const Heading1: FC<Props> = ({ weight, style, children, className }) => (
  <div
    className={clsx(className, styles.heading1)}
    style={{
      ...(style ?? {}),
      fontWeight: weight,
    }}
  >
    {children}
  </div>
);
