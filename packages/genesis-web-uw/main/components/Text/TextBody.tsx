import React, { FC, CSSProperties } from 'react';
import clsx from 'clsx';

import styles from './Text.module.scss';

export type WeightType = 'regular' | 'medium' | 'bold' | number;

interface Props {
  weight?: WeightType;
  style?: CSSProperties;
  className?: string;
  type?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'body' | 'caption';
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}

export const TextBody: FC<Props> = ({
  weight = 400,
  style,
  children,
  className,
  type = 'body',
  onClick,
}) => (
  <div
    className={clsx(className, styles.bodyText, styles[type])}
    style={{
      ...(style ?? {}),
      fontWeight: weight,
    }}
    onClick={onClick}
  >
    {children}
  </div>
);
