import React, { FC, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import {
  Card<PERSON>ctionsContainer,
  CardBodyHeader,
  CardBodyPrimaryInfo,
  <PERSON><PERSON>ooter,
  CardTagList,
  CardV2,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { GoodsCategoryIcon } from 'genesis-web-component/lib/components/GoodsCategoryIcon';
import { QueryGroupPolicyResponse } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { useDict } from '@uw/biz-dict/hooks';
import { PolicyStatusUpperEnum } from '@uw/interface/enum.interface';

interface Props {
  cardKey: string;
  groupPolicyData: QueryGroupPolicyResponse;
  salesChanelMap: Record<string, string>;
}
export const GroupPolicyCard: FC<Props> = ({
  groupPolicyData,
  cardKey,
  salesChanelMap,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const navigate = useNavigate();
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [PolicyStatusEnum] = useDict('policyStatus', 'enumItemName');

  const handleActionView = useCallback<(groupPolicyNo: string) => void>(
    groupPolicyNo => {
      navigate(`/uw/group-policy-query/detail/${groupPolicyNo}`);
    },
    [navigate]
  );

  const cardFooterList = useMemo(
    () => [
      {
        label: t('Sales Channel'),
        value: salesChanelMap[groupPolicyData.channelCode],
      },
      {
        label: t('Policyholder'),
        value: groupPolicyData.policyHolderName,
      },
      {
        label: t('Total Sub Policy'),
        value: groupPolicyData.totalSubPolicyNumber,
      },
    ],
    [
      groupPolicyData.channelCode,
      groupPolicyData.policyHolderName,
      groupPolicyData.totalSubPolicyNumber,
      salesChanelMap,
      t,
    ]
  );

  return (
    <CardV2
      key={cardKey}
      body={
        <>
          <CardBodyHeader
            leftContent={t('To', {
              start: dateFormat.getDateString(
                groupPolicyData.policyEffectiveDate
              ),
              end: groupPolicyData.policyExpiryDate
                ? dateFormat.getDateString(groupPolicyData.policyExpiryDate)
                : t('--'),
            })}
          />
          <CardBodyPrimaryInfo
            title={t('Group Policy No.')}
            content={groupPolicyData.groupPolicyNo}
            right={
              <GoodsCategoryIcon
                iconKey={cardKey}
                goodsCategory={+groupPolicyData.categoryId}
                className="card-goods-category-icon"
              />
            }
          />
          <CardTagList
            tagList={[
              {
                type: TagType.Tag,
                tagProps: {
                  statusI18n:
                    PolicyStatusEnum[
                      groupPolicyData.policyStatus as keyof typeof PolicyStatusEnum
                    ]?.toUpperCase(),
                  type:
                    groupPolicyData?.policyStatus ===
                    PolicyStatusUpperEnum.POLICY_EFFECT
                      ? 'success'
                      : 'no-status',
                },
              },
            ]}
          />
        </>
      }
      footer={<CardFooter list={cardFooterList} />}
      actions={
        <CardActionsContainer>
          <ViewAction
            onClick={() => handleActionView(groupPolicyData.groupPolicyNo)}
          />
        </CardActionsContainer>
      }
    />
  );
};
