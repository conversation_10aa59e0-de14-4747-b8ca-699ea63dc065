import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Row, Tooltip } from 'antd';

import clsx from 'clsx';
import { isEmpty } from 'lodash-es';

import { LabeledValue } from 'genesis-web-shared/lib/util/interface';

type BasicInfo = LabeledValue<string | number> & { visible?: boolean };

interface CommonInfoProps {
  basicInfo: BasicInfo[];
  id?: string;
  className?: string;
  colSpan?: string;
}

/**
 * @description CommonInfoDisplay组件
 */
export const CommonInfoDisplay: FC<CommonInfoProps> = ({
  basicInfo,
  id,
  className,
  colSpan,
}) => {
  const { t } = useTranslation(['uw', 'common']);

  return (
    <>
      {!isEmpty(basicInfo) && (
        <Row
          className={clsx(
            'bg-white py-md px-lg border-0 border-b border-solid border-b-defaultBg !ml-0 !mr-0',
            className
          )}
          id={id}
          gutter={24}
        >
          {basicInfo.map((item, index) => (
            <Col
              span={colSpan ?? 24 / basicInfo.length}
              className={clsx(
                'text-textTertiary text-xs truncate',
                basicInfo.length === index && 'mr-0'
              )}
              key={item.label}
            >
              <Tooltip placement="top" title={item.value}>
                {t('Label: Value', {
                  confirmedLabel: item.label,
                  confirmedValue: item.value ?? '--',
                })}
              </Tooltip>
            </Col>
          ))}
        </Row>
      )}
    </>
  );
};
