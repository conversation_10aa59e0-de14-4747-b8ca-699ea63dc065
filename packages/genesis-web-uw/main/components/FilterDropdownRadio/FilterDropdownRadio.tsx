import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Radio, Space } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';

import { LabelAndValue } from 'genesis-web-component/lib/components/MultipleSelectAll/MultipleSelectAll';

interface FilterDropdownRadioProps extends FilterDropdownProps {
  options: LabelAndValue<string>[];
  onSearch?: (key: string) => void;
  onReset?: () => void;
}

/**
 * @description FilterDropdownRadio组件
 */
export const FilterDropdownRadio: FC<FilterDropdownRadioProps> = ({
  options,
  selectedKeys,
  setSelectedKeys,
  confirm,
  onSearch,
  onReset,
  ...restProps
}) => {
  const { t } = useTranslation(['uw', 'common']);

  const handleReset = () => {
    setSelectedKeys([]);
    confirm();
    onReset?.();
  };

  const handleSearch = () => {
    confirm();
    onSearch?.(selectedKeys?.[0] as string);
  };

  return (
    <section className="w-[300px] relative left-0 right-0">
      <div className="p-xs max-h-[300px] overflow-y-scroll">
        <Radio.Group
          value={selectedKeys?.[0]}
          onChange={event =>
            setSelectedKeys(event.target.value ? [event.target.value] : [])
          }
          {...restProps}
        >
          <Space direction="vertical" size={12}>
            {options?.map(item => (
              <Radio value={item.value}>{item.label}</Radio>
            ))}
          </Space>
        </Radio.Group>
      </div>
      <Space
        size={16}
        className="sticky bottom-0 bg-white border-0 border-t border-solid border-[#9fb3c8] flex justify-end px-2 py-2.5"
      >
        <Button onClick={handleReset}>{t('Reset')}</Button>
        <Button type="primary" onClick={handleSearch}>
          {t('Search')}
        </Button>
      </Space>
    </section>
  );
};
