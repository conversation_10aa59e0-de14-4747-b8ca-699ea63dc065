import React, { FC, useState, useEffect } from 'react';
import { Drawer, DrawerProps } from 'antd';

import styles from './CommonDrawer.module.scss';

interface Props extends DrawerProps {
  width?: string;
  title?: string;
  action?: JSX.Element;
  visible: boolean;
  closable?: boolean;
  handleCloseDrawer: () => void;
}

export const CommonDrawer: FC<Props> = ({
  width,
  title = '',
  action,
  visible,
  children,
  closable,
  handleCloseDrawer,
  ...restProps
}) => {
  const [showDrawer, setShowDrawer] = useState(visible);
  useEffect(() => {
    setShowDrawer(visible);
  }, [visible]);

  return (
    <Drawer
      {...restProps}
      className={styles.drawerWrapper}
      closable={closable}
      width={width ?? '760px'}
      title={title}
      onClose={() => handleCloseDrawer()}
      open={showDrawer}
      footer={<div className={styles.footer}>{action}</div>}
      destroyOnClose={!!restProps?.destroyOnClose}
    >
      {children}
    </Drawer>
  );
};
