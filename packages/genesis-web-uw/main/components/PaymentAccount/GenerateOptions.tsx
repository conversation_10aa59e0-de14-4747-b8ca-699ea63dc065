import { BizDictValue } from 'genesis-web-service';

interface SelectEnumOptions {
  [key: string]: SelectEnumOption[];
}

interface SelectEnumOption {
  label?: string;
  value?: string | number;
  // 级联选项
  children?: SelectEnumOption[];
}

export const generateOptions = (
  enums: BizDictValue[] = []
): SelectEnumOptions => {
  const output = {};
  Object.values(enums ?? {})?.forEach(item => {
    const field = item.dictKey;
    // fixme: 有许多fallback，不统一
    const label = item.dictValueName || item.enumItemName;
    const value = item.enumItemName || item.dictValue;
    const children = item.childList;

    const dictItem: {
      value: string | number;
      label?: string | number;
      children?: { value: string; label: string }[];
    } = {
      value: value as string | number,
      // todo: 国际化相关的，后期转移到前端
      label,
    };

    // 提供级联选项，递归数据结构
    if (children && children?.length > 0) {
      const childOptions = generateOptions(children);
      dictItem.children = childOptions[
        Object.keys(childOptions ?? {})?.[0]
      ] as { value: string; label: string }[];
    }

    if (Object.prototype.hasOwnProperty.call(output, field)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
      output[field].push(dictItem);
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      output[field] = [dictItem];
    }
  });
  return output;
};
