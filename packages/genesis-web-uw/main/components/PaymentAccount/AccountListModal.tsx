import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { find, isEqual, uniqWith } from 'lodash-es';

import { Modal, Table } from '@zhongan/nagrand-ui';

import { AccountSubTypeField } from 'genesis-web-component/lib/components/PaymentMethodFormV4';
import { BankModelEnum } from 'genesis-web-component/lib/components/PaymentMethodFormV4/enum';
import {
  DynamicFormSchemaConfigs,
  ModuleProperties,
} from 'genesis-web-component/lib/components/PaymentMethodFormV4/interface';
import {
  BankFieldFilter,
  CommonAccountFieldsFilter,
} from 'genesis-web-component/lib/components/PaymentMethodFormV4/util';
import {
  AccountRecord,
  BizDictItem,
  LayoutEnum,
  MetadataService,
} from 'genesis-web-service';
import { useBizDict } from 'genesis-web-shared/lib/hook';
import { LabeledValue } from 'genesis-web-shared/lib/util/interface';

import { generateOptions } from './GenerateOptions';
import { transformValueToLabel } from './util';

interface AccountListModalProps {
  accountSubTypeField?: ModuleProperties;
  accountSubTypeOps?: LabeledValue<string>[];
  moduleProps?: DynamicFormSchemaConfigs;
  existAccountList: AccountRecord[];
  handleCancel: () => void;
  onSubmit: (data?: AccountRecord) => void;
  visible: boolean;
  rowKey?: string;
  initialValues?: Record<string, unknown>;
}

export const AccountListModal: React.FC<AccountListModalProps> = props => {
  const {
    visible,
    handleCancel,
    onSubmit,
    existAccountList,
    moduleProps = [],
    accountSubTypeField,
    accountSubTypeOps,
    rowKey = 'accountId',
    initialValues,
  } = props;

  const { t } = useTranslation(['uw', 'common']);
  const [selectedData, setSelectedData] = useState<AccountRecord>(
    existAccountList?.[0]
  );

  useEffect(() => {
    const initialSelectKey = initialValues?.[rowKey];
    if (initialSelectKey) {
      const intialSelectedData = find(
        existAccountList,
        account => account[rowKey] === initialSelectKey
      );
      setSelectedData(intialSelectedData ?? existAccountList?.[0]);
    }
  }, [existAccountList, initialValues, rowKey]);

  const handleSubmit = () => {
    if (selectedData) {
      onSubmit?.(selectedData);
    } else {
      message.error(t('selectAtLeastOne'));
    }
  };

  const bankModuleProps = useMemo(
    () => moduleProps?.filter(BankFieldFilter),
    [moduleProps]
  );

  const bankDictList = useBizDict('bankModel') as BizDictItem[];

  const isSelectType = useMemo(
    () => bankDictList?.[0]?.dictValue === BankModelEnum.Select,
    [bankDictList]
  );

  const dynamicModuleProps = useMemo(
    () => moduleProps?.filter(CommonAccountFieldsFilter),
    [moduleProps]
  );

  const getEnums = useCallback(async (keys: string[]) => {
    if (keys?.length <= 0) {
      return [];
    }
    const res = await MetadataService?.queryBizDict({ dictKeys: keys });
    if (!res) {
      return [];
    }
    // eslint-disable-next-line consistent-return
    return uniqWith(res, isEqual);
  }, []);

  const enums = useMemo(
    () =>
      getEnums(
        dynamicModuleProps
          ?.filter(
            prop =>
              prop?.dataType === LayoutEnum.Select ||
              prop?.dataType === LayoutEnum.MultiSelect
          )
          ?.map(prop => prop?.bizDictKey || prop?.propertyCode)
          ?.filter(Boolean)
      ),
    [dynamicModuleProps, getEnums]
  );

  const options = useMemo(
    () => generateOptions(enums as unknown as BizDictItem[]),
    [enums]
  );

  const bankColumns: ColumnProps<AccountRecord>[] = useMemo(
    () =>
      bankModuleProps?.map(module => ({
        title: module?.displayName,
        dataIndex: module?.propertyCode,
        key: module?.propertyCode,
        ellipsis: true,
        width: 200,
        render: text => {
          if (!isSelectType) {
            return text || t('No record');
          }
          return text ?? t('No record');
        },
      })),
    [bankModuleProps, isSelectType, t]
  );

  const accountSubTypeColumns: ColumnProps<AccountRecord>[] = useMemo(() => {
    if (!accountSubTypeField) {
      return [];
    }
    return [
      {
        title: accountSubTypeField?.displayName,
        dataIndex: AccountSubTypeField,
        ellipsis: true,
        width: 200,
        render: (text: string) =>
          accountSubTypeOps?.find(item => item?.value === text)?.label ||
          text ||
          t('No record'),
      },
    ];
  }, [accountSubTypeField, accountSubTypeOps, t]);

  const dynamicFieldColumn: ColumnProps<AccountRecord>[] = useMemo(
    () =>
      dynamicModuleProps?.map(module => ({
        title: module?.displayName,
        dataIndex: module?.propertyCode,
        ellipsis: true,
        width: 200,
        render: (text: string) =>
          transformValueToLabel(text, {
            dataType: module?.dataType,
            list: options?.bizDictKey || options?.propertyCode,
          }) ||
          text ||
          t('No record'),
      })),
    [dynamicModuleProps, options?.bizDictKey, options?.propertyCode, t]
  );

  const columns = bankColumns?.concat(
    accountSubTypeColumns,
    dynamicFieldColumn
  );

  return (
    <Modal
      open={visible}
      closable={false}
      maskClosable={false}
      width={1000}
      footer={
        <div>
          <Button onClick={handleCancel} className="mr-md">
            {t('Manual Input')}
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            {t('Fill In')}
          </Button>
        </div>
      }
    >
      <h3 className="!mb-md">{t('Fill in the Existing Account')}</h3>
      <Table
        rowKey={rowKey}
        columns={columns}
        rowSelection={{
          type: 'radio',
          selectedRowKeys:
            [
              selectedData?.[rowKey as unknown as number] as unknown as number,
            ] || [],
          onChange: (
            selectedRowKeys: React.Key[],
            selectedRows: AccountRecord[]
          ) => {
            setSelectedData(selectedRows[0]);
          },
        }}
        dataSource={existAccountList}
        pagination={false}
        scroll={{ y: 600 }}
      />
    </Modal>
  );
};
