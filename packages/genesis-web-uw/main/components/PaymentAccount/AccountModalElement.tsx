import React, { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Radio } from 'antd';

import { isUndefined } from 'lodash-es';

import { renderAccountListModalProps } from 'genesis-web-component/lib/components/PaymentMethodFormV4/interface';
import { AccountRecord } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { AddAccountType } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';

import { AccountListModal } from './AccountListModal';

const i18nMaps = {
  [AddAccountType.FillInTheExistingAccount]: i18nFn(
    'Fill in the existing account'
  ),
  [AddAccountType.ManualEntry]: i18nFn('Manual Entry'),
};

export interface AccountModalElementProps {
  fillAccountTextShow: boolean;
  setModalOpen: SetState<boolean>;
  modalOpen: boolean;
  accountList: AccountRecord[];
  propData: renderAccountListModalProps;
  onFillInSubmit: (accountInfo: unknown) => void;
  handleCancel: () => void;
  rowKey?: string;
  addAccountType: AddAccountType | undefined;
  setAddAccountType: SetState<AddAccountType | undefined>;
  isShowManualEntry?: boolean;
  initialValues?: Record<string, unknown>;
}

export const AccountModalElement: FC<AccountModalElementProps> = ({
  fillAccountTextShow,
  setModalOpen,
  modalOpen,
  accountList,
  propData,
  onFillInSubmit,
  handleCancel,
  rowKey = 'uniqueKey',
  addAccountType,
  setAddAccountType,
  isShowManualEntry,
  initialValues,
}) => {
  const { t } = useTranslation(['uw', 'common']);

  const tipText = useMemo(() => {
    if (isUndefined(addAccountType)) {
      return t('Method of adding account');
    }
    return t('Add Account Info');
  }, [addAccountType, t]);

  return (
    <React.Fragment>
      {fillAccountTextShow && (
        <div>
          <span className="mr-md">{tipText}</span>
          <Radio.Group
            defaultValue={addAccountType}
            onChange={event => {
              setAddAccountType?.(event?.target?.value);
            }}
          >
            <Radio value={AddAccountType.FillInTheExistingAccount}>
              {i18nMaps[AddAccountType.FillInTheExistingAccount]}
              {addAccountType === AddAccountType.FillInTheExistingAccount && (
                <span
                  className="ml-xs cursor-pointer underline text-primary"
                  onClick={() => {
                    setModalOpen(true);
                  }}
                >
                  {t('Select Account')}
                </span>
              )}
            </Radio>
            {isShowManualEntry && (
              <Radio value={AddAccountType.ManualEntry}>
                {i18nMaps[AddAccountType.ManualEntry]}
              </Radio>
            )}
          </Radio.Group>
        </div>
      )}
      {modalOpen && (
        <AccountListModal
          moduleProps={propData?.dynamicSchemaConfigs?.sort(
            (prev, next) => prev?.orderNo - next?.orderNo
          )}
          visible={modalOpen}
          handleCancel={() => {
            handleCancel();
          }}
          existAccountList={accountList}
          accountSubTypeField={propData?.accountSubTypeField}
          accountSubTypeOps={propData?.accountSubTypeOps}
          onSubmit={onFillInSubmit}
          rowKey={rowKey}
          initialValues={initialValues}
        />
      )}
    </React.Fragment>
  );
};
