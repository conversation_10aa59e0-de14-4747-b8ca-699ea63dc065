/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { LayoutEnum } from 'genesis-web-service';
import { NamePath } from 'antd/es/form/interface';
import {
  dateFormatInstance,
  amountFormatInstance,
} from 'genesis-web-shared/lib/l10n';

const MultiSelectSeparator = ',';

export const formatCurrencyAmount = (
  amount: string | number | undefined,
  currency?: string,
  noCurrency = false,
  dp?: number
) => {
  const amountText = amount
    ? amountFormatInstance.formatAmount(amount, currency ?? '', dp)
    : '';

  const textNode = noCurrency ? amountText : 'DefaultShowAmount';

  return textNode;
};

//  output tenant date string
export const formatMomentInputToTenantDate = (input: string, zoneId?: string) =>
  dateFormatInstance.getDateString(input, zoneId);

// output tenant date time without timezone string
export const formatMomentInputToTenantDateTime = (
  input: string,
  zoneId?: string
) =>
  dateFormatInstance.l10nMoment(
    input,
    zoneId ?? dateFormatInstance.defaultTimeZone
  );

// 多选, 字符串=>数组
export const transformMultiSelectFromString = (value?: string): string[] =>
  value ? value?.split(MultiSelectSeparator) : [];

// 转换成label
export const transformMultiSelectValueToLabel = (
  list: { label?: string | undefined; value?: unknown }[],
  value?: string
): string =>
  transformMultiSelectFromString(value)
    .map(
      (itemValue: unknown) =>
        list?.find(option => itemValue === option.value)?.label
    )
    ?.join(MultiSelectSeparator);

// value转换成label
export const transformValueToLabel = (
  value: string | number | undefined,
  {
    dataType,
    list,
    zoneId,
    currency,
  }: {
    dataType?: LayoutEnum;
    list: { value?: unknown; label?: string | undefined }[];
    zoneId?: string;
    currency?: string;
  }
): string | number | undefined => {
  // 初始化before元素
  if (dataType === LayoutEnum.Select) {
    return list?.find(item => item.value === value)?.label || value;
  }

  if (dataType === LayoutEnum.MultiSelect) {
    return transformMultiSelectValueToLabel(list, value as string);
  }

  if (dataType === LayoutEnum.Date) {
    // 根据租户的配置进行格式化
    return value
      ? formatMomentInputToTenantDate(value as string, zoneId)
      : undefined;
  }
  if (dataType === LayoutEnum.DateTime) {
    return value
      ? formatMomentInputToTenantDateTime(value as string, zoneId)
      : undefined;
  }

  if (dataType === LayoutEnum.Currency) {
    return formatCurrencyAmount(value, currency, true);
  }

  return value;
};

/**
 * 生成数组形式的NamePath使用，保持一致性
 * 特别地，在数组拼接字符串的时候比较简化了
 * @param fields
 */
export const generateFormNamePath = (...fields: NamePath[]) => {
  let paths: (string | number)[] = [];
  paths = fields.flatMap(field => (field instanceof Array ? field : [field]));

  return paths;
};
