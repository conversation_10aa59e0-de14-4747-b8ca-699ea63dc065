import React, { FC, ReactNode, useCallback, useMemo, useState } from 'react';

import { FormInstance } from 'antd';

import { useUpdateEffect } from 'ahooks';
import clsx from 'clsx';
import { filter, isUndefined } from 'lodash-es';

import { PaymentMethodForm } from 'genesis-web-component/lib/components/PaymentMethodFormV4';
import { renderAccountListModalProps } from 'genesis-web-component/lib/components/PaymentMethodFormV4/interface';
import {
  AccountRecord,
  PaymentOrCollection,
  TransactionType,
} from 'genesis-web-service';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import {
  AddAccountType,
  PaymentMethods,
  SubCategoryEnum,
} from '@uw/interface/enum.interface';

import { AccountModalElement } from './AccountModalElement';

export interface PaymentAccountProps {
  totalAccountList?: AccountRecord[] | undefined;
  form: FormInstance;
  hasEditAuth: boolean;
  initialValue: Record<string, unknown>;
  valueField?: string;
  transactionType?: TransactionType | string;
  paymentOrCollection?: PaymentOrCollection | string;
  needFillIn?: boolean;
  isShowManualEntry?: boolean; // 控制是否显示 manualEntry 单选框
  subCategory?: SubCategoryEnum;
  // 针对sameAs逻辑
  disabled?: boolean;
  readPretty?: boolean;
  width?: number;
  colSpan?: number;
  itemMargin?: number;
  className?: string;
}

export const PaymentAccount: FC<PaymentAccountProps> = ({
  form,
  totalAccountList = [],
  hasEditAuth,
  initialValue,
  valueField = 'firstAccount',
  transactionType,
  paymentOrCollection,
  needFillIn = false,
  isShowManualEntry = true,
  subCategory,
  disabled,
  width,
  colSpan,
  itemMargin,
  className,
  readPretty = false,
}) => {
  const accountTypeEnum = useBizDictByKey('accountType');
  const [modalOpen, setModalOpen] = useState(false);
  const [paymentAccountDisabled, setPaymentAccountDisabled] = useState(false);
  const [formValues, setFormValues] =
    useState<Record<string, unknown>>(initialValue);
  const [addAccountType, setAddAccountType] = useState<
    AddAccountType | undefined
  >();

  useUpdateEffect(() => {
    // if (size(initialValue)) {
    setFormValues(initialValue);
    // }
  }, [initialValue]);

  const readonly = useMemo(
    () => !hasEditAuth || disabled,
    [disabled, hasEditAuth]
  );

  const fillInCancel = useCallback(() => {
    setPaymentAccountDisabled(false);
    setModalOpen(false);
    // 获取 paymentMethod, accountType
    const { paymentMethod, accountType } = form.getFieldValue(valueField);
    // 取消时 清空之前填写的内容
    form.setFieldValue(valueField, {
      paymentMethod,
      accountType,
    });
    setFormValues({
      paymentMethod,
      accountType,
    });
  }, [form, valueField]);

  const paymentMethodWrapper = useCallback(
    method => ({
      disabled:
        !hasEditAuth && [PaymentMethods?.VOUCHER].includes(method.value),
      ...method,
    }),
    [hasEditAuth]
  );

  const onFillInSubmit = useCallback(
    accountInfo => {
      // 设置为 account
      form.setFieldValue(valueField, {
        ...form?.getFieldValue(valueField),
        ...accountInfo,
      });
      // 这边会重新设置3遍 form 值，只有第一次可以获取到最新值，所以保存起来，当作 initValue 使用，仅作为回显
      setFormValues({
        ...form?.getFieldValue(valueField),
        ...accountInfo,
      });

      setModalOpen(false);
      // 禁用 account 的输入
      setPaymentAccountDisabled(true);
    },
    [form, valueField]
  );

  const getAccountModalElement = useCallback(
    (propData: renderAccountListModalProps): ReactNode => {
      if (!needFillIn) {
        return undefined;
      }
      // 获取 account 信息
      const fillInData = form?.getFieldValue(valueField);
      // 如果 account 没有填写账户类型和支付方式并且非只读就不展示 fill in 选项
      const fillAccountTextShow =
        hasEditAuth && fillInData?.accountType && fillInData?.paymentMethod;

      // fill in 的可选数据，应该只包含已有 paymentMethod 和 accountType 的数据
      const fillInAccountList = filter(
        totalAccountList ?? [],
        account =>
          // 只有寿险需要判断paymentMethod值是否一致，车险无需判断
          ((subCategory !== SubCategoryEnum.MOTOR &&
            account?.paymentMethod === fillInData?.paymentMethod) ||
            true) &&
          account?.accountType === fillInData?.accountType
      );

      return (
        (fillAccountTextShow || modalOpen) && (
          <AccountModalElement
            fillAccountTextShow={fillAccountTextShow}
            isShowManualEntry={isShowManualEntry}
            setModalOpen={setModalOpen}
            modalOpen={modalOpen}
            accountList={fillInAccountList}
            handleCancel={fillInCancel}
            addAccountType={addAccountType}
            setAddAccountType={setAddAccountType}
            propData={propData}
            onFillInSubmit={onFillInSubmit}
            initialValues={formValues}
          />
        )
      );
    },
    [
      needFillIn,
      form,
      valueField,
      hasEditAuth,
      totalAccountList,
      isShowManualEntry,
      modalOpen,
      fillInCancel,
      addAccountType,
      onFillInSubmit,
      formValues,
      subCategory,
      readPretty,
    ]
  );

  const isFillInMode = useMemo(() => {
    if (!needFillIn) {
      return undefined;
    }
    return (
      isUndefined(addAccountType) ||
      addAccountType === AddAccountType.FillInTheExistingAccount
    );
  }, [addAccountType, needFillIn]);

  return (
    <>
      {/** 最小938px才能正常显示 里面写死了240 没必要传width了，某些情况下要展示4列，需要重新设置宽度才能正常展示 */}
      <div className={clsx('w-[1176px]', className)}>
        <PaymentMethodForm
          form={form}
          initialValues={formValues}
          readonly={readonly || paymentAccountDisabled}
          paymentMethodMapper={paymentMethodWrapper}
          paymentOrCollection={paymentOrCollection as PaymentOrCollection}
          renderAccountListModal={getAccountModalElement}
          transactionType={transactionType as TransactionType}
          valueField={valueField}
          isUwPremiumPayment={true}
          isFillInMode={isFillInMode}
          width={width}
          colSpan={colSpan}
          itemMargin={itemMargin}
          readPretty={readPretty}
          needConfirmPaymentMethodChanged={false}
          accountTypes={accountTypeEnum}
        />
      </div>
    </>
  );
};
