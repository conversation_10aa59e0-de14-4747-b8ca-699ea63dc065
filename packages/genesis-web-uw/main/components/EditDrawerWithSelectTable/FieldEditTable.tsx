import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, FormInstance, Popconfirm } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { cloneDeep } from 'lodash-es';

import { ColumnEditingType, Table, cssVars } from '@zhongan/nagrand-ui';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';

import { FieldDataType } from '@uw/interface/field.interface';
import { selectEnums } from '@uw/redux/selector';

import EditDrawer from './EditDrawer';

interface Props<T> {
  tableColumns: ColumnProps<T>[];
  data: (T & { key: string })[];
  disableEdit?: boolean;
  scroll?: number;
  setData: (args: (T & { key: string })[]) => void;
  setIsEditing: (isEditing: boolean) => void;
  tableFields?: FieldDataType[] | undefined;
  handleSaveData?:
    | ((row: T, uid?: string) => Promise<boolean>)
    | (() => Promise<boolean>);
  handleDel?: (index: number) => void;
  disableAdd?: boolean;
  showPage?: boolean;
  uKey?: string;
  handleEdit?: (row: T, index?: number) => void;
  handleAdd?: () => void;
  handleCancel?: () => void;
  outerForm?: FormInstance<unknown>;
  headline?: string;
  title?: string;
}

export function EditTableWithSelectTable<T>({
  tableColumns,
  data,
  disableEdit,
  setData,
  setIsEditing,
  tableFields,
  handleSaveData,
  handleDel,
  disableAdd,
  showPage,
  uKey,
  handleEdit,
  handleCancel,
  outerForm,
  handleAdd,
  headline,
  title,
}: Props<T>) {
  const enums = useSelector(selectEnums);
  const [t] = useTranslation(['uw', 'common']);
  const [showDrawer, setShowDrawer] = useState(false);
  const [editTableMode, setEditTableMode] = useState<Mode>(Mode.Add);
  const [rowKey, setRowKey] = useState('');
  const [rowItem, setRowItem] = useState<T & { key: string }>();
  const [uid, setUid] = useState<string>();

  const handleClickEdit = useCallback<
    (record: T & { key: string }, mode: Mode, index: number) => void
  >(
    (record, mode, index) => {
      setIsEditing(true);
      setRowKey((record as Record<string, string>)[uKey as string]);
      setRowItem(record!);
      setShowDrawer(true);
      setEditTableMode(mode);
      setUid(uKey ? (record as Record<string, string>)[uKey] : undefined);
      handleEdit?.(record, index);
    },
    [setEditTableMode, setRowKey, handleEdit]
  );

  const deleteRow = useCallback<(key: string) => void>(
    key => {
      const dataClone = cloneDeep(data);
      if (dataClone) {
        const index = dataClone.findIndex(
          item => (item as Record<string, string>)?.[uKey as string] === key
        );
        if (handleDel) {
          dataClone.splice(index, 1);
          handleDel(index);
        } else {
          setData(dataClone);
        }
      }
    },
    [tableColumns, data, uKey]
  );

  const onCancel = useCallback(() => {
    setShowDrawer(false);
    setUid(undefined);
    setIsEditing(false);
    handleCancel?.();
  }, [setShowDrawer]);
  const handleAddColumn = useCallback(() => {
    setIsEditing(true);
    setShowDrawer(true);
    setEditTableMode(Mode.Add);
    setRowKey('');
    handleAdd?.();
  }, [tableColumns, data, setData]);

  const onSubmitEditDrawer = useCallback<(args: T & { key: string }) => void>(
    async values => {
      try {
        const row = values;
        const newData = data ? [...data] : [];
        let params = {} as T;
        if (editTableMode === Mode.Edit) {
          const index = newData.findIndex(
            item => rowKey === (item as Record<string, string>)[uKey as string]
          );
          if (index > -1) {
            const item = newData[index];
            const itemTemp = {
              ...item,
              ...row,
            };
            newData.splice(index, 1, itemTemp);
            setData(newData);
            params = { ...itemTemp };
          }
        } else {
          row.key = (Math.random() * 10).toString();
          newData.push(row);
        }
        if (editTableMode === Mode.Add) {
          params = row;
        }
        if (handleSaveData) {
          const saveResult = await handleSaveData(params, uid);
          if (saveResult) {
            onCancel();
          }
        } else {
          setData(newData);
          onCancel();
        }
      } catch (errInfo) {
        // TODO: remove
      }
    },
    [data, rowKey, editTableMode, setData, handleSaveData]
  );

  const columns = useMemo(() => {
    if (!disableEdit) {
      const newTableEdit = [
        ...tableColumns,
        {
          title: t('Actions'),
          dataIndex: 'action',
          fixed: 'right',
          align: 'right',
          className: 'uw-table-action',
          width: 120,
          render: (
            text: string,
            record: T & { key: string },
            index: number
          ) => (
            <>
              <span>
                <EditOutlined
                  style={{
                    color: 'var(--label-color)',
                    marginRight: cssVars.gapMd,
                  }}
                  onClick={() =>
                    handleClickEdit(
                      record as T & { key: string },
                      Mode.Edit,
                      index
                    )
                  }
                />
              </span>
              <span>
                <Popconfirm
                  title={t('Are you sure to delete?')}
                  onConfirm={() =>
                    deleteRow(
                      (record as Record<string, string>)?.[uKey as string]
                    )
                  }
                >
                  <DeleteOutlined style={{ color: 'var(--label-color)' }} />
                </Popconfirm>
              </span>
            </>
          ),
        },
      ];
      return newTableEdit;
    }
    return tableColumns;
  }, [enums, data, tableColumns, uKey]);
  return (
    <section
      style={{
        margin: `${cssVars.gapMd} 0`,
        width: '100%',
      }}
    >
      {title ? (
        <section className="flex justify-between h-big mb-xs">
          <div className="font-bold leading-[32px]">{title}</div>
          <Button
            className="!w-auto"
            onClick={handleAddColumn}
            block
            disabled={disableAdd}
          >
            {t('+ Add New')}
          </Button>
        </section>
      ) : (
        <Button
          style={{ marginBottom: cssVars.gapMd }}
          onClick={handleAddColumn}
          block
          disabled={disableAdd}
        >
          {t('+ Add')}
        </Button>
      )}

      <Table
        columns={columns as ColumnEditingType<T & { key: string }>[]}
        dataSource={data}
        pagination={typeof showPage !== 'undefined' ? false : undefined}
      />
      <EditDrawer<T>
        headline={headline}
        showDrawer={showDrawer}
        setShowDrawer={setShowDrawer}
        mode={editTableMode}
        setMode={setEditTableMode}
        onSubmit={onSubmitEditDrawer}
        tableFields={tableFields}
        onClose={onCancel}
        dataSource={rowItem}
        outerForm={outerForm}
      />
    </section>
  );
}
