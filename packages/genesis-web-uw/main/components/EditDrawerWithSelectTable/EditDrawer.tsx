import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { Form, Row, Col, Button, Drawer, FormInstance } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  FieldType,
  Mode,
} from 'genesis-web-component/lib/interface/enum.interface';
import { FieldDataType } from '@uw/interface/field.interface';
import { getFields } from '@uw/util/getFields';

interface Props<T> {
  showDrawer: boolean;
  setShowDrawer: Dispatch<SetStateAction<boolean>>;
  mode: Mode;
  setMode: Dispatch<SetStateAction<Mode>>;
  onSubmit: (args: T & { key: string }) => void;
  onClose: () => void;
  dataSource?: T & { key: string };
  tableFields?: FieldDataType[] | [];
  outerForm?: FormInstance<any>;
  headline?: string;
}
function EditDrawer<T>({
  showDrawer,
  mode,
  onSubmit,
  onClose,
  dataSource,
  tableFields,
  outerForm,
  headline,
}: Props<T>) {
  const [t] = useTranslation(['uw', 'common']);
  const [innerform] = Form.useForm();
  const form = outerForm || innerform;

  useEffect(() => {
    if (mode === Mode.Add) {
      form.resetFields();
    } else {
      form.setFieldsValue(dataSource);
    }
  }, [mode, showDrawer]);
  return (
    <section>
      <Drawer
        getContainer={() => document.querySelector('.uw-main') as HTMLElement}
        title={headline}
        width={1100}
        closable={false}
        open={showDrawer}
        bodyStyle={{ paddingBottom: 80 }}
        className="uw-list-wrap"
        footer={
          <div style={{ textAlign: 'right' }}>
            {mode === Mode.Add || mode === Mode.Edit ? (
              <>
                <Button onClick={onClose} style={{ marginRight: 8 }}>
                  {t('Cancel')}
                </Button>
                <Button
                  onClick={() => {
                    form.submit();
                  }}
                  type="primary"
                  style={{ marginRight: 8 }}
                >
                  {mode === Mode.Edit ? t('Save') : t('Submit')}
                </Button>
              </>
            ) : (
              <Button onClick={onClose} style={{ marginRight: 8 }}>
                {t('Close')}
              </Button>
            )}
          </div>
        }
      >
        <Form
          layout="vertical"
          form={form}
          name="selection-form"
          onFinish={onSubmit}
        >
          <Row>
            {tableFields?.map(field => (
              <Col span={field.col as number} key={field.key}>
                {field.type === FieldType.Text ? (
                  field.render?.()
                ) : (
                  <Form.Item
                    label={field.label}
                    name={field.key}
                    initialValue={field.initialValue}
                    rules={field.rules}
                  >
                    {field.type === FieldType.SelectableTable
                      ? field.render?.()
                      : getFields({ ...field })}
                  </Form.Item>
                )}
              </Col>
            ))}
          </Row>
        </Form>
      </Drawer>
    </section>
  );
}

export default EditDrawer;
