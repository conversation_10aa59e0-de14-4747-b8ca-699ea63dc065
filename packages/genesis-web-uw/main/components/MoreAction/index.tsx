import React, { FC, useMemo } from 'react';
import { MoreOutlined, EllipsisOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';

import styles from './MoreAction.module.scss';

enum Direction {
  Horizontal = 'horizontal',
  Vertical = 'vertical',
}

export interface MenuListType {
  key: string;
  label: string | React.ReactNode;
  disabled?: boolean;
  onClick: React.MouseEventHandler<HTMLParagraphElement>;
}
interface Props {
  menuList: MenuListType[];
  direction?: string;
  disabled?: boolean;
}
export const MoreAction: FC<Props> = ({
  menuList = [],
  direction = Direction.Vertical,
  disabled = false,
}) => {
  const style = useMemo(
    () => ({
      fontSize: styles.fontSizeLg,
      fontWeight: '700',
      height: 'fit-content',
      cursor: disabled ? 'not-allowed' : 'auto',
    }),
    [disabled]
  );
  const menu = useMemo(
    () => (
      <Menu>
        {menuList.map(menuItem => (
          <Menu.Item key={menuItem.key} disabled={menuItem.disabled}>
            <div
              onClick={event => {
                if (menuItem.disabled) return;
                menuItem?.onClick(event);
              }}
            >
              {menuItem.label}
            </div>
          </Menu.Item>
        ))}
      </Menu>
    ),
    [menuList]
  );
  return (
    <Dropdown disabled={disabled} overlay={menu} placement="bottomLeft" arrow>
      {direction === Direction.Horizontal ? (
        <EllipsisOutlined style={style} />
      ) : (
        <MoreOutlined style={style} />
      )}
    </Dropdown>
  );
};
