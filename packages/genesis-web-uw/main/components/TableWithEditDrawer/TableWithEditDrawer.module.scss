.table-title-wrapper {
  margin: $gap-md 0 var(--gap-xs) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-with-edit-drawer-container {
  .#{$ant-prefix}-btn svg {
    display: inline-block;
    line-height: 1;
    vertical-align: -0.125em;
    margin-right: var(--gap-xs);
  }

  .#{$ant-prefix}-table {
    border: 1px solid var(--border-color-base);
  }
}

:export {
  gapHuge: $gap-huge;
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  gapLg: $gap-lg;
  textColorQuaternary: var(--text-color-quaternary);
  gapXss: var(--gap-xss);
}
