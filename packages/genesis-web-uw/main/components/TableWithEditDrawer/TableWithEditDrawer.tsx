import { ReactNode, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, TableProps } from 'antd';
import type { ColumnType, ColumnsType } from 'antd/es/table';

import { Table, TextBody } from '@zhongan/nagrand-ui';

import { CommonDrawer } from '@uw/components/CommonDrawer';

import styles from './TableWithEditDrawer.module.scss';

interface Props<T> extends Omit<TableProps<T>, 'columns'> {
  children: ReactNode;
  columns: ColumnType<T & Record<string, unknown>>[];
  data: (T & Record<string, unknown>)[];
  disabled?: boolean;
  handleSave: () => Promise<boolean>;
  handleClose?: () => void;
  tableTitle?: string;
  buttonText?: string;
  drawerTitle: string;
  actionButton?: React.ReactNode | boolean;
  showDrawer?: boolean;
}

function TableWithEditDrawer<T extends Record<string, unknown>>({
  columns,
  data,
  handleSave,
  handleClose,
  children,
  disabled,
  tableTitle,
  buttonText,
  drawerTitle,
  actionButton,
  showDrawer,
  ...restProps
}: Props<T>) {
  const [t] = useTranslation(['uw', 'common']);
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  useEffect(() => {
    setVisible(!!showDrawer);
  }, [showDrawer]);

  const handleCloseDrawer = () => {
    handleClose?.();
    setVisible(false);
  };

  const handleSaveForm = async () => {
    setSubmitLoading(true);
    try {
      const closeDrawer = await handleSave();
      if (closeDrawer) {
        handleCloseDrawer();
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const actionBar = useMemo(() => {
    if (actionButton === false) {
      return <></>;
    }
    if (actionButton) {
      return actionButton;
    }
    return (
      <Button
        disabled={disabled}
        style={{ marginBottom: styles.gapXs }}
        onClick={() => {
          setVisible(true);
        }}
      >
        {buttonText ?? t('+ Add New')}
      </Button>
    );
  }, [actionButton, buttonText, disabled]);

  return (
    <div className={styles.tableWithEditDrawerContainer}>
      {tableTitle && (
        <div className={styles.tableTitleWrapper}>
          <TextBody weight={500}>{tableTitle}</TextBody>
          {actionBar}
        </div>
      )}

      <Table<T>
        scroll={restProps.scroll ?? { x: 1300 }}
        {...restProps}
        columns={columns as ColumnsType<T>}
        dataSource={data}
        pagination={false}
      />
      <CommonDrawer
        width={'1096px'}
        title={drawerTitle}
        visible={visible}
        destroyOnClose={true}
        handleCloseDrawer={handleCloseDrawer}
        action={
          <>
            <Button onClick={handleCloseDrawer}>{t('Cancel')}</Button>
            {!disabled && (
              <Button
                type={'primary'}
                onClick={handleSaveForm}
                style={{ marginLeft: styles.gapMd }}
                htmlType="submit"
                loading={submitLoading}
              >
                {t('Submit')}
              </Button>
            )}
          </>
        }
      >
        {children}
      </CommonDrawer>
    </div>
  );
}

export { TableWithEditDrawer };
