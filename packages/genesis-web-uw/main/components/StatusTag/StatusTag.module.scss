.status-tag-wrapper {
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  padding: 0 10px;
  line-height: 24px;
  border-radius: var(--border-radius-big);
  box-sizing: border-box;
  .statusText {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &.info {
    color: var(--info-color-text-dark);
    background: var(--info-color-bg);
  }
  &.success {
    color: var(--success-color-text-dark);
    background: var(--success-color-bg);
  }
  &.warning {
    color: var(--warning-color-text-dark);
    background: var(--warning-color-bg);
  }
  &.error {
    color: var(--error-color-text-dark);
    background: var(--error-color-bg);
  }
  &.no-status {
    color: var(--text-color-quaternary);
    background: var(--primary-disabled-color);
  }
  .dot {
    width: 8px;
    height: 8px;
    margin-right: var(--gap-xs);
    border-radius: 50%;
    &.info {
      background: var(--info-color);
    }
    &.success {
      background: var(--success-color);
    }
    &.warning {
      background: $warning-color;
    }
    &.error {
      background: var(--error-color);
    }
    &.no-status {
      background: var(--disabled-color);
    }
  }
}
