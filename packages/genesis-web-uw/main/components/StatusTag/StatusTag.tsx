import React, { CSSProperties, FC } from 'react';

import { Tooltip, TooltipProps } from 'antd';

import clsx from 'clsx';

import styles from './StatusTag.module.scss';

export type StatusType = 'success' | 'warning' | 'no-status' | 'info' | 'error';
interface Props {
  statusI18n: string;
  type?: StatusType;
  tooltip?: string;
  style?: CSSProperties;
  needDot?: boolean;
  tooltipExtraProps: TooltipProps;
}

export const StatusTag: FC<Props> = ({
  statusI18n,
  type,
  tooltip,
  style,
  needDot = false,
  tooltipExtraProps,
}) => (
  <>
    <Tooltip
      prefixCls="antd-uw-tooltip"
      placement="top"
      title={tooltip}
      overlayInnerStyle={{ display: tooltip ? 'block' : 'none' }}
      {...tooltipExtraProps}
    >
      <div
        className={clsx(styles.statusTagWrapper, styles[type ?? 'info'])}
        style={style}
      >
        {needDot && (
          <div className={clsx(styles.dot, styles[type ?? 'info'])}></div>
        )}
        <div className={styles.statusText}>{statusI18n}</div>
      </div>
    </Tooltip>
  </>
);
