import React, { FC, useCallback } from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';

import WarningSvg from '@uw/assets/icons/warning.svg';
import { withdrawnTipsText } from '@uw/util/withdrawnTipsText';

import { TaskPoolDataType, VerificationTaskPool } from 'genesis-web-service';

import styles from './WithdrawModal.module.scss';

interface Props {
  showModal: boolean;
  policyData: VerificationTaskPool | TaskPoolDataType;
  handleWithdrawnTask: (taskId: number) => void;
  handleCloseWithdrawnTaskModal: () => void;
  withdrawnLoading: boolean;
  taskType: string;
}

export const WithdrawModal: FC<Props> = ({
  showModal,
  handleCloseWithdrawnTaskModal,
  policyData,
  handleWithdrawnTask,
  withdrawnLoading,
  taskType,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const handleWithdrawn = useCallback(() => {
    handleWithdrawnTask(policyData?.taskId);
  }, [policyData]);

  return (<>
    <Modal
      open={showModal}
      confirmLoading={withdrawnLoading}
      title={
        <div className={styles.withdrawnModalTitle}>
          <WarningSvg />
          <span className={styles.withdrawnModalTitleText}>
            {t('Withdraw Task')}
          </span>
        </div>
      }
      okType="danger"
      okText={t('Withdraw the Task')}
      onOk={handleWithdrawn}
      cancelText={t('Cancel')}
      onCancel={handleCloseWithdrawnTaskModal}
    >
      <p>
        {policyData?.taskPoolNoticeType &&
          withdrawnTipsText(policyData.taskPoolNoticeType, taskType)}
      </p>
    </Modal>
  </>);
};
