import { mapReadPretty, connect } from '@formily/react';
import { FC } from 'react';
import { InputProps } from 'antd';

import { AmountInput as AmountInputComponent } from 'genesis-web-component/lib/components/AmountInput';

import { formatAmountRender } from '@uw/util/formatAmountCurrency';

import { PreviewText } from 'genesis-web-component/lib/components/Formily';

export interface AmountInputProps extends InputProps {
  currency: string;
}

/**
 * @description - AmountInput 的 PreviewText 形式支持 金额格式化
 */
export const PreviewTextAmountInput: FC<AmountInputProps> = ({
  currency,
  value,
  ...props
}) => {
  const text = formatAmountRender(currency, value as string);

  // 如果value没有值，则直接返回'--', 不需要传递addon / prefix 等属性
  return <PreviewText.Input {...(value ? props : {})} value={text} />;
};

export const AmountInput = connect(
  AmountInputComponent,
  mapReadPretty(PreviewTextAmountInput)
);
