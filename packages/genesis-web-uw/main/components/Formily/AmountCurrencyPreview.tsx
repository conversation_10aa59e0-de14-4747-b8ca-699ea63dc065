import { Field } from "@formily/core";
import { observer, useField } from "@formily/react";
import { FC } from "react";

import { getAmountCurrencyString } from '@uw/util/formatAmountCurrency';

interface AmountCurrencyProps {
  currency: string;
}

export const AmountCurrencyPreview: FC<AmountCurrencyProps> = observer(({ currency }) => {
  const { value } = useField<Field>();

  return (
    <>
      {getAmountCurrencyString(value, currency)}
    </>
  );
});

AmountCurrencyPreview.displayName = 'AmountCurrencyPreview';

