import React, { FC, useCallback, useState } from 'react';
import { Form, Col, Row, Button, FormInstance } from 'antd';
import { useTranslation } from 'react-i18next';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { FieldDataType } from '@uw/interface/field.interface';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { messagePopup } from '@uw/util/messagePopup';

import clx from 'clsx';

import styles from './QueryForm.module.scss';

export const FormGap: FC = () => (
  <div
    style={{
      maxWidth: '100px',
      minWidth: '40px',
      flexShrink: 0,
      flexGrow: 1,
    }}
  ></div>
);

type QueryDataType<T> = T & Record<string, unknown>;
interface Props<T> {
  className?: string;
  formTitle?: string;
  initialParams?: T;
  queryFields: FieldDataType[];
  desc?: string;
  transferQueryParams: (args: QueryDataType<T>) => void;
  propForm?: FormInstance;
  clearSearch?: () => void;
  submitRef?: React.Ref<HTMLElement>;
  formCols?: number;
  rowStyles?: React.CSSProperties;
  notClear?: boolean;
  fieldsMaxLength?: number;
  handleNoSearchValues?: () => void;
}
function QueryForm<T>({
  className,
  formTitle,
  queryFields,
  initialParams,
  desc,
  transferQueryParams,
  propForm,
  clearSearch,
  submitRef,
  formCols,
  rowStyles,
  notClear = false,
  fieldsMaxLength = 9,
  handleNoSearchValues,
}: Props<T>): JSX.Element {
  const [t] = useTranslation(['uw', 'common']);
  const [curForm] = Form.useForm();
  const [collapsed, setCollapsed] = useState(true);
  const form = propForm || curForm;
  const handleSearch = useCallback<(values: Record<string, unknown>) => void>(
    values => {
      const searchValues = { ...values };
      if (searchValues) {
        Object.keys(searchValues).forEach(param => {
          if (searchValues[param] === '') {
            delete searchValues[param];
          }
        });
      }
      if (!Object.values(searchValues).find(value => value)) {
        if (handleNoSearchValues) {
          handleNoSearchValues();
        } else {
          messagePopup(t('Please Select At Least One Condition!'), 'warn');
        }
      } else {
        transferQueryParams(searchValues as QueryDataType<T>);
      }
    },
    [transferQueryParams, handleNoSearchValues]
  );

  const showCollapse = queryFields.length > fieldsMaxLength && !collapsed;

  return (
    <div className={clx(styles['query-for-wrapper'], className)}>
      <section style={{ display: 'flex' }}>
        {formTitle && <div className={styles['form-title']}>{formTitle}</div>}
        <div className={styles['form-desc']}>{desc}</div>
      </section>
      <Col span={formCols ?? 18}>
        <Form
          layout="vertical"
          form={form}
          name="query-form"
          onFinish={handleSearch}
          initialValues={initialParams as T & Record<string, any>}
        >
          <Row
            style={
              rowStyles ?? {
                marginTop: '30px',
              }
            }
            className={styles.searchForm}
          >
            {queryFields.slice(0, fieldsMaxLength).map(field => (
              <Col
                span={field.col as number}
                key={field.key}
                style={{ display: 'flex' }}
              >
                <Form.Item label={field.label} name={field.key}>
                  {getFields({ ...field })}
                </Form.Item>
                <FormGap />
              </Col>
            ))}
            <Row
              style={
                showCollapse
                  ? { display: 'flex', width: '100%' }
                  : { display: 'none' }
              }
            >
              {queryFields
                .slice(fieldsMaxLength, queryFields.length)
                .map(field => (
                  <Col
                    span={field.col as number}
                    key={field.key}
                    style={{ display: 'flex' }}
                  >
                    <Form.Item label={field.label} name={field.key}>
                      {getFields({ ...field })}
                    </Form.Item>
                    <FormGap />
                  </Col>
                ))}
            </Row>
          </Row>
          <Row>
            <Col
              span={24}
              style={{ display: 'flex', justifyContent: 'flex-end' }}
            >
              <div>
                {queryFields.length > fieldsMaxLength && (
                  <Button
                    type="link"
                    onClick={() => {
                      setCollapsed(!collapsed);
                    }}
                  >
                    {collapsed ? t('Expand') : t('Collapse')}
                    {collapsed ? <DownOutlined /> : <UpOutlined />}
                  </Button>
                )}
                {!notClear && (
                  <Button
                    style={{ margin: `0 ${styles.gapXs}` }}
                    onClick={() => {
                      form.resetFields();
                      clearSearch?.();
                    }}
                  >
                    {t('Clear')}
                  </Button>
                )}
                <Button type="primary" htmlType="submit" ref={submitRef}>
                  {t('Search')}
                </Button>
              </div>
              <div
                style={{ width: 'calc((100% - 720px) / 3)', maxWidth: '100px' }}
              ></div>
            </Col>
          </Row>
        </Form>
      </Col>
    </div>
  );
}

export default QueryForm;
