import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox } from 'antd';

import {
  CardV2 as Card,
  CardActionsContainer,
  CardBodyHeader,
  CardBodyPrimaryInfo,
  CardFooter,
  CardTagList,
  EditAction,
  Icon,
  ReassignAction,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { UwTaskStatusEnum, VerificationTaskPool } from 'genesis-web-service';
import { useDict } from 'genesis-web-shared/lib/hook';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { Mode, TaskTypeEnum } from '@uw/interface/enum.interface';

import { WithdrawModal } from '../WithdrawModal/WithdrawModal';

interface Props {
  cardKey: string;
  hasEditAuth: boolean;
  hasReassignAuth: boolean;
  isMyTask: boolean;
  verificationPolicyData: VerificationTaskPool;
  handleGoToOperation: (mode: Mode, taskInfo: VerificationTaskPool) => void;
  handleWithdrawnTask: (taskId: number) => void;
  withdrawnLoading: boolean;
  showAssignDrawer: (taskId: number) => void;
  isBatchReassign: boolean;
  selectedTaskIds: number[];
  toggleCardSelection: (cardId: number) => void;
}

export const VerificationCard: FC<Props> = ({
  verificationPolicyData,
  hasEditAuth,
  isMyTask,
  cardKey,
  handleGoToOperation,
  handleWithdrawnTask,
  withdrawnLoading,
  showAssignDrawer,
  hasReassignAuth,
  isBatchReassign,
  selectedTaskIds,
  toggleCardSelection,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [verificationTaskStatusDict] = useDict('verificationTaskStatus');
  const [showModal, setShowModal] = useState(false);
  const handleWithdrawnTaskModal = () => {
    setShowModal(true);
  };
  const handleCloseWithdrawnTaskModal = () => {
    setShowModal(false);
  };
  useEffect(() => {
    if (!withdrawnLoading) {
      setShowModal(false);
    }
  }, [withdrawnLoading]);

  const {
    l10n: { dateFormat },
  } = useL10n();

  const bodySection = (
    <>
      <CardBodyHeader
        leftAction={
          isBatchReassign && (
            <Checkbox
              checked={
                verificationPolicyData.taskStatus ===
                  UwTaskStatusEnum.UW_IN_PROCESS &&
                selectedTaskIds?.includes(verificationPolicyData.taskId)
              }
              onChange={() => {
                toggleCardSelection?.(verificationPolicyData.taskId);
              }}
              disabled={
                verificationPolicyData.taskStatus !==
                  UwTaskStatusEnum.UW_IN_PROCESS ||
                !verificationPolicyData.taskId
              }
            />
          )
        }
        leftContent={dateFormat.getDateString(
          verificationPolicyData.createDate
        )}
      />
      <CardBodyPrimaryInfo
        title={t('Proposal No.')}
        content={verificationPolicyData.applicationNo}
      />
      <CardTagList
        tagList={[
          {
            type: TagType.Tag,
            tagProps: {
              statusI18n:
                verificationTaskStatusDict?.[
                  verificationPolicyData?.verificationStatus as string
                ]?.toUpperCase(),
              type: 'info',
              icon: verificationPolicyData?.taskPoolNoticeType && (
                <Icon type="info-circle" onClick={handleWithdrawnTaskModal} />
              ),
            },
          },
        ]}
      />
    </>
  );

  const actionSection = (
    <CardActionsContainer>
      {isMyTask && hasEditAuth ? ( // 是我的task且有edit权限，显示edit按钮
        <EditAction
          onClick={() => handleGoToOperation(Mode.Edit, verificationPolicyData)}
        />
      ) : (
        // 不是我的或者没有edit权限，显示view按钮
        <ViewAction
          onClick={() => handleGoToOperation(Mode.View, verificationPolicyData)}
        />
      )}
      {verificationPolicyData.taskStatus === UwTaskStatusEnum.UW_IN_PROCESS &&
        hasReassignAuth && (
          <ReassignAction
            onClick={() => showAssignDrawer(verificationPolicyData.taskId)}
          />
        )}
    </CardActionsContainer>
  );

  return (
    <>
      <Card
        key={cardKey}
        body={bodySection}
        footer={
          <CardFooter
            list={[
              {
                label: t('Policyholder Name'),
                value: verificationPolicyData.policyHolderName,
              },
              {
                label: t('Goods Name'),
                value: verificationPolicyData.goodsName,
              },
            ]}
          />
        }
        actions={actionSection}
      />
      <WithdrawModal
        showModal={showModal}
        handleCloseWithdrawnTaskModal={handleCloseWithdrawnTaskModal}
        policyData={verificationPolicyData}
        withdrawnLoading={withdrawnLoading}
        handleWithdrawnTask={handleWithdrawnTask}
        taskType={TaskTypeEnum.VERIFICATION}
      />
    </>
  );
};
