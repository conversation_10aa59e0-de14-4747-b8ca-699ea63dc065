import React, { FC, ReactNode, useMemo } from 'react';
import { Tree, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { UserOutlined } from '@ant-design/icons';
import {
  ChannelRole,
  PolicyChannelListType,
} from 'genesis-web-service/lib/query/query.interface';
import { tc } from '@uw/util/tailwindUtil';
import { groupBy } from 'lodash-es';

interface Props {
  agentTypeList: PolicyChannelListType[];
  agentTypeItem: PolicyChannelListType;
}

interface TreeDataType {
  title: ReactNode;
  key: string | number;
}
const userIconCls = `[&_span>svg]:!fill-[#d9d9d9]`;
const statusLabelCls = `[&_.nagrand-status-label]:!text-infoTextDark`;
const agentLabelCls = `[&_.nagrand-status-label]:!text-infoTextDark [&_.nagrand-status-label]:!ml-10`;
const AgentTreeListCls = `
&_.antd-tree-list-holder-inner:!ml-4 
&_.antd-tree-switcher-leaf-line-before:!border-l-0
&_.antd-tree-switcher-leaf-line-before:!border-y-0
&_.antd-tree-switcher-leaf-line-before:!border-dashed 
&_.antd-tree-switcher-leaf-line-before:!border-gray 
&_.antd-tree-switcher-leaf-line-after:!border-dashed
&_.antd-tree-switcher-leaf-line-after:!border-t-0
&_.antd-tree-switcher-leaf-line-after:!border-x-0
&_.antd-tree-switcher-leaf-line-after:!w-8
`;

/**
 * 在policy query & proposal query详情的sales channel 和 Agent模块需要增加agent type的展示
 * - 代理人可配置不同的agentType:
 *   - issue agent - agent who issues the polic
 *   - service agent - agent who provides service to customer, correspondence will be sent to service agent
 *   - commission agent - agent who can get commission payme
 */

const handleList = (list: PolicyChannelListType[]) =>
  Object.values(groupBy(list, 'agentCode'));

export const AgentListTree: FC<Props> = ({ agentTypeList, agentTypeItem }) => {
  const [t] = useTranslation(['uw', 'common']);

  const treeData: TreeDataType[] = handleList(agentTypeList).map(agentItem => ({
    title: (
      <div className={tc('ml-4 !text-text', agentLabelCls)}>
        <span
          className={tc(
            userIconCls,
            "before:content-['•'] before:relative before:top-px before:right-1.5 before:text-[#d9d9d9]"
          )}
        >
          <UserOutlined />
        </span>
        <span className="ml-3 font-normal text-root">
          {agentItem?.[0]?.agentName}
        </span>
        <div className="bg-labelBg px-xs rounded-checkbox !text-[#2a78d0] !text-sm !font-medium">
          {agentItem?.[0]?.agentType &&
            (() => {
              const str = `${agentItem
                .map(item => t(item.agentType as string))
                .join(' ; ')}`;
              if (str?.length && str.length > 27) {
                return (
                  <Tooltip
                    placement="top"
                    className="!text-[#2a78d0] !text-sm !font-medium align-top"
                    title={
                      <>
                        {agentItem.map(item => (
                          <section>{t(item.agentType as string)}</section>
                        ))}
                      </>
                    }
                  >
                    <div className="!text-[#2a78d0] !text-sm !font-medium align-top inline-block max-w-[165px] whitespace-nowrap">
                      {str?.slice(0, 24)}
                    </div>
                    ...
                    {`+${agentItem?.length}`}
                  </Tooltip>
                );
              }
              return str;
            })()}
        </div>
      </div>
    ),
    key: `${agentItem?.[0]?.agentCode}-${agentItem?.[0]?.agentName}`,
  }));

  const channelName = useMemo(() => {
    const channelRole = agentTypeItem?.channelRole;
    return channelRole === ChannelRole.OFFICE
      ? t('Direct sales')
      : agentTypeItem?.channelName;
  }, [agentTypeItem?.channelName, agentTypeItem?.channelRole, t]);
  return (
    <>
      <div className="flex justify-start w-full h-16 min-h-[48px] mt-3 leading-[48px] items-center ">
        <span className="block w-[48px] !h-[48px] mr-xs !text-white !text-[28px] leading-[48px] !text-center bg-[#5b64a1] !rounded-full">
          {channelName?.slice(0, 1).toUpperCase()}
        </span>
        <div className={tc('block leading-normal', statusLabelCls)}>
          <span
            className="block w-[200px] truncate !text-text !font-normal !text-root"
            title={channelName}
          >
            {channelName}
          </span>
        </div>
      </div>
      {agentTypeItem?.agentName && (
        <Tree
          className={AgentTreeListCls}
          showLine
          defaultExpandAll
          selectable={false}
          treeData={treeData}
        />
      )}
    </>
  );
};
