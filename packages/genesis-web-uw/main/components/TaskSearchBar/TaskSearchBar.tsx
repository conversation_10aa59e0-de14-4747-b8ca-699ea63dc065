import React, {
  ReactNode,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Divider } from 'antd';

import {
  AddNewButton,
  OperationContainer,
  QueryOperationSelect,
  RenderMode,
  RenderModeSwitch,
} from '@zhongan/nagrand-ui';

import { getDefaultTaskOptions } from '@uw/util/getDefaultTaskOptions';

interface Props {
  sortMenus?: Record<string, string>;
  showFilterMyTask: boolean;
  showAddNewButton?: boolean;
  handleUserSort?: (isMyTask: boolean) => void;
  handleSearch: (soryKey: string, isMyTask?: boolean) => void;
  handleChangeDisplayMode: (showMode: RenderMode) => void;
  handleAddNew?: () => void;
  isMyTask?: boolean;
  canBatchReassign?: boolean;
  customizedAddNew?: ReactNode;
  loading?: boolean;
  reAssignable?: boolean;
  isBatchReassign?: boolean;
  selectedTaskIds?: (string | number)[];
  handleCancelBatchReassign?: () => void;
  handleBatchReassign?: (isReassign: boolean) => void;
  showAssignDrawer?: (taskId: string[]) => void;
  handleSelectAll?: () => void;
  selectAll?: boolean;
}

export const TaskSearchBar = forwardRef(
  (
    {
      sortMenus = {},
      showFilterMyTask: filterMyTaskVisible,
      showAddNewButton = false,
      handleSearch,
      handleUserSort,
      handleChangeDisplayMode,
      handleAddNew,
      isMyTask,
      canBatchReassign = false,
      customizedAddNew = undefined,
      loading = false,
      reAssignable = false,
      isBatchReassign = false,
      selectedTaskIds,
      handleCancelBatchReassign,
      handleBatchReassign,
      showAssignDrawer,
      handleSelectAll,
      selectAll,
    }: Props,
    ref
  ) => {
    const [showMode, setShowMode] = useState<RenderMode>(RenderMode.Card);
    const [sortKey, setSortKey] = useState<string>(Object.keys(sortMenus)[0]);
    const [isMyShowTask, setIsShowMyTask] = useState(isMyTask ?? false);
    const [t] = useTranslation(['uw', 'common']);

    useImperativeHandle(
      ref,
      () => ({
        sortKey,
        setSortKey,
        isMyShowTask,
        setIsShowMyTask,
        showMode,
        setShowMode,
      }),
      [sortKey, isMyShowTask, showMode]
    );

    const handleSortByTime = useCallback(
      (value: string) => {
        setSortKey(value);
        handleSearch(value, isMyShowTask);
      },
      [handleSearch, isMyShowTask]
    );

    const handleSortByUser = useCallback(
      (value: boolean) => {
        setIsShowMyTask(value);
        if (handleUserSort) {
          handleUserSort(value);
        }
      },
      [handleUserSort]
    );

    const handleChangeMode = useCallback(
      (value: RenderMode) => {
        setShowMode(value);
        handleChangeDisplayMode(value);
      },
      [handleChangeDisplayMode]
    );

    const filterMyTask = useMemo(() => {
      return (
        <QueryOperationSelect
          options={getDefaultTaskOptions()}
          onChange={handleSortByUser}
          value={isMyShowTask}
        />
      );
    }, [isMyShowTask, handleSortByUser]);

    const addNew = useMemo(() => {
      if (showAddNewButton) {
        if (customizedAddNew) {
          return customizedAddNew;
        }
        return <AddNewButton onClick={handleAddNew} type="primary" ghost />;
      }
    }, [handleAddNew, showAddNewButton, customizedAddNew]);

    const sortBySelection = useMemo(() => {
      return (
        <QueryOperationSelect
          options={Object.keys(sortMenus).map(key => ({
            label: sortMenus[key],
            value: key,
          }))}
          value={sortKey}
          onChange={handleSortByTime}
        />
      );
    }, [handleSortByTime, sortMenus, sortKey]);

    const batchReassignButton = useMemo(
      () => (
        <Button
          disabled={loading || !reAssignable}
          onClick={() => handleBatchReassign?.(true)}
        >
          {t('Batch Reassign')}
        </Button>
      ),
      [t, loading, reAssignable, handleBatchReassign]
    );

    const selectAllBox = useMemo(
      () => (
        <>
          {showMode === RenderMode.Card && (
            <div>
              <Checkbox checked={selectAll} onChange={handleSelectAll}>
                {t('Select All')}
              </Checkbox>
            </div>
          )}
        </>
      ),
      [handleSelectAll, selectAll, showMode, t]
    );

    const [showBatchReassign, showSortBy, showFilterMyTask] = useMemo(
      () => [
        canBatchReassign,
        showMode === RenderMode.Card && !!Object.keys(sortMenus)?.length,
        filterMyTaskVisible,
      ],
      [canBatchReassign, showMode, sortMenus, filterMyTaskVisible]
    );

    return (
      <OperationContainer>
        <OperationContainer.Left>
          {/* batch reassign 时只显示reassign、select all和cancel */}
          {isBatchReassign ? (
            <>
              <Button
                type="primary"
                ghost
                disabled={!selectedTaskIds?.length}
                onClick={() => showAssignDrawer?.(selectedTaskIds as string[])}
              >
                {t('Reassign | {{selectLength}} Option(s)', {
                  selectLength: selectedTaskIds?.length || 0,
                })}
              </Button>
              {canBatchReassign && selectAllBox}
              <Button onClick={handleCancelBatchReassign}>{t('Cancel')}</Button>
            </>
          ) : (
            <>{addNew}</>
          )}
        </OperationContainer.Left>
        {!isBatchReassign && (
          <OperationContainer.Right>
            {showBatchReassign && batchReassignButton}
            {showSortBy && sortBySelection}
            {showFilterMyTask && filterMyTask}
            {(showBatchReassign || showSortBy || showFilterMyTask) && (
              <Divider type="vertical" />
            )}
            <RenderModeSwitch value={showMode} onChange={handleChangeMode} />
          </OperationContainer.Right>
        )}
      </OperationContainer>
    );
  }
);
