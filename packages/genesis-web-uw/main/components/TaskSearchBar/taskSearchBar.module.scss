@import '@uw/styles/variables.scss';

.short-filter {
  margin-right: $gap-md;
}
.switch-wrapper {
  display: flex;
  align-items: center;
  margin-right: $gap-lg;
  .switch-label {
    font-weight: 500;
    font-size: $font-size-root;
    color: var(--text-color);
    margin-right: var(--gap-xs);
    margin-left: var(--gap-xs);
  }
}
:export {
  gapXs: var(--gap-xs);
  gapMd: $gap-md;
  gapLg: $gap-lg;
  gapBig: $gap-big;
  fontSizeLg: $font-size-lg;
}
