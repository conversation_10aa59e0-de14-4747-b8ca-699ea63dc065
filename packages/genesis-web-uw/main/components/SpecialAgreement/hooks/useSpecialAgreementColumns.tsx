import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, FormListFieldData } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { DeleteAction } from '@zhongan/nagrand-ui';

import {
  BizDict,
  FieldType,
} from 'genesis-web-component/lib/interface/enum.interface';
import { MasterPolicy } from 'genesis-web-service';

import { getFields } from '@uw/util/getFieldsQueryForm';
import { i18nFn } from '@uw/util/i18nFn';

export enum SpecialAgreementType {
  PRE_DEFINED = 'PRE_DEFINED',
  USER_INPUT = 'USER_INPUT',
}

export const useSpecialAgreementColumns = (
  form: FormInstance,
  formKey: (number | string)[] = [],
  policySpecialAgreementEnums: BizDict[],
  codeOptions: BizDict[],
  readOnly?: boolean,
  readPretty?: boolean
) => {
  const { t } = useTranslation(['uw']);
  const specialAgreementList: MasterPolicy.SpecialAgreementInfo[] =
    Form.useWatch(formKey, form);
  const specialAgreementColumns = useCallback<
    (
      remove: (index: number | number[]) => void
    ) => ColumnProps<FormListFieldData>[]
  >(
    remove => [
      {
        title: i18nFn('Special Agreement Type'),
        dataIndex: 'specialAgreementType',
        width: 150,
        render: (text, field) => (
          <Form.Item
            name={[field.name, 'specialAgreementType']}
            rules={[{ required: true, message: t('please select one') }]}
          >
            {getFields({
              placeholder: i18nFn('Please select'),
              type: FieldType.Select,
              showSearch: true,
              disabled: readOnly,
              readPretty,
              className: '!w-60',
              options: policySpecialAgreementEnums,
              onChange: () => {
                form.setFieldValue(
                  [...formKey, field.name, 'specialAgreementCode'],
                  undefined
                );
                form.setFieldValue(
                  [...formKey, field.name, 'specialAgreementDescription'],
                  undefined
                );
              },
            })}
          </Form.Item>
        ),
      },
      {
        title: i18nFn('Special Agreement Code'),
        dataIndex: 'specialAgreementCode',
        width: 150,
        render: (text, field, index) => {
          const record = specialAgreementList?.[index];
          const currentCode = record?.specialAgreementCode;
          const filterCodeList = specialAgreementList
            ?.filter(
              specialAgreementItem =>
                specialAgreementItem.specialAgreementCode !== currentCode
            )
            ?.map(
              specialAgreementItem => specialAgreementItem.specialAgreementCode
            );
          const isUserInput =
            record?.specialAgreementType === SpecialAgreementType.USER_INPUT;

          const specialAgreementCodeOptions = codeOptions?.filter(
            option => !filterCodeList?.includes(option.enumItemName as string)
          );

          return isUserInput ? (
            <div className="!w-60">{i18nFn('--')}</div>
          ) : (
            <Form.Item
              name={[field.name, 'specialAgreementCode']}
              rules={[{ required: true, message: t('please select one') }]}
            >
              {getFields({
                placeholder: i18nFn('Please select'),
                className: '!w-60',
                type: FieldType.Select,
                disabled: readOnly,
                readPretty,
                options: specialAgreementCodeOptions,
                onChange: (value: string) => {
                  const currentContent = codeOptions?.find(
                    option => option.enumItemName === value
                  )?.specialAgreement;
                  if (currentContent) {
                    form.setFieldValue(
                      [...formKey, field.name, 'specialAgreementDescription'],
                      currentContent
                    );
                  }
                },
              })}
            </Form.Item>
          );
        },
      },
      {
        title: i18nFn('Special Agreement Description'),
        dataIndex: 'specialAgreementDescription',
        width: 150,
        render: (text, field, index) => {
          const record = specialAgreementList?.[index];
          return (
            <Form.Item name={[field.name, 'specialAgreementDescription']}>
              {getFields({
                placeholder: i18nFn('Please input'),
                className: '!w-60',
                type: FieldType.Input,
                readPretty,
                disabled:
                  record?.specialAgreementType ===
                    SpecialAgreementType.PRE_DEFINED || readOnly,
              })}
            </Form.Item>
          );
        },
      },
      ...((readOnly || readPretty
        ? []
        : [
            {
              title: i18nFn('Actions'),
              dataIndex: 'action',
              width: 150,
              fixed: 'right',
              align: 'right',
              render: (text, field) => (
                <DeleteAction onClick={() => remove(field.name)} />
              ),
            },
          ]) as ColumnProps<FormListFieldData>[]),
    ],
    [
      readOnly,
      readPretty,
      policySpecialAgreementEnums,
      form,
      specialAgreementList,
      codeOptions,
      formKey,
    ]
  );

  return {
    specialAgreementColumns,
  };
};
