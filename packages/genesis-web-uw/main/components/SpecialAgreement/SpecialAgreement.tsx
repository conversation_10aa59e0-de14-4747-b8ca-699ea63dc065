import { FC, useMemo } from 'react';

import { Form, FormInstance } from 'antd';
import { StoreValue } from 'antd/es/form/interface';

import { v4 as uuid } from 'uuid';

import { Table, TextBody } from '@zhongan/nagrand-ui';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { MasterPolicy } from 'genesis-web-service';

import { AddButton } from '@uw/components';
import { SPECIAL_FORM_NAME } from '@uw/pages/proposal-entry/ProposalEntryDetail/sections/SPECIAL_AGREEMENT/constant';

import { useSpecialAgreementColumns } from './hooks';
import styles from './index.module.scss';

interface Props {
  form: FormInstance;
  // 前置的form的key集合
  namespace?: (number | string)[];
  // 被FormList 包裹才需要
  fieldName?: number;
  specialAgreementOriginDetail: MasterPolicy.MarketSpecialAgreementInfo[];
  policySpecialAgreementEnums?: BizDict[];
  specialAgreementKey: string;
  readOnly?: boolean;
  readPretty?: boolean;
  uniqueKey?: string;
  goodFieldsLength?: number;
}
export const SpecialAgreement: FC<Props> = ({
  form,
  fieldName,
  namespace,
  specialAgreementOriginDetail,
  policySpecialAgreementEnums = [],
  specialAgreementKey,
  readOnly,
  readPretty,
  uniqueKey,
  goodFieldsLength,
}) => {
  const codeOptions = useMemo(
    () =>
      specialAgreementOriginDetail?.map<BizDict>(specialAgreementItem => ({
        ...specialAgreementItem,
        enumItemName: specialAgreementItem.code,
        itemName: specialAgreementItem.code,
      })),
    [specialAgreementOriginDetail]
  );

  const formKey = useMemo(
    () => [
      ...(namespace ?? []),
      ...(typeof fieldName !== 'undefined' ? [fieldName] : []),
      specialAgreementKey,
    ],
    [namespace, fieldName, specialAgreementKey]
  );
  const { specialAgreementColumns } = useSpecialAgreementColumns(
    form,
    formKey,
    policySpecialAgreementEnums,
    codeOptions,
    readOnly,
    readPretty
  );

  const addButtonView = (
    add: (defaultValue?: StoreValue, insertIndex?: number) => void
  ) => {
    if (readPretty) {
      if (!goodFieldsLength || goodFieldsLength > 1) {
        return (
          <div className="flex justify-between items-center mb-4">
            <TextBody type="h5" weight={700}>
              {form.getFieldValue([
                uniqueKey
                  ? `${uniqueKey}_${SPECIAL_FORM_NAME}`
                  : SPECIAL_FORM_NAME,
                fieldName as number,
                'goodsName',
              ])}
            </TextBody>
          </div>
        );
      }
      return null;
    }
    if (!goodFieldsLength || goodFieldsLength > 1) {
      return (
        <div className="flex justify-between items-center mb-4">
          <TextBody type="h5" weight={700}>
            {form.getFieldValue([
              uniqueKey
                ? `${uniqueKey}_${SPECIAL_FORM_NAME}`
                : SPECIAL_FORM_NAME,
              fieldName as number,
              'goodsName',
            ])}
          </TextBody>
          <AddButton
            onClick={() =>
              add(
                {
                  specialAgreementType: undefined,
                  specialAgreementCode: undefined,
                  uniqueKey: uuid(),
                },
                0
              )
            }
          />
        </div>
      );
    }
    return (
      <AddButton
        className="absolute right-16 top-6"
        onClick={() =>
          add(
            {
              specialAgreementType: undefined,
              specialAgreementCode: undefined,
              uniqueKey: uuid(),
            },
            0
          )
        }
      />
    );
  };

  return (
    <section className="w-full">
      <Form.List
        name={
          typeof fieldName !== 'undefined'
            ? [fieldName, specialAgreementKey]
            : specialAgreementKey
        }
      >
        {(fields, { add, remove }) => (
          <>
            {addButtonView(add)}
            <Table
              className={styles.productTable}
              dataSource={fields}
              columns={specialAgreementColumns(remove)}
              pagination={false}
              scroll={{ x: 'max-content' }}
            />
          </>
        )}
      </Form.List>
    </section>
  );
};
