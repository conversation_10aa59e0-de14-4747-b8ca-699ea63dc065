import React, { <PERSON> } from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';

import styles from './FootBar.module.scss';

interface Props {
  onSubmit: () => void;
  hasEditAuth: boolean;
  onCancel?: () => void;
}
export const FootBar: FC<Props> = ({ onSubmit, hasEditAuth, onCancel }) => {
  const [t] = useTranslation(['uw', 'common']);

  return (
    <div className={styles['foot-bar-wrapper']}>
      <Button disabled={!hasEditAuth} onClick={onCancel}>
        {t('Cancel')}
      </Button>
      <Button disabled={!hasEditAuth} type="primary" onClick={onSubmit}>
        {t('Submit')}
      </Button>
    </div>
  );
};
