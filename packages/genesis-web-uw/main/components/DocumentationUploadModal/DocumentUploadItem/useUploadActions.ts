import { message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { AttachmentPartialProps } from './interface';

export const useUploadActions = () => {
  const [curUploaderItems, setCurUploaderItem] = useState<
    AttachmentPartialProps[]
  >([]);
  const { t } = useTranslation();

  const deleteFile = (item: AttachmentPartialProps) => {
    setCurUploaderItem(prevData =>
      prevData.filter(curUploaderItem => curUploaderItem.uid !== item.uid)
    );

    message.success(t('success'));
  };

  const addFile = (newValue: AttachmentPartialProps) => {
    setCurUploaderItem(prevData => [...prevData, newValue]);
  };

  const updateFile = (
    uid: AttachmentPartialProps['uid'],
    newValue: AttachmentPartialProps
  ) => {
    setCurUploaderItem(prevData =>
      prevData.map(item => {
        if (item.uid === uid) {
          return {
            ...item,
            ...newValue,
          };
        }
        return item;
      })
    );
  };

  return {
    deleteFile,
    addFile,
    updateFile,
    curUploaderItems,
  };
};
