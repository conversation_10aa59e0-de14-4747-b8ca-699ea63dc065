import { FormInstance } from 'antd';
import { UploadFileResponse } from 'genesis-web-service';

export interface AttachmentProps extends UploadFileResponse {
  // 前端用
  errorMessage?: string;
  percent?: number;
  descriptionModalVisible?: boolean;
  uid?: string;
}

export type AttachmentPartialProps = Partial<AttachmentProps>;

export type DocumentUploadItemProps = {
  form: FormInstance;
  changeLoading: (loading: boolean) => void;
  onChange?: (value: AttachmentPartialProps[]) => void;
};
