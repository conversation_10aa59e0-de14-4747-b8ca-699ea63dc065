import { VFC, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Row, Upload, message } from 'antd';
import { UploadChangeParam } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/lib/upload';
import { isEmpty } from 'lodash-es';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { ProposalService, QueryService } from 'genesis-web-service';
import { security } from 'genesis-web-shared';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { isImgFile } from 'genesis-web-shared/lib/util/fileType';

import { UploadOutlined } from '@ant-design/icons';
import { UploadStatus } from '@uw/interface/enum.interface';
import { getQueryFieldParamsString } from '@uw/util/getAttachmentMap';

import { AttachmentPartialProps, DocumentUploadItemProps } from './interface';
import { useUploadActions } from './useUploadActions';

export const DocumentUploadItem: VFC<DocumentUploadItemProps> = props => {
  const { changeLoading, onChange, form } = props;
  const { t } = useTranslation();

  const { deleteFile, updateFile, addFile, curUploaderItems } =
    useUploadActions();

  useEffect(() => {
    // 避免初始化校验出现红字
    if (isEmpty(curUploaderItems)) {
      form.setFieldValue('documents', curUploaderItems);
    } else {
      onChange?.(curUploaderItems);
    }
  }, [curUploaderItems, form]);

  const getUploadProps = useCallback(
    () => ({
      headers: { ...security.csrf() },
      customRequest: ({ file, onSuccess }: any) => {
        changeLoading(true);
        const formData = new FormData();
        formData.append('file', file);
        ProposalService.uploadAttachment(formData)
          .then(result => onSuccess?.(result, file))
          .catch(error => message.error(error))
          .finally(() => changeLoading(false));
      },
      showUploadList: false,
      multiple: true,
      beforeUpload: (file: RcFile) => {
        addFile({
          attachmentName: file.name,
          percent: 0,
          uid: file.uid,
          descriptionModalVisible: false,
        });

        return true;
      },
      onChange: ({ file }: UploadChangeParam) => {
        const { status, response } = file;

        if (status !== UploadStatus.Uploading) {
          // response 返回数组长度大于0为errors
          if (
            (status === UploadStatus.Done && response?.length) ||
            status === UploadStatus.Error
          ) {
            updateFile(file.uid, {
              errorMessage: file.response?.message || 'error',
              attachmentName: 'loser',
              percent: 100,
            });
            message.error(`${file.name} ${t('Upload Failed')}`);
          } else {
            updateFile(file.uid, {
              percent: file.percent,
              attachmentUrl: file.response.fileUniqueCode,
              ...file.response,
            });

            message.success(`${file.name} ${t('Upload Successfully')}`);
          }
        }
      },
    }),
    [addFile, changeLoading, t, updateFile]
  );

  const handleDownload = (url?: string) => {
    if (!url) {
      message.error(t('Download failed'));
      return;
    }

    QueryService.downloadFile(url)
      .then(response => {
        downloadFile(response).then(() => {
          message.success(t('Download successfully'));
        });
      })
      .catch((error: Error) => {
        message.error(error.message || t('Download failed'));
      });
  };

  const renderHoverInfoList = (curUploaderItem: AttachmentPartialProps) => {
    const hoverList = [
      {
        icon: <Icon type="download" />,
        key: 'download',
        onClick: () => handleDownload(curUploaderItem.attachmentUrl),
      },
      {
        key: 'delete',
        icon: <Icon type="delete" />,
        onClick: () => deleteFile(curUploaderItem),
      },
    ];

    // 上传失败不显示download和description按钮
    if (curUploaderItem.errorMessage) {
      return hoverList.filter(
        item => !['download', 'description'].includes(item.key)
      );
    }

    return hoverList;
  };

  const renderUploadItems = () => {
    if (isEmpty(curUploaderItems)) return null;

    return curUploaderItems.map(curUploaderItem => (
      <div className="document-upload-item">
        <UploadFileItem
          className="w-[200px] ml-xs"
          key={curUploaderItem.uid}
          fileName={curUploaderItem?.attachmentName || ''}
          fileUrl={
            curUploaderItem.attachmentUrl &&
            `/api/query/query/file/download/${curUploaderItem.attachmentUrl}?${getQueryFieldParamsString({ thirdPartyFile: curUploaderItem?.thirdPartyFile, docName: curUploaderItem?.attachmentName })}`
          }
          needPreview={isImgFile(curUploaderItem?.attachmentName)}
          errorMessage={curUploaderItem.errorMessage}
          showIconDirectly={true}
          hoverInfoList={renderHoverInfoList(curUploaderItem)}
          isShowProgress={
            curUploaderItem.percent
              ? curUploaderItem.percent > 0 && curUploaderItem.percent !== 100
              : false
          }
          percent={curUploaderItem.percent}
        />
      </div>
    ));
  };

  return (
    <div className="mt-6">
      <div className="upload-drag-box">
        <Row gutter={[16, 16]}>
          <Col className="upload-drag-col">
            <Upload
              {...getUploadProps()}
              className="border-none bg-transparent"
            >
              <Button icon={<UploadOutlined />}>{t('Upload Document')}</Button>
            </Upload>
          </Col>
          {renderUploadItems()}
        </Row>
      </div>
    </div>
  );
};
