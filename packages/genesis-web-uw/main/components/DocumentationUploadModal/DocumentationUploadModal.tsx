import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Form, Modal, Row } from 'antd';

import { isString } from 'lodash-es';

import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';
import { getFields } from 'genesis-web-component/lib/util/getFieldsQueryForm';
import { useBoolean } from 'genesis-web-shared/lib/hook';
import { DEFAULT_REQUIRED_RULES } from 'genesis-web-shared/lib/util/constants';

import { DocumentUploadItem } from './DocumentUploadItem';
import { useSubmitAttachments } from './hooks/useSubmitAttachments';
import {
  DocumentUploadFormValue,
  DocumentationUploadModalProps,
} from './interface';

export const DocumentationUploadModal: React.FC<
  DocumentationUploadModalProps
> = props => {
  const {
    visible,
    businessNo,
    queryModule,
    refDetail,
    uploadAttachmentTypes,
    onSubmit,
    onClose,
  } = props;
  const [form] = Form.useForm<DocumentUploadFormValue>();
  const { handleAttachmentSubmit } = useSubmitAttachments(
    businessNo,
    queryModule
  );
  const { t } = useTranslation(['uw', 'common']);
  const [loading, setLoading] = useBoolean(false);

  const handleSubmit = useCallback(async () => {
    const formValue = await form.validateFields();
    if (isString(formValue.attachmentTypes)) {
      formValue.attachmentTypes = [formValue.attachmentTypes];
    }
    await handleAttachmentSubmit(formValue, refDetail);
    onClose?.();
    onSubmit?.();
  }, [form, refDetail]);

  const footerElement = useMemo(
    () => (
      <>
        <Button size="large" className="mr-[16px]" onClick={onClose}>
          {t('Cancel')}
        </Button>

        <Button
          type="primary"
          size="large"
          onClick={handleSubmit}
          loading={loading}
        >
          {t('Submit')}
        </Button>
      </>
    ),
    [handleSubmit, onClose, t, loading]
  );

  const attachmentTypeSelect = useMemo(
    () => (
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Form.Item
            name="attachmentTypes"
            label={t('Document Type')}
            rules={DEFAULT_REQUIRED_RULES.Select}
            className="mb-0"
          >
            {getFields({
              label: t('Document Type'),
              placeholder: t('Please select'),
              type: FieldType.Select,
              allowClear: true,
              col: 24,
              key: 'attachmentTypes',
              options: uploadAttachmentTypes,
              rules: DEFAULT_REQUIRED_RULES.Select,
              valProps: 'value',
              labelProps: 'label',
            })}
          </Form.Item>
        </Col>
      </Row>
    ),
    [t, uploadAttachmentTypes]
  );

  return (
    <Modal
      open={visible}
      width={504}
      closable={false}
      footer={footerElement}
      destroyOnClose
      title={t('Upload Document')}
    >
      <Form layout="vertical" form={form} preserve={false}>
        <div>
          <div className="mb-4 text-light">
            {t(
              'All upload data you currently submit will be recorded. If the file verified failed, it will not be saved.'
            )}
          </div>
          {attachmentTypeSelect}
          <Form.Item
            name="documents"
            rules={[{ required: true, message: t('Please Upload Document') }]}
          >
            <DocumentUploadItem form={form} changeLoading={setLoading} />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};
