import { QueryModuleEnum, YesOrNo } from '@uw/interface/enum.interface';
import {
  PolicyService,
  ProposalService,
  UploadAttachmentListDetail,
} from 'genesis-web-service';

import { RefDetail } from '@uw/interface/common.interface';

import { DocumentUploadFormValue } from '../interface';

export const useSubmitAttachments = (
  businessNo: string,
  queryModule: QueryModuleEnum
) => {
  const handleAttachmentSubmit = async (
    payload: DocumentUploadFormValue,
    refDetail: RefDetail
  ) => {
    if (!businessNo || !queryModule || !refDetail) {
      return;
    }

    const param: UploadAttachmentListDetail[] = [];
    const { attachmentTypes, documents } = payload;

    attachmentTypes.forEach(documentType => {
      documents.forEach(({ attachmentUrl, attachmentName }) => {
        param.push({
          ...refDetail,
          attachmentUrl,
          attachmentName,
          needUpdate: YesOrNo.YES,
          documentType,
        });
      });
    });
    if (queryModule === QueryModuleEnum.PolicyQuery) {
      await PolicyService.uploadPolicyAttachments(businessNo, param);
    }
    if (queryModule === QueryModuleEnum.ProposalQuery) {
      await ProposalService.uploadProposalAttachments(businessNo, param);
    }
  };

  return { handleAttachmentSubmit };
};
