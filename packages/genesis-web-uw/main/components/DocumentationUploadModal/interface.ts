import { LabeledValue } from 'antd/lib/select';

import { QueryModuleEnum } from '@uw/interface/enum.interface';
import { RefDetail } from '@uw/interface/common.interface';

import { AttachmentPartialProps } from './DocumentUploadItem';

export type DocFormValues = {
  attachmentTypes: string[];
  documents: AttachmentPartialProps[];
};

export type DocumentationUploadModalProps = {
  visible: boolean;
  businessNo: string;
  queryModule: QueryModuleEnum;
  refDetail: RefDetail;
  uploadAttachmentTypes: LabeledValue[];
  onClose?: () => void;
  onSubmit?: () => void;
};

interface DocumentDetail {
  attachmentUrl: string;
  attachmentName: string;
}

export interface DocumentUploadFormValue {
  attachmentTypes: string[];
  documents: DocumentDetail[];
}
