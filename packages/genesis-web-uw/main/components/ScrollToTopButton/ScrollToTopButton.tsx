import { useCallback, useEffect, useState } from 'react';

import { TopUp } from '@uw/assets/new-icons';

interface ScrollToTopButtonProps {
  isAlwaysVisible?: boolean;
}

export const ScrollToTopButton = ({
  isAlwaysVisible,
}: ScrollToTopButtonProps) => {
  const [isHover, setIsHover] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const handleScroll = useCallback(() => {
    if (isAlwaysVisible) return;
    const isScrolled =
      document.documentElement.scrollTop >
      document.documentElement.clientHeight;
    setIsVisible(isScrolled);
  }, [isAlwaysVisible]);

  useEffect(() => {
    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  const onScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    setIsHover(false);
  };

  return (
    <>
      {(isVisible || isAlwaysVisible) && (
        <div
          className="w-12 h-12 rounded-full bg-white flex justify-center items-center shadow-lg fixed bottom-20 right-4 z-50"
          onMouseEnter={() => setIsHover(true)}
          onMouseLeave={() => setIsHover(false)}
          onClick={onScrollToTop}
        >
          <TopUp
            color={
              isHover ? 'var(--primary-color)' : 'var(--text-color-secondary)'
            }
          />
        </div>
      )}
    </>
  );
};
