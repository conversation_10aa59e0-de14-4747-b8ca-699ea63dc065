import React, { FC, useCallback, useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Col } from 'antd';
import { FactorsType } from 'genesis-web-service';
import {
  BizDict,
  InfoTab,
  IsCurrencyAmountTypeEnum,
} from '@uw/interface/enum.interface';

import { TaskContext } from '@uw/pages/uw-pages/uwOperationPage/context-manager';

import { amountFormatInstance } from 'genesis-web-shared/lib/l10n';

import styles from './BasicInfoList.module.scss';
import { BasicFormItem } from './BasicFormItem';

interface Props {
  key: string;
  label: string;
  value?: string | JSX.Element;
  span?: number;
  className?: string;
  editKeys?: string[];
  name: string;
  factor?: FactorsType;
  enumsMap?: Record<string, BizDict[]>;
  plateNo?: string;
  currency?: string;
  hasEditAuth?: boolean;
  tabInfo?: InfoTab;
}

export const BasicInfoList: FC<Props> = ({
  key,
  label,
  value,
  span,
  className,
  editKeys,
  factor,
  enumsMap,
  plateNo,
  currency,
  hasEditAuth,
  tabInfo,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const { state } = useContext(TaskContext);

  const vehicleInfo = useMemo(() => {
    Object.keys(state?.currentVehicleInfo ?? {}).reduce<{
      [key: string]: unknown;
    }>((result, current) => {
      const resultInfo = { ...result };
      const vehicleKey = current.split('/')[2];
      resultInfo[vehicleKey] = state?.currentVehicleInfo?.[current as string];
      return result;
    }, {});
  }, [state?.currentVehicleInfo]);

  const getEditableValue = useCallback(
    (factorCode: string) => {
      const currentValue = vehicleInfo?.[factorCode];
      if (currentValue) {
        return currentValue === value ? value : currentValue;
      }
      return value;
    },
    [state?.currentVehicleInfo, value]
  );

  const displayValue = useMemo(() => {
    if (factor?.isCurrencyAmount === IsCurrencyAmountTypeEnum.Yes) {
      return currency
        ? amountFormatInstance.getAmountCurrencyString(
          value as string,
          currency ?? ''
        )
        : value;
    }
    return value;
  }, [currency, value, factor]);

  return (
    <Col span={span} key={key} className={className}>
      <div className={styles.basicTitle}>{label}</div>
      {factor &&
        editKeys?.includes(factor?.factorCode) &&
        tabInfo === InfoTab.Quotation ? (
        <BasicFormItem
          factor={factor}
          enumsMap={enumsMap}
          value={getEditableValue(factor?.factorCode)}
          plateNo={plateNo}
          hasEditAuth={hasEditAuth}
        />
      ) : (
        <div className={styles.basicContent}>{displayValue ?? t('--')}</div>
      )}
    </Col>
  );
};
