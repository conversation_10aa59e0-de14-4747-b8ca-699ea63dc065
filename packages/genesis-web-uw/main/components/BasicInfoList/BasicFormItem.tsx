import { FC, useEffect, useMemo, useState } from 'react';
import { QuotationEditableEnum } from '@uw/interface/enum.interface';
import {
  DataTypeEnum,
  BizDict,
} from 'genesis-web-component/lib/interface/enum.interface';
import { handleFileType } from '@uw/pages/verification-query-pages/VerificationDetail/components/userInfo/getEditFormConfig';
import { Form } from 'antd';
import { FactorsType } from 'genesis-web-service';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { getFields } from '@uw/util/getFields';

import { handleRequired } from '@uw/util/handleApplicationElements';

import styles from './BasicInfoList.module.scss';

interface Props {
  value?: string | JSX.Element;
  factor: FactorsType;
  enumsMap?: Record<string, BizDict[]>;
  plateNo?: string;
  hasEditAuth?: boolean;
}
export const BasicFormItem: FC<Props> = ({
  factor,
  value,
  enumsMap,
  plateNo,
  hasEditAuth = true,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const [placeholder, setPlaceholder] = useState<string>();

  useEffect(() => {
    const text =
      +factor?.dataType === DataTypeEnum.INPUT || DataTypeEnum.NUMBER
        ? t('please input')
        : t('please select');

    setPlaceholder(text);
  }, [factor]);

  const mapBizDicts = useMemo(
    () => enumsMap?.[factor.bizDictKey] ?? enumsMap?.[factor.factorCode] ?? [],
    [enumsMap, factor]
  );

  const formatInitialValue = useMemo(() => {
    if (!value) {
      return undefined;
    }
    if (+factor?.dataType === DataTypeEnum.DATE) {
      return moment(value as string);
    }
    return value;
  }, [factor?.dataType, value]);

  return (
    <>
      <Form.Item
        name={`${QuotationEditableEnum.VEHICLE}/${
          encodeURIComponent(plateNo as string) ?? ''
        }/${factor?.factorCode}`}
        key={factor?.factorCode}
        rules={[
          {
            required: handleRequired(factor),
            message: placeholder,
          },
        ]}
        initialValue={formatInitialValue}
        style={{ margin: `${styles.gapXss} 0 0` }}
      >
        {getFields({
          type: handleFileType(+factor?.dataType),
          options: mapBizDicts ?? [],
          format: +factor?.dataType === DataTypeEnum.DATE ? 'YYYY-MM-DD' : '',
          hideTime: +factor?.dataType === DataTypeEnum.DATE,
          placeholder,
          disabled: !hasEditAuth,
        })}
      </Form.Item>
    </>
  );
};
