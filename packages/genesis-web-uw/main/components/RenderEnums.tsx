import { Fragment } from 'react';

import { dictMap } from '@uw/hook/useBizDict';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

interface RenderEnumsProp {
  keyName: string;
  enums: BizDict[];
  renderKey?: string;
}

/**
 * @description - RenderEnums组件，用于通过matedata的key去查找对应的name（也就是对应的国际化）
 */

export const RenderEnums = ({
  keyName,
  enums,
  renderKey,
}: RenderEnumsProp): JSX.Element => (
  <Fragment>{dictMap(enums, keyName, renderKey)}</Fragment>
);
