import useSWR from 'swr';

import {
  MetadataService,
  PaymentOrCollection,
  TransactionType,
} from 'genesis-web-service';

import { generateFlatOptions } from './util';

// todo: de-duplicate
const PayMethodDictKey = 'payMethod';

/** 收费/退费根据各级域配置的选项
 * https://jira.zaouter.com/browse/GIS-61259
 * @param paymentOrCollection
 */
export const usePaymentOrCollectionFlatOptions = (
  paymentOrCollection?: PaymentOrCollection,
  transactionType?: TransactionType
) => {
  const queryBizDict = async () =>
    MetadataService.getPaymentMethodsBizDict(
      paymentOrCollection,
      transactionType ?? TransactionType.POS
    );

  const swrKey = {
    paymentOrCollection,
    transactionType: transactionType ?? TransactionType.POS,
  };
  const { data: paymentTreeData } = useSWR(
    JSON.stringify(swrKey),
    queryBizDict
  );
  const bizDictKey = paymentTreeData?.[0]?.dictKey ?? PayMethodDictKey;
  const {
    parentOptions: paymentMethodFlatOptions,
    childrenOptions: accountTypeFlatOptions,
  } = generateFlatOptions(paymentTreeData, bizDictKey);
  return {
    paymentMethodFlatOptions,
    accountTypeFlatOptions,
  };
};
