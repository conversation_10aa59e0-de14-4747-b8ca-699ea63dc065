import { BizDictItem } from 'genesis-web-service';

/**
 * 生成tree的选项，级联
 * @param data
 * @param key
 * @param value
 */
export const generateTreeOptions = (
  data: BizDictItem[] = [],
  key: string,
  value?: unknown,
  valueKey: keyof BizDictItem = 'enumItemName',
  labelKey: keyof BizDictItem = 'dictValueName'
) => {
  const dataSource = data?.filter(dictItem => dictItem.dictKey === key) ?? [];
  const parentOptions = dataSource.map(dictItem => ({
    label: dictItem[labelKey],
    value: dictItem[valueKey],
    children: dictItem.childList?.map(childItem => ({
      label: childItem[labelKey],
      value: childItem[valueKey],
      childList: childItem.childList,
    })),
  }));

  const chosenChildren = parentOptions.find(
    option => option.value === value
  )?.children;

  const childrenOptions = chosenChildren?.map(dictItem => ({
    label: dictItem.label,
    value: dictItem.value,
    children: dictItem.childList,
  }));

  return {
    parentOptions,
    childrenOptions,
  };
};

/**
 * 生成所有的级联选项，平铺用于比如columns展示
 * @param data
 * @param key
 */
export const generateFlatOptions = (
  data: BizDictItem[] = [],
  key: string,
  valueKey: keyof BizDictItem = 'enumItemName',
  labelKey: keyof BizDictItem = 'dictValueName'
) => {
  const dataSource = data?.filter(dictItem => dictItem.dictKey === key) ?? [];
  const parentOptions = dataSource.map(dictItem => ({
    label: dictItem[labelKey] as string,
    value: dictItem[valueKey] as unknown,
  }));

  const childrenOptions = dataSource.reduce(
    (prev, cur) =>
      prev.concat(
        cur.childList?.map(dictItem => ({
          label: dictItem[labelKey] as string,
          value: dictItem[valueKey] as unknown,
        })) ?? []
      ),
    [] as { label: string; value: unknown }[]
  );

  return {
    parentOptions,
    childrenOptions,
  };
};
