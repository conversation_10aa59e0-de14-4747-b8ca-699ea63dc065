import React, { ReactNode, VFC } from 'react';

import cx from 'clsx';
import { twMerge } from 'tailwind-merge';

import { usePaymentSchema } from 'genesis-web-component/lib/components/PaymentMethodFormV4/usePaymentSchema';
import { AccountSubTypeFieldFilter } from 'genesis-web-component/lib/components/PaymentMethodFormV4/util';
import {
  BankFieldFilter,
  CommonAccountFieldsFilter,
} from 'genesis-web-component/lib/components/PaymentMethodFormV4/util';
import { TransactionType, UWRefundPaymentAccount } from 'genesis-web-service';
import { PaymentOrCollection } from 'genesis-web-service/lib/common.interface';

import { SchemaForm } from '@uw/components/SchemaForm';
import { SelfDefinedSchemaType } from '@uw/interface/enum.interface';
import { i18nFn } from '@uw/util/i18nFn';

import { ThinTableClassName } from './constant';

type RecordType = UWRefundPaymentAccount;

interface Props {
  transactionType?: TransactionType;
  /**
   * 编辑修改后的信息, 初始化为`beforeRecord
   */
  afterRecord?: RecordType;
  /**
   * 从table中带过来的数据
   */
  className?: string;
  methodValue?: string;
  payMethodLabel?: ReactNode;
  paymentOrCollection?: PaymentOrCollection;
  isPremiumPaymentAccount?: boolean; // 判断是否为 Premium Payment Account 引用此处组件
  /** UI的样式布局 */
}

export const PaymentAccountInfoView: VFC<Props> = props => {
  const {
    transactionType,
    afterRecord,
    className,
    methodValue,
    payMethodLabel = i18nFn('Payment Method'),
    paymentOrCollection = PaymentOrCollection.Payment,
    isPremiumPaymentAccount = false,
  } = props;
  const paymentMethodValue = methodValue ?? afterRecord?.paymentMethod;
  const accountTypeValue = afterRecord?.accountType;
  const { paymentMethodOptions, accountTypeOptions, dynamicSchemaConfigs } =
    usePaymentSchema(
      paymentMethodValue,
      accountTypeValue,
      paymentOrCollection,
      transactionType ?? TransactionType.POS
    );

  // 需求： 动态字段按照orderNo排序后再按照bankModule、accountSubTypeModuleProps、其余accountModuleProps排序。
  const sortModuleProps =
    dynamicSchemaConfigs?.sort(
      (prev, next) => prev.orderNo ?? 0 - next.orderNo ?? 0
    ) || [];

  const bankModuleProps = sortModuleProps.filter(BankFieldFilter) || [];
  const accountSubTypeModuleProps =
    sortModuleProps.filter(AccountSubTypeFieldFilter)?.map(item => ({
      ...item,
      // GIS-78119
      bizDictKey: 'accountType',
    })) || [];

  const accountModuleProps =
    sortModuleProps.filter(CommonAccountFieldsFilter) || [];
  const sortDynamicSchemaConfig = [
    {
      schemaType: SelfDefinedSchemaType?.Property,
      moduleValueField: '',
      moduleName: '',
      // GIS-106830
      moduleProperties: isPremiumPaymentAccount
        ? bankModuleProps
            .concat(accountSubTypeModuleProps, accountModuleProps)
            .filter(item => item.code !== 'cardNumber')
        : bankModuleProps.concat(accountSubTypeModuleProps, accountModuleProps),
    },
  ];

  const paymentMethodText =
    paymentMethodOptions?.find(
      paymentMethod => paymentMethod.value === paymentMethodValue
    )?.label ?? i18nFn('--');
  const accountTypeText =
    accountTypeOptions?.find(
      accountType => accountType.value === accountTypeValue
    )?.label ?? i18nFn('--');

  return (
    <div className={cx(className)}>
      <>
        <section className={twMerge(ThinTableClassName.row, 'bg-[#f4f6f7]')}>
          <div className={ThinTableClassName.rowItem}>
            {payMethodLabel} / {i18nFn('Account Type')}
          </div>
          <div className={'ml-10'}>
            {paymentMethodText} / {accountTypeText}
          </div>
        </section>
        <SchemaForm
          rowClassName={ThinTableClassName.row}
          tableItemClassName={ThinTableClassName.rowItem}
          schema={sortDynamicSchemaConfig}
          formFieldPrefix=""
          showBeforeColumn={false}
          afterRecord={afterRecord}
        />
      </>
    </div>
  );
};
