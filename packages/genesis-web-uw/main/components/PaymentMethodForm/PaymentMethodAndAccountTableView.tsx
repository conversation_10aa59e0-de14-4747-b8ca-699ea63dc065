import { DynamicFormSchemaConfigs } from 'genesis-web-component/lib/components/PaymentMethodFormV4/interface';
import { PaymentOrCollection, PolicyPayerListType } from 'genesis-web-service';
import React, { FC, ReactNode } from 'react';

import { PaymentAccountInfoView } from '@uw/components/PaymentMethodForm/PaymentAccountInfoView';

interface Props {
  data?: PolicyPayerListType;
  schemaConfigs?: DynamicFormSchemaConfigs[];
  payMethodLabel?: ReactNode;
}

export const PaymentMethodAndAccountTableView: FC<Props> = props => {
  const { data, payMethodLabel } = props;

  return (
    <PaymentAccountInfoView
      afterRecord={data}
      className={'grow'}
      methodValue={data?.payMethod}
      paymentOrCollection={PaymentOrCollection.Payment}
      payMethodLabel={payMethodLabel}
    />
  );
};
