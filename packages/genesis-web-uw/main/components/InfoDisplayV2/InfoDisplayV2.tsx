import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { PreviewText } from '@zhongan/nagrand-ui';

import { BasicInfoData, PersonInfoType } from 'genesis-web-service';

import { PartyType } from '@uw/interface/enum.interface';

import { LinkToPolicyDetail, POS_SEARCH_LINK } from './constant';

export const InfoDisplayV2: FC<{
  basicInfo: BasicInfoData;
  policyHolder: PersonInfoType;
}> = ({ basicInfo, policyHolder }) => {
  const [t] = useTranslation(['uw', 'common']);

  const infoItems = [
    {
      label: t('POS No.'),
      value: basicInfo?.applicationNo,
      isLink: true,
      // 由于POS跳转的detail页面和search点击进去detail的页面不一样，这里根据产品最终的结论是只跳转search页面
      link: POS_SEARCH_LINK,
    },
    {
      label: t('Policy No.'),
      value: basicInfo?.policyNo,
      isLink: true,
      link: LinkToPolicyDetail(basicInfo?.policyNo),
    },
    {
      label: t('Goods Name'),
      value: basicInfo?.goodsName,
    },
    {
      label: t('PolicyHolder'),
      value:
        policyHolder?.partyType === PartyType.COMPANY
          ? policyHolder?.company?.organizationName
          : policyHolder?.fullName,
    },
  ];
  return (
    <div className="px-6 h-[48px] flex items-center justify-start border-b border-b-solid border-tableHeaderSortBg text-textTertiary">
      {infoItems.map(item => (
        <span className="text-sm font-medium flex-1 flex items-center">
          <span className="!leading-5">{`${item.label}:`}</span>
          {item.isLink ? (
            // <a></a>的样式颜色被.uw-wrapper覆盖了，所以这里用div包一层
            <div className="text-primary ">
              <a
                href={item.link}
                className="flex !leading-5 !text-sm ml-2 cursor-pointer underline"
              >
                {item.value}
              </a>
            </div>
          ) : (
            <PreviewText
              value={item.value}
              className="flex !leading-5 !text-sm ml-2 !text-textTertiary"
            ></PreviewText>
          )}
        </span>
      ))}
    </div>
  );
};
