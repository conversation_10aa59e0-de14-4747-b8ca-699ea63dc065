import {
  DetailedHTMLProps,
  HTMLAttributes,
  ReactNode,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';

import { Collapse, CollapseProps } from 'antd';
import { CollapsibleType } from 'antd/es/collapse/CollapsePanel';

import { size } from 'lodash-es';
import { twMerge } from 'tailwind-merge';

import UpCircle from '@uw/assets/icons/up-circle.svg';

export interface CollapsibleSectionRef {
  setActiveKey: (keys: string[]) => void;
}

const collapseStyle =
  '&_.antd-collapse>.antd-collapse-item>.antd-collapse-header:p-[24px_16px] &_.antd-collapse>.antd-collapse-item>.antd-collapse-header:items-center &_.antd-collapse>.antd-collapse-item>.antd-collapse-header_.antd-collapse-expand-icon:h-[32px] &_.antd-collapse_.antd-collapse-content:border-0';
interface Props
  extends DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  extra?: ReactNode;
  label: ReactNode;
  type: 'outer' | 'inner';
  defaultExpand?: boolean;
  collapsible?: CollapsibleType;
}

export const CollapsibleSection = forwardRef<CollapsibleSectionRef, Props>(
  (props, ref) => {
    const {
      type,
      extra,
      label,
      defaultExpand = true,
      children,
      collapsible = 'icon',
      className,
      ...restDivProps
    } = props;

    const [activeKey, setActiveKey] = useState<string[]>([]);
    useEffect(() => {
      setActiveKey(defaultExpand ? ['1'] : []);
    }, [defaultExpand]);
    const items = useCallback<() => CollapseProps['items']>(
      () => [
        {
          key: 1,
          label,
          children,
          extra,
        },
      ],
      [children, label, extra]
    );

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      setActiveKey,
    }));

    const collapseItemStyle = useMemo(
      () =>
        type === 'inner'
          ? `&_.antd-collapse-item_.antd-collapse-content.antd-collapse-content-active:bg-formAddonBg &_.antd-collapse-item.antd-collapse-item-active_.antd-collapse-header:bg-formAddonBg`
          : `border-0 &_.antd-collapse-item:border-0 &_.antd-collapse-item_.antd-collapse-content.antd-collapse-content-active:bg-white &_.antd-collapse-item.antd-collapse-item-active_.antd-collapse-header:bg-white`,
      [type]
    );

    return (
      <div className={twMerge(className, collapseStyle)} {...restDivProps}>
        <Collapse
          activeKey={activeKey}
          expandIcon={({ isActive }) => (
            <UpCircle
              className={twMerge(
                '!text-[32px]',
                '!text-textQuaternary',
                isActive ? 'rotate-0' : 'rotate-180'
              )}
            />
          )}
          className={twMerge(
            'bg-white',
            '&_.antd-collapse-item.antd-collapse-item-active_.antd-collapse-header:pb-md',
            '&_.antd-collapse-item.antd-collapse-item-active_.antd-collapse-header:rounded-t-[10px] &_.antd-collapse-item.antd-collapse-item-active_.antd-collapse-header:rounded-b-[0px]',
            '&_.antd-collapse-item>.antd-collapse-content>.antd-collapse-content-box:p-[0_16px_24px]',
            collapseItemStyle
          )}
          items={items()}
          ghost={size(activeKey) > 0 ? true : false}
          expandIconPosition="end"
          collapsible={collapsible}
          onChange={activeKeys => {
            setActiveKey(activeKeys as string[]);
          }}
        />
      </div>
    );
  }
);
