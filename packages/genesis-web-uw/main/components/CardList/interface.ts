import { ReactNode } from 'react';

import { YesOrNo } from 'genesis-web-service';

export interface CardInfo extends Record<string, unknown> {
  proposalStatus: string;
  policyEffectiveDate: string;
  policyExpiryDate: string;
  zoneId: string;
  goodsCategoryId: string;
  proposalNo: string;
  temporary: string;
  canIssue?: YesOrNo;
  pendingCheckList?: string[];
}

export interface ExtraActionType {
  title: string;
  icon: ReactNode;
  onClick: (data: CardInfo) => void;
  type?: string;
  show?: (data: CardInfo) => boolean;
}
