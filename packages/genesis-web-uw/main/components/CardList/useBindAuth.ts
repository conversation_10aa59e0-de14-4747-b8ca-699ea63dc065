import { usePermission } from '@uw/hook/permission';
import {
  ProposalStatusEnum,
  QueryModuleEnum,
} from '@uw/interface/enum.interface';
import { useMemo } from 'react';

export const useBindAuth = (queryModule: QueryModuleEnum) => {
  const hasBindAuth = usePermission('query.quotation.bind');
  const showQuoteBoundButton = useMemo(
    () =>
      queryModule === QueryModuleEnum.QuotationQuery &&
      ProposalStatusEnum.DATA_ENTRY_IN_PROGRESS &&
      hasBindAuth,
    [hasBindAuth, queryModule]
  );
  return { showQuoteBoundButton };
};
