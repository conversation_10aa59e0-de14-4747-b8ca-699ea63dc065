import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { isEmpty, size } from 'lodash-es';

import {
  CardV2 as Card,
  CardActionsContainer,
  CardBodyHeader,
  CardBodyPrimaryInfo,
  CardFooter,
  CardLayoutV2 as CardLayout,
  CardTagList,
  CommonIconAction,
  Icon,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { GoodsCategoryIcon } from 'genesis-web-component/lib/components/GoodsCategoryIcon';
import { SectionFieldsConfigItem } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { useDict } from '@uw/biz-dict/hooks';
import { QueryModuleEnum } from '@uw/interface/enum.interface';
import { getStatusTagType } from '@uw/util/getStatusTagType';
import { i18nFn } from '@uw/util/i18nFn';

import { ProposalStatusTag } from '../ProposalStatusTag';
import { CardInfo, ExtraActionType } from './interface';
import { useBindAuth } from './useBindAuth';

interface Props {
  loading: boolean;
  policyList: CardInfo[];
  cardKey: string;
  queryModule?: QueryModuleEnum;
  statusEnumMap?: Record<string, string>;
  handleActionView: (
    proposalNo: string,
    module: string,
    isTemp: string
  ) => void;
  boundTask?: (proposalNo: string) => void;
  extraActions?: ExtraActionType[];
  cardFooterFields?: SectionFieldsConfigItem[];
}

const numberTextMap: Record<string, string> = {
  [QueryModuleEnum.ProposalQuery]: i18nFn('Proposal No.'),
  [QueryModuleEnum.PolicyQuery]: i18nFn('Policy No.'),
};

const CardList: React.FC<Props> = ({
  loading,
  policyList,
  cardKey,
  queryModule = QueryModuleEnum.ProposalQuery,
  statusEnumMap,
  handleActionView,
  boundTask,
  extraActions,
  cardFooterFields,
}) => {
  const [t] = useTranslation(['uw', 'common']);
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [statusEnum] = useDict('issuanceStatus');
  const [bizStatusEnum] = useDict('bizStatus');
  const { showQuoteBoundButton } = useBindAuth(queryModule);
  const statusOptions = statusEnumMap ?? statusEnum;

  const bodySection = useCallback(
    (record: CardInfo) => {
      const tagList = [];
      if (queryModule === QueryModuleEnum.ProposalQuery) {
        tagList.push({
          type: TagType.Customize,
          render: (
            <ProposalStatusTag
              statusI18n={statusOptions?.[record.proposalStatus]?.toUpperCase()}
              proposalStatus={record.proposalStatus}
              type={getStatusTagType(record.proposalStatus)}
              pendingCheckList={record.pendingCheckList}
              proposalNo={record.proposalNo}
            />
          ),
          tooltip: statusOptions?.[record.proposalStatus],
        });
      } else {
        tagList.push({
          type: TagType.Tag,
          tagProps: {
            statusI18n: statusOptions?.[record.proposalStatus]?.toUpperCase(),
            type: getStatusTagType(record.proposalStatus),
            needBgColor: true,
            tooltip: !isEmpty(record.pendingCheckList) && (
              <>
                {record.pendingCheckList?.map(pendingCheck => (
                  <div>{bizStatusEnum[pendingCheck]}</div>
                ))}
              </>
            ),
          },
        });
      }
      return (
        <>
          <CardBodyHeader
            leftContent={t('To', {
              start: dateFormat
                .l10nMoment(record.policyEffectiveDate, record.zoneId)
                .format(dateFormat.dateFormat),
              end: record.policyExpiryDate
                ? dateFormat
                    .l10nMoment(record.policyExpiryDate, record.zoneId)
                    .format(dateFormat.dateFormat)
                : t('--'),
            })}
          />
          {/*新卡片改造时，与pdm确认不会出现goodsCategoryId不存在的情况*/}
          <CardBodyPrimaryInfo
            title={numberTextMap[queryModule]}
            content={record.proposalNo}
            right={
              <GoodsCategoryIcon
                iconKey={cardKey}
                goodsCategory={+record.goodsCategoryId}
                className="card-goods-category-icon"
              />
            }
          />
          <CardTagList tagList={tagList} />
        </>
      );
    },
    [t, dateFormat, statusOptions, cardKey, queryModule, bizStatusEnum]
  );

  const footerSection = useCallback(
    (record: CardInfo) => {
      let footerList =
        cardFooterFields?.map(cardFooterField => ({
          label: cardFooterField.displayName,
          value: record?.[cardFooterField?.field],
        })) || [];
      if (size(cardFooterFields) === 0) {
        footerList = [
          {
            label: t('query-Policyholder Name'),
            value: record.policyHolderName,
          },
          {
            label: t('query-Goods Name'),
            value: `${record.goodsName} ${record.goodsVersion}`,
          },
          {
            label: t('Plan Name'),
            value: record.goodsPlanName,
          },
        ];
      }
      return <CardFooter list={footerList} />;
    },
    [cardFooterFields, t]
  );

  const actionSection = useCallback(
    (record: CardInfo): JSX.Element => (
      <CardActionsContainer>
        <ViewAction
          onClick={() =>
            handleActionView(record.proposalNo, queryModule, record.temporary)
          }
        />
        {showQuoteBoundButton && (
          <CommonIconAction
            icon={<Icon type="history" />}
            tooltipTitle={t('Quote Bound')}
            onClick={() => boundTask?.(record.proposalNo)}
          />
        )}
        {extraActions
          ?.filter(action => action.show?.(record))
          .map(action => (
            <CommonIconAction
              key={action.title}
              icon={<action.icon type={action.type ?? ''} />}
              tooltipTitle={action.title}
              onClick={() => action.onClick?.(record)}
            />
          ))}
      </CardActionsContainer>
    ),
    [
      boundTask,
      extraActions,
      handleActionView,
      queryModule,
      showQuoteBoundButton,
      t,
    ]
  );

  return (
    <CardLayout loading={loading}>
      <>
        {policyList?.map(record => (
          <Card
            key={record.proposalNo}
            body={bodySection(record)}
            footer={footerSection(record)}
            actions={actionSection(record)}
          />
        ))}
      </>
    </CardLayout>
  );
};

export default CardList;
