.fix-menu {

  :global {
    // 去除卡片右上 右下圆角
    .#{$ant-prefix}-card {
      border-radius: var(--gap-xs) 0 0 var(--gap-xs);
    }

    .#{$ant-prefix}-card-body {
      padding: var(--gap-lg) var(--gap-md);

    }
    .#{$ant-prefix}-anchor-ink {
      &::before {
        width: 0.5px;
      }
    }
    .#{$ant-prefix}-anchor-ink-visible {
      height: var(--gap-big) !important;
    }
    .#{$ant-prefix}-anchor-link {
      padding-block: var(--gap-xs) !important;
    }
    .#{$ant-prefix}-anchor-ink-ball {
      width: 1px;
      height: 40px;
      border: 1px solid var(--primary-color);
      transform: translate(-50%, -50%);
    }

    .#{$ant-prefix}-anchor-link-active
      .#{$ant-prefix}-anchor-link-title-active {
      color: var(--primary-color) !important;
      font-weight: 500 !important;
    }
  }

  // 暂时没找到怎么转成tailwindcss形式，先在此处理样式
  .anchor-pin {
    &:hover {
      svg {
        path {
          stroke: var(--primary-color);
        }
      }
    }
  }
}
