import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Anchor, Card } from 'antd';
import { AnchorLinkItemProps } from 'antd/es/anchor/Anchor';
import { AnchorContainer } from 'antd/lib/anchor/Anchor';

import clx from 'clsx';
import { useAtomValue } from 'jotai';
import { isEmpty } from 'lodash-es';

import {
  CommonIconAction,
  Icon,
  TextEllipsisDetect,
} from '@zhongan/nagrand-ui';

import { MasterAgreementPageSections } from 'genesis-web-component/lib/components/MasterAgreement';

import PinIcon from '@uw/assets/new-icons/pin.svg';
import { isTravelObjectTypeAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { AnchorConnector } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/config';
import { ProposalEntryPageSections } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/sections/interface';
import { i18nFn } from '@uw/util/i18nFn';

import styles from './index.module.scss';

const { Link } = Anchor;

const anchorModuleStyles = `
&_.antd-card-head:!h-[46px]
&_.antd-card-head:!min-h-[46px]
&_.antd-card-head:!px-md
&_.antd-card-head-title:!text-heading
&_.antd-card-head-title:!text-root
&_.antd-card-head-title:!font-medium
&_.antd-link-anchor-link-title:!font-medium
&_.antd-link-anchor-link-title:!text-sm
&_.antd-link-anchor-link-title:!mb-xs
&_.antd-link-anchor-link-title:!text-textTertiary
&_.antd-link-anchor-link-title:!break-words
&_.antd-link-anchor-link-title:!whitespace-normal
&>_.antd-card-body:!px-0
&>_.antd-card-body:!py-md
&_.antd-card-extra_.antd-btn-hover:!bg-formAddonBg
&_.antd-card-extra_.antd-btn-hover:!rounded-sm
`;

export interface AnchorGroup {
  title: string;
  anchorItems: AnchorLinkItemProps[];
}

interface Props {
  anchorKeys?: string[];
  anchorGroups?: AnchorGroup[];
  containerId: string;
  defaultVisible?: boolean;
  defaultPin?: boolean;
  anchorModuleClassName?: string;
  onExpandChange?: (isCollapse: boolean) => void;
  getCurrentAnchor?: (activeLink: string) => string;
}

/**
 * @param anchorKeys 单个anchor场景
 * @param anchorGroups 多个anchor组场景 与anchorKeys选择一个使用  目前combined journey的proposal entry使用了anchorGroups
 *  anchor组时滚动页面等不反应在active ink
 * @param containerId
 * @param onExpandChange
 * @param getCurrentAnchor
 * @param defaultVisible 默认是否可见
 * @param defaultPin 默认是否pin在右上角
 */
// anchorKey有可能是3af8938d93__SALES_CHANNEL这样的数据，则取后面的部分作为国际化的key值
export const getSectionTitleI18nKey = (anchorKey: string) => {
  const titleKey = anchorKey?.includes(AnchorConnector)
    ? anchorKey?.split?.(AnchorConnector)?.[1]
    : anchorKey;
  return titleKey;
};

export const AnchorModule: FC<Props> = ({
  anchorKeys,
  anchorGroups,
  containerId,
  defaultVisible,
  defaultPin,
  onExpandChange,
  getCurrentAnchor,
  anchorModuleClassName,
}) => {
  const { t } = useTranslation(['uw', 'common']);
  const [showAnchorMenu, setShowAnchorMenu] = useState(defaultVisible ?? false);
  const [hasPin, setHasPin] = useState(defaultPin);
  const isTravelObjectType = useAtomValue(isTravelObjectTypeAtom);

  useEffect(() => {
    onExpandChange?.(showAnchorMenu);
  }, [showAnchorMenu, onExpandChange]);

  // 递归函数用来处理每个 title
  const processTitles = items =>
    items?.map(item => ({
      ...item,
      href: item?.disable ? undefined : item?.href,
      title: <TextEllipsisDetect text={item?.title} line={2} />,
      children: processTitles(item?.children ?? []),
    }));

  return (
    <div
      className={clx(
        '!fixed top-[6rem] right-0 !z-50',
        styles.fixMenu,
        anchorModuleStyles,
        anchorModuleClassName
      )}
      onMouseEnter={() => setShowAnchorMenu(true)}
      onMouseLeave={() => setShowAnchorMenu(false)}
    >
      <div
        className={clx(
          'w-[42px] h-9 flex justify-center items-center box-border bg-white border border-r-0 border-solid border-[var(--collapse-header-bg)] rounded-l-full rounded-r-none shadow-[0px_4px_10px_rgba(16, 42, 67, 0.08)] cursor-pointer',
          !hasPin && !showAnchorMenu ? 'visible' : 'hidden'
        )}
      >
        <CommonIconAction
          icon={<Icon type="drag" />}
          tooltipTitle={t('Open Menu')}
          tooltipPlacement="left"
        />
      </div>
      <Card
        className={clx(
          'w-[200px] rounded-b-lg rounded-l-xl shadow-[0px_4px_24px_0px_#102A431F] bg-white',
          hasPin || showAnchorMenu ? 'visible' : 'hidden'
        )}
        title={i18nFn('Quick Menu')}
        extra={
          <CommonIconAction
            icon={<PinIcon className={clx(hasPin && 'rotate-45')} />}
            tooltipTitle={hasPin ? t('Cancel pin') : t('Pin')}
            tooltipPlacement="topRight"
            onClick={() => setHasPin(!hasPin)}
            className={clx(
              '!w-6 !h-6 !p-0 flex items-center justify-center',
              styles.anchorPin
            )}
          />
        }
      >
        {!isEmpty(anchorKeys) && (
          <div className="px-md">
            <Anchor
              affix={false}
              getContainer={() =>
                document.getElementById(containerId) as AnchorContainer
              }
            >
              {anchorKeys
                ?.filter(
                  item =>
                    item !== MasterAgreementPageSections.VEHICLE_LIST_UPLOAD
                )
                .map(item => {
                  //TODO: [GIS-125489]Travel产品后端api section没给返回TravelObject模块，暂时hardcode
                  const sectionTitle =
                    item.includes(
                      ProposalEntryPageSections.MULTIPLE_OBJECT_INFO
                    ) && isTravelObjectType
                      ? t(ProposalEntryPageSections.TRAVEL_OBJECT_INFO)
                      : t(getSectionTitleI18nKey(item));

                  return (
                    <Link
                      key={`#${item}`}
                      href={`#${item}`}
                      title={
                        <TextEllipsisDetect text={sectionTitle} line={2} />
                      }
                    />
                  );
                })}
              {anchorKeys
                ?.filter(
                  item =>
                    item === MasterAgreementPageSections.VEHICLE_LIST_UPLOAD
                )
                .map(item => (
                  <Link
                    key={`#${item}`}
                    href={`#${item}`}
                    title={
                      <TextEllipsisDetect
                        text={getSectionTitleI18nKey(item)}
                        line={2}
                      />
                    }
                  />
                ))}
            </Anchor>
          </div>
        )}

        {!isEmpty(anchorGroups) && (
          <div className="grid gap-2.5 px-md overflow-scroll max-h-[calc(100vh-15rem)]">
            {anchorGroups?.map(anchorGroup => (
              <div key={anchorGroup.title}>
                {anchorGroup.title && (
                  <div className="font-bold text-xs leading-4 pb-xs text-[var(--heading-color)]">
                    {anchorGroup.title}
                  </div>
                )}
                <Anchor
                  affix={false}
                  getCurrentAnchor={getCurrentAnchor}
                  getContainer={() =>
                    document.getElementById(containerId) as AnchorContainer
                  }
                  items={processTitles(anchorGroup.anchorItems)}
                />
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};
