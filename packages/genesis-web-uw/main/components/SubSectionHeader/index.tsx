import { FC } from 'react';

import clsx from 'clsx';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

interface Props {
  className?: string;
}

const classNames = `[&.nagrand-title-before::before]:!bg-[var(--border-color-base)]
[&.nagrand-body-text]:text-[var(--heading-color)]
[&.nagrand-body-text]:!mb-xs`;
export const SubSectionHeader: FC<Props> = ({ className, children }) => (
  <SimpleSectionHeader
    weight={700}
    type="h5"
    className={clsx(classNames, className)}
  >
    {children}
  </SimpleSectionHeader>
);
