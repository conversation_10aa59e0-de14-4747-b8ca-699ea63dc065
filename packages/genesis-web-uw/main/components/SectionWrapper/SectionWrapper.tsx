import { FC } from 'react';

import clsx from 'clsx';

import { SimpleSectionHeader, titleLevelEnum } from '@zhongan/nagrand-ui';
import { TextType, WeightType } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';

import styles from './SectionWrapper.module.scss';

interface Props {
  id: string;
  title?: string;
  type?: TextType;
  hideDivider?: boolean;
  className?: string;
  weight?: WeightType;
  titleLevel?: titleLevelEnum;
}

export const SectionWrapper: FC<Props> = ({
  id,
  title,
  hideDivider = false,
  children,
  type = 'h5',
  className,
  weight = 500,
  titleLevel = titleLevelEnum.First,
}) => (
  <section className={clsx(styles.sectionWrapper, className)} id={id}>
    {title && (
      <SimpleSectionHeader
        weight={weight}
        type={type}
        className={titleLevel === titleLevelEnum.Second ? 'mb-2' : 'mb-4'}
        titleLevel={titleLevel}
      >
        {title}
      </SimpleSectionHeader>
    )}
    <section className={styles.formWrapper}>{children}</section>
    {!hideDivider && <Divider category="body" className="!my-6" />}
  </section>
);
