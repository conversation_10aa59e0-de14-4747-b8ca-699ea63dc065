import { SimpleSectionHeader } from '@zhongan/nagrand-ui';
import { FC, ReactNode } from 'react';

import { Divider } from 'genesis-web-component/lib/components';

import styles from './SectionWrapper.module.scss';

interface Props {
  id: string;
  title: ReactNode;
  extraButtons?: ReactNode;
}

export const SectionWrapperWithBg: FC<Props> = ({
  id,
  title,
  children,
  extraButtons,
}) => (
  <>
    <section className={styles.sectionWrapperWithBg} id={id}>
      <SimpleSectionHeader
        weight={500}
        type={'h5'}
        style={{
          marginBottom: styles.gapMd,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
        className="h-big before:!h-[22px] before:!top-[5px]"
      >
        {title}
        {extraButtons}
      </SimpleSectionHeader>
      <section className={styles.formWrapper}>{children}</section>
    </section>
    <Divider category="body" />
  </>
);
