import { FC } from 'react';

import cls from 'clsx';

import { SectionContainer } from '@zhongan/nagrand-ui';

export const SectionContainerRender: FC<{ readPretty: boolean }> = ({
  readPretty = false,
  children,
}) => (
  <SectionContainer
    className={cls(
      readPretty
        ? '[&.nagrand-section-container]:!p-0 [&.nagrand-section-container]:!pb-xs'
        : '!bg-formAddonBg !pt-md !pb-lg !mb-xs'
    )}
  >
    {children}
  </SectionContainer>
);
