import React from 'react';
import { connect, mapProps, mapReadPretty } from '@formily/react';

import { LoadingOutlined } from '@ant-design/icons';

export const withFormily = (WrappedComponent: React.FC<any>) => () =>
  connect(
    WrappedComponent,
    mapProps((props, field) => ({
      ...props,
      suffix: (
        <span>
          {field?.loading || field?.validating ? (
            <LoadingOutlined />
          ) : (
            props.suffix
          )}
        </span>
      ),
    })),
    mapReadPretty(WrappedComponent, { readPretty: true })
  );
