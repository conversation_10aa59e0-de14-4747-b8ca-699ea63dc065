import { message } from 'antd';
import {
  BizModuleType,
  BusinessNumberConfigurationService,
} from 'genesis-web-service';

import { useAsyncEffect } from 'ahooks';
import { useState, useMemo } from 'react';
import { GenerationRuleEnum } from '@uw/interface/enum.interface';

export const useBizModule = (moduleEnum?: GenerationRuleEnum) => {
  const [modulePermissionData, setModulePermissionData] =
    useState<BizModuleType>({
      editModuleList: [],
      viewModuleList: [],
    });

  useAsyncEffect(async () => {
    try {
      const res = await BusinessNumberConfigurationService.getBiznoModule();
      if (res) {
        setModulePermissionData(res);
      }
    } catch (error) {
      message.error(error?.toString?.());
    }
  }, []);

  const hasEditAuth = useMemo(
    () => modulePermissionData.editModuleList?.includes(moduleEnum ?? ''),
    [modulePermissionData, moduleEnum]
  );

  return { modulePermissionData, hasEditAuth };
};
