import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance } from 'antd';
import { ColumnType } from 'antd/es/table';

import { cloneDeep, find, sortBy } from 'lodash-es';

import {
  ColumnEditingType,
  FieldType as NagrandFieldType,
} from '@zhongan/nagrand-ui';

import { DatePickerSupport24 } from 'genesis-web-component/lib/components';
import {
  BizDict,
  DataTypeEnum,
  DataTypeFieldTypeMap,
  FieldType,
  PolicyInsuredObjectType,
} from 'genesis-web-component/lib/interface/enum.interface';
import {
  handleDictOptions,
  handleFactorValue,
} from 'genesis-web-component/lib/util/handleDictOptions';
import {
  handleFactorDisplayName,
  handleFactorMessage,
} from 'genesis-web-component/lib/util/objectDisplayValue';
import { FactorsType, PackageEnumItem } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ApplicantElementsSortList } from '@uw/constant';
import { IsExtension } from '@uw/interface/enum.interface';
import {
  handleApplicationElementsFormRules,
  handleRequired,
} from '@uw/util/handleApplicationElements';

import { getDefaultSelectValue } from '../pages/proposal-entry/CombinedJourneyProposalEntryDetail/utils/getDefaultSelectValue';

export interface FactorFieldType extends Partial<FactorsType> {
  placeholder: string;
  col: number;
  label: string;
  type: FieldType;
  options: BizDict[] | undefined;
  maxLength?: number;
  valProps?: string;
  format?: string;
}

export const CascadeChildParentAppElementMap: Record<string, string> = {
  vehicleUsage: 'vehicleGroup',
  vehicleSubGroup: 'vehicleGroup',
  E_ColorCode: 'vehicleColor',
};

export const isCascadeAppElementChild = (
  factorCode?: string,
  elementMap = CascadeChildParentAppElementMap
) => Object.keys(elementMap).includes(factorCode ?? '');

export const getAppElementsOptions = (
  mapBizDicts: BizDict[],
  factor: FactorsType | FactorFieldType,
  parentValue?: string,
  parentBizDicts?: BizDict[]
): BizDict[] => {
  let bizDicts: BizDict[] = cloneDeep(
    mapBizDicts?.[0] ? mapBizDicts : (factor.options ?? [])
  );
  // 只有配置了vehicleSubGroup 才会返回children层级
  if (factor.factorCode === 'vehicleSubGroup') {
    return (
      parentBizDicts
        ?.find(bizDict => bizDict.dictValue === parentValue)
        ?.children?.find(item => item.schemaFieldCode === factor.factorCode)
        ?.items?.map(item => ({
          ...item,
          enumItemValue: item.value,
          dictValue: item.value,
          dictValueName: item.displayName,
          enumItemName: item.enumItemName ?? item?.dictValue ?? item.value,
          itemName: item.displayName,
        })) || []
    );
  }

  if (factor.factorValues && factor.factorValues.length > 0) {
    const values = factor.factorValues.split(',');
    bizDicts = mapBizDicts
      ?.filter(bizDict =>
        values.includes(
          factor.factorCode !== 'nationality'
            ? (bizDict.dictValue as string)
            : (bizDict.value as string)
        )
      )
      ?.map(item => ({
        ...item,
        enumItemName: item.enumItemName ?? item?.dictValue ?? item.value,
      }));
  }

  let dicts = factor.bizDictKey ? bizDicts : [];
  if (parentValue && Array.isArray(parentBizDicts)) {
    dicts = (
      parentBizDicts.find(bizDict => bizDict.dictValue === parentValue)
        ?.child as unknown as PackageEnumItem
    )?.items?.map(item => ({
      ...item,
      enumItemValue: item.value,
      dictValue: item.value,
      dictValueName: item.displayName,
      enumItemName: item.enumItemName ?? item?.dictValue ?? item.value,
      itemName: item.displayName,
    })) as BizDict[];
  }
  return sortBy(dicts, ApplicantElementsSortList) ?? [];
};

export const transferAppElementToField = (
  enumMap: Record<string, BizDict[]>,
  factor: FactorsType,
  objectSubCategory?: PolicyInsuredObjectType
): FactorFieldType => {
  const mapBizDicts = objectSubCategory
    ? enumMap?.[factor?.factorCode]?.filter(
        item => objectSubCategory === item.objectSubCategory
      )
    : enumMap?.[factor?.factorCode];

  const dicts = getAppElementsOptions(mapBizDicts, factor);
  return {
    ...factor,
    col: 8,
    placeholder: handleFactorMessage(
      factor.dataType as unknown as DataTypeEnum
    ),
    label: handleFactorDisplayName(factor),
    type: DataTypeFieldTypeMap[+factor.dataType],
    options: factor.bizDictKey ? dicts : [],
    format: dateFormatInstance?.dateFormat,
    valProps:
      factor.bizDictKey && mapBizDicts?.[0]?.enumItemName
        ? undefined
        : 'dictValue',
  };
};

export const useAppElementsToFields = (
  factors: Record<string, FactorsType[]>,
  enumMap: Record<string, BizDict[]>,
  objectSubCategory?: PolicyInsuredObjectType
): {
  fields: Record<string, FactorFieldType[]>;
} => {
  const fields = useMemo(
    () =>
      Object.keys(factors).reduce(
        (cur, next) => ({
          ...cur,
          [next]: factors[next].map(factor =>
            transferAppElementToField(enumMap, factor, objectSubCategory)
          ),
        }),
        {}
      ),
    [factors, enumMap, objectSubCategory]
  );

  return { fields };
};

export const useAppElementsToColumns = (
  factors: FactorsType[],
  enumMap: Record<string, BizDict[]>
): {
  tableColumns: ColumnType<Record<string, unknown>>[];
} => {
  const [t] = useTranslation(['uw', 'common']);

  const tableColumns = useMemo<ColumnType<Record<string, unknown>>[]>(
    () =>
      factors.map<ColumnType<Record<string, unknown>>>(factor => ({
        title: handleFactorDisplayName(factor),
        dataIndex: factor.factorCode,
        ellipsis: true,
        render: text =>
          handleFactorValue(text, factor as FactorsType, enumMap) ?? t('--'),
      })),
    [factors, enumMap]
  );

  return { tableColumns };
};

/**
 * @description 将投保要素字段转换为genesis-web-component editable table识别的columns
 */
export const useAppElementsToTableColumns = (
  tableFields: FactorFieldType[],
  enumMap: Record<string, BizDict[]>
): [ColumnType<Record<string, unknown>>[]] => {
  const [t] = useTranslation(['uw', 'common']);

  const tableColumns = useMemo<ColumnType<Record<string, unknown>>[]>(
    () =>
      tableFields.map<ColumnType<Record<string, unknown>>>(field => ({
        title: field.label,
        dataIndex: field.factorCode,
        editable: true,
        fieldProps: {
          required: handleRequired(field),
          placeholder: handleFactorMessage(
            field.dataType as unknown as DataTypeEnum
          ),
          type: DataTypeFieldTypeMap[+(field.dataType ?? 0)],
          format:
            field.dataType && +field.dataType === DataTypeEnum.DATETIME
              ? 'YYYY-MM-DD HH:mm:ss'
              : '',

          mode:
            +(field.dataType ?? 0) === DataTypeEnum.MULTISELECT
              ? 'multiple'
              : '',
          extraProps: {
            options: handleDictOptions(enumMap?.[field.bizDictKey!]),
          },
        },
        render: text =>
          handleFactorValue(text, field as FactorsType, enumMap) ?? t('--'),
      })),
    [tableFields, enumMap]
  );

  return [tableColumns];
};

// 目前需求只需要用到以下基本类型数据，后续若需要其他类型，在此扩充
const dataTypeFieldTypeMap: Partial<Record<DataTypeEnum, NagrandFieldType>> = {
  [DataTypeEnum.INPUT]: NagrandFieldType.Input,
  [DataTypeEnum.SELECT]: NagrandFieldType.Select,
  [DataTypeEnum.NUMBER]: NagrandFieldType.InputNumber,
  [DataTypeEnum.MULTISELECT]: NagrandFieldType.Select,
  [DataTypeEnum.CHECKBOX]: NagrandFieldType.Checkbox,
};

/**
 * @see https://jira.zaouter.com/browse/GIS-125174
 * @see https://jira.zaouter.com/browse/GIS-124462
 */
const fieldConstraints = {
  positiveInteger: {
    fields: ['numberOfInsured', 'averageAge'],
    props: {
      min: 1,
      step: 1,
      precision: 0,
    },
  },
  percentage: {
    fields: ['malePercentage'],
    props: {
      min: 0,
      max: 100,
      step: 1,
      precision: 0,
    },
  },
  positiveDecimal: {
    fields: ['averageSalary'],
    props: {
      min: 0.01,
      step: 0.01,
      precision: 2,
    },
  },
};

const getCustomizedColumnProps = (field: FactorFieldType) => {
  if (!field?.factorCode) return {};

  const factorCode = field.factorCode;

  for (const [, config] of Object.entries(fieldConstraints)) {
    if (config.fields.includes(factorCode)) {
      return config.props;
    }
  }
  // 没有匹配到任何规则，返回空对象
  return {};
};

/**
 * @description 将单个投保要素字段转换为nagrand editable table识别的column
 * @param factor
 * @param enumsMap 投保要素字段枚举
 * @param options
 */
export const formatAppElement2TableColumn = <T extends object>(
  factor: FactorsType,
  enumsMap: Record<string, BizDict[]>,
  options: {
    industryCode?: string;
    isNeedExtensionPath?: boolean;
    disabledFieldKeys?: string[];
  }
): ColumnEditingType<T> => {
  const { industryCode, isNeedExtensionPath, disabledFieldKeys = [] } = options;
  const field = transferAppElementToField(enumsMap, factor);

  const factorDataType = +factor.dataType;
  const disabled = disabledFieldKeys.includes(field?.factorCode as string);
  return {
    ...field,
    title: field.label,
    dataIndex:
      isNeedExtensionPath && field.isExtension === IsExtension.Yes
        ? ['extensions', field?.factorCode]
        : field?.factorCode,
    editable: true,
    formItemProps: {
      rules: handleApplicationElementsFormRules(factor),
    },
    // 加如下判断逻辑是为了统一解决date渲染时页面报错问题，报错原因是由于value不是moment类型，DatePickerSupport24组件中转成moment即可
    fieldProps: [DataTypeEnum.DATE, DataTypeEnum.DATETIME].includes(
      factorDataType
    )
      ? {
          key: field?.factorCode,
          type: NagrandFieldType.Customized,
          render: (
            <DatePickerSupport24
              showTime={factorDataType === DataTypeEnum.DATETIME}
              disabled={disabled}
            />
          ),
        }
      : {
          type: dataTypeFieldTypeMap[factorDataType as DataTypeEnum],
          extraProps: {
            disabled,
            options: field.bizDictKey
              ? handleDictOptions(
                  field.factorCode === 'occupationCode'
                    ? find(enumsMap?.industryCode, ['dictValue', industryCode])
                        ?.childList
                    : enumsMap?.[field.factorCode]
                )
              : [],
            fieldNames: {
              label: 'dictValueName',
              value: 'enumItemName',
            },
            optionFilterProp: 'dictValueName',
            defaultValue: getDefaultSelectValue(
              field,
              field.bizDictKey
                ? handleDictOptions(
                    field.factorCode === 'occupationCode'
                      ? find(enumsMap?.industryCode, [
                          'dictValue',
                          industryCode,
                        ])?.childList
                      : enumsMap?.[field.factorCode]
                  )
                : []
            ),
            ...getCustomizedColumnProps(field),
          },
        },
    render: (text, record) =>
      handleFactorValue(
        text,
        factor as FactorsType,
        enumsMap,
        undefined,
        record as Record<string, unknown>
      ),
  } as ColumnEditingType<T>;
};

interface ExternalParams<T> {
  isNeedNoColumn?: boolean;
  isNeedExtensionPath?: boolean;
  disabledFieldKeys?: string[];
  form?: FormInstance<T>;
}

/**
 * @description 将投保要素字段转换为nagrand editable table识别的columns
 * @param factors 投保要素字段列表
 * @param enumsMap 投保要素字段枚举
 * @param isNeedNoColumn 第一列是否显示No.
 * @param isNeedExtensionPath 路径是否拼extensions
 */
export const useAppElement2TableColumns = <T extends object>(
  factors: FactorsType[],
  enumsMap: Record<string, BizDict[]>,
  externalParams: ExternalParams<T> = {}
): ColumnEditingType<T>[] => {
  const {
    isNeedNoColumn = true,
    isNeedExtensionPath,
    disabledFieldKeys,
    form,
  } = externalParams;
  const [t] = useTranslation(['uw', 'common']);
  const industryCode = Form?.useWatch('industryCode', form);

  const columns = useMemo(() => {
    return factors?.map(factor =>
      formatAppElement2TableColumn(factor, enumsMap, {
        industryCode,
        isNeedExtensionPath,
        disabledFieldKeys,
      })
    ) as ColumnEditingType<T>[];
  }, [disabledFieldKeys, enumsMap, factors, industryCode, isNeedExtensionPath]);
  if (isNeedNoColumn) {
    return [
      {
        dataIndex: 'index',
        editable: false,
        title: t('No.'),
        render: (txt, record, index) => index + 1,
      },
      ...columns,
    ];
  }

  return columns;
};
