import { useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import { ProposalCombinedService } from 'genesis-web-service';
import { useUserPermission } from '@uw/pages/proposal-entry/ProposalTaskPool/hooks/useBindAuth';
import { useSearchActions } from '@uw/pages/proposal-entry/ProposalTaskPool/hooks/useSearchActions';
import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';

export const useNavigateToProposalEntry = () => {
  const { hasView, hasEdit } = useUserPermission();
  const { handleViewOrEdit } = useSearchActions();
  const [issuanceNo, setIssuanceNo] = useState<string>();

  useAsyncEffect(async () => {
    try {
      if (issuanceNo) {
        const proposalInfo =
          await ProposalCombinedService.queryIsCombinedJourney(issuanceNo);
        if (Object.keys(proposalInfo).length > 0) {
          if (hasEdit && hasView) {
            handleViewOrEdit({
              mode: Mode.Edit,
              temporaryId: proposalInfo?.templateId,
              isCombinedJourney: proposalInfo?.isCombinedJourney,
              pageCode: proposalInfo?.pageCode,
              goodsId: proposalInfo?.goodsId,
            });
          }
        }
      }
    } catch (e) {
      messagePopup((e as Error).message, 'error');
    }
  }, [issuanceNo]);
  return { setIssuanceNo };
};
