import { useMemo } from 'react';

import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';

import { useBizDictByKeys } from '@uw/biz-dict/hooks';

type DictValue = string | number;
// 获取customer详情
export const useObjectCategory = (): {
  objectCategoryMap: Record<string, BizDict> | undefined;
  objectSubCategoryMap: Record<string, BizDict> | undefined;
  objectTypeMap: Record<string, BizDict> | undefined;
  objectComponentMap: Record<string, BizDict> | undefined;
} => {
  const [
    objectCategoryDicts,
    objectSubCategoryDicts,
    objectType,
    objectComponent,
  ] = useBizDictByKeys([
    'objectCategory',
    'objectSubCategory',
    'objectType',
    'objectComponent',
  ]);

  const objectCategoryMap = useMemo(
    () =>
      objectCategoryDicts?.reduce<Record<string, BizDict>>(
        (cur, next) => ({
          ...(cur ?? {}),
          [next.dictValue as DictValue]: next as BizDict,
        }),
        {}
      ),
    [objectSubCategoryDicts]
  );

  const objectSubCategoryMap = useMemo(
    () =>
      objectSubCategoryDicts?.reduce<Record<string, BizDict>>(
        (cur, next) => ({
          ...(cur ?? {}),
          [next.dictValue as DictValue]: next as BizDict,
        }),
        {}
      ),
    [objectSubCategoryDicts]
  );

  const objectTypeMap = useMemo(
    () =>
      objectType?.reduce<Record<string, BizDict>>(
        (cur, next) => ({
          ...(cur ?? {}),
          [next.dictValue as DictValue]: next as BizDict,
        }),
        {}
      ),
    [objectType]
  );

  const objectComponentMap = useMemo(
    () =>
      objectComponent?.reduce<Record<string, BizDict>>(
        (cur, next) => ({
          ...(cur ?? {}),
          [next.dictValue as DictValue]: next as BizDict,
        }),
        {}
      ),
    [objectComponent]
  );

  return {
    objectCategoryMap,
    objectSubCategoryMap,
    objectTypeMap,
    objectComponentMap,
  };
};
