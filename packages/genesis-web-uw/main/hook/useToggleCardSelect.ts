import { useCallback } from 'react';

import { useSetAtom } from 'jotai';
import { intersection, size } from 'lodash-es';

import { QueryProposalTaskPoolRecord } from 'genesis-web-service';

import { SetState } from '@uw/interface/common.interface';
import { relationAtom } from '@uw/pages/proposal-entry/ProposalTaskPool/atom';

export const useToggleCardSelect = (
  selectedTaskIds: (string | number)[],
  currentCardList: (string | number)[],
  setSelectAll: SetState<boolean>,
  setSelectedTaskIds: SetState<(string | number)[]>
) => {
  const setRelation = useSetAtom(relationAtom);

  const toggleCardSelection = useCallback(
    (record: QueryProposalTaskPoolRecord) => {
      const { taskNo: cardId } = record;
      let updateId = [...selectedTaskIds];
      if (selectedTaskIds?.includes(cardId)) {
        updateId = updateId.filter(id => id !== cardId);
        setRelation(prev => prev.filter(item => item.taskNo !== cardId));
      } else {
        updateId = [...updateId, cardId];
        setRelation(prev => [...prev, record]);
      }
      setSelectedTaskIds(updateId);
      // 当前页选中的card数量
      const currentId = intersection(updateId, currentCardList);
      setSelectAll(size(currentCardList) === size(currentId));
    },
    [
      currentCardList,
      selectedTaskIds,
      setRelation,
      setSelectAll,
      setSelectedTaskIds,
    ]
  );
  return { toggleCardSelection };
};
