import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Mode, SubCategoryEnum } from '@uw/interface/enum.interface';

export const IsNormalMasterPolicy = (subCategory: string) =>
  !!subCategory && subCategory !== SubCategoryEnum.MOTOR_FLEET;

export const useMasterPolicyRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleClick = useCallback<
    ({
      subCategory,
      masterPolicyNo,
      mode,
      isQuery,
    }: {
      subCategory: string;
      masterPolicyNo: string;
      mode: Mode;
      isQuery: boolean;
    }) => void
  >(
    ({ subCategory, masterPolicyNo, mode, isQuery }) => {
      navigate(
        IsNormalMasterPolicy(subCategory)
          ? `/uw/master-policy/${mode}/${masterPolicyNo}`
          : `/uw/master-agreement/${mode}/${masterPolicyNo}${
              isQuery ? '/query' : ''
            }`,
        { state: location.state }
      );
    },
    [navigate, location.state]
  );

  return {
    handleClick,
  };
};
