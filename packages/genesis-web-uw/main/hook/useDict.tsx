import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { selectEnums } from '@uw/redux/selector';

/**
 * @deprecated 使用packages/genesis-web-uw/main/biz-dict/hooks.ts下的useDict
 * @param key
 * @param keyName
 */
export const useDict = (
  key: string | string[],
  keyName?: 'dictValue' | 'enumItemName' | 'enumItemValue'
): Record<string, string>[] => {
  const enums = useSelector(selectEnums);

  const enumsMap = useMemo(() => {
    if (typeof key === 'string') {
      return [
        enums?.[key]?.reduce(
          (cur, dict) => {
            const curDictMap = { ...cur };
            curDictMap[dict?.[keyName ?? ('enumItemName' as string)]] =
              dict.itemName;
            return curDictMap;
          },
          {} as Record<string, string>
        ),
      ];
    }
    return key.map(enumKey =>
      enums?.[enumKey]?.reduce(
        (cur, dict) => {
          const curDictMap = { ...cur };
          curDictMap[dict?.[keyName ?? ('enumItemName' as string)]] =
            dict.itemName;
          return curDictMap;
        },
        {} as Record<string, string>
      )
    );
  }, [key, enums, keyName]);

  return [...enumsMap];
};
