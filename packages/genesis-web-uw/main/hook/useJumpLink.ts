/* eslint-disable no-case-declarations */
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  ApiResponseError,
  POSQueryDetailSourceEnums,
} from 'genesis-web-service';

import {
  Detail,
  MicroAppPrefix,
  Proposal,
  QueryPolicy,
} from '@uw/router/constants';
import { messagePopup } from '@uw/util/messagePopup';
import { getClaimAppNo } from '@uw/util/request';

export enum JumpModule {
  UW_WORK_SHEET = 'UW_WORK_SHEET',
  POS_QUERY = 'POS_QUERY',
  CLAIM_QUERY = 'CLAIM_QUERY',
  PROPOSAL_QUERY = 'PROPOSAL_QUERY',
  POLICY_QUERY = 'POLICY_QUERY',
}

export type UseJumpLink = (
  hasAuth: boolean,
  jumpModule: JumpModule,
  openInCurrentTab?: boolean
) => (
  no: string,
  fromCommonQuery?: boolean,
  extraInfo?: {
    jumpToModule?: JumpModule;
    version?: string;
    posTransType?: string;
  }
) => void;

export const useJumpLink: UseJumpLink = (
  hasAuth,
  jumpModule,
  openInCurrentTab = false
) => {
  const navigate = useNavigate();

  const openInBrowser = useCallback(
    (url: string, extraParams?: unknown) => {
      if (openInCurrentTab) {
        navigate(
          url,
          extraParams as { replace?: boolean | undefined; state?: unknown }
        );
      } else {
        window.open(url, '_blank');
      }
    },
    [navigate, openInCurrentTab]
  );

  const handleLink = useCallback(
    async (
      no: string,
      fromCommonQuery?: boolean,
      extraInfo?: {
        jumpToModule?: JumpModule;
        version?: string;
        posTransType?: string;
      }
    ) => {
      const to = extraInfo?.jumpToModule || jumpModule;
      if (!hasAuth) {
        return;
      }
      try {
        switch (to) {
          case JumpModule.CLAIM_QUERY:
            const res = await getClaimAppNo(no);
            openInBrowser(`/claim-v2/task-detail/${res}/${no}`);
            break;
          case JumpModule.POS_QUERY:
            const params = new URLSearchParams();
            params.append('caseNo', no);
            params.append('sourceType', POSQueryDetailSourceEnums.PolicyQuery);
            if (extraInfo?.posTransType) {
              params.append('posTransType', extraInfo.posTransType);
            }
            openInBrowser(`/pos/pos-query-detail?${params.toString()}`);
            break;
          case JumpModule.PROPOSAL_QUERY:
            if (fromCommonQuery) {
              openInBrowser(
                `/${MicroAppPrefix}/${Proposal}/${Detail}/${no}/?module=ProposalQuery&isTemp=`
              );
            } else {
              openInBrowser(`/${MicroAppPrefix}/${Proposal}/${Detail}/${no}`);
            }
            break;
          case JumpModule.POLICY_QUERY:
            const version = extraInfo?.version;
            if (version) {
              openInBrowser(
                `/${MicroAppPrefix}/${QueryPolicy}/${Detail}?policyNo=${no}&version=${version}`
              );
            } else {
              openInBrowser(
                `/${MicroAppPrefix}/${QueryPolicy}/${Detail}?policyNo=${no}`
              );
            }

            break;
          default:
            break;
        }
      } catch (error) {
        messagePopup(
          (error as ApiResponseError)?.message?.toString?.(),
          'error'
        );
      }
    },
    [hasAuth, jumpModule, openInBrowser]
  );

  return handleLink;
};
