import { useSelector } from 'react-redux';

import { DefaultOptionType } from 'antd/lib/select';

import {
  BizDict,
  bigBizDictKeys,
} from 'genesis-web-component/lib/interface/enum.interface';
import { BizDictItem, MetadataService } from 'genesis-web-service';
import { getBigKeyI18n } from 'genesis-web-shared/lib/util/getBigKeyI18n';

import { PolicyTypeNameEnum } from '@uw/interface/enum.interface';

import { selectEnums } from '../redux/selector';

/**
 * @deprecated 换用packages/genesis-web-uw/main/biz-dict/hooks.ts useBizDictByKey
 */
export const useBizDict = (key: string): BizDict[] | undefined => {
  const enums: Record<string, BizDict[]> = useSelector(selectEnums);
  return enums?.[key];
};

/**
 * @deprecated 换用packages/genesis-web-uw/main/biz-dict/hooks.ts useBizDictOptions
 */
export const useBizDictAntd = (
  key: string,
  labelProp = 'itemName',
  valueProp = 'enumItemName'
): DefaultOptionType[] | undefined => {
  const options = useBizDict(key);
  return options?.map(option => ({
    label: option?.[labelProp],
    value: option?.[valueProp],
    ...option,
    id: option.id?.toString(),
  }));
};

export const usePolicyScenarioType = (): BizDict[] =>
  (useBizDict('policyScenarioType') ?? []).filter(type =>
    [
      PolicyTypeNameEnum.NORMAL,
      PolicyTypeNameEnum.BY_EVENT,
      PolicyTypeNameEnum.GROUP_POLICY,
    ].includes(type.enumItemName as PolicyTypeNameEnum)
  );

/**
 * @deprecated 换用packages/genesis-web-uw/main/biz-dict/hooks.ts getDictLabel
 */
// 原本写法
export const dictMap = (
  curEnums: BizDict[] | undefined,
  keyName: string,
  renderKey = 'dictValueName'
): string | undefined => {
  if (!keyName) return undefined;

  return (
    curEnums?.find(
      enumItem =>
        enumItem?.enumItemName?.toString() === keyName?.toString() ||
        enumItem?.dictValue?.toString() === keyName?.toString()
    )?.[renderKey] ?? keyName
  );
};

// 已抽出到 web-component
export const bigBizDictMap = async (key: string, enumItemNames: string[]) => {
  let bizDicts: BizDict[] = [];
  if (bigBizDictKeys.includes(key)) {
    try {
      bizDicts = (await getBigKeyI18n(key, enumItemNames)) as BizDict[];
    } catch (e) {
      // error
    }
  }
  return bizDicts;
};

export const occupationBizDictI18n = async (enumItemNames: string[]) => {
  const occupationI18nMap = await MetadataService.queryI18nListBizDictRequest({
    entryDictKey: 'occupation',
    targetLevelNo: 1,
    targetLevelValues: Array.isArray(enumItemNames)
      ? enumItemNames
      : [enumItemNames],
  });
  return occupationI18nMap?.[0]?.valueName;
};

export interface AddressMapType {
  name: string;
  [index: string]: string | AddressMapType;
}
export const getAddressNode = (addressList: BizDictItem[]): AddressMapType =>
  addressList?.reduce(
    (cur, address) => ({
      ...cur,
      [address.dictValue as string]: {
        name: (address.dictValueName as string) ?? address.dictValue,
      },
    }),
    {} as AddressMapType
  );

interface AddressType {
  address11?: string;
  address12?: string;
  address13?: string;
  address14?: string;
  address15?: string;
  address21?: string;
  address22?: string;
  address23?: string;
  address24?: string;
  address25?: string;
}
const addressOrLocal = (
  flag: number,
  addressMap: AddressMapType,
  addressObj?: AddressType & Record<string, string | undefined>
): (AddressType & Record<string, string | undefined>) | undefined => {
  const addressKeyList = [
    `address${flag}1`,
    `address${flag}2`,
    `address${flag}3`,
    `address${flag}4`,
    `address${flag}5`,
  ];
  const addressTemp = {
    [`address${flag}1`]: addressObj?.[`address${flag}1`],
    [`address${flag}2`]: addressObj?.[`address${flag}2`],
    [`address${flag}3`]: addressObj?.[`address${flag}3`],
    [`address${flag}4`]: addressObj?.[`address${flag}4`],
    [`address${flag}5`]: addressObj?.[`address${flag}5`],
  };
  Object.entries(addressObj ?? {}).forEach(([addressKey, addressValue]) => {
    if (addressKeyList.includes(addressKey)) {
      addressTemp[addressKey] =
        (addressMap[addressValue as string] as AddressMapType)?.name ??
        addressValue;
    }
  });
  return addressTemp;
};

export const getAllAddressValues = (
  addressObj: AddressType & Record<string, string | undefined>
): string[] =>
  Object.entries(addressObj ?? {})
    ?.filter(([addressKey]) =>
      [
        'address11',
        'address12',
        'address13',
        'address14',
        'address15',
        'address21',
        'address22',
        'address23',
        'address24',
        'address25',
      ].includes(addressKey)
    )
    ?.map(([, addressvalue]) => addressvalue)
    ?.filter(address => !!address) as string[];
export const getAddressObject = async (
  isDropdown: boolean,
  addressObj?: AddressType & Record<string, string | undefined>
): Promise<AddressType | undefined> => {
  let addresses: Record<string, string | undefined> | undefined;
  if (isDropdown && addressObj) {
    const addressValues = getAllAddressValues(addressObj);
    const res = await getBigKeyI18n('address1', addressValues);
    const addressMap = getAddressNode(res ?? []);
    const addressTemp1 = addressOrLocal(1, addressMap, addressObj);
    const addressTemp2 = addressOrLocal(2, addressMap, addressObj);
    addresses = {
      ...addressObj,
      ...addressTemp1,
      ...addressTemp2,
    };
  } else {
    addresses = undefined;
  }

  return addresses;
};

export interface AccountType {
  bankCode: string;
  bankBranchCode: string;
}
export const getAccountObject = async (
  isDropdown: boolean,
  accountObj?: AccountType & Record<string, string>
) => {
  if (isDropdown && accountObj) {
    const { bankCode, bankBranchCode } = accountObj;
    const bankDicts = await getBigKeyI18n('bank', [bankCode, bankBranchCode]);
    const bankI18nObj = bankDicts?.reduce(
      (cur, next) => ({
        ...cur,
        [`${next.dictKey}Code`]: next.dictValue,
        [`${next.dictKey}Name`]: next?.dictValueName ?? next.dictValue,
      }),
      {}
    );
    return {
      ...accountObj,
      ...bankI18nObj,
    };
  }
  return accountObj;
};
