import { Dispatch, ReactNode, SetStateAction, useCallback } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import Icon from '@ant-design/icons';

import { AxiosResponse } from 'axios';

import { Modal } from '@zhongan/nagrand-ui';

import {
  InferObjectRecord,
  ProposalService,
  QuotationService,
} from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { Error, Success, Warning } from '@uw/assets/new-icons';
import { QueryTypeEnum } from '@uw/interface/enum.interface';
import { combinedJourneyTaskPoolUrl } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/constant';
import urls from '@uw/router/constants';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';

const { confirm, success, warning, error } = Modal;

type PageInfo = InferObjectRecord<string>;
type ModuleInfo = Record<
  'quotation' | 'proposalEntry' | 'combinedJourney',
  PageInfo
>;

export const moduleInfo: ModuleInfo = {
  quotation: {
    backUrl: urls.QuotationTaskPool,
  },
  proposalEntry: {
    backUrl: urls.ProposalTaskPool,
  },
  combinedJourney: {
    backUrl: combinedJourneyTaskPoolUrl,
  },
};

export const useWorkflowAction = (
  pageInfo: PageInfo,
  getQuotationInfo?: () => void
) => {
  const { proposalNo } = useParams();
  const location = useLocation();

  const proposalNoDiv = useCallback<(issuanceNo?: string) => ReactNode>(
    issuanceNo => (
      <div style={{ fontWeight: 'bold' }}>
        {i18nFn('Proposal No. {{proposalNo}}', ['uw', 'common'], {
          proposalNo: issuanceNo ?? proposalNo,
        })}
      </div>
    ),
    [proposalNo]
  );
  const navigate = useNavigate();

  // Data Entry阶段，回到task pool
  const handleBack = useCallback(() => {
    confirm({
      width: 500,
      title: i18nFn('Confirm'),
      content: i18nFn(
        'The updated information will not be saved, are you sure to back?'
      ),
      okText: i18nFn('Yes'),
      cancelText: i18nFn('No'),
      onOk: () => {
        navigate(pageInfo.backUrl, {
          state: location.state,
        });
      },
    });
  }, [navigate, pageInfo.backUrl, location.state]);

  // Data entry阶段， submit成功，自动核保通过，进入下一个Quote send阶段
  const handleAutoSuccess = useCallback(() => {
    confirm({
      title: i18nFn('Success'),
      content: (
        <>
          <div>{i18nFn('The proposal has passed automated underwriting.')}</div>
          {proposalNoDiv()}
        </>
      ),
      okText: i18nFn('Go to Quote Sent'),
      cancelText: i18nFn('Back To task pool'),
      onOk: () => {
        navigate(`/uw/quotation/detail/view/${proposalNo}`);
        if (getQuotationInfo) {
          getQuotationInfo();
        }
      },
      onCancel: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [getQuotationInfo, navigate, pageInfo.backUrl, proposalNo, proposalNoDiv]);

  // Data entry阶段， submit成功，需要人工核保
  const handleSuccessGoToManualUw = useCallback(
    (content?: ReactNode) => {
      success({
        title: i18nFn('Success'),
        content: content ?? (
          <>
            <div>
              {i18nFn(
                'The proposal has been submitted to manual underwriting.'
              )}
            </div>
            {proposalNoDiv()}
          </>
        ),
        okText: i18nFn('Back To task pool'),
        okType: 'default',
        onOk: () => {
          navigate(pageInfo.backUrl);
        },
      });
    },
    [navigate, pageInfo.backUrl, proposalNoDiv]
  );

  // Issue Policy阶段，需要下载e-polciy保单
  const handleIssuePolicyAndDownload = useCallback(
    (content?: ReactNode, issuanceNo?: string[]) => {
      confirm({
        title: i18nFn('Success'),
        icon: <Icon component={Success} />,
        content: content ?? (
          <>
            <div>
              {i18nFn(
                'The proposal has been submitted to manual underwriting.'
              )}
            </div>
            {proposalNoDiv()}
          </>
        ),
        okText: i18nFn('Download E-Policy'),
        onOk: () =>
          ProposalService.downloadEPolicy(
            (issuanceNo as string[]).filter(number => !!number),
            QueryTypeEnum.POLICY
          )
            .then((response: AxiosResponse<Blob>) =>
              downloadFile(response).then(() => {
                messagePopup(i18nFn('Download successfully'), 'success');
              })
            )
            // 不管失败还是成功需要弹窗保持，而不是关闭
            .catch((err: { message: string }) => {
              if (err?.message === 'BIZ_ISS_100287') {
                messagePopup(
                  i18nFn(
                    'The file is still being generated. Please wait a moment.'
                  ),
                  'error'
                );
              }
            })
            .finally(() => Promise.reject()),
        cancelText: i18nFn('Back To task pool'),
        onCancel: () => {
          navigate(pageInfo.backUrl);
        },
      });
    },
    [navigate, pageInfo.backUrl, proposalNoDiv]
  );

  // 下发修改
  const handleToRequote = useCallback(() => {
    warning({
      width: 500,
      title: i18nFn('Result'),
      content: (
        <>
          <div>{i18nFn('The proposal has failed automated underwriting.')}</div>
          {proposalNoDiv()}
        </>
      ),
      cancelText: i18nFn('Back To task pool'),
      okText: i18nFn('Back to Modify'),
      onCancel: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [navigate, pageInfo.backUrl, proposalNoDiv]);

  // 自动核保直接Decline
  const handleAutoUwFailed = useCallback(() => {
    error({
      title: i18nFn('Failed'),
      content: (
        <>
          <div>
            {i18nFn(
              'The proposal has failed automated underwriting and was declined by the system.'
            )}
          </div>
          {proposalNoDiv()}
        </>
      ),
      okText: i18nFn('Back To task pool'),
      okType: 'default',
      onOk: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [navigate, pageInfo.backUrl, proposalNoDiv]);

  //  Quote Send / Quote Bound: Lapse Result
  const handleLapseTask = useCallback(() => {
    success({
      title: i18nFn('Result'),
      content: (
        <>
          <div>{i18nFn('The proposal has Lapsed.')}</div>
          {proposalNoDiv}
        </>
      ),
      okText: i18nFn('Close'),
      okType: 'default',
      onOk: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [navigate, pageInfo.backUrl, proposalNoDiv]);

  // Quote Send / Quote Bound: Confirm Lapse
  const lapseTask = useCallback<
    (setButtonLoading: Dispatch<SetStateAction<boolean>>) => unknown
  >(
    async setButtonLoading => {
      try {
        await QuotationService.lapseTask({
          issuanceNo: proposalNo,
        });
        handleLapseTask();
      } catch (e: unknown) {
        messagePopup((e as Error)?.toString(), 'error');
      }
      setButtonLoading(false);
    },
    [handleLapseTask, proposalNo]
  );

  const confirmLapse = useCallback<
    (
      buttonLoading: boolean,
      setButtonLoading: Dispatch<SetStateAction<boolean>>
    ) => unknown
  >(
    async (buttonLoading, setButtonLoading) =>
      confirm({
        title: i18nFn('Confirm'),
        icon: <Icon component={Warning} />,
        content: (
          <>
            <div>
              {i18nFn('The proposal will lapse, are you sure to continue？')}
            </div>
          </>
        ),
        okButtonProps: {
          loading: buttonLoading,
        },
        okText: i18nFn('Yes'),
        cancelText: i18nFn('No'),
        onOk: async () => {
          setButtonLoading(true);
          await lapseTask(setButtonLoading);
          return true;
        },
      }),
    [lapseTask]
  );

  // Quote Send / Quote Bound: Requote
  const handleRequoteTask = useCallback(() => {
    confirm({
      title: i18nFn('Requote'),
      icon: <Icon component={Success} />,
      content: (
        <>
          <div>{i18nFn('The proposal has been sent to New Quote.')}</div>
          {proposalNoDiv}
        </>
      ),
      okText: i18nFn('Go to New Quote'),
      cancelText: i18nFn('Back To task pool'),
      onOk: () => {
        navigate(`/uw/quotation/detail/edit/${proposalNo}`);
        if (getQuotationInfo) {
          getQuotationInfo();
        }
      },
      onCancel: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [getQuotationInfo, navigate, pageInfo.backUrl, proposalNo, proposalNoDiv]);

  // Quote Bound
  const handleQuoteBound = useCallback(() => {
    confirm({
      width: 500,
      title: i18nFn('Result'),
      icon: <Icon component={Success} />,
      content: (
        <>
          <div>{i18nFn('The proposal has been sent to Quote Bound.')}</div>
          {proposalNoDiv}
        </>
      ),
      okText: i18nFn('Go to Quote Bound'),
      cancelText: i18nFn('Back To task pool'),
      onOk: () => {
        navigate(`/uw/quotation/detail/edit/${proposalNo}`);
        if (getQuotationInfo) {
          getQuotationInfo();
        }
      },
      onCancel: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [getQuotationInfo, navigate, pageInfo.backUrl, proposalNo, proposalNoDiv]);

  // Issue Policy Success
  const handleIssuePolicy = useCallback(
    (issuanceNo: string, policyNo?: string) => {
      confirm({
        width: 500,
        title: i18nFn('Success'),
        icon: <Icon component={Success} />,
        content: (
          <>
            <div>{i18nFn('The policy has been issued successfully.')}</div>
            <div style={{ fontWeight: 'bold' }}>
              {i18nFn('Policy No. {{policyNo}}', ['uw', 'common'], {
                policyNo: policyNo ?? '',
              })}
            </div>
            {proposalNoDiv}
          </>
        ),
        okText: i18nFn('Generate Policy Schedule'),
        cancelText: i18nFn('Back To task pool'),
        onOk: () => {
          window.open(`/api/policy/quote/issuances/${issuanceNo}/print`);
        },
        onCancel: () => {
          navigate(pageInfo.backUrl);
        },
      });
    },
    [navigate, pageInfo.backUrl, proposalNoDiv]
  );

  // Issue Policy To Pay
  const handlePayPremium = useCallback(() => {
    warning({
      title: i18nFn('Result'),
      icon: <Icon component={Warning} />,
      content: (
        <>
          <div>
            {i18nFn(
              'The customer should pay the premium before issuing the policy .'
            )}
          </div>
          {proposalNoDiv}
          <div style={{ fontWeight: 'bold' }}>
            {i18nFn('Debit Note No. No. {{debitNoteNo}}', ['uw', 'common'], {
              debitNoteNo: '--',
            })}
          </div>
        </>
      ),
      okText: i18nFn('Close'),
      okType: 'default',
      onOk: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [navigate, pageInfo.backUrl, proposalNoDiv]);

  // Issue Policy Fail
  const handleIssuePolicyFail = useCallback(() => {
    confirm({
      width: 500,
      title: i18nFn('Failed'),
      icon: <Icon component={Error} />,
      content: (
        <>
          <div>
            {i18nFn(
              'The proposal has failed automated underwriting due to the changes.'
            )}
          </div>
          {proposalNoDiv()}
        </>
      ),
      okText: i18nFn('Back to Modify'),
      cancelText: i18nFn('Back To task pool'),
      onCancel: () => {
        navigate(pageInfo.backUrl);
      },
    });
  }, [navigate, proposalNoDiv, pageInfo]);

  // Issue Policy Fail for Combine Journey Proposal Entry
  const handleCombineIssuePolicyFail = useCallback<
    (issuanceNo?: string) => void
  >(
    issuanceNo => {
      error({
        width: 500,
        title: i18nFn('Failed'),
        content: (
          <>
            <div>
              {i18nFn(
                'The proposal has failed automated underwriting due to the changes.'
              )}
            </div>
            {proposalNoDiv(issuanceNo)}
          </>
        ),
        okText: i18nFn('Back To task pool'),
        okType: 'default',
        onOk: () => {
          navigate(pageInfo.backUrl);
        },
      });
    },
    [navigate, proposalNoDiv, pageInfo]
  );

  return {
    handleBack,
    handleAutoSuccess,
    handleSuccessGoToManualUw,
    handleToRequote,
    handleAutoUwFailed,
    handleRequoteTask,
    handleQuoteBound,
    confirmLapse,
    handleLapseTask,
    handleIssuePolicy,
    handlePayPremium,
    handleIssuePolicyFail,
    handleIssuePolicyAndDownload,
    handleCombineIssuePolicyFail,
  };
};
