import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Mode } from 'genesis-web-component/lib/interface/enum.interface';
import { Permission } from 'genesis-web-shared/lib/redux';

import { selectGrantedPermissionMap } from '../redux/selector';

export const usePermission = (
  id: string | string[]
): Permission | Permission[] | undefined => {
  const permissionMap: Record<string, Permission> = useSelector(
    selectGrantedPermissionMap
  );
  return useMemo(() => {
    if (Array.isArray(id)) {
      return id.map(permissionId => permissionMap[permissionId]);
    }
    return permissionMap[id as string];
  }, [id, permissionMap]);
};

export const useUwTaskPermission = (mode: Mode, source?: string): boolean => {
  const hasEditAuth = !!usePermission('uw.manual.task.edit');
  return hasEditAuth && mode !== Mode.View && !source;
};
