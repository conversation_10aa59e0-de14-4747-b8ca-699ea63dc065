import { DesTableType, policyCampaignDetailType } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { selectEnums } from '@uw/redux/selector';

import { dictMap } from './useBizDict';

export const usePurchase = (
  policyCampaignDetailList?: policyCampaignDetailType
): DesTableType[] | [] => {
  const [t] = useTranslation(['uw', 'common']);
  const {
    l10n: { dateFormat },
  } = useL10n();
  const enums: Record<string, BizDict[]> = useSelector(selectEnums);

  const dataSource = [
    {
      label: t('Gift Delivery Method'),
      value: dictMap(
        enums?.giftDeliveryMethod,
        policyCampaignDetailList?.giftDeliveryMethod
      ),
    },
    {
      label: t('Gift Delivery Time'),
      value: dictMap(
        enums?.giftDeliveryTime,
        policyCampaignDetailList?.giftDeliveryPoint
      ),
    },
    {
      label: t('Gift Delivery Dimension'),
      value: dictMap(
        enums?.giftDeliveryDimension,
        policyCampaignDetailList?.giftDeliveryDimension
      ),
    },
    {
      label: t('Delivery Status'),
      value: dictMap(
        enums?.policyGiftCampaignDeliveryStatus,
        policyCampaignDetailList?.giftDeliveryStatus
      ),
    },
    {
      label: t('Actual Delivery Time'),
      value: policyCampaignDetailList?.deliveryTime
        ? dateFormat.getDateTimeString(policyCampaignDetailList?.deliveryTime)
        : t('--'),
    },
    {
      label: t('Gift Code'),
      value: policyCampaignDetailList?.giftCode,
    },
  ];

  return dataSource;
};
