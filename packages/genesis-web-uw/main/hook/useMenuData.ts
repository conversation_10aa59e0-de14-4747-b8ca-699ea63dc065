import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';

import { isEmpty, values } from 'lodash-es';

import { ExtendSecondMenuType } from 'genesis-web-component/lib/components/SecondMenu/extend-second-menu.interface';
import { handleFilterPermission } from 'genesis-web-component/lib/util/handleFilterPermission';
import { Permission } from 'genesis-web-service';

import { selectGrantedPermissionMap } from '@uw/redux/selector';
import { i18nFn } from '@uw/util/i18nFn';

export const menus = [
  {
    title: i18nFn('New Business & Renewal Configuration'),
    key: 'nbConfiguration',
    parentKey: null,
    level: 1,
  },
  {
    title: i18nFn('Quotation Configuration'),
    key: 'quotationConfiguration',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['nb.configuration.quotation.view'],
    path: 'quotation-configuration',
  },
  {
    title: i18nFn('Proposal Configuration'),
    key: 'proposalConfiguration',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['nb.configuration.proposal.view'],
    path: 'proposal-configuration',
    type: 'anchorParentNode',
    id: 'proposalContent',
  },
  {
    title: i18nFn('Proposal Flow Setting'),
    key: 'proposalFlowSetting',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'proposalFlowSetting',
  },
  {
    title: i18nFn('Verification Check'),
    key: 'verificationCheck',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'verificationCheck',
  },
  {
    title: i18nFn('Underwriting Check'),
    key: 'underwritingCheck',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'underwritingCheck',
  },
  {
    title: i18nFn('Compliance Check'),
    key: 'complianceCheck',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'complianceCheck',
  },
  {
    title: i18nFn('Policy Issuance Rules'),
    key: 'policyIssuanceRules',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'policyIssuanceRules',
  },
  {
    title: i18nFn('Opt-In Rules'),
    key: 'optInRules',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'optInRules',
  },
  {
    title: i18nFn('Proposal Withdraw Rule'),
    key: 'proposalWithdrawRule',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'proposalWithdrawRule',
  },
  {
    title: i18nFn('Proposal Reminder Rule'),
    key: 'proposalReminderRule',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'proposalReminderRule',
  },
  {
    title: i18nFn('Policy Sign Off Rule'),
    key: 'policySignOffRule',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'policySignOffRule',
  },
  {
    title: i18nFn('Policy Number Generation Rule'),
    key: 'policyNumberGenerationRule',
    parentKey: 'proposalConfiguration',
    level: 3,
    permissions: ['nb.configuration.proposal.view'],
    path: 'policyNumberGenerationRule',
  },
  {
    title: i18nFn('Policy Configuration'),
    key: 'policyConfiguration',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['policy.configuration.view'],
    path: 'policy-configuration',
  },
  {
    title: i18nFn('Random Check Configuration'),
    key: 'randomCheckConfiguration',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['nb.configuration.proposal.view'],
    path: 'random-check-configuration',
  },
  {
    title: i18nFn('Attachment Configuration'),
    key: 'attachmentConfiguration',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['nb.configuration.proposal.view'],
    path: 'attachment-configuration',
  },
  {
    title: i18nFn('Notice Reminder'),
    key: 'premiumNoticeReminder',
    parentKey: 'nbConfiguration',
    level: 2,
    permissions: ['configuration.renew.notice.view'],
    path: 'premium-notice-reminder',
  },
];

export const useSecondMenuMap = (
  nbConfigMenu: ExtendSecondMenuType.ExtendMenuDataType[]
): {
  defaultKey?: string;
  defaultMenuMap: Record<string, ExtendSecondMenuType.MenuItemProps>;
  handleMenuClick: (info: { key: string; keyPath: string[] }) => void;
} => {
  const navigate = useNavigate();
  const params = useParams();
  const curPath = params['*'];
  const permissionMap: Record<string, Permission> = useSelector(
    selectGrantedPermissionMap
  );

  const handleMenuClick = ({ keyPath }: { keyPath: string[] }) => {
    if (curPath !== keyPath[0]) {
      navigate(keyPath[0]);
    }
  };

  const showMenuData = useMemo(() => {
    const result = nbConfigMenu.filter(
      config =>
        !config.permissions ||
        !!handleFilterPermission(permissionMap, config.permissions?.[0])
    );
    return result;
  }, [nbConfigMenu, permissionMap]);

  const defaultMenuMap = useMemo(
    () =>
      showMenuData?.reduce<Record<string, ExtendSecondMenuType.MenuItemProps>>(
        (cur, next) => ({
          ...cur,
          [next.key]: next,
        }),
        {}
      ),
    [showMenuData]
  );

  useEffect(() => {
    if (isEmpty(curPath)) {
      navigate(values(defaultMenuMap ?? {})[1]?.path as string);
    }
  }, [defaultMenuMap]);

  const defaultKey = useMemo(
    () => values(defaultMenuMap ?? {}).find(menu => menu.path === curPath)?.key,
    [defaultMenuMap]
  );

  return {
    defaultKey,
    defaultMenuMap,
    handleMenuClick,
  };
};
