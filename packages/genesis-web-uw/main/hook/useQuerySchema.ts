import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { useAsyncEffect } from 'ahooks';
import { useAtomValue } from 'jotai';
import { size } from 'lodash-es';
import useSWRMutation from 'swr/mutation';

import { PageTemplateService, PageTemplateTypes } from 'genesis-web-service';

import { SchemaType } from '@uw/interface/common.interface';
import {
  extraSectionConditionsAtom,
  queryDetailMetaDataAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { generateSchema } from '@uw/util/schemaUtil';

const domain = PageTemplateTypes.ModuleType.ISSUANCE;

type Factors = PageTemplateTypes.FactorRecord[];
type PageCode = PageTemplateTypes.PageType;
type Schemas = Record<string, SchemaType>;
export type SectionRender = Omit<PageTemplateTypes.RenderResponse, 'render'> & {
  render: SchemaType;
};
type SectionRenderMap = Record<string, SectionRender>;
export type Sections = PageTemplateTypes.SectionDetailResponse[];
export type SubSections = PageTemplateTypes.SubSectionDetailResponse[];
export type BothSections = (
  | PageTemplateTypes.SectionDetailResponse
  | PageTemplateTypes.SubSectionDetailResponse
)[];
type UseQueryBothSections = (props: {
  basicSection?: string;
  basicPageCode: PageCode;
  schemas?: Schemas;
  factors?: Factors;
  needGetInitSection?: boolean;
}) => {
  loading: boolean;
  setLoading: Dispatch<SetStateAction<boolean>>;
  sectionRenderMap: SectionRenderMap;
  sections: BothSections;
  querySection: (props: {
    factors?: Factors;
    subSection?: string;
    pageCode: PageCode;
    section?: string;
  }) => Promise<Record<string, Sections>>;
};
type UseQuerySections = (
  pageCode: PageCode,
  schemas?: Schemas
) => {
  loading: boolean;
  sectionRenderMap: SectionRenderMap;
  sections: Sections;
  querySection: (factors: Factors) => void;
};
type UseQuerySubSections = (
  section: string,
  pageCode: PageCode,
  schemas?: Schemas,
  needGetInitSection?: boolean
) => {
  subSections: SubSections;
  sectionRenderMap: SectionRenderMap;
  querySubSections: (factors: Factors) => void;
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const queryRenders = async (
  sectionCodes: string[],
  pageCode: PageCode
) => {
  const renders = await PageTemplateService.queryRender({
    pageCode,
    domain,
    sections: sectionCodes,
  });

  return renders?.reduce(
    (cur, next: PageTemplateTypes.RenderResponse) => ({
      ...cur,
      [next.section]: {
        ...next,
        // 将后端返回的 string 类型转换为 SchemaType
        render: generateSchema(next?.render),
      },
    }),
    {}
  ) as SectionRenderMap;
};

export interface QuerySectionsTypes {
  module?: PageTemplateTypes.ModuleType;
  pageCode: PageCode;
  needGetInitSection?: boolean;
  factors?: Factors;
  sectionCode?: string;
}

export const useQuerySectionsSwr = ({ pageCode }: QuerySectionsTypes) =>
  useSWRMutation(
    pageCode && `/external/template/pages`,
    (url, { arg: params }: { arg: QuerySectionsTypes }) =>
      pageCode &&
      PageTemplateService.querySections(
        params.module,
        params.pageCode,
        params.factors ?? []
      )?.then(res => res?.pageSections)
  );

export const useQuerySubSectionsSwr = ({
  pageCode,
  sectionCode,
}: QuerySectionsTypes) => {
  return useSWRMutation(
    pageCode && sectionCode && `/external/template/pages/sections`,
    (_url, { arg: params }: { arg: QuerySectionsTypes }) =>
      pageCode &&
      sectionCode &&
      PageTemplateService.querySubSections(
        params.module,
        params.pageCode,
        params.sectionCode,
        params.factors ?? []
      )
  );
};

// section / subSection 都可
export const useQueryBothSections: UseQueryBothSections = ({
  basicPageCode,
  basicSection,
  factors: basicFactors = [],
  schemas,
  needGetInitSection = true,
}) => {
  const queryDetailMetaData = useAtomValue(queryDetailMetaDataAtom);
  const extraSectionConditions = useAtomValue(extraSectionConditionsAtom);
  const initSchema = useMemo<Record<string, SectionRender>>(
    () =>
      Object.entries(schemas ?? {})?.reduce(
        (prev, [key, value]) => ({
          ...prev,
          [key]: {
            render: value,
          },
        }),
        {}
      ),
    [schemas]
  );

  const [sectionRenderMap, setSectionRenderMap] =
    useState<SectionRenderMap>(initSchema);

  const [allSections, setAllSections] = useState<BothSections>([]);

  const [loading, setLoading] = useState(false);
  const {
    data: pageSections,
    isMutating: isLoading,
    trigger: fetchSections,
  } = useQuerySectionsSwr({
    pageCode: basicPageCode,
  });
  const {
    data: subSections,
    isMutating: isSubLoading,
    trigger: fetchSubSections,
  } = useQuerySubSectionsSwr({
    pageCode: basicPageCode,
    sectionCode: basicSection,
  });

  const updateSectionAndRender = useCallback<
    (sections: BothSections | undefined, sectionKey: PageCode) => void
  >(
    async (sections = [], sectionKey) => {
      // 当有 sectionType 并且 section?.existTenant 字段为 true 那么就需要去后端请求 render
      const allSectionCodes = sections
        ?.filter(item => item.sectionTypes?.[0] && item?.existTenant)
        ?.map(item => item.section);
      setAllSections(sections);

      // if the sectionCode array is empty, needn't request
      let renderMap = initSchema;
      if (size(allSectionCodes) > 0) {
        renderMap = await queryRenders(allSectionCodes, sectionKey);
      }
      // 将后端请求的结果与本地的 schema 进行混合
      const rendersMap = Object.entries({
        ...initSchema,
        ...renderMap,
      })?.reduce((prev, [key, value]) => {
        // 如果是后端返回的 schema 那么就替换本地的 schema
        if (renderMap?.[key]) {
          return {
            ...prev,
            [key]: renderMap?.[key],
          };
        }
        // 否则兜底展示本地的 schema
        return {
          ...prev,
          [key]: value,
        };
      }, {});
      setSectionRenderMap(rendersMap);
      return rendersMap;
    },
    [initSchema]
  );

  useEffect(() => {
    setLoading(isLoading || isSubLoading);
  }, [isLoading, isSubLoading]);

  useEffect(() => {
    updateSectionAndRender(
      basicSection ? subSections : pageSections,
      basicSection ?? basicPageCode
    );
  }, [
    basicPageCode,
    basicSection,
    pageSections,
    subSections,
    updateSectionAndRender,
  ]);

  // 将 factors 作为 params 传递， effect 中使用会死循环
  const querySection = useCallback(
    async ({ factors = [], pageCode, sectionCode }: QuerySectionsTypes) => {
      const sectionKey = pageCode || basicPageCode;
      const subSectionKey = sectionCode || basicSection;
      // 嵌入场景下可能需要一些 condition 以满足不同场景下的区块差异,比如 pos
      const mergedFactors = [...factors, ...extraSectionConditions];
      // readPretty 模式下需要增加 condition,这里统一处理下
      if (queryDetailMetaData?.module) {
        mergedFactors.push({
          factor: 'module',
          factorValue: queryDetailMetaData?.module,
        });
      }
      try {
        if (!subSectionKey && sectionKey) {
          return await fetchSections({
            module: domain,
            pageCode: sectionKey,
            factors: mergedFactors,
          });
        }
        if (sectionKey && subSectionKey) {
          return await fetchSubSections({
            module: domain,
            pageCode: sectionKey,
            factors: mergedFactors,
            sectionCode: subSectionKey,
          });
        }
      } catch (e) {
        setLoading(false); // 异常场景下保留 setLoading(false)
      }
      return [];
    },
    [
      basicPageCode,
      basicSection,
      queryDetailMetaData?.module,
      extraSectionConditions,
    ]
  );

  useAsyncEffect(async () => {
    if (needGetInitSection) {
      querySection({
        module: domain,
        pageCode: basicPageCode,
        sectionCode: basicSection,
        factors: basicFactors,
      });
    }
  }, []);

  return {
    loading,
    setLoading,
    sectionRenderMap,
    sections: allSections,
    querySection,
    updateSectionAndRender,
  };
};

export const useQuerySections: UseQuerySections = (pageCode, schemas = {}) => {
  const initSchema = useMemo<Record<string, SectionRender>>(
    () =>
      Object.entries(schemas ?? {})?.reduce(
        (prev, [key, value]) => ({
          ...prev,
          [key]: {
            render: value,
          },
        }),
        {}
      ),
    [schemas]
  );

  const [sectionRenderMap, setSectionRenderMap] =
    useState<SectionRenderMap>(initSchema);

  const [allSections, setAllSections] = useState<
    PageTemplateTypes.SectionDetailResponse[]
  >([]);

  const [loading, setLoading] = useState(false);

  // 将 factors 作为 params 传递， effect 中使用会死循环
  const querySection = useCallback(
    async (factors: Factors = []) => {
      let sections: PageTemplateTypes.SectionDetailResponse[] = [];
      setLoading(true);
      try {
        sections = (
          await PageTemplateService.querySections(domain, pageCode, factors)
        )?.pageSections;
      } catch (e) {
        // e
      }
      setLoading(false);
      // 当有 sectionType 并且 section?.existTenant 字段为 true 那么就需要去后端请求 render
      const allSectionCodes = sections
        ?.filter(section => section.sectionTypes?.[0] && section?.existTenant)
        ?.map(section => section.section);

      setAllSections(sections);

      // if the sectionCode array is empty, needn't request
      if (allSectionCodes?.length) {
        const renderMap = await queryRenders(allSectionCodes, pageCode);
        // 将后端请求的结果与本地的 schema 进行混合
        const rendersMap = Object.entries(initSchema)?.reduce(
          (prev, [key, value]) => {
            // 如果是后端返回的 schema 那么就替换本地的 schema
            if (renderMap?.[key]) {
              return {
                ...prev,
                [key]: renderMap?.[key],
              };
            }
            // 否则兜底展示本地的 schema
            return {
              ...prev,
              [key]: {
                render: value,
              },
            };
          },
          {}
        );
        setSectionRenderMap(rendersMap);
      }
    },
    [pageCode, initSchema]
  );

  useEffect(() => {
    querySection();
  }, []);

  return {
    loading,
    sectionRenderMap,
    sections: allSections,
    querySection,
  };
};

export const useQuerySubSections: UseQuerySubSections = (
  section,
  pageCode,
  schemas = {},
  needGetInitSection = true
) => {
  const initSchema = useMemo(
    () =>
      Object.entries(schemas ?? {})?.reduce(
        (prev, [key, value]) => ({
          ...prev,
          [key]: {
            render: value,
          },
        }),
        {}
      ),
    [schemas]
  );

  const [sectionRenderMap, setSectionRenderMap] =
    useState<SectionRenderMap>(initSchema);
  const [subSections, setSubSections] = useState<
    PageTemplateTypes.SubSectionDetailResponse[]
  >([]);

  // 将 factors 作为 params 传递， effect 中使用会死循环
  const querySubSections = useCallback(
    async (factors: Factors = []) => {
      let sections: PageTemplateTypes.SubSectionDetailResponse[] = [];
      sections = await PageTemplateService.querySubSections(
        domain,
        pageCode,
        section,
        factors
      );

      // 当有 sectionType 并且 section?.existTenant 字段为 true 那么就需要去后端请求 render
      const allSectionCodes = sections
        ?.filter(item => item.sectionTypes?.[0] && item?.existTenant)
        ?.map(item => item.section);
      setSubSections(sections);

      // if the sectionCode array is empty, needn't request
      if (allSectionCodes?.length) {
        const renderMap = await queryRenders(allSectionCodes, pageCode);
        // 将后端请求的结果与本地的 schema 进行混合
        const rendersMap = Object.entries(initSchema)?.reduce(
          (prev, [key, value]) => {
            // 如果是后端返回的 schema 那么就替换本地的 schema
            if (renderMap?.[key]) {
              return {
                ...prev,
                [key]: renderMap?.[key],
              };
            }
            // 否则兜底展示本地的 schema
            return {
              ...prev,
              [key]: value,
            };
          },
          {}
        );
        setSectionRenderMap(rendersMap);
      }
    },
    [pageCode, section, initSchema]
  );

  useEffect(() => {
    if (needGetInitSection) {
      querySubSections();
    }
  }, [needGetInitSection]);

  return { sectionRenderMap, subSections, querySubSections };
};
