import { useMemo } from 'react';

import { BizDictConfigListEnum, YesOrNo } from '@uw/interface/enum.interface';

import { useBizDict } from './useBizDict';

export const useShowFullName2 = () => {
  const customerNameGroup = useBizDict('customerNameGroup') ?? [];

  return useMemo(
    () =>
      customerNameGroup.find(
        ({ dictValue }) => BizDictConfigListEnum.CustomerNameGroup === dictValue
      )?.isOptional === YesOrNo.YES,
    [customerNameGroup]
  );
};
