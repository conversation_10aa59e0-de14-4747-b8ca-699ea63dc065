import { useState, useEffect } from 'react';
import { PaginationProps } from 'antd';

const defaultPaginationProps = {
  pageSize: 10,
  current: 1,
  showQuickJumper: true,
  showSizeChanger: true,
};
export const usePagination = (
  initialValues: PaginationProps = defaultPaginationProps
): [PaginationProps, (pagination: PaginationProps) => void] => {
  const [paginationParams, setPaginationParams] = useState<PaginationProps>({
    ...defaultPaginationProps,
    ...initialValues,
  });
  useEffect(() => {
    setPaginationParams(paginationParams);
  }, [
    paginationParams.pageSize,
    paginationParams.current,
    paginationParams.total,
  ]);

  return [paginationParams, setPaginationParams];
};
