import { FactorEnum } from 'genesis-web-component/lib/interface/enum.interface';
import { FactorsType, YesNoType } from 'genesis-web-service';
import { useMemo } from 'react';

import { useApplicationElements } from './useApplicationElements';

export const useGetQuerySchemaDef = (
  packageId: number
): {
  policyFactors: FactorsType[];
  extensionFactors: FactorsType[];
} => {
  // 在其他页面已展示以下字段
  const haveShownFactors = [
    'plan code',
    'package code',
    'sales currency',
    'insure date',
    'policy year',
    'policy status',
    'goods type',
    'proposal confirm date',
    'payment date',
    'policy no',
    'biz type',
    'goods code',
  ];
  const [factors] = useApplicationElements({
    packageId,
    topic: FactorEnum.POLICY,
  });
  const curShownFactors = factors?.filter(
    item => !haveShownFactors.includes(item.factorName)
  );
  const policyFactors = useMemo(
    () =>
      curShownFactors?.filter(factor => factor.isExtension === +YesNoType.No) ??
      [],
    [curShownFactors]
  );
  const extensionFactors = useMemo(
    () =>
      curShownFactors?.filter(
        factor => factor.isExtension === +YesNoType.Yes
      ) ?? [],
    [curShownFactors]
  );

  return {
    policyFactors,
    extensionFactors,
  };
};
