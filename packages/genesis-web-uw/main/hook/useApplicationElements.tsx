import { useCallback, useEffect, useState } from 'react';

import { groupBy, sortBy } from 'lodash-es';

import {
  bankCollections,
  judgeDropDownByBankModel,
} from 'genesis-web-component/lib/components/BankSelect';
import {
  BizDict,
  DataTypeEnum,
  FactorEnum,
  FactorEnumMap,
  IsExtension,
} from 'genesis-web-component/lib/interface/enum.interface';
import { handleEnumArray } from 'genesis-web-component/lib/util/handleEnumArray';
import {
  BFFPackageEnumItem,
  FactorsType,
  ObjectComponentEnum,
  ObjectType,
  QueryApplicationElementsRequestType,
  QueryService,
} from 'genesis-web-service';

import { useBizDictByKey } from '@uw/biz-dict/hooks';
import { ApplicantElementsSortList } from '@uw/constant';

import { useObjectCategory } from './useObjectCategory';

export const getConfiguredFactors = (
  factors: FactorsType[] = [],
  isBankFieldTypeSelect: boolean
) => {
  // 获取所有已配置的该topic下的投保要素
  const originConfiguredFactors = factors?.filter(factor => factor?.configured);
  const configuredFactors = sortBy(
    originConfiguredFactors?.map(factor => {
      let dataType: DataTypeEnum = factor.dataType as DataTypeEnum;
      // bankName ｜ bankCode ｜ bankAddress ｜ bankBranchName ｜ bankBranchCode ｜ bankBranchAddress 额外处理
      if (bankCollections?.includes(factor?.factorCode)) {
        dataType = isBankFieldTypeSelect
          ? DataTypeEnum.SELECT
          : DataTypeEnum.INPUT;
      }
      if (
        factor.isExtension === IsExtension.Yes &&
        factor.dataType === DataTypeEnum.MULTISELECT
      ) {
        dataType = DataTypeEnum.FACTOREXTENSIONMULTISELECT;
      }
      return {
        ...factor,
        // 特殊处理countryCode相关投保要素的bizDictKey
        bizDictKey: ['countryCode', 'registeredPhoneCountryCode'].includes(
          factor.factorCode
        )
          ? 'countryCode'
          : factor.bizDictKey,
        dataType,
      };
    }) ?? [],
    ApplicantElementsSortList
  );
  return configuredFactors;
};

export const getObjectFactors = (
  effectiveFactors: FactorsType[],
  objectCategoryMap: Record<string, BizDict> = {},
  objectSubCategoryMap: Record<string, BizDict> = {}
) => {
  const categorizedFactor = effectiveFactors?.reduce<
    Record<string, Record<string, FactorsType[]>>
  >((cur, next) => {
    const objectCategoryEnum =
      objectCategoryMap[next.objectCategory]?.enumItemName;
    const objectSubCategoryEnum =
      objectSubCategoryMap[next.objectSubCategory]?.enumItemName;
    const curObjectCategory = cur?.[objectCategoryEnum];
    const curObjectSubCategory = curObjectCategory?.[objectSubCategoryEnum];
    return {
      ...cur,
      ...(objectCategoryEnum
        ? {
            [objectCategoryEnum]: {
              ...(curObjectCategory ?? {}),
              [objectSubCategoryEnum]: [...(curObjectSubCategory ?? []), next],
            },
          }
        : {}),
    };
  }, {});
  return categorizedFactor;
};

/**
 * @description for new object structure objectCategory -> objectType, objectSubCategory -> objectComponent
 * @param effectiveFactors
 * @param objectTypeMap
 * @param objectComponentMap
 * @returns
 */
export const getNewObjectFactors = (
  effectiveFactors: FactorsType[],
  objectTypeMap: Record<string, BizDict> = {},
  objectComponentMap: Record<string, BizDict> = {}
) => {
  const categorizedFactor = effectiveFactors?.reduce<
    Record<string, Record<string, FactorsType[]>>
  >((cur, next) => {
    const objectTypeEnum = objectTypeMap[next.objectType!]?.enumItemName;
    const objectComponentEnum =
      objectComponentMap[next.objectComponent!]?.enumItemName;
    const curObjectType = cur?.[objectTypeEnum];
    const curObjectComponent = curObjectType?.[objectComponentEnum];
    return {
      ...cur,
      ...(objectTypeEnum
        ? {
            [objectTypeEnum]: {
              ...(curObjectType ?? {}),
              [objectComponentEnum]: [...(curObjectComponent ?? []), next],
            },
          }
        : {}),
    };
  }, {});
  return categorizedFactor;
};
export const useApplicationElements = (
  params: QueryApplicationElementsRequestType
): [
  FactorsType[],
  Record<string, Record<string, FactorsType[]>> | undefined,
] => {
  const { packageId, goodsId, topic } = params;
  const { objectCategoryMap, objectSubCategoryMap } = useObjectCategory();
  const [effectiveFactors, setFactors] = useState<FactorsType[]>([]);
  const [categorizedFactors, setCategorizedFactors] =
    useState<Record<string, Record<string, FactorsType[]>>>();

  // 获取配置中心的bank相关配置
  const bankModel = useBizDictByKey('bankModel');
  const isBankFieldTypeSelect = judgeDropDownByBankModel(bankModel);

  const queryApplicationElements = useCallback(async () => {
    const { factors } = await QueryService.queryApplicationElements(params);

    setFactors(
      getConfiguredFactors(factors as FactorsType[], isBankFieldTypeSelect)
    );
  }, [params]);

  useEffect(() => {
    if (!packageId && !goodsId) return;
    queryApplicationElements();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [packageId, goodsId]);

  // 对上面获取到的投保要素根据objectCategory & objectSubCategory进行分类
  useEffect(() => {
    if (
      topic === FactorEnum.OBJECT &&
      objectCategoryMap &&
      objectSubCategoryMap
    ) {
      const categorizedFactor = getObjectFactors(
        effectiveFactors,
        objectCategoryMap,
        objectSubCategoryMap
      );

      setCategorizedFactors(categorizedFactor);
    }
  }, [effectiveFactors, objectCategoryMap, objectSubCategoryMap, topic]);

  return [effectiveFactors, categorizedFactors];
};

/**
 * @description 将多标的的elements enum 按照objectType objectComponent schemaFieldCode分组
 * @param enumsData
 * @return 返回数据类型 {
 *   [objectType]: {
 *     [objectComponent]: {
 *       [schemaFieldCode]: 枚举数据
 *     }
 *   }
 * }
 */

export const useMultiObjectCategorizedEnumsMap = (
  enumsData: Record<string, BFFPackageEnumItem[]> | undefined
): Record<
  ObjectType,
  Record<ObjectComponentEnum, Record<string, BizDict[]>>
> => {
  const objectItems = enumsData?.[FactorEnumMap[FactorEnum.OBJECT]];
  const objectTypedItems = groupBy(objectItems, 'objectType');
  const categorizedEnumsMap = {};
  Object.entries(objectTypedItems).forEach(([key, value]) => {
    const groupedByBizKey = {};
    Object.entries(groupBy(value, 'objectComponent'))?.forEach(
      ([keyItem, valueItem]) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        groupedByBizKey[keyItem] = handleEnumArray(
          valueItem ?? [],
          'schemaFieldCode'
        );
      }
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    categorizedEnumsMap[key] = groupedByBizKey;
  });

  return categorizedEnumsMap;
};
