import { BizDict } from 'genesis-web-component/lib/interface/enum.interface';
import { messagePopup } from '@uw/util/messagePopup';
import { getTenantDicts } from '@uw/util/request';
import { useAsyncEffect } from 'ahooks';
import { DefaultOptionType } from 'antd/es/select';
import { useCallback, useState } from 'react';

export const useQueryDicts = (
  dictKeys: string[]
): {
  bizDicts: Record<string, DefaultOptionType[]>;
  dictListMap: Record<string, BizDict[]>;
} => {
  const [bizDicts, setBizDicts] = useState<Record<string, DefaultOptionType[]>>(
    {}
  );
  const [dictListMap, setDictMap] = useState<Record<string, BizDict[]>>({});
  const queryDicts = useCallback(async (bizDictKeys: string[]) => {
    try {
      const dictMap = await getTenantDicts(bizDictKeys);
      setDictMap(dictMap);
      Object.entries(dictMap ?? {}).forEach(([bizDictKey, dicts]) => {
        dictMap[bizDictKey] = dicts.map(dict => {
          const value = Number.isNaN(+(dict.dictValue ?? ''))
            ? dict.dictValue
            : +(dict.dictValue ?? '');
          return {
            ...dict,
            label: dict.dictValueName,
            value:
              !dict.enumItemName ||
              dict.enumItemName === '' ||
              dict.dictKey === 'agreementBasis'
                ? value
                : dict.enumItemName,
          };
        });
      });
      return dictMap as Record<string, DefaultOptionType[]>;
    } catch (e) {
      messagePopup((e as string)?.toString(), 'error');
      return {};
    }
  }, []);

  useAsyncEffect(async () => {
    const optionMap = await queryDicts(dictKeys);
    setBizDicts(optionMap);
  }, []);

  return { bizDicts, dictListMap };
};
