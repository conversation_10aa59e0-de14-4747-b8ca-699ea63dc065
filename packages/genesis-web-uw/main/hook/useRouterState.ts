/**
 * 数据存储在 History 的 State 里面, 页面回退后能取回上一次的数据
 */
import { type Dispatch, type SetStateAction, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  isEmpty,
  isNil,
  isObjectLike,
  isPlainObject,
  mergeWith,
} from 'lodash-es';

const cacheKey = '_search_';

const customizer = (_: unknown, srcValue: unknown) => {
  if (isNil(srcValue)) return null;
  if ((isObjectLike(srcValue) && isEmpty(srcValue)) || Array.isArray(srcValue))
    return srcValue;
};

/**
 * 数据存储在路由的 state 中
 * name 对应上可以跨组件共享
 * @typedef S = undefined
 * @param {S} [initialState] 初始化数据
 * @param {string} [name = '-'] 数据存储区分标识
 * @returns {[S, Dispatch<SetStateAction<S> | Partial<S>>]}
 */
export function useRouterState<S = undefined>(
  initialState?: S,
  name = '-'
): [S, Dispatch<SetStateAction<S> | Partial<S>>] {
  const navigate = useNavigate();
  const location = useLocation();

  const { state, search, pathname } = location;
  const { [cacheKey]: searchData = {} } = (state || {}) as {
    [cacheKey]: Record<string, S>;
  };

  // 采用引用类型保证本次多个实例使用的是一个对象数据, 只在初始化的时候赋值从 History 中读取
  const { current: values } = useRef(searchData);

  if (!state || typeof state !== 'object' || !(cacheKey in state)) {
    // 存储引用数据对象到 state 中
    navigate(pathname, { state: { [cacheKey]: values }, replace: true });
  }

  if (values[name] === undefined && initialState !== undefined) {
    // 赋初始值, 后续调用即可拿到前面的初始值
    values[name] = initialState;
  }

  const handle = (stateValue: SetStateAction<S> | Partial<S>) => {
    let value = stateValue;
    if (typeof stateValue === 'function') {
      value = (stateValue as (prevState: S) => S)(values[name]);
    } else if (isPlainObject(stateValue)) {
      if (isEmpty(stateValue)) {
        value = stateValue;
      } else {
        value = mergeWith({}, values[name], stateValue, customizer);
      }
    }

    values[name] = value as S;
    navigate({ search }, { state: { [cacheKey]: values }, replace: true });
  };
  return [values[name], handle];
}
