import { useCallback, useState } from 'react';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';

export const useCheckboxGroup = (options: CheckboxValueType[]) => {
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);

  const onChange = useCallback(
    (list: CheckboxValueType[]) => {
      setCheckedList(list);
      setIndeterminate(!!list.length && list.length < options.length);
      setCheckAll(list.length === options.length);
    },
    [options]
  );

  const onCheckAllChange = useCallback(
    (e: CheckboxChangeEvent) => {
      setCheckedList(e.target.checked ? options : []);
      setIndeterminate(false);
      setCheckAll(e.target.checked);
    },
    [options]
  );

  return {
    checkedList,
    indeterminate,
    checkAll,
    setCheckAll,
    onChange,
    onCheckAllChange,
  };
};
