import { useBizDict } from './useBizDict';

export const useBizDictChild = (
  parentEnum: string,
  parentsKey?: string,
  childEnum?: string,
  childKey?: string
) => {
  const parentEnums = useBizDict(parentEnum);
  let enumsValue: string | undefined;
  const commonEnums = parentEnums?.find(
    item =>
      item.enumItemName === parentsKey?.toString() ||
      item.dictValue === parentsKey?.toString()
  );

  if (childEnum) {
    enumsValue =
      commonEnums?.childList?.find(
        item => item?.dictValue === childKey && item?.dictKey === childEnum
      )?.dictValueName ?? childKey;
  } else {
    enumsValue = commonEnums?.dictValueName ?? parentsKey;
  }
  return enumsValue;
};
