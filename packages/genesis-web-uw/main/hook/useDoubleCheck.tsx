import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Modal } from '@zhongan/nagrand-ui';

import { PolicyCommonService } from 'genesis-web-service';

import { SaveMode, UwDecisionEnum } from '@uw/interface/enum.interface';
import { WithdrawForm } from '@uw/pages/proposal-entry/ProposalEntryDetail/hooks/useWithdrawProposal';
import { messagePopup } from '@uw/util/messagePopup';

interface IUseDoubleCheck {
  (
    type: string,
    issuanceNo: string,
    onOK: (params?: WithdrawForm) => void,
    onCancel: () => void
  ): {
    handleDoubleCheck: (params?: WithdrawForm) => void;
  };
}

export const useDoubleCheck: IUseDoubleCheck = (
  type,
  issuanceNo,
  onOK,
  onCancel
) => {
  const [t] = useTranslation(['uw', 'common']);
  const handleDoubleCheck = useCallback(
    async (params?: WithdrawForm) => {
      let relatedPolices: string[] = [];
      try {
        relatedPolices =
          await PolicyCommonService.getRelatedPolices(issuanceNo);
      } catch (e) {
        messagePopup((e as Error).message, 'error');
      }
      if (relatedPolices && relatedPolices.length > 0) {
        let content = '';
        if (type === SaveMode.WITHDRAW) {
          content = t(
            'The issuance of the proposal {{relatedPolices}} depends on this proposal. If this proposal is withdrawn, the proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.',
            { relatedPolices: relatedPolices.join(',') }
          );
        } else if (type === UwDecisionEnum.DECLINE) {
          content = t(
            'The issuance of proposal {{relatedPolices} depends on proposal {{issuanceNo}}. If the decline of this underwriting task results in the withdrawal of proposal {{issuanceNo}}, proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.',
            { relatedPolices: relatedPolices.join(','), issuanceNo }
          );
        }
        Modal.confirm({
          title: t('Confirm'),
          content,
          onOk: () => {
            onOK(params);
          },
          onCancel: () => {
            onCancel();
          },
        });
      } else {
        onOK();
      }
    },
    [issuanceNo, t, onCancel, onOK]
  );

  return { handleDoubleCheck };
};
