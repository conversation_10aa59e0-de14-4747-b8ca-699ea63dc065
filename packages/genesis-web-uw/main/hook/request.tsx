import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { PaginationProps } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';

import { useSetState } from 'ahooks';
import { AxiosResponse } from 'axios';
import { useSetAtom } from 'jotai';
import { isEmpty, uniqBy } from 'lodash-es';
import useSWR from 'swr';

import { message } from '@zhongan/nagrand-ui';

import {
  AttachmentType,
  BasicInfoData,
  BatchListUploadHistoryRequest,
  BatchListUploadHistoryType,
  CombinedAttachmentType,
  CommentType,
  ComplianceInfoType,
  ConditioFilterSelectOptionsType,
  ConfirmBatchListUploadRequest,
  ConfirmEventUploadRequest,
  DownloadRequestType,
  EventUploadHistoryRequest,
  EventUploadHistoryType,
  ExaminationItemType,
  MarketService,
  MedicalExaminationType,
  PageTemplateTypes,
  PersonInfoType,
  PolicyService,
  ProposalCombinedTypes,
  QueryExclusionType,
  QueryGoodsPlanRequest,
  QueryMedicalPlanType,
  QueryService,
  RelatedPolicyResult,
  RuleResult,
  ScreeningResult,
  TaskDetailResponseData,
  UnderwritingResult,
  UnderwritingService,
  UnderwritingWorkSheetService,
  UploadResultType,
  UwOperationQuestionnaireRes,
  VerificationFileType,
  ruleService,
} from 'genesis-web-service';
import { GoodsInfoItem } from 'genesis-web-service/lib/policy';
import {
  GoodsInfoResponse,
  PlaceholderEnum,
  QueryGoodsInfoParams,
  VerificationFileMsgType,
  WrapperOrganizationDataType,
} from 'genesis-web-service/lib/policy/policy.interface';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import I18nInstance from '@uw/i18n';
import { ErrorType, SetState } from '@uw/interface/common.interface';
import {
  BizDict,
  ComplianceResultEnum,
  ExclusionStatusEnums,
  UploadStatus,
  YesOrNo,
} from '@uw/interface/enum.interface';
import { FileType } from '@uw/interface/field.interface';
import {
  MasterAgreementDetailAction,
  UPLOAD_RESULT,
} from '@uw/pages/nb-pages/MasterAgreementDetail/MasterAgreementDetailProvider';
import { handleSetUploadResult } from '@uw/pages/nb-pages/MasterAgreementDetail/components/UploadSubSection/util/handleSetUploadResults';
import {
  goodsIdAtom,
  templateCodeAtom,
} from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';
import { transferItemToOption } from '@uw/util/transferBizDictToOption';

import { useIsCombinedJourney } from '../pages/uw-pages/uwOperationPage/hooks/useIsCombinedJourney';

export const InitPagination = {
  current: 1,
  pageSize: 10,
};

// 查询goodsList
export const useSimpleGoodsInfo = (): [
  BizDict[],
  Record<number, BizDict[]> | undefined,
] => {
  const [goodsNameList, setGoodsnameList] = useState<BizDict[]>([]);
  const [goodsVersionMap, setGoodsVersionMap] =
    useState<Record<number, BizDict[]>>();
  const queryGoodsname = useCallback(async () => {
    const goodsInfo = await QueryService.queryGoods({
      page: {
        limit: 999,
        start: 1,
        condition: {},
      },
    });
    const goodsVerion: Record<number, BizDict[]> = {};
    const goodsNames =
      goodsInfo?.map(goods => {
        goodsVerion[goods.goodsId] = [
          {
            enumItemName: goods.goodsVersion,
            itemName: goods.goodsVersion,
          },
        ];
        if (goods?.children) {
          const childGoodsVersions = goods.children?.map(childGoods => ({
            enumItemName: childGoods.goodsVersion,
            itemName: childGoods.goodsVersion,
          }));
          goodsVerion[goods.goodsId] = [
            ...goodsVerion[goods.goodsId],
            ...childGoodsVersions,
          ];
        }
        setGoodsVersionMap(goodsVerion);
        return {
          enumItemName: goods.goodsId,
          itemName: `${goods.goodsName}-${goods.goodsVersion}`,
        };
      }) ?? [];

    setGoodsnameList(goodsNames);
  }, []);
  useEffect(() => {
    queryGoodsname();
  }, []);

  return [goodsNameList, goodsVersionMap];
};

export const queryPlanList = async (
  params: QueryGoodsPlanRequest['condition']
): Promise<DefaultOptionType[]> => {
  let results: DefaultOptionType[] = [];
  try {
    const res = await QueryService.queryGoodsPlanInfo({
      start: 0,
      pageIndex: 1,
      limit: 100,
      maxLimit: 1000,
      condition: params,
    });
    results =
      res?.results?.map(plan => ({
        value: plan.planId,
        label: plan.planName,
      })) ?? [];
  } catch (e) {
    messagePopup((e as Error).message, 'error');
  }

  return results;
};

// 查询plan信息 Query服务
export const usePlanList = (
  params: QueryGoodsPlanRequest['condition']
): DefaultOptionType[] => {
  const [data, setData] = useState<DefaultOptionType[]>([]);
  const queryPlanWithGoodsId = useCallback(async () => {
    const results = await queryPlanList(params);
    setData(results);
  }, [params]);

  useEffect(() => {
    queryPlanWithGoodsId();
  }, [params.goodsId]);

  return data;
};

// 查询goodsList 返回字段较全
export const useGoodsInfo = (): [
  (BizDict & { isMasterPolicy?: YesOrNo; usageBased?: YesOrNo })[],
  Record<number, string>,
] => {
  const [goodsInfo, setGoodsInfo] = useState<BizDict[]>([]);
  const [goodsInfoMap, setGoodsInfoMap] = useState<Record<number, string>>({});

  // goodsId列表
  useEffect(() => {
    PolicyService.queryGoodsInfo({
      page: {
        limit: 1000,
        start: 0,
        condition: {},
      },
    })
      .then(res => {
        const goodsMap: Record<number, string> = {};
        const mapSelect = res
          ?.filter(item => !isEmpty(item))
          ?.map<BizDict & { isMasterPolicy?: YesOrNo; usageBased?: YesOrNo }>(
            good => {
              const goodsLabel = `${good.goodsBasicInfo.goodsName} - ${good.goodsBasicInfo.goodsVersion}`;
              goodsMap[good.goodsBasicInfo.goodsId] = goodsLabel;
              return {
                enumItemName: good.goodsBasicInfo.goodsId.toString(),
                itemName: goodsLabel,
                isMasterPolicy: good.goodsBasicInfo.isMasterPolicy,
                usageBased: good.goodsBasicInfo.usageBased,
              };
            }
          );
        setGoodsInfoMap(goodsMap);
        setGoodsInfo(mapSelect);
      })
      .catch(() => {
        // error
      });
  }, []);

  return [goodsInfo, goodsInfoMap];
};

// agency, channel, organization 数据获取
export const useMasterPolicyInfo = (): ((
  PartnerTypeList: BizDict[]
) => Promise<
  [Record<string, BizDict[]>, Record<string, Record<string, string>>]
>) => {
  const fetchChannelAPI = useCallback(
    async (
      PartnerTypeList: BizDict[]
    ): Promise<
      [Record<string, BizDict[]>, Record<string, Record<string, string>>]
    > => {
      const partenerCodeObj: Record<string, BizDict[]> = {};
      const partnerMap: Record<string, Record<string, string>> = {};
      // 处理agency 跟 sale_channel type
      await Promise.all(
        PartnerTypeList.filter(
          holder => holder.enumItemName !== PlaceholderEnum.ORGANIZATION
        ).map(async holder => {
          const res = await PolicyService.queryChannnelsInfo({
            channelType: holder.enumItemName,
          });
          const partnerCodeRecord: Record<string, string> = {};
          partenerCodeObj[holder.enumItemName!] = uniqBy(res, 'partyId')
            .filter(item => item?.partyId)
            ?.reduce<BizDict[]>((cur, channel) => {
              const curChannel = [...cur];
              curChannel.push({
                enumItemName: channel.partyId?.toString(),
                itemName: channel.name,
                dictValue: channel.partyId?.toString(),
                code: channel.code?.toString(),
              });
              partnerCodeRecord[channel.partyId] = channel.name;
              return curChannel;
            }, []);
          partnerMap[holder.enumItemName!] = partnerCodeRecord;
        })
      );

      // 处理organization type
      // 接口巨大，并且没救，阻塞
      PolicyService.queryOrganizationsInfo({
        roleTypeList: ['HOLDER'],
      }).then((res: WrapperOrganizationDataType[]) => {
        const orgMap: Record<string, string> = {};
        partenerCodeObj[PlaceholderEnum.ORGANIZATION] = res?.map<BizDict>(
          company => {
            orgMap[company.organizationId.toString()] =
              company.organizationName;
            return {
              enumItemName: company.organizationId,
              itemName: company.organizationName,
            };
          }
        );
        partnerMap[PlaceholderEnum.ORGANIZATION] = orgMap;
      });
      return [partenerCodeObj, partnerMap];
    },
    []
  );
  return fetchChannelAPI;
};

// new configuration rule接口获取package
export const usePackages = () => {
  return useSWR('/api/packages/package-status/3', async () => {
    const res = await MarketService.queryPackageByCondition({
      id: '',
      packageStatus: '3',
    });

    return (
      res?.value?.packageList?.map(packageItem => ({
        ...packageItem,
        enumItemName: packageItem?.packageCode,
        dictValue: packageItem?.packageId?.toString() || '',
        itemName: `${packageItem?.packageCode}_${packageItem?.packageName}`,
        label: `${packageItem?.packageCode}_${packageItem?.packageName}`,
        value: packageItem?.packageId ? Number(packageItem.packageId) : 0,
      })) ?? []
    );
  });
};

// new configuration rule接口获取rules
export const useRules = (): BizDict[] | undefined => {
  const [rules, setRules] = useState<BizDict[]>();
  useEffect(() => {
    async function fetchMyAPI() {
      const enums = await ruleService.searchRuleEngineEnums();
      const allRules = await ruleService.queryRulesUnderCategory(
        enums?.ruleCategories?.map(category => category.code),
        true
      );
      setRules(
        allRules.map(rule => ({
          enumItemName: rule.code,
          itemName: `${rule.code}_${rule.name}`,
          code: rule.category,
        }))
      );
    }
    fetchMyAPI();
  }, []);
  return rules;
};

// new configuration 获取workflow
export const useWorkflowMap = () => {
  const [seletedWorkflow, setSeletedWorkflow] = useState<string[]>();
  useEffect(() => {
    PolicyService.queryWorkflowConfigurationInfo().then(res => {
      const seletedWorkflows = Object.keys(res?.workflow || {}).filter(
        workflowItem => res?.workflow[workflowItem] === YesOrNo.YES
      );
      setSeletedWorkflow(seletedWorkflows);
    });
  }, []);
  return seletedWorkflow;
};

// 获取attachments
export const useAttachments = (
  taskId: string,
  showDrawer?: boolean
): {
  comments?: CommentType[];
  attachments?: AttachmentType[];
  attachmentTypeMap?: Record<string, AttachmentType[]>;
  getNotesInfo: () => void;
  combinedAttachment: CombinedAttachmentType;
  loading: boolean;
} => {
  const [loading, setLoading] = useState(true);
  const [comments, setComments] = useState<CommentType[]>();
  const [attachments, setAttachments] = useState<AttachmentType[]>([]);
  const [attachmentTypeMap, setAttachmentTypeMap] = useState<
    Record<string, AttachmentType[]>
  >({});
  const [combinedAttachment, setCombinedAttachment] =
    useState<CombinedAttachmentType>({});

  const getNotesInfo = useCallback(
    () =>
      new Promise((resolve, reject) => {
        setLoading(true);
        UnderwritingService.queryNotes(taskId)
          .then(res => {
            setComments(res.commentList);
            setAttachments(res.attachmentList);
            setAttachmentTypeMap(res.attachmentTypeMap);
            setCombinedAttachment(res?.combinedAttachment);
            resolve(res);
          })
          .catch(e => {
            reject(e);
          })
          .finally(() => {
            setLoading(false);
          });
      }),
    [taskId, showDrawer]
  );

  useEffect(() => {
    getNotesInfo();
  }, [taskId, showDrawer]);

  return {
    comments,
    attachments,
    attachmentTypeMap,
    getNotesInfo,
    combinedAttachment,
    loading,
  };
};

export interface FormatMedicalPlanType
  extends Omit<QueryMedicalPlanType, 'examinationItemRelationList'> {
  examinationItemRelationList: ExaminationItemType[];
}
// 获取medical plans
export const useMedicalPlans = (): {
  medicalPlans: (FormatMedicalPlanType & { key: string })[];
  queryMedicalPlans: (pageNum: number, pageSize: number) => void;
  medicalPlansBizDict: BizDict[];
  medicalPlanMap: Record<string, ExaminationItemType[]>;
  onChange: (pageNum: number, pageSize: number) => void;
  current: number;
  total: number;
} => {
  const [medicalPlans, setMedicalPlans] = useState<
    (FormatMedicalPlanType & { key: string })[]
  >([]);
  const [medicalPlansBizDict, setMedicalPlansBizDict] = useState<BizDict[]>();
  const [medicalPlanMap, setMedicalPlanMap] =
    useState<Record<string, ExaminationItemType[]>>();
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const queryMedicalPlans = useCallback((pageNum: number, pageSize: number) => {
    UnderwritingService.queryMedicalPlanItems({ pageNum, pageSize }).then(
      res => {
        setMedicalPlans(
          res?.results?.map(item => ({
            ...item,
            key: item.id || item.medicalPlanCode,
            examinationItemRelationList: item.examinationItemRelationList?.map(
              items => items.examinationItem
            ),
          }))
        );
        setTotal(res.total);
      }
    );
  }, []);

  useEffect(() => {
    queryMedicalPlans(1, 10);
  }, []);
  const onChange = useCallback<(page: number, pageSize: number) => void>(
    (page, pageSize) => {
      setCurrent(page);
      queryMedicalPlans(page, pageSize);
    },
    []
  );

  useEffect(() => {
    const medicalPlanMapVal: Record<string, ExaminationItemType[]> = {};
    setMedicalPlansBizDict(
      medicalPlans?.map<BizDict>(medical => {
        medicalPlanMapVal[medical.medicalPlanCode] =
          medical.examinationItemRelationList;
        return {
          enumItemName: medical.medicalPlanCode,
          itemName: medical.medicalPlanName,
        };
      }) || []
    );
    setMedicalPlanMap(medicalPlanMapVal);
  }, [medicalPlans]);

  return {
    medicalPlans,
    queryMedicalPlans,
    medicalPlansBizDict: medicalPlansBizDict ?? [],
    medicalPlanMap: medicalPlanMap ?? {},
    onChange,
    current,
    total,
  };
};

export const useExaminationItems = (): {
  examinationItems: ExaminationItemType[];
  queryMedicalExaminationItems: () => void;
} => {
  const [examinationItems, setExaminationItems] = useState<
    ExaminationItemType[]
  >([]);

  const queryMedicalExaminationItems = useCallback(() => {
    UnderwritingService.queryMedicalExaminationItems().then(res => {
      setExaminationItems(res);
    });
  }, []);

  useEffect(() => {
    queryMedicalExaminationItems();
  }, []);
  return { examinationItems, queryMedicalExaminationItems };
};

export const useGetTaskDetail = (
  taskId: string
): {
  meidicalList: (MedicalExaminationType & { key: string })[];
  queryTaskDetail: () => void;
  loading: boolean;
} => {
  const [meidicalList, setMeidicalList] = useState<
    (MedicalExaminationType & { key: string })[]
  >([]);
  const [loading, setLoading] = useState<boolean>(false);

  const queryTaskDetail = useCallback(() => {
    setLoading(true);
    UnderwritingService.queryUWTaskDetail(taskId)
      .then(res => {
        setMeidicalList(
          res.uwMedicalExaminationList?.map((exam, index) => ({
            ...exam,
            key: `${exam.id}`,
            no: index + 1,
          }))
        );
      })
      .catch(err => {
        messagePopup(err?.message, 'error');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [taskId]);
  useEffect(() => {
    queryTaskDetail();
  }, [queryTaskDetail]);
  return { meidicalList, queryTaskDetail, loading };
};

const useAboutCombinedJourney = (basicInfo: BasicInfoData | undefined) => {
  const setGoodsId = useSetAtom(goodsIdAtom);
  const setTemplateCode = useSetAtom(templateCodeAtom);
  const callBack = useCallback(
    (res: ProposalCombinedTypes.IsCombinedJourneyResponse) => {
      if (!res?.isCombinedJourney) {
        return;
      }

      // 向jotai中设置goodsId和templateCode
      if (basicInfo?.goodsId) {
        setGoodsId(`${basicInfo?.goodsId}`);
      }
      if (res?.templateCode) {
        setTemplateCode(res?.templateCode);
      }
    },
    [basicInfo, setGoodsId, setTemplateCode]
  );

  // 查是否是CombinedJourney的录单页
  const isCombinedJourney = useIsCombinedJourney(
    basicInfo?.applicationNo,
    callBack
  );

  return isCombinedJourney;
};
// @deprecated 后续使用 useUwTaskDetail, 因导出了 setLoading 和 queryUwTaskDetail 直接迁移比较麻烦
export const useQueryUwTaskDetail = (
  taskId: string
): {
  task: TaskDetailResponseData | undefined;
  loading: boolean;
  queryUwTaskDetail: () => Promise<void>;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isCombinedJourney: boolean | undefined;
} => {
  const [loading, setLoading] = useState<boolean>(true);
  const [task, setTask] = useState<TaskDetailResponseData>();
  const queryUwTaskDetail = useCallback(async () => {
    setLoading(true);
    try {
      const res = await UnderwritingService.queryUWTaskDetail(taskId);
      const { personInfo } = res || {};
      setTask({
        ...res,
        personInfo: {
          holder: personInfo?.holder as PersonInfoType,
          insured: personInfo?.insured,
        },
      });
    } catch (error) {
      message.error((error as Error).toString());
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    queryUwTaskDetail();
  }, [queryUwTaskDetail]);

  const isCombinedJourney = useAboutCombinedJourney(task?.basicInfo);

  return {
    task,
    loading,
    queryUwTaskDetail,
    setLoading,
    isCombinedJourney,
  };
};

// 相同的 SWR key，请求会被自动去除重复、缓存 和 共享,不会发送多个请求
export const useUwTaskDetail = (
  taskId: string
): {
  loading: boolean;
  task?: TaskDetailResponseData;
  isWorkSheetPage?: boolean;
  isCombinedJourney?: boolean;
} => {
  const { data: taskDetail, isLoading } = useSWR(
    `/api/uw/${taskId}`,
    () => UnderwritingService.queryUWTaskDetail(taskId),
    {
      suspense: true,
      onError: err => message.error(err?.message),
    }
  );

  const isCombinedJourney = useAboutCombinedJourney(taskDetail?.basicInfo);
  const isWorkSheetPage =
    taskDetail?.basicInfo?.pageCode ===
    PageTemplateTypes.PageType.UNDERWRITING_V3;

  return {
    loading: isLoading,
    task: taskDetail,
    isCombinedJourney,
    isWorkSheetPage,
  };
};

export const useGetQuestionnaireList: (taskId?: number) => {
  questionnaireList: UwOperationQuestionnaireRes[];
  pagination: PaginationProps;
  getQuestionnaireList: (current: number, size: number) => void;
  errorMessage?: string;
} = (taskId?: number) => {
  const [questionnaireList, setQuestionnaireList] = useState<
    UwOperationQuestionnaireRes[]
  >([]);
  const [errorMessage, setErrorMessage] = useState<string>();
  const [pagination, setPagination] = useState<PaginationProps>({
    current: InitPagination.current,
    pageSize: InitPagination.pageSize,
    total: 0,
  });

  const getQuestionnaireList = useCallback(
    async (page, size) => {
      try {
        const { total, results } =
          await UnderwritingService.queryUWQuestionnaire(taskId as number, {
            page,
            size,
          });
        setQuestionnaireList(results);
        setPagination({
          ...pagination,
          current: page,
          pageSize: size,
          total,
        });
      } catch (error) {
        setQuestionnaireList([]);
        setErrorMessage((error as Error).toString() ?? i18nFn('System Error'));
        messagePopup((error as Error).toString(), 'error');
      }
    },
    [taskId, pagination]
  );
  useEffect(() => {
    if (taskId) {
      getQuestionnaireList(InitPagination.current, InitPagination.pageSize);
    }
  }, [taskId]);

  return {
    questionnaireList,
    pagination,
    getQuestionnaireList,
    errorMessage,
  };
};

// 获取Exclusion
export const useGetExclusion = (): {
  exclusionItem: QueryExclusionType[];
  queryExclusion: (pageNum: number, pageSize: number) => void;
  setExclusionItem: React.Dispatch<React.SetStateAction<QueryExclusionType[]>>;
  activeExclusionItem: QueryExclusionType[];
  onChange: (pageNum: number, pageSize: number) => void;
  current: number;
  total: number;
} => {
  const [exclusionItem, setExclusionItem] = useState<QueryExclusionType[]>([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const queryExclusion = useCallback((pageNum: number, pageSize: number) => {
    UnderwritingService.getExclusionPage({ pageNum, pageSize }).then(res => {
      setExclusionItem(
        res.results?.map((results, idx) => ({
          ...results,
          key: `${results.id}${idx}`,
        }))
      );
      setTotal(res.total);
    });
  }, []);

  const activeExclusionItem = useMemo(
    () =>
      exclusionItem.filter(item => item.status === ExclusionStatusEnums.ACTIVE),
    [exclusionItem]
  );

  useEffect(() => {
    queryExclusion(1, 10);
  }, [queryExclusion]);
  const onChange = useCallback<(page: number, pageSize: number) => void>(
    (page, pageSize) => {
      setCurrent(page);
      queryExclusion(page, pageSize);
    },
    [queryExclusion]
  );
  return {
    exclusionItem,
    queryExclusion,
    setExclusionItem,
    activeExclusionItem,
    onChange,
    current,
    total,
  };
};
export const useWithdrawnTask = (): {
  withdrawnLoading: boolean;
  isWithdrawnRefresh: boolean;
  handleWithdrawn: (taskId: number) => void;
} => {
  const [isWithdrawnRefresh, setIsWithdrawnRefresh] = useState(false);
  const [withdrawnLoading, setWithdrawnLoading] = useState(false);
  const handleWithdrawn = useCallback((taskId: number) => {
    setWithdrawnLoading(true);
    setIsWithdrawnRefresh(false);
    UnderwritingService.withdrawnTaskV2(taskId)
      .then(res => {
        if (!res) {
          messagePopup(
            I18nInstance.t('Task Successfully Withdrawn'),
            'success'
          );
          setIsWithdrawnRefresh(true);
        } else {
          messagePopup(I18nInstance.t('Submit Failed'), 'error');
        }
      })
      .catch(e => {
        messagePopup(e.message, 'error');
      })
      .finally(() => {
        setWithdrawnLoading(false);
      });
  }, []);

  return { handleWithdrawn, isWithdrawnRefresh, withdrawnLoading };
};

export const useBatchListUploadHistories = (
  args: BatchListUploadHistoryRequest
): {
  loading: boolean;
  total: number;
  uploadHistories: BatchListUploadHistoryType[];
} => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadHistories, setUploadHistories] = useState<
    BatchListUploadHistoryType[]
  >([]);
  const handleBatchListUploadHistories = (
    params: BatchListUploadHistoryRequest
  ) => {
    setLoading(true);
    PolicyService.queryBatchUploadHistory(params)
      .then(res => {
        const { results, total: totalNumber } = res;
        setUploadHistories(results || []);
        setTotal(totalNumber || 0);
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    handleBatchListUploadHistories(args);
  }, [args]);

  return { loading, total, uploadHistories };
};
export const useBatchDownloadTemplate = (): {
  downLoading: boolean;
  handleDownloadTemplate: (goodsId: number) => void;
} => {
  const [downLoading, setDownLoading] = useState(false);
  const handleDownloadTemplate = (goodsId: number) => {
    setDownLoading(true);
    PolicyService.downloadBatchUploadTemplate({
      goodsId,
    })
      .then(downloadFile)
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      })
      .finally(() => setDownLoading(false));
  };
  return { downLoading, handleDownloadTemplate };
};
export const useBatchConfirmSubmit = (
  getFileList: FileType[]
): {
  confirmLoding: boolean;
  handleConfirmSubmit: (
    values: ConfirmBatchListUploadRequest,
    fileName: string,
    fileList: FileType[],
    handleClearUpload: () => void,
    pagination: BatchListUploadHistoryRequest,
    setPagination: React.Dispatch<
      React.SetStateAction<BatchListUploadHistoryRequest>
    >
  ) => void;
} => {
  const { l10n } = useL10n();
  const [confirmLoding, setConfirmLoding] = useState(false);

  const handleConfirmSubmit = useCallback(
    (
      values: ConfirmBatchListUploadRequest,
      fileName: string,
      fileList: FileType[],
      handleClearUpload: () => void,
      pagination: BatchListUploadHistoryRequest,
      setPagination: React.Dispatch<
        React.SetStateAction<BatchListUploadHistoryRequest>
      >
    ) => {
      if (!fileList?.[0]) {
        messagePopup(i18nFn('Please Upload File'), 'error');
        return;
      }

      const params = {
        ...values,
        fileUniqueCode: fileList[0].fileUniqueCode,
        premiumCollectionDate: values.premiumCollectionDate
          ? l10n.dateFormat.formatTz(values.premiumCollectionDate)
          : undefined,
        fileName,
      };

      setConfirmLoding(true);
      PolicyService.confirmBatchListUpload(params)
        .then(() => {
          messagePopup(i18nFn('Submit Successfully'), 'success');
          handleClearUpload();
          setPagination({ ...pagination, pageNumber: 1, pageSize: 10 });
        })
        .catch((error: Error) => {
          messagePopup(error.message, 'error');
        })
        .finally(() => setConfirmLoding(false));
    },
    [getFileList]
  );

  return { confirmLoding, handleConfirmSubmit };
};

export enum UploadVehicleStatus {
  PROCESSING = 'PROCESSING',
}

type ConfirmSubmitFn = (params: {
  fileName: string;
  fileList: FileType[];
  masterAgreementNo: string;
  setShowUploadingModal: Dispatch<SetStateAction<boolean>>;
  dispatch?: React.Dispatch<MasterAgreementDetailAction>;
  batchNum: string | undefined;
}) => void;
export const useListUploadResults = (
  setUploadResult: Dispatch<SetStateAction<UploadResultType[]>>,
  setUploadingBatchNum: Dispatch<SetStateAction<string | undefined>>,
  setChangingMasterPolicyNo?: SetState<boolean>
): {
  confirmLoding: boolean;
  handleConfirmSubmit: ConfirmSubmitFn;
  handleGetResult: (
    masterAgreementNo: string,
    batchUploadNum: string | undefined,
    dispatch?: React.Dispatch<MasterAgreementDetailAction>
  ) => Promise<void>;
  uploadStatus: {
    uploadStatus: UploadVehicleStatus | undefined;
    uploadStatusDesc: string | undefined;
  };
} => {
  const [confirmLoding, setConfirmLoding] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<UploadVehicleStatus>();
  const [uploadStatusDesc, setUploadStatusDesc] = useState<string>();

  const handleGetResult = useCallback<
    (
      masterAgreementNo: string,
      batchUploadNum: string | undefined,
      dispatch?: React.Dispatch<MasterAgreementDetailAction>
    ) => Promise<void>
  >(
    async (masterAgreementNo, batchUploadNum, dispatch) => {
      const res = await PolicyService.getUploadResult(
        masterAgreementNo,
        batchUploadNum
      );
      // 同步上传提交之后立刻获取create返回的uploadresult信息(编辑刷新时用的是单独接口获取)
      if (dispatch) {
        dispatch({
          type: UPLOAD_RESULT,
          uploadResult: res,
        });
      }
      setUploadingBatchNum(res.batchNumber);
      setUploadResult(handleSetUploadResult(res));
      setUploadStatus(res.viewStatus as UploadVehicleStatus);
      setUploadStatusDesc(res.viewStatusDesc);
    },
    [setUploadResult, setUploadingBatchNum]
  );
  const handleConfirmSubmit = useCallback<ConfirmSubmitFn>(
    async ({
      fileName,
      fileList,
      masterAgreementNo,
      setShowUploadingModal,
      dispatch,
      batchNum,
    }) => {
      if (!fileList?.[0]) {
        messagePopup(i18nFn('Please Upload File'), 'error');
        return;
      }
      const params = {
        fileUniqueCode: fileList[0].fileUniqueCode as string,
        fileName,
        masterAgreementNo,
        batchNumber: batchNum,
      };
      setConfirmLoding(true);
      try {
        const res = await PolicyService.listUploadResultReq(params);
        const batchUploadNum = res.batchNumber;
        // 同步上传提交之后立刻获取create返回的uploadresult信息(编辑刷新时用的是单独接口获取)
        await handleGetResult(
          params.masterAgreementNo,
          batchUploadNum,
          dispatch
        );
        setChangingMasterPolicyNo?.(false);
        messagePopup(i18nFn('Submit Successfully'), 'success');
      } catch (error) {
        messagePopup((error as Error).message, 'error');
      }

      setShowUploadingModal(false);
      setConfirmLoding(false);
    },
    [setUploadResult, setChangingMasterPolicyNo, setUploadingBatchNum]
  );

  return {
    confirmLoding,
    handleConfirmSubmit,
    handleGetResult,
    uploadStatus: {
      uploadStatus,
      uploadStatusDesc,
    },
  };
};

export const useDownloadBatchUploading = (): {
  handleDownloadBatchUploading: (args: DownloadRequestType) => void;
} => {
  const handleDownloadBatchUploading = (args: DownloadRequestType) => {
    PolicyService.downloadBatchUploading(
      args.batchNum,
      args.verifyStage,
      args.onlyFailed
    )
      .then((response: AxiosResponse<Blob>) =>
        downloadFile(response).then(() => {
          messagePopup(i18nFn('Download successfully'), 'success');
        })
      )
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      });
  };
  return { handleDownloadBatchUploading };
};

export const useBatchListDownload = (): {
  handleBatchListDownload: (uploadCode: string) => void;
} => {
  const handleBatchListDownload = (uploadCode: string) => {
    PolicyService.downloadFile(uploadCode)
      .then(downloadFile)
      .catch(error => {
        messagePopup(error.message, 'error');
      });
  };
  return { handleBatchListDownload };
};
export const useVerifyUploadFile = (): {
  handleFileVerify: (params: VerificationFileType) => void;
  verifyFileMsg: VerificationFileMsgType;
  setVerifyFileMsg: React.Dispatch<
    React.SetStateAction<VerificationFileMsgType>
  >;
} => {
  const [verifyFileMsg, setVerifyFileMsg] = useState(
    {} as VerificationFileMsgType
  );
  const handleFileVerify = (params: VerificationFileType) => {
    PolicyService.batchFileVerify(params)
      .then(res => {
        setVerifyFileMsg(res);
      })
      .catch(error => {
        setVerifyFileMsg({} as VerificationFileMsgType);
        messagePopup(error.message, 'error');
      });
  };
  return { handleFileVerify, verifyFileMsg, setVerifyFileMsg };
};

export const useDownloadBatchList = (): {
  handleDownloadBatchList: (
    batchNum: string,
    onlyFailed?: boolean,
    verifyStage?: boolean
  ) => void;
} => {
  const handleDownloadBatchList = (
    batchNum: string,
    onlyFailed?: boolean,
    verifyStage?: boolean
  ) => {
    PolicyService.downloadBatchlist(batchNum, onlyFailed, verifyStage)
      .then((response: AxiosResponse<Blob>) =>
        downloadFile(response).then(() => {
          messagePopup(i18nFn('Download successfully'), 'success');
        })
      )
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      })
      .finally(() => {});
  };
  return { handleDownloadBatchList };
};
export const useGetCreatorList = (): {
  creatorList: ConditioFilterSelectOptionsType[];
} => {
  const [creatorList, setCreatorList] = useState<
    Array<ConditioFilterSelectOptionsType>
  >([]);
  useEffect(() => {
    PolicyService.getBatchListCreatorList()
      .then(res => {
        setCreatorList(
          transferItemToOption(res) as ConditioFilterSelectOptionsType[]
        );
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      });
  }, []);
  return { creatorList };
};

export const useEventsPolicyUploadHistories = (
  args: EventUploadHistoryRequest
): {
  loading: boolean;
  total: number;
  uploadHistories: EventUploadHistoryType[];
} => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadHistories, setUploadHistories] = useState<
    EventUploadHistoryType[]
  >([]);

  const handleEventsPolicyUploadHistories = useCallback(params => {
    setLoading(true);
    PolicyService.queryEventUploadHistory(params)
      .then(res => {
        const { results, total: totalNumber } = res;
        setUploadHistories(results || []);
        setTotal(totalNumber || 0);
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      })
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    handleEventsPolicyUploadHistories(args);
  }, [args]);

  return { loading, total, uploadHistories };
};

export const useShowMasterPolicy = (
  selectedGoodsId: number | undefined
): {
  showMasterPolicy: boolean;
  setShowMasterPolicy: React.Dispatch<React.SetStateAction<boolean>>;
} => {
  const [showMasterPolicy, setShowMasterPolicy] = useState(false);

  const handleShowMasterPolicy = useCallback((goodsId: number) => {
    PolicyService.queryGoodsInfoByGoodsId({
      goodsId,
      queryBasic: true,
    })
      .then(res => {
        setShowMasterPolicy(
          res?.goodsBasicInfo?.isMasterPolicy === YesOrNo.YES
        );
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      });
  }, []);

  useEffect(() => {
    if (selectedGoodsId) {
      handleShowMasterPolicy(selectedGoodsId);
    } else {
      setShowMasterPolicy(false);
    }
  }, [selectedGoodsId]);

  return { showMasterPolicy, setShowMasterPolicy };
};

export const useUploadFile = (): {
  fileList: FileType[];
  setFileList: React.Dispatch<React.SetStateAction<FileType[]>>;
  handleUploadFile: (
    formData: FormData,
    busiNo: string | number,
    masterAgreementNo?: string
  ) => Promise<void>;
} => {
  const [fileList, setFileList] = useState<Array<FileType>>([]);

  const handleUploadFile = useCallback(
    async (formData, busiNo, masterAgreementNo) => {
      try {
        const res = await PolicyService.uploadFile(formData, busiNo);
        messagePopup(i18nFn('Upload Successfully'), 'success');
        const index = res.fileUrl.lastIndexOf('/');
        const param = {
          uid: res.fileUniqueCode,
          name: res.fileUrl.substring(index + 1, res.fileUrl.length),
          status: UploadStatus.Done,
          url: res.fileUrl,
          creator: res.creator?.split('@')[0],
          gmtCreated: res.gmtCreated,
          fileUniqueCode: res.fileUniqueCode,
          curMaterPolicyNo: masterAgreementNo,
        };
        setFileList([param]);
      } catch (error) {
        messagePopup((error as Error).message, 'error');
      }
    },
    []
  );

  return { fileList, setFileList, handleUploadFile };
};

export const useDownloadTemplate = (): {
  downLoading: boolean;
  handleDownloadTemplate: (goodsId: number, planId: number) => void;
} => {
  const [downLoading, setDownLoading] = useState(false);
  const handleDownloadTemplate = useCallback((goodsId, planId) => {
    setDownLoading(true);
    PolicyService.downloadEventUploadTemplate({
      goodsId,
      planId,
    })
      .then(downloadFile)
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      })
      .finally(() => setDownLoading(false));
  }, []);

  return { downLoading, handleDownloadTemplate };
};

export const useConfirmSubmit = (): {
  confirmLoding: boolean;
  handleConfirmSubmit: (
    values: ConfirmEventUploadRequest,
    fileList: FileType[],
    handleClearUpload: () => void,
    pagination: EventUploadHistoryRequest,
    setPagination: React.Dispatch<
      React.SetStateAction<EventUploadHistoryRequest>
    >
  ) => void;
} => {
  const { l10n } = useL10n();
  const [confirmLoding, setConfirmLoding] = useState(false);

  const handleConfirmSubmit = useCallback(
    (values, fileList, handleClearUpload, pagination, setPagination) => {
      if (!fileList?.[0]) {
        messagePopup(i18nFn('Please Upload File'), 'error');
        return;
      }

      const params = {
        ...values,
        fileUniqueCode: fileList[0].fileUniqueCode,
        premiumCollectionDate: values.premiumCollectionDate
          ? l10n.dateFormat.formatTz(values.premiumCollectionDate)
          : undefined,
      };

      setConfirmLoding(true);
      PolicyService.confirmEventUpload(params)
        .then(() => {
          messagePopup(i18nFn('Success'), 'success');
          handleClearUpload();
          setPagination({ ...pagination, pageNumber: 1, pageSize: 10 });
        })
        .catch((error: Error) => {
          messagePopup(error.message, 'error');
        })
        .finally(() => setConfirmLoding(false));
    },
    []
  );

  return { confirmLoding, handleConfirmSubmit };
};

export const useDownloadFailed = (): {
  handleDownloadFailed: (uploadNo: number) => void;
} => {
  const handleDownloadFailed = useCallback(uploadNo => {
    PolicyService.downloadFailed(uploadNo)
      .then(downloadFile)
      .catch(error => {
        messagePopup(error.message, 'error');
      });
  }, []);
  return { handleDownloadFailed };
};

export const useQueryUwRuleResult = (): {
  ruleResult: RuleResult[] | undefined;
  queryUwRuleResult: (taskId: string) => void;
} => {
  const [ruleResult, setRuleResult] = useState<RuleResult[]>();
  const queryUwRuleResult = useCallback((taskId: string) => {
    UnderwritingService.queryUwRuleResult(taskId)
      .then(res => {
        setRuleResult(res);
      })
      .catch(error => {
        messagePopup((error as ErrorType).message, 'error');
      });
  }, []);

  return {
    ruleResult,
    queryUwRuleResult,
  };
};
export const useQueryUwComplianceInfo = (): {
  complianceInfo: ComplianceInfoType | undefined;
  queryUwComplianceInfo: (taskId: string) => void;
} => {
  const [complianceInfo, setComplianceInfo] = useState<ComplianceInfoType>();
  const queryUwComplianceInfo = useCallback((taskId: string) => {
    UnderwritingService.queryUwComplianceInfo(taskId)
      .then(res => {
        setComplianceInfo(res);
      })
      .catch(error => {
        messagePopup((error as ErrorType).message, 'error');
      });
  }, []);

  return {
    complianceInfo,
    queryUwComplianceInfo,
  };
};

export const useScreeningResult = (
  taskType?: string,
  applicationNo?: string
) => {
  const [dataSource, setDataSource] = useState<ScreeningResult[]>();
  const [hasError, setHasError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>(
    i18nFn('System Error')
  );
  const [pagination, setPagination] = useState<PaginationProps>({
    current: InitPagination.current,
    pageSize: InitPagination.pageSize,
    total: 0,
    showSizeChanger: true,
    showTotal: undefined,
  });
  const queryScreeningResult = useCallback(
    (page, size) => {
      setLoading(true);
      setDataSource([]);
      UnderwritingService.getScreeningResult({
        businessType: taskType as string,
        businessNo: applicationNo as string,
        pageable: { page, size },
      })
        .then(res => {
          setDataSource(res?.results);
          setPagination({
            ...pagination,
            current: page,
            pageSize: size,
            total: res?.total,
          });
        })
        .catch(error => {
          setHasError(true);
          setDataSource([]);
          setErrorMessage(
            (error as { message: string })?.message ?? i18nFn('System Error')
          );
          setPagination(null as unknown as PaginationProps);
          messagePopup((error as ErrorType).message, 'error');
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [applicationNo, pagination, taskType]
  );

  useEffect(() => {
    if (applicationNo && taskType) {
      queryScreeningResult(InitPagination?.current, InitPagination?.pageSize);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationNo, taskType]);

  return {
    dataSource,
    hasError,
    pagination,
    errorMessage,
    loading,
    queryScreeningResult,
  };
};

export const useUnderwritingResult = (
  taskId: string
): {
  uwResult: UnderwritingResult[];
  elapsedDay?: number;
} => {
  const [uwResult, setUwResult] = useState<UnderwritingResult[]>([]);
  const [elapsedDay, setElapsedDay] = useState<number>();
  useEffect(() => {
    UnderwritingWorkSheetService.queryUnderwritingResult(taskId)
      .then(res => {
        setUwResult(res);
        if (res?.[0]?.elapsedDay) {
          setElapsedDay(res[0].elapsedDay);
        }
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      });
  }, [taskId]);
  return { uwResult, elapsedDay };
};

export const useComplianceResult = (
  taskId: string
): {
  complianceResult: UnderwritingResult[];
  finalResult: string;
} => {
  const [complianceResult, setComplianceResult] = useState<
    UnderwritingResult[]
  >([]);
  const [finalResult, setFinalResult] = useState<string>('');
  useEffect(() => {
    UnderwritingWorkSheetService.queryComplianceResult(taskId)
      .then(res => {
        setComplianceResult(res);
        if (
          res?.some(
            item => item?.complianceResult === ComplianceResultEnum.PENDING
          )
        ) {
          setFinalResult(ComplianceResultEnum.PENDING);
        } else if (
          res?.some(
            item => item?.complianceResult === ComplianceResultEnum.REJECTED
          )
        ) {
          setFinalResult(ComplianceResultEnum.DECLINED);
        } else if (
          res?.every(
            item => item?.complianceResult === ComplianceResultEnum.ACCEPTED
          )
        ) {
          setFinalResult(ComplianceResultEnum.ACCEPTED);
        }
      })
      .catch((error: Error) => {
        messagePopup(error.message, 'error');
      });
  }, [taskId]);
  return { complianceResult, finalResult };
};

export const useRelatedPolicyResult = (
  taskId: string
): {
  loading: boolean;
  relatedPolicyResult: RelatedPolicyResult[];
} => {
  const { data: relatedPolicyResult, isLoading } = useSWR(
    `/api/related-policy-overview/${taskId}`,
    () => UnderwritingWorkSheetService.queryRelatedPolicyResult(taskId),
    {
      suspense: true,
      onError: err => message.error(err?.message),
    }
  );

  return {
    loading: isLoading,
    relatedPolicyResult,
  };
};

// 获取Goods Name/Product Name/Plan Name数据源
export const useExternalQueryGoodsInfo = (options?: {
  goodsValueKey?: keyof GoodsInfoItem;
}): {
  goodsInfo: GoodsInfoResponse;
  handleChange: (currentFieldName: string, fieldValue?: number) => void;
} => {
  const { goodsValueKey = 'goodsId' } = options ?? {};
  const [goodsInfo, setGoodsInfo] = useSetState<GoodsInfoResponse>({});

  const [temporaryParamsInfo, setTemporaryParamsInfo] =
    useSetState<QueryGoodsInfoParams>({});

  const queryGoodsInfo = useCallback(
    (params?: QueryGoodsInfoParams, currentFieldName?: string) =>
      PolicyService.externalQueryGoodsInfo(params)
        .then(res => {
          if (!currentFieldName || currentFieldName === 'categoryId') {
            // 1. 查找选中categoryId下对应的goodsName 2. currentFieldName不存在时，goodsName查询全量
            const goodsInfoRespList = (res?.goodsInfoRespList ?? []).map(
              goods => ({
                ...goods,
                key: goods.goodsId,
                label:
                  goods?.goodsVersionList?.length > 0
                    ? `${goods.goodsName}-`.concat(
                        goods?.goodsVersionList.join('-')
                      )
                    : goods.goodsName,
                value: goods[goodsValueKey],
                data: goods,
              })
            );
            setGoodsInfo({
              goodsInfoRespList,
              planInfoRespList: [],
              packageInfoRespList: [],
            });
          } else if (currentFieldName === 'goodsId') {
            setGoodsInfo({
              planInfoRespList: res?.planInfoRespList,
              packageInfoRespList: res?.packageInfoRespList,
            });
          } else if (currentFieldName === 'goodsPlanId') {
            setGoodsInfo({
              packageInfoRespList: res?.packageInfoRespList,
            });
          }
        })
        .catch(error => {
          message.error(error?.message);
        }),
    []
  );

  const handleChange = useCallback(
    async (currentFieldName: string, fieldValue?: number) => {
      const { categoryId, goodsId } = temporaryParamsInfo;

      if (currentFieldName === 'categoryId') {
        // 查询categoryId下对应的Goods Name; 当fieldValue为undefined时查询的是全量的goodsName
        queryGoodsInfo({ categoryId: fieldValue }, currentFieldName);
      } else if (currentFieldName === 'goodsId') {
        // 查询对应goodsId下的 Plan Name / Product Name
        queryGoodsInfo({ categoryId, goodsId: fieldValue }, currentFieldName);
      } else if (currentFieldName === 'goodsPlanId') {
        // 查询goodsPlanId下对应的Product Name
        queryGoodsInfo(
          { categoryId, goodsId, planId: fieldValue as unknown as string },
          currentFieldName
        );
      }

      setTemporaryParamsInfo({
        [currentFieldName]: fieldValue,
      });
    },
    [temporaryParamsInfo]
  );

  useEffect(() => {
    // 查询全量的goodsName
    queryGoodsInfo();
  }, [queryGoodsInfo]);

  return { goodsInfo, handleChange };
};
