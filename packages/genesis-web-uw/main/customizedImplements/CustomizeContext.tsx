import { FC, PropsWithChildren, createContext } from 'react';

import { CustomizeSection } from './customizeSection';

export interface ConfigProviderProps {
  pageCode: string;
  // sectionCode: string;
  customizeSection?: CustomizeSection;
}

export const CustomizationContext = createContext<ConfigProviderProps | null>(
  null
);

export const CustomermizationProvider: FC<
  PropsWithChildren<ConfigProviderProps>
> = ({
  children,
  pageCode,
  // sectionCode,
}) => {
  const customizeSection = new CustomizeSection(pageCode);
  return (
    <CustomizationContext.Provider
      value={{
        pageCode,
        customizeSection,
      }}
    >
      {children}
    </CustomizationContext.Provider>
  );
};
