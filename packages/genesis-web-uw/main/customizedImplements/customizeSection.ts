import axios, { AxiosResponse } from 'axios';
import { FactorType } from 'genesis-web-service';
import { Dispatch } from 'react';

export class CustomizeSection {
  pageCode = '';

  pageState: Record<string, unknown> = {};

  dispatch: Dispatch<Record<string, unknown>> | undefined;

  // sectionCode = '';

  constructor(pageCode: string) {
    this.pageCode = pageCode;
    // this.sectionCode = sectionCode;
  }

  // 因为是通用逻辑所以这里只能是unknown
  // TODO 暂时没用上 考虑好再打开
  // static getPageDetailData(
  //   url: string,
  //   body: unknown
  // ): Promise<AxiosResponse<any>> {
  //   return axios.post(url, body);
  // }

  initPageStateDispatch = (
    state: Record<string, unknown>,
    dispatch: Dispatch<Record<string, unknown>>
  ) => {
    this.pageState = state;
    this.dispatch = dispatch;
  };

  updateApplicationElements = (appElements: Record<string, FactorType[]>) => {
    this.dispatch?.({
      type: 'UPDATE_APPLICATION_ELEMENT_CONFIG',
      appElements,
    });
  };

  // 自定义接口请求
  queryData = (params: {
    url: string;
    body: Record<string, unknown>;
  }): Promise<AxiosResponse<any>> => {
    const { url } = params;
    return axios.get(url);
  };
}
