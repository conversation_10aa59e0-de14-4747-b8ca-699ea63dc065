import { createSchemaField } from '@formily/react';
import { But<PERSON> } from 'antd';

import {
  FormItem,
  FormGrid,
  FormLayout,
  Select,
  Radio,
  FormCollapse,
  ArrayCollapse,
  Space,
  ArrayItems,
  Checkbox,
  NumberPicker,
  ArrayTable,
  ArrayBase,
  Switch,
} from '@formily/antd-v5';
import {
  Input,
  PreviewText,
  SearchSelect,
  ArrayTableIndex,
  ArrayTableAction,
  JumpItem,
  JumpLink,
  DictSelect,
  MetaDataEnumsPreviewText,
  DatePicker,
} from 'genesis-web-component/lib/components/Formily';

import { TextBody, StatusTag } from '@zhongan/nagrand-ui';
import { Divider } from 'genesis-web-component/lib/components';

import { PersonCardSlick } from '@uw/components/PersonCard';

import { SectionWrapper } from '@uw/components/SectionWrapper';

export const SchemaField = createSchemaField({
  components: {
    FormCollapse,
    FormItem,
    Input,
    FormGrid,
    FormLayout,
    Select,
    DatePicker,
    SearchSelect,
    SectionWrapper,
    PersonCardSlick,
    Radio,
    Checkbox,
    PreviewText,
    ArrayCollapse,
    Space,
    ArrayItems,
    NumberPicker,
    ArrayTable,
    TextBody,
    Divider,
    ArrayBase,
    ArrayTableIndex,
    ArrayTableAction,
    JumpItem,
    JumpLink,
    Switch,
    DictSelect,
    MetaDataEnumsPreviewText,
    Button,
    StatusTag,
  },
});
