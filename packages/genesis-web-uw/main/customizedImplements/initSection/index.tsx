import { FC, useMemo } from 'react';

import { FormProvider } from '@formily/react';
import { Field, createForm, onFormValuesChange } from '@formily/core';
import { useTranslation } from 'react-i18next';

import { SchemaType } from '@uw/interface/common.interface';

import { InferObjectRecord, PageTemplateTypes } from 'genesis-web-service';

import { isEmpty } from 'lodash-es';

import { action } from '@formily/reactive';

import { AxiosResponse } from 'axios';

import { SchemaField } from './SchemaField';
import { useCustomization } from '../useCustomizedImplements';

export interface SectionProps {
  title: string;
  id: string;
  detail: Record<string, unknown> | undefined;
  render: SchemaType;
  disableEdit?: boolean;
  section?:
    | PageTemplateTypes.SubSectionDetailResponse
    | PageTemplateTypes.SectionDetailResponse;
}

export const CustomizedComponent: FC<SectionProps> = ({
  render,
  title,
  id,
  detail,
  section,
  disableEdit,
}) => {
  const { t } = useTranslation(['uw']);
  const { customizeSection } = useCustomization();

  const form = useMemo(
    () =>
      createForm({
        initialValues: detail,
        effects() {
          onFormValuesChange(() => {
            // record the changed value
          });
        },
      }),
    [detail]
  );

  const useAsyncDataSource =
    (
      service: (params: {
        url: string;
        body: Record<string, unknown>;
      }) => Promise<unknown>,
      url: string,
      body: Record<string, unknown>
    ) =>
    (field: Field) => {
      service({ url, body }).then(
        action.bound?.((data: unknown) => {
          // eslint-disable-next-line no-param-reassign
          field.value = (data as AxiosResponse)?.data;
        })
      );
    };

  const fieldMap = useMemo(
    () =>
      (
        section as PageTemplateTypes.SubSectionDetailResponse
      )?.sectionFieldInstances?.reduce(
        (cur, next) => ({
          ...cur,
          [next.fieldName]: next,
        }),
        {} as InferObjectRecord<PageTemplateTypes.SectionConfigs>
      ),
    [section]
  );

  return (
    <FormProvider form={form}>
      <SchemaField
        scope={{
          t,
          title,
          id,
          customizeSection,
          fieldMap,
          disableEdit,
          showAllFields: isEmpty(fieldMap),
          useAsyncDataSource,
        }}
        schema={render}
      />
    </FormProvider>
  );
};
