# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Genesis Web UW is a React-based micro-frontend application for policy business (保单业务), including underwriting (UW), policy configuration, new business (NB) configuration, and policy-related business processes. It's part of a larger monorepo structure and operates as a qiankun micro-application.

## Common Development Commands

**Development:**
- `pnpm start` - Start development server (defaults to dev environment)
- `pnpm start:dev` - Start with dev proxy channel
- `pnpm start:sit` - Start with SIT proxy channel

**Building:**
- `pnpm build` - Production build using Rspack
- `pnpm rs:build` - Direct Rspack build
- `pnpm clear` - Clean build directory

**Tools:**
- `pnpm lint-staged` - Run lint-staged checks
- `pnpm check-same-keys` - Check i18n key consistency

**Important Notes:**
- Development server runs on port 8080 (required for user-auth whitelist)
- Environment switching: Use Shift + D shortcut or navigate to `http://localhost:8080/dev`
- Uses Rspack as the primary bundler (Webpack config exists as fallback)

## Code Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript
- **State Management**: Redux Toolkit + Jotai for local state
- **Forms**: Formily (v2.3.1) with Ant Design v5 integration
- **Styling**: SCSS modules + Tailwind CSS + Ant Design
- **Build**: Rspack (primary), Webpack (fallback)
- **Router**: React Router Dom v6
- **Data Fetching**: SWR + Axios
- **Micro-frontend**: Qiankun integration
- **Internationalization**: react-i18next

### Workspace Dependencies
- `genesis-web-component` - Shared UI components
- `genesis-web-service` - Business logic services  
- `genesis-web-shared` - Shared utilities and types

### Key Architectural Patterns

**Page Structure Pattern:**
```
pages/[page-group]/
├── components/           # Page-specific components
├── hooks/               # Page-specific React hooks
├── sections/            # Section configurations by section code
│   └── [section-code]/
│       ├── sub-sections/    # Sub-section configurations
│       │   └── [sub-section-code]/
│       │       ├── *.tsx
│       │       ├── *.module.sass
│       │       └── index.ts
│       ├── *.tsx
│       ├── *.module.sass
│       └── index.ts
├── index.ts
├── *.tsx                # Main page component
└── *.module.sass        # Page styles
```

**Component Architecture:**
- Use functional components with hooks (HOCs are deprecated)
- SCSS modules for styling (`.module.scss` files)
- Component-specific request methods in `hooks/request.ts`
- TypeScript interfaces in dedicated `interface/` folders

**State Management:**
- Redux for global state (`main/redux/`)
- Jotai atoms for component-level state (`main/atom/`)
- Business dictionaries cached in Redux (`useBizDict` hook)

### Core Directories

**`main/components/`** - Reusable business components:
- `BasicInfoList` - Dynamic form rendering for policy elements
- `QueryForm` - Search form layouts (prefer @zhongan/nagrand-ui)
- `EditDrawer` - Edit/create drawers with tables
- `StatusTag` - Status indicators (prefer @zhongan/nagrand-ui)

**`main/pages/`** - Feature modules:
- `configuration-pages/` - System configuration features
- `nb-pages/` - New business workflows
- `uw-pages/` - Underwriting operations
- `policy-query-pages/` - Policy search and details
- `proposal-pages/` - Proposal management

**`main/util/`** - Utility functions:
- `request.ts` - Common API request methods
- `schemaUtil.ts` - Schema to Formily conversion
- `renderSections.tsx` - Dynamic section rendering
- `i18nFn.ts` - Internationalization outside React components

## Development Guidelines

### Component Preferences
1. **Nagrand UI**: Prefer `@zhongan/nagrand-ui` components over local equivalents
2. **Genesis Components**: Use `genesis-web-component` for shared business logic
3. **Local Components**: Only for UW-specific business requirements

### Code Conventions
- **Files**: Use TypeScript (.tsx/.ts), SCSS modules for styles
- **Naming**: PascalCase for components, camelCase for utilities
- **URLs**: Must include `/uw` prefix after port
- **Internationalization**: Use `uw` namespace, add keys to `locales/` files
- **API Requests**: Encapsulate as custom hooks in `hooks/request.ts`

### Form Development
- Use Formily v2.3.1 with Ant Design v5 integration
- Schema-driven forms via `useQuerySchema` hook
- Dynamic field rendering with `getFieldsQueryForm` utility
- Investment elements handling with `useApplicationElements`

### Styling Guidelines
- SCSS modules for component-specific styles
- Tailwind utilities for common patterns
- Ant Design theme integration
- Variables defined in `styles/variables.scss`

### Testing and Quality Assurance
- No specific test framework configured in package.json
- Uses @testing-library/react for component testing infrastructure
- Always run `pnpm lint-staged` before committing
- Check README or ask for project-specific test commands

### 文档生成风格指南

当为 Genesis Web UW 项目生成文档时，遵循以下统一风格：

**文档结构模板：**
1. **概述** - 简要说明模块业务功能和职责
2. **核心业务组件** - 按组件逐个详细说明，包含文件位置和核心业务逻辑
3. **核心业务流程** - 使用 Mermaid 流程图描述关键业务流程
4. **数据结构与状态管理** - 说明关键 Atom 状态和接口类型
5. **业务规则与约束** - 详细说明业务逻辑和规则
6. **集成与依赖** - 外部依赖和内部模块依赖
7. **扩展点与定制** - 可扩展的功能点
8. **错误处理** - 异常情况处理
9. **📋 待办事项** - 待完成功能
10. **文档版本信息** - 更新时间和版本号

**格式规范：**
- 使用中英文混合，业务概念用中文，技术术语保持英文
- 代码引用格式：`[描述](#锚点) → [文件名:行号](./文件路径#L行号)`
- 流程图使用 Mermaid 语法
- 重要提示使用 **⚠️ 重要：** 标记
- 状态管理链接格式：`[atomName](/完整路径): 描述`
- 分段使用 `###` 三级标题，具体功能使用 `####` 四级标题

### Important Technical Notes
- **Qiankun Integration**: Check `isQiankun()` utility for micro-frontend context
- **Port Requirements**: Development must use port 8080 for authentication
- **Business Dictionary**: Use `useBizDict` for metadata-driven options
- **Schema System**: Leverage `useQuerySchemaDef` for dynamic form configurations
- **Internationalization**: Support for multiple locales (zh_CN, en_US, ja_JP, in_ID, tkf_en)
- **Route Structure**: All routes include `/uw` prefix and use permission-based access control
- **Module Resolution**: Uses path aliases (`@uw` maps to `main/`) for cleaner imports